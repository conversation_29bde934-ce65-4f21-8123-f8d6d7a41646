package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.User;

@Repository
public interface PlanningValidationRepository extends JpaRepository<PlanningValidation, Long> {
	@Query("SELECT p from PlanningValidation p WHERE p.delegate.id =:id and p.date=:date ")
	PlanningValidation findPlanningValidationByUserAndDate(@Param("id") Long id, @Param("date") Date date);

	@Query("SELECT p.delegate from PlanningValidation p WHERE p.delegate.user.id IN (:subUsersIds) AND  p.date=:date ")
	List<Delegate> findDelegatesWaitingForValidationByWeek(@Param("date") Date date , @Param("subUsersIds") List <Long> subUsersIds);

	@Query("SELECT p.delegate from PlanningValidation p WHERE  p.date=:date")
	List<Delegate> findDelegatesWaitingForValidationByWeekBySupervisor(@Param("date") Date date);

	@Modifying
	@Query("DELETE FROM PlanningValidation where id=:id")
	void deleteById(@Param("id") Long id);

	@Query("SELECT p from PlanningValidation p WHERE DATE(p.date) > DATE(:date) And p.delegate.id = :userId") 
	List<PlanningValidation> findPlanningValidationByUser(@Param("date") Date date, @Param("userId") Long userId );

	@Query("SELECT p from PlanningValidation p WHERE p.delegate.id = :userId") 
	List<PlanningValidation> findAllPlanningValidationByUser( @Param("userId") Long userId );

	
	@Modifying
	@Query("DELETE PlanningValidation pv WHERE pv.id in (:planningValidationsIds)")
	void deleteByIds(@Param("planningValidationsIds") List<Long> planningValidationsIds);

	@Query("SELECT p FROM PlanningValidation p WHERE p.identifier = ?1 And p.delegate.id = ?2")
	PlanningValidation findByIdentifier(Long identifier, Long userId );
	
	@Modifying
	@Query("Delete FROM PlanningValidation p WHERE p.identifier = ?1 And p.delegate.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);
	
	@Query ("SELECT p.id FROM PlanningValidation p where p.identifier = ?1 And p.delegate.id = ?2")
	Long getIdByIdentifier (Long identifier, Long userId);

	List<PlanningValidation> findByDelegateAndDateGreaterThanEqual(Delegate delegate, Date currentWeekDate);
	
	@Query("SELECT p from PlanningValidation p WHERE  p.date= ?2 and p.delegate.id = ?1 and p.status <> 'DELETED'")
	PlanningValidation findByUserAndDate(Long userId, Date date);
	
	@Modifying
	@Query(value ="Update planning_validation set planning_date =planning_date + interval '1 week' WHERE delegate_id =? and planning_date >= ? and status <>'DELETED'", nativeQuery = true)
	void postponePlanningValidation(Long userId, Date currentWeekDate);
	
	
}
