
package com.intellitech.birdnotes;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.support.ResourceBundleMessageSource;

@SpringBootApplication
public class BirdnotesWebBackApplication extends SpringBootServletInitializer {

	private static final Logger LOG = LoggerFactory.getLogger(BirdnotesWebBackApplication.class);
	
	/**
	 * Used when run as WAR
	 */
	
	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(BirdnotesWebBackApplication.class);
	}
	
	 @Bean
	    public ResourceBundleMessageSource messageSource() {
		 ResourceBundleMessageSource messageSource = new ResourceBundleMessageSource();
			messageSource.setBasenames("lang/messages");
			messageSource.setDefaultEncoding("UTF-8");
	        return messageSource;
	    }
	
	/**
	 * 
	 * Used when run as JAR
	 */
	
	public static void main(String[] args) {
		try {
			LOG.info("Application started at {} " , new Date());
			SpringApplication.run(BirdnotesWebBackApplication.class, args);
		} catch (Exception e) {
			LOG.error("An exception occurred at " + new Date(), e);
		}
	}
}
