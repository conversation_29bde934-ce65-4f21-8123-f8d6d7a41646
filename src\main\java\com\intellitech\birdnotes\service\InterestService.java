package com.intellitech.birdnotes.service;

import java.util.List;
import java.util.Set;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.dto.InterestDto;


public interface InterestService {
	
	List<InterestDto> findAll() throws BirdnotesException;
	
	void saveAllInterests(List<InterestDto> interestDtos) throws BirdnotesException;

	InterestDto findInterestDto(String interestName, List<InterestDto> interestDtos);
	
	Interest saveInterest(InterestDto interestDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;
	
    Set<Interest> findInterestsByIds(List<Long> interestIds);






	

}