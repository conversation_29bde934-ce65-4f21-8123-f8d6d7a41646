package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.RangeRequestDto;
import com.intellitech.birdnotes.service.RangeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/range")
public class RangeController {
	private static final Logger LOG = LoggerFactory.getLogger(RangeController.class);

	@Autowired
	private RangeService gammeService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addGamme(@RequestBody RangeRequestDto gammeRequestDto) {
		try {
			if (userService.checkHasPermission("GAMME_ADD")) {
				Range gammeSaved = gammeService.add(gammeRequestDto);
				if (gammeSaved != null) {
					return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
				}
				return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<String>("", HttpStatus.CONFLICT);
			}

		} catch (BirdnotesException fe) {
			return new ResponseEntity<String>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in saveGamme", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}


	@RequestMapping(value = "findAllRange", method = RequestMethod.GET)
	public ResponseEntity<List<RangeDto>> findAllGamme() {

		try {
			if (userService.checkHasPermission("GAMME_VIEW")) {
				List<RangeDto> rangeDto = gammeService.findAll();
				return new ResponseEntity<>(rangeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all gamme", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findSubRange", method = RequestMethod.GET)
	public ResponseEntity<List<RangeDto>> findSubRange() {

		try {
			if (userService.checkHasPermission("GAMME_VIEW")) {
				List<RangeDto> rangeDto = gammeService.findSubRange();
				return new ResponseEntity<>(rangeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all gamme", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteGamme(@PathVariable("id") Integer id) {

		try {
			if (userService.checkHasPermission("GAMME_DELETE")) {
				gammeService.delete(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in delete Gamme", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/saveRange", method = RequestMethod.POST)
	public ResponseEntity<String> saveRange(@RequestBody RangeDto gammeDto) {

	    try {
	        if (userService.checkHasPermission("GAMME_EDIT")) {
	            Range savedRange = gammeService.saveRange(gammeDto);
	            if (savedRange != null) {
	                return new ResponseEntity<>(savedRange.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving range", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving range", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

}
