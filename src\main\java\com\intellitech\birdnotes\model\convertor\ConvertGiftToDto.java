package com.intellitech.birdnotes.model.convertor;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.GiftDto;


@Component("convertGadgetToDto")
public class ConvertGiftToDto {
	private static final Logger LOG = LoggerFactory.getLogger(ConvertGiftToDto.class);

	public GiftDto convert(Gift gadget) throws BirdnotesException {

		if (gadget == null) {
			LOG.error("gadgets is null");
			throw new BirdnotesException("gadgets is null");
		}
		
		GiftDto gadgetDto = new GiftDto();
		gadgetDto.setName(gadget.getName());
		gadgetDto.setId(gadget.getId());
		return gadgetDto;
	}

}
