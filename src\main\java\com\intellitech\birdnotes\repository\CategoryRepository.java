package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Category;

@Repository
public interface CategoryRepository extends JpaRepository<Category, Integer> {
	@Override
	@Query("SELECT c from Category c order by c.name")
	List<Category> findAll();

}