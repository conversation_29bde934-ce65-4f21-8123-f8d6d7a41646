package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class NextActionRuleDto implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private Long id;
	private Integer totalRevenue;
	private Integer period;
	private String action;
	
	public NextActionRuleDto() {
		super();
	}
	
	public NextActionRuleDto(Long id, Integer totalRevenue, Integer period, String action) {
		super();
		this.id = id;
		this.totalRevenue = totalRevenue;
		this.period = period;
		this.action = action;
		
	}

	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getAction() {
		return action;
	}
	
	public void setAction(String action) {
		this.action = action;
	}

	public Integer getTotalRevenue() {
		return totalRevenue;
	}

	public void setTotalRevenue(Integer totalRevenue) {
		this.totalRevenue = totalRevenue;
	}

	public Integer getPeriod() {
		return period;
	}

	public void setPeriod(Integer period) {
		this.period = period;
	}



}