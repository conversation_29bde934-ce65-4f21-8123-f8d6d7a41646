package com.intellitech.birdnotes.model.convertor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.GoalItemRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.UserService;

@Component("convertGoalManagementToDto")
public class ConvertGoalToDto {
	private UserRepository userRepository;
	private GoalItemRepository goalItemRepository;
	@Autowired
	private DelegateRepository delegateRepository;
	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
		
	}
	@Autowired
	public void setGoalItemRepository(GoalItemRepository goalItemRepository) {
		this.goalItemRepository = goalItemRepository;
		
	}
	private static final Logger LOG = LoggerFactory.getLogger(ConvertGoalToDto.class);

	public GoalDto convert(Goal goal) throws BirdnotesException {
		if (goal == null) {
			LOG.error("goal is null");
			throw new BirdnotesException("goal is null");
		}
		GoalDto goalManagementDto = new GoalDto();
		goalManagementDto.setId(goal.getId());
		goalManagementDto.setName(goal.getName());
		goalManagementDto.setItemsOrder(goal.getItemsOrder());
		goalManagementDto.setFirstDate(goal.getFirstDate());
		goalManagementDto.setLastDate(goal.getLastDate());
		goalManagementDto.setGoalType(goal.getGoalType().name());
		if(goal.getPeriod() != null) {
			goalManagementDto.setPeriod(goal.getPeriod().name());
		}
		
		Set<UserDto> users = new HashSet <UserDto>();
		Set<User> userList= userRepository.findUsersOfGoal(goal.getId());
		for(User user: userList) {
		UserDto userDto = new UserDto();
		if (user != null) {
			userDto.setId(user.getId());
			Delegate delegate = delegateRepository.findDelegateByUserId(user.getId());
			if(delegate != null) {
				userDto.setFirstName(delegate.getFirstName());
				userDto.setLastName(delegate.getLastName());
			}
			else {
				userDto.setUsername(user.getUsername());
			}
			users.add(userDto);
		}
		else {
			LOG.error("user is null");
			throw new BirdnotesException("user is null");
		}
		}
		goalManagementDto.setUsers(users);
		List<GoalItemDto> goalItems = new ArrayList <GoalItemDto>();
		Set<GoalItem> goalItemList = goalItemRepository.findByGoal(goal);
		for(GoalItem goalItem: goalItemList) {
		GoalItemDto goalItemDto = new GoalItemDto();
		if (goalItem != null) {
			goalItemDto.setId(goalItem.getId());
			goalItemDto.setValue(goalItem.getValue());
			if(goalItem.getActivity() != null) {
			goalItemDto.setActivity(goalItem.getActivity());
			}
			if (goalItem.getProduct() != null) {
			goalItemDto.setProductId(goalItem.getProduct().getId());
			}
			if(goalItem.getPotential() != null) {
			goalItemDto.setPotentialId(goalItem.getPotential().getId());
			}
			if (goalItem.getSector()!= null) {
			goalItemDto.setSectorId(goalItem.getSector().getId());
			}
			if (goalItem.getSpeciality() != null) {
			goalItemDto.setSpecialityId(goalItem.getSpeciality().getId());
			}
			if (goalItem.getProspectType() != null) {
			goalItemDto.setProspectTypeId(goalItem.getProspectType().getId());
			}

			goalItems.add(goalItemDto);
		} else {
			LOG.error("goal item is null");
			throw new BirdnotesException("goal item is null");
		}
		}
		goalManagementDto.setGoalItems(goalItems);
		return goalManagementDto;

	}
}
