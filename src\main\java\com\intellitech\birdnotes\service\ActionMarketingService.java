package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.dto.ActionMarketingDto;
import com.intellitech.birdnotes.model.dto.ActionMarketingResponseDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.MarketingActionOrderPredictionDto;
import com.intellitech.birdnotes.model.request.ActionMarketingRequest;

public interface ActionMarketingService {
	
	ActionMarketing add(ActionMarketingRequest actionMarketingRequest) throws BirdnotesException;

	   
	void delete(Long id) throws BirdnotesException;

	ActionMarketing saveMarketingAction(ActionMarketingDto actionMarketingDto) throws BirdnotesException;
	
	List<ActionMarketingResponseDto> findActionMarketingByUser(Long userId);

	
	List<ActionMarketingDto> findActionMarketingByDate(Date firstDate, Date lastDate) throws BirdnotesException;


	List<LabelValueDto> getMarketingExpensesByDelegate(Date startDate, Date endDate);


	void acceptValidationStep (Long id) throws BirdnotesException;
	void refuseValidationStep (Long id) throws BirdnotesException;
	ActionMarketingDto getActionMarketingById(long id)throws BirdnotesException;


	Long predictMarketingActionOrder(MarketingActionOrderPredictionDto predictionDto) throws BirdnotesException;
}
