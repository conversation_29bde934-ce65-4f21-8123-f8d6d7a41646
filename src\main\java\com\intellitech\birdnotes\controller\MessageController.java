package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.ReportMessageRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.MessageDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.CommissionDto;
import com.intellitech.birdnotes.model.dto.CommissionItemDto;
import com.intellitech.birdnotes.model.dto.DelegateCommissionDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.MessageService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.CommissionService;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.SampleSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/message")
public class MessageController {
	private static final Logger LOG = LoggerFactory.getLogger(MessageController.class);
	@Autowired
	private MessageService messageService;
	@Autowired
	UserService userService;
	
	@Autowired
	DelegateService delegateService;
	
	@Autowired
	private CurrentUser currentUser;

	


	@RequestMapping(value = "/getMessageByDateAndUser", method = RequestMethod.POST)

	public ResponseEntity<List<MessageDto>> getMessageByDateAndUser(
			@RequestBody ReportMessageRequestDto reportMessageRequestDto) {

		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				List<MessageDto> messageDto = messageService
						.getMessageByDateAndUser(reportMessageRequestDto.getStartDate(), reportMessageRequestDto.getLastDate(), reportMessageRequestDto.getUserId());
				return new ResponseEntity<>(messageDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting all messageDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}



	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("COMMISSION_VIEW")) {
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	
}
