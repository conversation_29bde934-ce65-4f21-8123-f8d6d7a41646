package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.data.dto.SurveyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Criteria;
import com.intellitech.birdnotes.model.CriteriaList;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Survey;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.SurveyDto;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.SurveyRepository;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.SurveyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.thread.ThreadSendEmail;

@Service("surveyService")
@Transactional
public class SurveyServiceImpl implements SurveyService {
	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	private ProductRepository productRepository;
	@Autowired
	private SurveyRepository surveyRepository;
	@Autowired
	ResourceLoader resourceLoader;
	@Autowired
	private ProductService productService;
    @Autowired
    private JavaMailSender javaMailSender;
    @Autowired
    private UserService userService;
    private String currentCriteriaFileName;

	private void sendSurveyNotificationEmail(String criteriaFileName, String email) {
        try {
            String sendSurveyEmailSubject = userService.getTranslatedLabel("sendSurveyEmailSubject");
            String sendSurveyEmailHtmlBody = userService.getTranslatedLabel("sendSurveyEmailHtmlBody");
            
            MessageFormat messageFormat = new MessageFormat(sendSurveyEmailHtmlBody);
            String emailBody = messageFormat.format(new String[]{criteriaFileName});

            String[] to = {email};

            ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, sendSurveyEmailSubject, emailBody);
            Thread thread = new Thread(threadSendEmail);
            thread.start();
        } catch (Exception e) {
            log.error("Error sending survey notification email", e);
        }
    }

    @Override
    public void saveSurvey(SurveyDto surveyDto) throws BirdnotesException {

        if (surveyDto.getEvaluationDate() == null) {
            surveyDto.setEvaluationDate(new Date());
        }

        for (Map.Entry<String, Float> entry : surveyDto.getRatings().entrySet()) {
            Survey survey = null;
            if (surveyDto.getId() != null) {
                survey = surveyRepository.findOne(surveyDto.getId());
            }
            if (survey == null) {
                survey = new Survey();
            }

            if (surveyDto.getProductId() != null) {
                Product product = productRepository.findById(surveyDto.getProductId());
                survey.setProduct(product);
            }

            survey.setEvaluationDate(surveyDto.getEvaluationDate());

            if (surveyDto.getEvaluationCriteriaValue() != null) {
                survey.setEvaluationCriteriaName(entry.getKey());
                survey.setEvaluationCriteriaValue(entry.getValue());
            }
            if (surveyDto.getComment() != null) {
                survey.setComment(surveyDto.getComment());
            }

            surveyRepository.save(survey);
        }
         
        String email = getDestinationEmail(surveyDto.getCriteriaFileName());
        if(email != null && !email.isEmpty()) {
        	this.sendSurveyNotificationEmail(surveyDto.getCriteriaFileName(), email);
        }
        
        
    }

		
		

		

	

    @Override
    public SurveyFormData getAllDataSurvey(String criteriaFileName) throws IOException, BirdnotesException {
        this.currentCriteriaFileName = criteriaFileName;  
        List<ProductDto> productDto = productService.getAllProducts();
        List<Criteria> criteria = getCriteriaXml(criteriaFileName);
        SurveyFormData surveyFormData = new SurveyFormData();
        surveyFormData.setProducts(productDto);
        surveyFormData.setCriterias(criteria);

        return surveyFormData;
    }
    
    
 
    private  String getDestinationEmail(String criteriaFileName)  {
        try {

            Resource resource = resourceLoader.getResource("classpath:" + criteriaFileName +".xml");

            InputStream input = resource.getInputStream();
            JAXBContext context = JAXBContext.newInstance(CriteriaList.class, Criteria.class);
            Unmarshaller unmarshaller = context.createUnmarshaller();

            // Use InputStream or File for unmarshaling
            CriteriaList criteriaList = (CriteriaList) unmarshaller.unmarshal(input);

        

            return criteriaList.getDestination();

        } catch (Exception e) {
            log.error("Error in getDestinationEmail ", e);
            return "";
        }
    }


	@Override
    public List<Criteria> getCriteriaXml(String criteriaFileName) throws IOException {
        try {

            Resource resource = resourceLoader.getResource("classpath:" + criteriaFileName +".xml");

            InputStream input = resource.getInputStream();
            JAXBContext context = JAXBContext.newInstance(CriteriaList.class, Criteria.class);
            Unmarshaller unmarshaller = context.createUnmarshaller();

            // Use InputStream or File for unmarshaling
            CriteriaList criteriaList = (CriteriaList) unmarshaller.unmarshal(input);

        

            return criteriaList.getCriteria();

        } catch (Exception e) {
            log.error("Error in getXml ", e);
            return new ArrayList<>();
        }
    }

	@Override
	public List<SurveyDto> getAllSurveys() throws BirdnotesException {
		List<SurveyDto> result = new ArrayList<>();
		List<Survey> surveys = surveyRepository.findAll();
		if (surveys != null && !surveys.isEmpty()) {
			for (Survey survey : surveys) {
				SurveyDto surveyDto = new SurveyDto();
				surveyDto.setId(survey.getId());
				surveyDto.setEvaluationCriteriaName(survey.getEvaluationCriteriaName());
				surveyDto.setEvaluationCriteriaValue(survey.getEvaluationCriteriaValue());
				surveyDto.setEvaluationDate(survey.getEvaluationDate());
				surveyDto.setComment(survey.getComment());
				surveyDto.setProductName(survey.getProduct().getName());

				result.add(surveyDto);
			}

		}
		return result;
	}

}
