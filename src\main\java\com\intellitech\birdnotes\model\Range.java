package com.intellitech.birdnotes.model;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.RANGE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Range {

	private static final long serialVersionUID = 1L;
	@Id
	@SequenceGenerator(name = Sequences.RANGE_SEQUENCE, sequenceName = Sequences.RANGE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.RANGE_SEQUENCE)
	@Column(name = Columns.ID)
	private Integer id;
	
	@Column(name = Columns.NAME)
	private String name;
	
	@OneToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.PARENT_ID)
	private Range parent;
	
	public Range() { }

	public Range(Integer id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	

	@ManyToMany(mappedBy = "ranges")
    private Set<Product> products = new HashSet<>();
	
	@ManyToMany(mappedBy = "ranges")
    private Set<User> users = new HashSet<>();

	


	
	public Range getParent() {
		return parent;
	}

	public void setParent(Range parent) {
		this.parent = parent;
	}
	
}
