package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.model.Goal;

@Repository
public interface GoalRepository extends JpaRepository<Goal, Long>{
	
	Goal findByName(String name);
	
	Goal findFirstByNameIgnoreCase(String name);
	
	
	@Query("SELECT g from Goal g order by g.name")
	List<Goal> findAll();
	
	@Modifying
	@Query("DELETE FROM Goal where id=:id")
	void deleteById(@Param ("id") Integer id);
	
	@Query("SELECT count(g) from Goal g join g.users u where g.firstDate <= (:startDate) AND  g.lastDate >= (:endDate) AND g.goalType = (:goalType) AND u.id = (:userId)")
	Long checkGoalPeriodUnicity(@Param("startDate")Date firstDate, @Param("endDate")Date lastDate,@Param("goalType") GoalType goalType, @Param("userId") Long userId);

	@Query("SELECT g from Goal g WHERE g.name=:name AND g.id!=:id order by g.name")
	Goal findByNameWithDiffId(@Param("name")String name, @Param("id")Long id);

}
