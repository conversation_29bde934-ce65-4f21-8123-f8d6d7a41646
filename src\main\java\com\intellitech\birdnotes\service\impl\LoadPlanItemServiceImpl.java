package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.model.LoadPlan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.LoadPlanItem;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.LoadPlanItemDto;
import com.intellitech.birdnotes.model.dto.LoadPlanRequest;
import com.intellitech.birdnotes.repository.LoadPlanItemRepository;
import com.intellitech.birdnotes.repository.LoadPlanRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.LoadPlanItemService;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Service("loadPlanItemService")
@Transactional
public class LoadPlanItemServiceImpl implements LoadPlanItemService {
	private ProductRepository productRepository;
	private LoadPlanItemRepository loadPlanItemRepository;
	private LoadPlanRepository loadPlanRepository;
	private SpecialityRepository specialityRepository;
	@Autowired
	private CurrentUser currentUser;

	@Autowired
	public void setProductRepository(ProductRepository productRepository) {
		this.productRepository = productRepository;
	}

	@Autowired
	public void setLoadPlanRepository(LoadPlanRepository loadPlanRepository) {
		this.loadPlanRepository = loadPlanRepository;
	}


	@Autowired
	public void setLoadPlanItemRepository(LoadPlanItemRepository loadPlanItemRepository) {
		this.loadPlanItemRepository = loadPlanItemRepository;
	}

	@Autowired
	public void setSpecialityRepository(SpecialityRepository specialityRepository) {
		this.specialityRepository = specialityRepository;
	}

	@Override
	public void saveLoadPlan(List<LoadPlanRequest> loadPlanItemRequest) throws BirdnotesException {

		if (loadPlanItemRequest == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_CHARGE_PLAN);
		} else {
			LoadPlan loadPlan = null;
			List<Long> productToDelete = productRepository
					.findProductByRange(loadPlanItemRequest.get(0).getSelectedRanges());
			if (productToDelete.size() > 0) {
				loadPlanItemRepository.deleteByRangeAndSpeciality(loadPlanItemRequest.get(0).getSelectedSpecialities(),
						productToDelete);
			}
			if(loadPlanItemRequest.get(0).getLoadPlanId() != null) {
				loadPlan =  loadPlanRepository.findOne(loadPlanItemRequest.get(0).getLoadPlanId());
			}
			for (LoadPlanRequest specialityProduct : loadPlanItemRequest) {

				Integer i = 1;
				if (specialityProduct.getProductNames() != null) {
					for (String productId : specialityProduct.getProductNames()) {
						LoadPlanItem loadPlanItem = new LoadPlanItem();
						loadPlanItem.setProduct(productRepository.findByName(productId));
						loadPlanItem.setSpeciality(specialityRepository.findByName(specialityProduct.getName()));
						loadPlanItem.setRank(i);
						i++;
						if(loadPlan != null) {
							loadPlanItem.setLoadPlan(loadPlan);
						}
						
						loadPlanItemRepository.save(loadPlanItem);

					}
				}
			}
		}
	}

	@Override
	public List<LoadPlanItemDto> findAll(List<Integer> rangeIds, List<Long> specialityIds, Long loadPlanId) throws BirdnotesException {
		List<LoadPlanItemDto> back = new ArrayList<>();
		List<Speciality> allSpecialities = specialityRepository.findAll(specialityIds);
		Long userId = currentUser.getBirdnotesUser().getUserDto().getId();
		for (Speciality speciality : allSpecialities) {
			LoadPlanItemDto loadPlanItemDto = new LoadPlanItemDto();
			loadPlanItemDto.setId(speciality.getId());
			loadPlanItemDto.setName(speciality.getName());
			Set<String> productNames = loadPlanItemRepository.findProductsBySpeciality(userId, speciality.getId(), rangeIds, loadPlanId);

			loadPlanItemDto.setProductNames(productNames);

			back.add(loadPlanItemDto);

		}

		return back;
	}

}