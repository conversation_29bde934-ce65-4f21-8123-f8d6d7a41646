package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.model.dto.OrderProductDto;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.service.VisitsProductsService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesUtils;

@Service("visitsProductsService")
@Transactional
public class VisitsProductsServiceImpl implements VisitsProductsService {

	private VisitsProductsRepository visitsProductsRepository;

	private ProductRepository productRepository;

	@Autowired
	public VisitsProductsServiceImpl(VisitsProductsRepository visitsProductsRepository,
			ProductRepository productRepository) {
		super();
		this.visitsProductsRepository = visitsProductsRepository;
		this.productRepository = productRepository;
	}

	@Override
	public List<OrderProductDto> averageOrderProduct(Long productId, Date mondayDate) {
		Date saturdayDate = BirdnotesUtils.addDaysToDate(mondayDate, 5);

		List<OrderProductDto> orderProductDtos = visitsProductsRepository.averageOrderProduct(productId, mondayDate,
				saturdayDate);

		List<OrderProductDto> _orderProductDtos = new ArrayList<>();

		Long countProductNumber = productRepository.count();

		if (orderProductDtos != null && !orderProductDtos.isEmpty()) {
			averageOrder(countProductNumber,orderProductDtos,_orderProductDtos);
		} else {
			for (Integer i = 1; i < countProductNumber + 1; i++) {
				OrderProductDto __orderProductDto = new OrderProductDto(i, 0L);
				__orderProductDto.setPresentationLabel(BirdnotesConstants.Common.ORDER_LABEL + " " + i);
				_orderProductDtos.add(__orderProductDto);
			}
		}
		return _orderProductDtos;
	}

	private void averageOrder(Long countProductNumber, List<OrderProductDto> orderProductDtos, List<OrderProductDto> _orderProductDtos) {
		for (Integer i = 1; i < countProductNumber + 1; i++) {
			boolean isPresent = false;
			OrderProductDto _orderProductDto = null;
			for (OrderProductDto orderProductDto : orderProductDtos) {
				if (orderProductDto.getRank() == i) {
					isPresent = true;
					_orderProductDto = orderProductDto;
					break;
				}

			}

			if (isPresent) {
				_orderProductDto.setPresentationLabel(
						BirdnotesConstants.Common.ORDER_LABEL + " " + _orderProductDto.getRank());
				_orderProductDtos.add(_orderProductDto);
			} else {
				OrderProductDto __orderProductDto = new OrderProductDto(i, 0L);
				__orderProductDto.setPresentationLabel(BirdnotesConstants.Common.ORDER_LABEL + " " + i);
				_orderProductDtos.add(__orderProductDto);
			}

		}
	}

}
