package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.NotificationDto;
import com.intellitech.birdnotes.model.request.NotificationRequest;

public interface NotificationService {

	List<NotificationDto> findByUser(User user, NotificationRequest issueRequest);

	List<NotificationDto> findAll(User user, NotificationRequest issueRequest) throws BirdnotesException;


	int getNotificationNumber(User user);

	//List<NotificationDto> findDelegateNotification(Long id, User delegate);

	void updateNotificationStatus (User user);

	void sentPushNotification(Notification notification);
	
	void sentEmailNotification(Notification notification);

	void generateUsingAllNotificationMethods(Notification notification);

	void sentSMSNotification(Notification notification);
	
	public  void sendSingleNotification(User user, Long entityId , String messageType, List <String > prospectNames );
	
	public  void sendSingleNotification(User user, Long entityId , String messageType, Prospect prospect );
	
	public  void sendSingleNotification(User user, Long entityId, String messageType  );
	
	public  void sendNotificationUsingWorkflow(User sourceUser , String eventType, Date date, String status );
	
	public  void sendNotificationUsingWorkflow(User sourceUser , Long entityId, String eventType, List <String > prospectNames );
	
	public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId,  String eventType);
	
	public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId, String eventType, Prospect prospect );
	
	public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId, String eventType, Prospect prospect, Date eventDate, String text, String productName );
	
	public void sendAppNotification(Notification notification);

	List<NotificationDto> findDelegateNotification(Long id, Long userId);
}