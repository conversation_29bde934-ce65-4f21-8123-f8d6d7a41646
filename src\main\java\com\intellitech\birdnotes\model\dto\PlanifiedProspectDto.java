package com.intellitech.birdnotes.model.dto;

import java.util.Date;

public class PlanifiedProspectDto extends ProspectDto {

	private Long planningId;
	private Date planningDate;
	private boolean prospectVisited;
	private String activityTypeName;
	private Long activityId;
	private String plannedActivityStatus;

	public boolean isProspectVisited() {
		return prospectVisited;
	}

	public void setProspectVisited(boolean prospectVisited) {
		this.prospectVisited = prospectVisited;
	}

	private static final long serialVersionUID = 1L;

	public Date getPlanningDate() {
		return planningDate;
	}

	public void setPlanningDate(Date planningDate) {
		this.planningDate = planningDate;
	}

	public Long getPlanningId() {
		return planningId;
	}

	public void setPlanningId(Long planningId) {
		this.planningId = planningId;
	}

	public String getActivityTypeName() {
		return activityTypeName;
	}

	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getPlannedActivityStatus() {
		return plannedActivityStatus;
	}

	public void setPlannedActivityStatus(String plannedActivityStatus) {
		this.plannedActivityStatus = plannedActivityStatus;
	}

}