package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ReportCron;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ReportCronDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.request.ReportCronRequest;
import com.intellitech.birdnotes.repository.ReportCronRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.ReportCronService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("reportCronService")
@Transactional
public class ReportCronServiceImpl implements ReportCronService {

	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	private UserRepository userRepository;
	private ReportCronRepository reportCronRepository;
	private UserToDtoConvertor userToDtoConvertor;

	@Autowired
	private UserService userService;
	
	@Autowired
	private TaskScheduler taskScheduler;
	
	private Map<Integer, ScheduledFuture<?>> scheduledTasks;
	
	@Autowired
	public ReportCronServiceImpl(UserRepository userRepository, ReportCronRepository reportCronRepository,
			UserToDtoConvertor userToDtoConvertor) {
		super();
		this.userRepository = userRepository;
		this.reportCronRepository = reportCronRepository;
		this.userToDtoConvertor = userToDtoConvertor;
	}
	

	@Override
	public void add(ReportCronRequest reportCronRequest) throws BirdnotesException {
		if (reportCronRequest.getPeriod() == null || "".equals(reportCronRequest.getPeriod())) {
			throw new BirdnotesException(Exceptions.PERIOD_IS_EMPTY);
		}
		if (reportCronRequest.getReportLink() == null || "".equals(reportCronRequest.getReportLink())) {
			throw new BirdnotesException(Exceptions.LINK_IS_EMPTY);
		}
		if (reportCronRequest.getUsers() == null || reportCronRequest.getUsers().size() == 0) {
			throw new BirdnotesException(Exceptions.REPORTCRON_USER_IS_EMPTY);
		}
		List<User> users = new ArrayList<User>();
		ReportCron reportCron = new ReportCron();
		reportCron.setLink(reportCronRequest.getReportLink());
		reportCron.setPeriod(reportCronRequest.getPeriod());

		for (Long userId : reportCronRequest.getUsers()) {
			User user = userRepository.findOne(userId);
			user.setReportCron(reportCron);
			users.add(user);
			userRepository.save(user);
		}
		reportCron.setUsers(users);
		reportCronRepository.save(reportCron);
	}

	@Override
	public List<ReportCronDto> findAll()  {
		List<ReportCronDto> result = new ArrayList<>();
		List<ReportCron> allReportCron = reportCronRepository.findAll();

		for (ReportCron reportCron : allReportCron) {
			ReportCronDto r = new ReportCronDto();
			List<Long> usersId = new ArrayList<>();
			List<UserDto> users = new ArrayList<UserDto>();
			r.setId(reportCron.getId());
			r.setPeriod(reportCron.getPeriod());
			r.setReportLink(reportCron.getLink());
			for (User user : reportCron.getUsers()) {
				UserDto userDto = userToDtoConvertor.convert(user);
				users.add(userDto);
				usersId.add(user.getId());
			}
			r.setUsersId(usersId);
			r.setUsers(users);
			result.add(r);
		}
		return result;
	}

	@Override
	public void delete(Integer id) throws BirdnotesException {
			ReportCron reportCron = reportCronRepository.findOne(id);
			for (User user : userRepository.findByReportCron(reportCron)) {
				user.setReportCron(null);
				userRepository.save(user);
			}
			reportCronRepository.delete(id);

	}

	@Override
	public ReportCron saveReportCron(ReportCronDto reportCronDto) throws BirdnotesException {
		if (reportCronDto == null || reportCronDto.getId() == null) {
			throw new BirdnotesException(Exceptions.REPORT_CRON_ID_DTO_IS_NULL);
		}
		if (reportCronDto.getPeriod() == null || reportCronDto.getPeriod().isEmpty()) {
			throw new BirdnotesException(Exceptions.REPORT_CRON_PERIOD_IS_EMPTY);
		}
		if (reportCronDto.getReportLink() == null || reportCronDto.getReportLink().isEmpty()) {
			throw new BirdnotesException(Exceptions.REPORT_CRON_LINK_IS_EMPTY);
		}

		if (reportCronDto.getUsersId() == null || reportCronDto.getUsersId().isEmpty()) {
			throw new BirdnotesException(Exceptions.REPORT_CRON_USERS_IS_EMPTY);
		}
		ReportCron reportCron = null;

		if (reportCronDto.getId() != null) {
			reportCron = reportCronRepository.findOne(reportCronDto.getId());
			if (reportCron != null && !reportCronDto.getPeriod().equals(reportCron.getPeriod())) {
			    if (scheduledTasks != null && scheduledTasks.get(reportCronDto.getId()) != null) {
			        scheduledTasks.get(reportCronDto.getId()).cancel(false);
			    }			}

		}
		if (reportCron == null) {
			reportCron = new ReportCron();
		} else {
			for (User user : userRepository.findByReportCron(reportCron)) {
				user.setReportCron(null);
				userRepository.save(user);
			}
		}

		reportCron.setLink(reportCronDto.getReportLink());
		reportCron.setPeriod(reportCronDto.getPeriod());

		List<User> users = new ArrayList<User>();
		for (Long userId : reportCronDto.getUsersId()) {
			User user = userRepository.findOne(userId);
			user.setReportCron(reportCron);
			users.add(user);
			userRepository.save(user);
		}
		reportCron.setUsers(users);
		ReportCron savedReportCron = reportCronRepository.save(reportCron);
		
		CronTrigger cronTrigger = new CronTrigger(reportCronDto.getPeriod());
		Runnable runnable = () -> {
			userService.sendMailReport(reportCronDto.getReportLink(), reportCronDto.getUsers());
		};
		if (scheduledTasks == null) {
		    scheduledTasks = new ConcurrentHashMap<>(); 
		}

		
		scheduledTasks.put(reportCron.getId(), taskScheduler.schedule(runnable, cronTrigger));
		
		
		
		return savedReportCron;
	}

}