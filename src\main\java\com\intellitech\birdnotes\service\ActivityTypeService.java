package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;

public interface ActivityTypeService {
	ActivityType add(ActivityType activityType) throws BirdnotesException;
	void delete(Long id) throws BirdnotesException;
	List<ActivityTypeDto> findAll() throws BirdnotesException;
	ActivityType saveActivityType(ActivityTypeDto activityTypeDto) throws BirdnotesException;
	


}
