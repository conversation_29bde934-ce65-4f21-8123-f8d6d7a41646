package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.FavoriteMenuItem;
import com.intellitech.birdnotes.model.dto.FavoriteDto;
import com.intellitech.birdnotes.model.dto.FavoriteRequestDto;
import com.intellitech.birdnotes.service.FavoriteService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/favorites")
public class FavoriteMenuItemController {
	private static final Logger LOG = LoggerFactory.getLogger(FavoriteMenuItemController.class);

	@Autowired
	private FavoriteService favoriteService;

	@Autowired
	UserService userService;

	@RequestMapping(value = "/findAllFavorites", method = RequestMethod.GET)
	public ResponseEntity<List<FavoriteDto>> findAllFavorites() {
		try {
			if (userService.checkHasPermission("FAVORITE_ADD")) {
				List<FavoriteDto> favoritesDtos = favoriteService.findAll();
				return new ResponseEntity<>(favoritesDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all favorites", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<List<FavoriteMenuItem>> addFavorite(@RequestBody FavoriteRequestDto favoriteRequestDto) {
		try {
			if (userService.checkHasPermission("FAVORITE_ADD")) {
				List<FavoriteMenuItem> favoriteSaved = favoriteService.add(favoriteRequestDto);
				if (favoriteSaved != null) {
					return new ResponseEntity<List<FavoriteMenuItem>>(favoriteSaved, HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add favorite", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new favorite", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteFavorite(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("FAVORITE_DELETE")) {
	            favoriteService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting favorite", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the favorite with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

}
