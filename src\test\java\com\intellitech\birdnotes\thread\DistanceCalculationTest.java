package com.intellitech.birdnotes.thread;

import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.repository.LocationRepository;
import com.intellitech.birdnotes.service.DistanceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DistanceCalculationTest {

    @Mock
    private Delegate mockDelegate;
    @Mock
    private DistanceService mockDistanceService;
    @Mock
    private LocationRepository mockLocationRepository;

    private DistanceCalculation distanceCalculationUnderTest;

    @Before
    public void setUp() throws Exception {
        distanceCalculationUnderTest = new DistanceCalculation(Arrays.asList(
                new LocationDto(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), new Delegate(), 0.0,
                        0.0)), mockDelegate, mockDistanceService, mockLocationRepository);
    }

    @Test
    public void testRun() throws Exception {
        // Setup
        // Configure DistanceService.distance(...).
        final DistanceResponse distanceResponse = new DistanceResponse();
        distanceResponse.setDestination_addresses(new ArrayList<>(Arrays.asList("value")));
        distanceResponse.setOrigin_addresses(new ArrayList<>(Arrays.asList("value")));
        distanceResponse.setRows(new ArrayList<>(Arrays.asList("value")));
        distanceResponse.setStatus("status");
        final ResponseEntity<DistanceResponse> distanceResponseEntity = new ResponseEntity<>(distanceResponse,
                HttpStatus.OK);
        when(mockDistanceService.distance(0.0, 0.0, 0.0, 0.0)).thenReturn(distanceResponseEntity);

        // Run the test
        distanceCalculationUnderTest.run();

        // Verify the results
        //verify(mockLocationRepository).save(any(Location.class));
    }

    @Test
    public void testRun_DistanceServiceThrowsBirdnotesException() throws Exception {
        // Setup
        when(mockDistanceService.distance(0.0, 0.0, 0.0, 0.0)).thenThrow(BirdnotesException.class);

        // Run the test
        distanceCalculationUnderTest.run();

        // Verify the results
        //verify(mockLocationRepository).save(any(Location.class));
    }
}
