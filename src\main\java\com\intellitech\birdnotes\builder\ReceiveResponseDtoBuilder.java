package com.intellitech.birdnotes.builder;

import java.util.List;

import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.model.dto.ActionMarketingResponseDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.AttachmentDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.MinimizedChargePlan;
import com.intellitech.birdnotes.model.dto.NoteDto;
import com.intellitech.birdnotes.model.dto.NotificationDto;
import com.intellitech.birdnotes.model.dto.OpportunityNoteResponseDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PlanningValidationDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateDto;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.ReceiveResponsedDto;
import com.intellitech.birdnotes.model.dto.RecoveryDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SendResultDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationResponse;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.model.dto.VisitProductDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

public class ReceiveResponseDtoBuilder {

	ReceiveResponsedDto receiveResponsedDto = new ReceiveResponsedDto();

	public ReceiveResponseDtoBuilder buildDeletedDto(List<Long> deletedLocalities, List<Long> deletedSectors,
			List<Long> deletedProducts, List<Long> deletedWholesalers, List<Long> deletedPotentialProducts,
			List<Long> deletedSpecialities, List<Long> deletedTypes, List<Integer> deletedGadgets) {

		receiveResponsedDto.setDeletedProducts(deletedProducts);

		return this;
	}

	public ReceiveResponseDtoBuilder buildProductDto(List<ProductDto> products) {
		receiveResponsedDto.setProducts(products);
		return this;
	}

	public ReceiveResponseDtoBuilder buildWholesalerDto(List<WholesalerDto> wholesalers) {
		receiveResponsedDto.setWholesalers(wholesalers);
		return this;
	}

	public ReceiveResponseDtoBuilder buildPotentialProductDto(List<ProductPotentielDto> potentialProducts) {
		receiveResponsedDto.setPotentialProducts(potentialProducts);
		return this;
	}

	public ReceiveResponseDtoBuilder buildGadgetDto(List<GiftDto> gadgets) {
		receiveResponsedDto.setGadgets(gadgets);
		return this;

	}

	public ReceiveResponseDtoBuilder buildProspectDto(List<ProspectDto> prospects, List<SendResultDto> mergedProspects,
			List<Long> deletedProspects, List<Long> refusedProspects, List<ValidationResponse> prospectsValidationResponse) {

		receiveResponsedDto.setProspects(prospects);
		receiveResponsedDto.setMergedProspects(mergedProspects);
		receiveResponsedDto.setDeletedProspects(deletedProspects);
		receiveResponsedDto.setRefusedProspects(refusedProspects);
		receiveResponsedDto.setProspectsValidationResponse(prospectsValidationResponse);
		return this;
	}

	public ReceiveResponseDtoBuilder buildSectors(List<SectorDto> sectors) {
		receiveResponsedDto.setSectors(sectors);
		return this;
	}

	public ReceiveResponseDtoBuilder buildGoal(List<GoalDto> goals) {
		receiveResponsedDto.setGoals(goals);
		return this;
	}

	public ReceiveResponseDtoBuilder buildGoalItems(List<GoalItemDto> goalItem) {
		receiveResponsedDto.setGoalItems(goalItem);
		return this;
	}

	public ReceiveResponseDtoBuilder buildUsers(List<UserDto> users) {
		receiveResponsedDto.setUsers(users);
		return this;
	}

	public ReceiveResponseDtoBuilder buildPurchaseOrderTemplates(
			List<PurchaseOrderTemplateDto> purchaseOrderTemplates) {
		receiveResponsedDto.setPurchaseOrderTemplates(purchaseOrderTemplates);
		return this;
	}

	public ReceiveResponseDtoBuilder buildFreeQuantityRuleItems(List<FreeQuantityRuleItemDto> freeQuantityRuleItems) {
		receiveResponsedDto.setFreeQuantityRuleItems(freeQuantityRuleItems);
		return this;
	}
	
	public ReceiveResponseDtoBuilder buildDoubleNotes(List<NoteDto> notes) {
		receiveResponsedDto.setNotes(notes);
		return this;
	}
	
	
	
	public ReceiveResponseDtoBuilder buildDoubleVisits(List<VisitDto> doubelVisits) {
		receiveResponsedDto.setDoubleVisits(doubelVisits);
		return this;
	}
	
	public ReceiveResponseDtoBuilder buildDoubleVisitProducts(List<VisitProductDto> doubleVisitProducts) {
		receiveResponsedDto.setDoubleVisitProducts(doubleVisitProducts);
		return this;
	}

	public ReceiveResponseDtoBuilder buildLocalities(List<LocalityDto> localities) {
		receiveResponsedDto.setLocalities(localities);
		return this;
	}

	public ReceiveResponseDtoBuilder buildSpecialities(List<SpecialityDto> specialities) {
		receiveResponsedDto.setSpecialities(specialities);
		return this;
	}

	public ReceiveResponseDtoBuilder buildProspectTypes(List<ProspectTypeDto> prospectTypes) {
		receiveResponsedDto.setProspectType(prospectTypes);
		return this;
	}

	public ReceiveResponseDtoBuilder buildEstablishments(List<EstablishmentDto> establishments) {
		receiveResponsedDto.setEstablishments(establishments);
		return this;
	}

	public ReceiveResponseDtoBuilder buildPurchaseOrderDto(List<PurchaseOrderDto> purchaseOrders) {
		receiveResponsedDto.setPurchaseOrders(purchaseOrders);
		return this;
	}

	public ReceiveResponseDtoBuilder buildRecoveryDto(List<RecoveryDto> recoveries) {
		receiveResponsedDto.setRecoveries(recoveries);
		return this;
	}

	public ReceiveResponseDtoBuilder buildAttachmentDto(List<AttachmentDto> attachments) {
		receiveResponsedDto.setAttachments(attachments);
		return this;
	}

	public ReceiveResponseDtoBuilder buildExpensesDto(List<ExpenseReportDto> expenses) {
		receiveResponsedDto.setExpenses(expenses);
		return this;
	}

	public ReceiveResponseDtoBuilder buildExpenseTypeDto(List<ExpenseTypeDto> expenseType) {
		receiveResponsedDto.setExpenseTypes(expenseType);
		return this;
	}

	public ReceiveResponseDtoBuilder buildActivitysDto(List<ActivityTypeDto> activityTypes) {
		receiveResponsedDto.setActivityTypes(activityTypes);
		return this;
	}

	public ReceiveResponseDtoBuilder buildPotentialDtoDto(List<PotentialDto> potentials) {
		receiveResponsedDto.setPotential(potentials);
		return this;
	}

	public ReceiveResponseDtoBuilder buildValidationOfPlanning(List<PlanningValidationDto> validatonOfPlanning,
			List<Long> deletedPlannings, List<Long> deletedPlanningValidations) {
		receiveResponsedDto.setPlanningValidationResponse(validatonOfPlanning);
		receiveResponsedDto.setDeletedPlannings(deletedPlannings);
		receiveResponsedDto.setDeletedPlanningValidations(deletedPlanningValidations);
		return this;
	}

	public ReceiveResponseDtoBuilder buildValidationOfExpense(List<ExpenseReportDto> validationOfExpense) {
		receiveResponsedDto.setExpenseValidationResponse(validationOfExpense);

		return this;
	}

	public ReceiveResponseDtoBuilder buildValidationOfActionMarketing(
			List<ActionMarketingResponseDto> validationOfActionMarketing) {
		receiveResponsedDto.setMarketingActionValidationResponse(validationOfActionMarketing);
		return this;
	}

	public ReceiveResponseDtoBuilder buildActionMarketing(List<ActionMarketingResponseDto> actionMarketing) {
		receiveResponsedDto.setActionMarketing(actionMarketing);
		return this;
	}

	public ReceiveResponseDtoBuilder buildActivity(List<ActivityDto> activitiesDto) {
		receiveResponsedDto.setActivities(activitiesDto);
		return this;
	}

	public ReceiveResponseDtoBuilder buildConfig(ConfigurationDto config) {
		receiveResponsedDto.setConfig(config);
		return this;
	}

	public ReceiveResponseDtoBuilder buildPlanningDto(List<PlanningDto> plannings) {
		receiveResponsedDto.setPlannings(plannings);
		return this;
	}

	public ReceiveResponseDtoBuilder buildPlanningValidationDto(List<PlanningValidationDto> planningValidations) {
		receiveResponsedDto.setPlanningValidationResponse(planningValidations);
		return this;
	}

	public ReceiveResponseDtoBuilder buildValidationOfOpportunityNote(
			List<OpportunityNoteResponseDto> validationOfOpportunityNote) {
		receiveResponsedDto.setOpportinityNoteValidationResponse(validationOfOpportunityNote);
		return this;
	}

	public ReceiveResponseDtoBuilder buildRecommendedProspectsDate(String recommendedProspectsDate) {
		receiveResponsedDto.setRecommendedProspectsDate(recommendedProspectsDate);
		return this;

	}

	public ReceiveResponseDtoBuilder buildVisitsDto(List<VisitDto> visitsList,
			List<VisitProductDto> visitsProductsList) {
		receiveResponsedDto.setVisits(visitsList);
		receiveResponsedDto.setVisitsProducts(visitsProductsList);
		return this;
	}

	public ReceiveResponseDtoBuilder buildChargePlanDto(List<MinimizedChargePlan> LoadPlanDtos) {
		receiveResponsedDto.setChargePlan(LoadPlanDtos);
		return this;
	}

	public ReceiveResponseDtoBuilder buildNotifications(List<NotificationDto> notifications) {
		receiveResponsedDto.setNotifications(notifications);
		return this;
	}

	public ReceiveResponseDtoBuilder buildGammeDto(List<RangeDto> gammesDtoList) {
		receiveResponsedDto.setRanges(gammesDtoList);
		return this;

	}

	public void buildMergedProspect(List<SendResultDto> mergedProspects) {
		this.receiveResponsedDto.setMergedProspects(mergedProspects);

	}

	public ReceiveResponsedDto getReceiveResponseDto() {
		return receiveResponsedDto;
	}

	public ReceiveResponseDtoBuilder buildProspectsOrderPrediction(
			List<ProspectOrderPredictionResponse> prospectsOrderPrediction) {
		receiveResponsedDto.setProspectsOrderPrediction(prospectsOrderPrediction);
		return this;
	}

}
