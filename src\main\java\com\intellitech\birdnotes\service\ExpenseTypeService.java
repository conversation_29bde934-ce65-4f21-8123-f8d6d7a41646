package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.TypeNoteFraisRequestDto;

public interface ExpenseTypeService {
	
	List<ExpenseTypeDto> findAll() throws BirdnotesException;

	void add(TypeNoteFraisRequestDto typeNoteFraisDto) throws BirdnotesException;

	void delete(Long id) throws BirdnotesException;

	List<ExpenseTypeDto> findByUser(Long userId);
	
	ExpenseType saveTypeNoteFrais(ExpenseTypeDto typeNoteFraisDto) throws BirdnotesException;
}
