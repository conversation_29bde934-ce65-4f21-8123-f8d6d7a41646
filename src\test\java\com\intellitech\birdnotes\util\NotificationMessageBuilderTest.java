package com.intellitech.birdnotes.util;

import com.intellitech.birdnotes.model.*;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.service.ConfigurationService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.env.MockEnvironment;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.HashSet;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NotificationMessageBuilderTest {

    @Mock
    private ConfigurationService mockConfigurationService;
    @Mock
    private User mockUser;
    @Mock
    private User mockTargetUser;

    @InjectMocks
    private NotificationMessageBuilder notificationMessageBuilderUnderTest;

    @Before
    public void setUp() throws Exception {
        notificationMessageBuilderUnderTest.setUser(mockUser);
        notificationMessageBuilderUnderTest.setTagetUser(mockTargetUser);
        notificationMessageBuilderUnderTest.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        notificationMessageBuilderUnderTest.setProspects(Arrays.asList("value"));
        notificationMessageBuilderUnderTest.setMessageType("messageType");
        notificationMessageBuilderUnderTest.setNote("note");
        notificationMessageBuilderUnderTest.setProductName("productName");
        ReflectionTestUtils.setField(notificationMessageBuilderUnderTest, "env", new MockEnvironment());
    }

    @Test
    public void testBuild() {
        // Setup
        // Configure User.getDelegate(...).
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setFirstName("firstName");
        delegate.setHiringDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        delegate.setLastName("lastName");
        delegate.setImage("image");
        when(mockUser.getDelegate()).thenReturn(delegate);

        // Configure ConfigurationService.findConfiguration(...).
        final ConfigurationDto configurationDto = new ConfigurationDto();
        configurationDto.setMultiWholesaler(false);
        configurationDto.setAutoSync(false);
        configurationDto.setAcceptedPointingDistance(0);
        configurationDto.setServerPath("serverPath");
        when(mockConfigurationService.findConfiguration()).thenReturn(configurationDto);

        // Run the test
//        final Notification result = notificationMessageBuilderUnderTest.Build();

        // Verify the results
    }

    @Test
    public void testBuild_UserGetDelegateReturnsNull() {
        // Setup
        when(mockUser.getDelegate()).thenReturn(null);
        when(mockUser.getUsername()).thenReturn("result");

        // Configure ConfigurationService.findConfiguration(...).
        final ConfigurationDto configurationDto = new ConfigurationDto();
        configurationDto.setMultiWholesaler(false);
        configurationDto.setAutoSync(false);
        configurationDto.setAcceptedPointingDistance(0);
        configurationDto.setServerPath("serverPath");
        when(mockConfigurationService.findConfiguration()).thenReturn(configurationDto);

        // Run the test
//        final Notification result = notificationMessageBuilderUnderTest.Build();

        // Verify the results
    }

    @Test
    public void testSetProspect() {
        // Setup
        final Prospect prospect = new Prospect();
        final Range range = new Range();
        range.setId(0);
        range.setName("name");
        prospect.setRanges(new HashSet<>(Arrays.asList(range)));
        prospect.setFirstName("firstName");
        prospect.setLastName("lastName");

        // Run the test
        notificationMessageBuilderUnderTest.setProspect(prospect);

        // Verify the results
    }
}
