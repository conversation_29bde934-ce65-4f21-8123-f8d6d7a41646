package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("prospectTypeService")
@Transactional
public class ProspectTypeServiceImpl implements ProspectTypeService {

	private ProspectTypeRepository prospectTypeRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;

	
	

	@Autowired
	ProspectTypeServiceImpl(ProspectTypeRepository prospectTypeRepository, ConvertSpecialityToDto convertSpecialityToDto) {
		super();
		this.prospectTypeRepository = prospectTypeRepository;
	}



	@Override
	public List<ProspectTypeDto> findAll() throws BirdnotesException {
		List<ProspectTypeDto> back = new ArrayList<>();
		List<ProspectType> allTypes= prospectTypeRepository.findAll();
		for (ProspectType prospectType : allTypes) {
			ProspectTypeDto prospectTypeDto = new ProspectTypeDto();
			prospectTypeDto.setId(prospectType.getId());
			prospectTypeDto.setName(prospectType.getName());
			back.add(prospectTypeDto);
		}
		return back;
	}

	@Override
	public ProspectTypeDto findProspectTypeDto(String prospectTypeName, List<ProspectTypeDto>prospectTypeDtos)   {
		for (ProspectTypeDto prospectTypeDto : prospectTypeDtos) {
			if (prospectTypeDto.getName().equalsIgnoreCase(prospectTypeName)) {
				return prospectTypeDto;
			}
		}
		return null;
	}
	
	@Override
	public void saveAllProspectTypes(List<ProspectTypeDto> prospectTypeDtos) throws BirdnotesException {
		for(ProspectTypeDto prospectTypeDto:prospectTypeDtos) {
		ProspectType prospectType = new ProspectType();
		if (prospectTypeDto.getName() == null || prospectTypeDto.getName().equals("")) {
			throw new BirdnotesException("ProspectType name is empty");
		}
		
		prospectType.setName(prospectTypeDto.getName());
		prospectTypeRepository.save(prospectType);
		}
	}
	@Override
	public ProspectType saveProspectType(ProspectTypeDto prospectTypeDto) throws BirdnotesException {
	    if (prospectTypeDto == null || prospectTypeDto.getId() == null) {
	        throw new BirdnotesException(Exceptions.PROSPECT_TYPE_DTO_IS_NULL);
	    }

	    if (prospectTypeDto.getName() == null || prospectTypeDto.getName().isEmpty()) {
	        throw new BirdnotesException(Exceptions.PROSPECT_TYPE_NAME_IS_EMPTY);
	    }

	    ProspectType existingProspectType = prospectTypeRepository.findByNameAndAnotherId(prospectTypeDto.getName(), prospectTypeDto.getId());
	    if (existingProspectType != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    ProspectType prospectType = null;
	    if (prospectTypeDto.getId() != null) {
	        prospectType = prospectTypeRepository.findOne(prospectTypeDto.getId());
	    }
	    if (prospectType == null) {
	        prospectType = new ProspectType();
	    }

	    prospectType.setId(prospectTypeDto.getId());
	    prospectType.setName(prospectTypeDto.getName());
	    return prospectTypeRepository.save(prospectType);
	}

	
	public void delete (long id)throws BirdnotesException {
			prospectTypeRepository.deleteById(id);
	}
}
