package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.RangeRequestDto;


public interface RangeService {
	Range add (RangeRequestDto gammeRequestDto) throws BirdnotesException;
	List<RangeDto> findAll() throws BirdnotesException;
	public void delete (Integer id) throws BirdnotesException;
	List<RangeDto> findByUserRange(Long userId) throws BirdnotesException;
	Range saveRange(RangeDto gammeDto) throws BirdnotesException;
	List<RangeDto> findSubRange() throws BirdnotesException;
}
