package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.ATTACHMENT, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Attachment implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.ATTACHMENT_SEQUENCE, sequenceName = Sequences.ATTACHMENT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.ATTACHMENT_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	@Column(name = Columns.IDENTIFIER)
	private Long identifier;
	
	@Column(name = BirdnotesConstants.Columns.ATTACHMENT_NAME, length = BirdnotesConstants.Numbers.N_255)
	private String attachmentName;

	@Column(name = BirdnotesConstants.Columns.ATTACHEMENT_BASE64)
	@Type(type = "text")
	private String attachmentBase64;
	
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	private Delegate delegate;

	public Attachment() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}

	



}
