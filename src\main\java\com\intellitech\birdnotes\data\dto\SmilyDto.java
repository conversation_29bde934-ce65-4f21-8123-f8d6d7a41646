package com.intellitech.birdnotes.data.dto;

public class SmilyDto {

	private Long productId;
	private String productName;
	private Integer smilyId;
	private Long count;
	
	public SmilyDto() {
		super();
	}

	public SmilyDto(Long productId, String productName, Integer smilyId, Long count) {
		this.productId = productId;
		this.productName = productName;
		this.smilyId = smilyId;
		this.count = count;
	}
	
	public SmilyDto(Integer smilyId, Long count) {
		this.smilyId = smilyId;
		this.count = count;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Integer getSmilyId() {
		return smilyId;
	}

	public void setSmilyId(Integer smilyId) {
		this.smilyId = smilyId;
	}

	public Long getCount() {
		return count;
	}

	public void setCount(Long count) {
		this.count = count;
	}

}