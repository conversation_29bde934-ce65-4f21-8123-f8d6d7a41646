package com.intellitech.birdnotes.controller;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.model.Issue;
import com.intellitech.birdnotes.model.dto.UserTransferDto;
import com.intellitech.birdnotes.model.request.AuthenticationRequest;
import com.intellitech.birdnotes.service.AuthenticationService;
import com.intellitech.birdnotes.service.StorageFileLogService;
import com.intellitech.birdnotes.service.SynchronisationService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/authentication")
public class AuthenticationController {
	private static final Logger LOG = LoggerFactory.getLogger(AuthenticationController.class);

	@Autowired
	private AuthenticationService authenticationService;

	@Autowired
	private SynchronisationService synchronisationService;

	@Autowired
	private StorageFileLogService storageFileLogService;
	@Autowired
	private UserService userService;

	private List<String> files = new ArrayList<>();
	private DateFormat df;

	@PostConstruct
	public void init() {

		df = new SimpleDateFormat("yyyy-MM-dd");
	}

	@RequestMapping(value = "authenticate", method = RequestMethod.POST)
	public ResponseEntity<UserTransferDto> authenticate(@RequestBody AuthenticationRequest authenticationRequest) {
		try {

			UserTransferDto userTransferDto = authenticationService.authenticate(authenticationRequest);
			Set<String> permissionsOfUser = userService.getAllPermissionsOfCurrentUser();
			// appler le code qui recupre les permissions du user puis les mettre dans
			// userTransferDto
			if (userTransferDto != null) {
				userTransferDto.setPermissions(permissionsOfUser);
				return new ResponseEntity<>(userTransferDto, HttpStatus.OK);
			}

			return new ResponseEntity<>(userTransferDto, HttpStatus.FORBIDDEN);

		} catch (BadCredentialsException bce) {
			LOG.error("Error in authenticate", bce);
			return new ResponseEntity<>(new UserTransferDto(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("Error in authenticate", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "checkTokenIsExpired/{authToken}", method = RequestMethod.GET)
	public ResponseEntity<Boolean> checkTokenIsExpired(@PathVariable("authToken") String authToken) {
		try {
			boolean result = authenticationService.checkTokenIsExpired(authToken);
			if (result) {
				return new ResponseEntity<>(Boolean.TRUE, HttpStatus.OK);
			}
			return new ResponseEntity<>(Boolean.FALSE, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in checkTokenIsExpired", e);
			return new ResponseEntity<>(Boolean.FALSE, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/sendIssue", method = RequestMethod.POST)
	public ResponseEntity<Long> send(@RequestBody Issue issueDto) {

		try {
			Long issueId = synchronisationService.insertIssue(issueDto);

			return new ResponseEntity<>(issueId, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while send a new data", e);
			return new ResponseEntity<>(0L, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@PostMapping("/upload/{issueId}")
	public ResponseEntity<String> handleFileUpload(@PathVariable("issueId") Long issueId,
			@RequestParam("logFile") MultipartFile logFile) {
		String message = "";

		try {
			storageFileLogService.store(logFile, issueId);
			message = "You successfully uploaded !";
			return ResponseEntity.status(HttpStatus.OK).body(message);

		} catch (Exception e) {
			LOG.error("An exception occurred :FAIL to upload ", e);
			message = "FAIL to upload " + logFile.getOriginalFilename() + "!";
			return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(message);
		}
	}
	
	@RequestMapping(value = "/getLogo", method = RequestMethod.GET)
	public ResponseEntity<String> getLogo() {
		try {
			String result = authenticationService.getLogo();
			if (result != null) {
				return new ResponseEntity<>(result, HttpStatus.OK);
			}
			return new ResponseEntity<>("", HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in getLogo", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
