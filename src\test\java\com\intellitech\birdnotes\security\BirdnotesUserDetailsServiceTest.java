package com.intellitech.birdnotes.security;

import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.service.UserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import java.util.Arrays;
import java.util.HashSet;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BirdnotesUserDetailsServiceTest {

    @Mock
    private UserService mockUserService;

    @InjectMocks
    private BirdnotesUserDetailsService birdnotesUserDetailsServiceUnderTest;

    @Test
    public void testLoadUserByUsername() {
        // Setup
        // Configure UserService.findByUsername(...).
        final UserDto userDto = new UserDto();
        userDto.setRangeIds(Arrays.asList(0));
        final Role role = new Role();
        role.setName("name");
        userDto.setRoles(new HashSet<>(Arrays.asList(role)));
        userDto.setUsername("username");
        userDto.setPassword("password");
        when(mockUserService.findByUsername("username")).thenReturn(userDto);

        // Run the test
        final UserDetails result = birdnotesUserDetailsServiceUnderTest.loadUserByUsername("username");

        // Verify the results
    }

    @Test(expected = UsernameNotFoundException.class)
    public void testLoadUserByUsername_UserServiceReturnsNull() {
        // Setup
        when(mockUserService.findByUsername("username")).thenReturn(null);

        // Run the test
        birdnotesUserDetailsServiceUnderTest.loadUserByUsername("username");
    }
}
