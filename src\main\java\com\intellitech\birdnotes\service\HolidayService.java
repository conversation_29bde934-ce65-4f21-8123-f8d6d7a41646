package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Holiday;
import com.intellitech.birdnotes.model.dto.HolidayDto;

public interface HolidayService {
	public List<HolidayDto> getAllHolidays() throws BirdnotesException;

	public Holiday saveHoliday(HolidayDto holidayDto) throws BirdnotesException;

	public void deleteHoliday(Long holidayId);
}
