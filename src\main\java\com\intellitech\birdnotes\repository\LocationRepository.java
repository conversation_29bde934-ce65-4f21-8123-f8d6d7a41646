package com.intellitech.birdnotes.repository;


import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Location;


public interface LocationRepository extends JpaRepository<Location, Long> {
	@Query("Select a from Location a")
		List<Location> findAll();
	@Query("SELECT p from Location p WHERE p.delegate.id =:id  and (DATE(p.date) BETWEEN DATE(:startDate) AND DATE(:endDate))")
	List<Location> findLocationSelected(@Param("id") Long id , @Param("startDate") Date startDate, @Param("endDate") Date endDate);

	
	@Query("SELECT p from Location p, Visit v WHERE p.delegate.id = v.delegate.id and v.prospect.id=:id  and (DATE(p.date) BETWEEN DATE(:startDate) AND DATE(:endDate)) order by p.date ASC")
	List<Location> findLocationSelectedProspect(@Param("id") Long id , @Param("startDate") Date startDate, @Param("endDate") Date endDate);

	@Query("SELECT max(p.distance) from Location p where (DATE(p.date) BETWEEN DATE(:startDate) AND DATE(:endDate))")
	Integer findMaxDistanceByDay(@Param("startDate") Date startDate, @Param("endDate") Date endDate);
}