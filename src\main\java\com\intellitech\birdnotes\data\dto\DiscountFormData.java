package com.intellitech.birdnotes.data.dto;

import java.util.List;

import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

public class DiscountFormData {
	private List<ProductDto> products;
	private List<WholesalerDto> wholesalers;
	private List<RangeDto> ranges;

	public List<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(List<ProductDto> products) {
		this.products = products;
	}

	public List<WholesalerDto> getWholesalers() {
		return wholesalers;
	}

	public void setWholesalers(List<WholesalerDto> wholesalers) {
		this.wholesalers = wholesalers;
	}

	public List<RangeDto> getRanges() {
		return ranges;
	}

	public void setRanges(List<RangeDto> ranges) {
		this.ranges = ranges;
	}
}
