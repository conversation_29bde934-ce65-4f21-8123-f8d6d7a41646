package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.PotentielProduit;

@Repository
public interface ProductsPotentialRepository extends JpaRepository<PotentielProduit, Long> {
	
	@Query("SELECT p.potential from PotentielProduit p WHERE p.product.id=:productId AND p.prospect.id=:prospectId ")
	Potential findpotentialbyIds(@Param("productId")Long productId, @Param("prospectId")Long prospectId);
	
	@Modifying
	@Query("DELETE FROM PotentielProduit where id=:id")
	void deleteById(@Param ("id") Long id);
	
	@Query("SELECT p.id from PotentielProduit p WHERE p.id in (:potentialProduits)")
	List<Long> findWhereIdIn(@Param("potentialProduits") List<Long> potentialProduits);
	
	@Override
	@Query("SELECT p from PotentielProduit p")
	List<PotentielProduit> findAll();
}
