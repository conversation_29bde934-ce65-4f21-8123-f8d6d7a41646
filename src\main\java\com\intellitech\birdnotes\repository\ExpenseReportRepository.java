package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.dto.LabelValueDto;

public interface ExpenseReportRepository extends JpaRepository<ExpenseReport, Long> {

	ExpenseReport findById(Long id);

	@Query("SELECT n from ExpenseReport n WHERE n.delegate.id  = :userId and n.id not in (:expenseReportIds)")
	List<ExpenseReport> findByUser(@Param("userId") Long userId,
			@Param("expenseReportIds") List<Long> expenseReportIds);

	@Query("SELECT n from ExpenseReport n WHERE n.delegate.id  = :userId ")
	List<ExpenseReport> findByUser(@Param("userId") Long userId);

	@Query("SELECT n from ExpenseReport n WHERE n.delegate.id IN (:subUsersIds) AND (DATE(n.date) BETWEEN DATE(:startDate) AND DATE(:endDate)) order by n.date")
	List<ExpenseReport> findByDate(@Param("startDate") Date startDate, @Param("endDate") Date endDate,
			@Param("subUsersIds") List<Long> subUsersIds);

	@Modifying
	@Query("UPDATE ExpenseReport SET date=:date, montant=:montant, description=:description, expenseType=:expenseType, piece_jointe =:nameAttachment, attachment_base64=:attachmentBase64 WHERE id=:id")
	void update(@Param("date") Date date, @Param("montant") Float montant, @Param("description") String description,
			@Param("expenseType") ExpenseType expenseType, @Param("nameAttachment") String nameAttachment,
			@Param("attachmentBase64") String attachmentBase64, @Param("id") Long id);

	@Override
	@Query("Select n from ExpenseReport n ")
	List<ExpenseReport> findAll();

	@Query("SELECT n from ExpenseReport n WHERE n.expenseType.id=:id ")
	List<ExpenseReport> findByExpenseTypeId(@Param("id") Long id);

	@Modifying
	@Query("DELETE ExpenseReport n WHERE n.id = ?1")
	void deleteById(Long idNoteFrais);

	@Query("SELECT t.id from ExpenseReport t WHERE t.id in (:noteFrais) ")
	List<Long> findWhereIdIn(@Param("noteFrais") List<Long> noteFrais);

	@Query("SELECT t from ExpenseReport t WHERE t.id in (:noteFrais) and t.delegate.id  = :userId ")
	List<ExpenseReport> findWhereIdInByUser(@Param("noteFrais") List<Long> noteFrais, @Param("userId") Long userId);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(er.delegate.lastName, sum(er.montant)) "
			+ "FROM ExpenseReport er WHERE DATE(er.date) >=  DATE(:stardDate) AND DATE(er.date) <=  DATE(:endDate)   GROUP BY er.delegate.lastName order by  er.delegate.lastName ")
	List<LabelValueDto> getExpensesReportByDelegate(@Param("stardDate") Date stardDate, @Param("endDate") Date endDate);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(er.expenseType.name, sum(er.montant)) "
			+ "FROM ExpenseReport er WHERE DATE(er.date) >=  DATE(:stardDate) AND DATE(er.date) <=  DATE(:endDate)  GROUP BY er.expenseType.name  ")
	List<LabelValueDto> getExpensesReportByType(@Param("stardDate") Date stardDate, @Param("endDate") Date endDate);

	@Query("SELECT n FROM ExpenseReport n WHERE n.identifier = ?1 And n.delegate.id = ?2")
	ExpenseReport findByIdentifier(Long identifier, Long userId);

	@Modifying
	@Query("Delete FROM ExpenseReport n WHERE n.identifier = ?1 And n.delegate.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);

	@Query("SELECT n.id FROM ExpenseReport n where n.identifier = ?1 And n.delegate.id = ?2")
	Long getIdByIdentifier(Long identifier, Long userId);

	@Query("SELECT n from ExpenseReport n WHERE n.identifier in (:expenseWaitingValidationIds)  And n.delegate.id = :userId")
	List<ExpenseReport> findExpenseValidationByUser(
			@Param("expenseWaitingValidationIds") List<Long> expenseWaitingValidationIds, @Param("userId") Long userId);

}
