package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class VisitDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Long identifier;
	private String day;
	private Date visitDate;
	private Long prospectId;
	private ProspectDto prospectDto;
    private String generalNote;
	private Integer patientNumber;
	private Long gadgetId;
	private Integer gadgetQuantity;
	private Long userId;
	private String userName;
	private List<UserDto>tagedUsers;
	private Long companionId;
	private Double lat;
	private Double lng;
	private Long purchaseOrderTemplateId;
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getDay() {
		return day;
	}
	
	public void setDay(String day) {
		this.day = day;
	}
	
	public Long getProspectId() {
		return prospectId;
	}
	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}
	public String getGeneralNote() {
		return generalNote;
	}
	public void setGeneralNote(String generalNote) {
		this.generalNote = generalNote;
	}
	public Integer getPatientNumber() {
		return patientNumber;
	}
	public void setPatientNumber(Integer patientNumber) {
		this.patientNumber = patientNumber;
	}
	public Long getGadgetId() {
		return gadgetId;
	}
	public void setGadgetId(Long gadgetId) {
		this.gadgetId = gadgetId;
	}
	public Integer getGadgetQuantity() {
		return gadgetQuantity;
	}
	public void setGadgetQuantity(Integer gadgetQuantity) {
		this.gadgetQuantity = gadgetQuantity;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Long getIdentifier() {
		return identifier;
	}
	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}
	public Date getVisitDate() {
		return visitDate;
	}
	public void setVisitDate(Date visitDate) {
		this.visitDate = visitDate;
	}
	public List<UserDto> getTagedUsers() {
		return tagedUsers;
	}
	public void setTagedUsers(List<UserDto> tagedUsers) {
		this.tagedUsers = tagedUsers;
	}
	public Double getLat() {
		return lat;
	}
	public void setLat(Double lat) {
		this.lat = lat;
	}
	public Double getLng() {
		return lng;
	}
	public void setLng(Double lng) {
		this.lng = lng;
	}
	public Long getPurchaseOrderTemplateId() {
		return purchaseOrderTemplateId;
	}
	public void setPurchaseOrderTemplateId(Long purchaseOrderTemplateId) {
		this.purchaseOrderTemplateId = purchaseOrderTemplateId;
	}
	public ProspectDto getProspectDto() {
		return prospectDto;
	}
	public void setProspectDto(ProspectDto prospectDto) {
		this.prospectDto = prospectDto;
	}
	public Long getCompanionId() {
		return companionId;
	}
	public void setCompanionId(Long companionId) {
		this.companionId = companionId;
	}
	
	
}