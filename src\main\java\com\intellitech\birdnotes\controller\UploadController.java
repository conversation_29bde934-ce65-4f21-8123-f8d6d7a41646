package com.intellitech.birdnotes.controller;


import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.method.annotation.MvcUriComponentsBuilder;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.service.ImportService;
import com.intellitech.birdnotes.service.StorageService;

@RestController
@RequestMapping("/upload")
public class UploadController {
	
	private static final Logger LOG = LoggerFactory.getLogger(UploadController.class);

	@Autowired
	private StorageService storageService;
	@Autowired
	private ImportService importService;

	private List<String> files = new ArrayList<>();
	
	@Value("${logoPath}")
	private String logoPath;
	@Value("${uploadPath}")
	private String uploadPath;

	@PostMapping("/post")
	public ResponseEntity<String> handleFileUpload(@RequestParam("file") MultipartFile file) {
		String message = "";
		
		try {
			storageService.deleteAll();
			File logo = new File(uploadPath+logoPath); 
			logo.mkdir(); 
			storageService.store(file);
			files.add(file.getOriginalFilename());

			message = "You successfully uploaded " + file.getOriginalFilename() + "!";
			return ResponseEntity.status(HttpStatus.OK).body(message);
		} catch (Exception e) {
			message = "FAIL to upload " + file.getOriginalFilename() + "!";
			LOG.error("An exception occurred while handleFileUpload", e);
			return ResponseEntity.status(HttpStatus.EXPECTATION_FAILED).body(message);
		}
	}

	@PostMapping("/import")
	public ResponseEntity<String> handleFieImport(@RequestParam("file") MultipartFile file) throws BirdnotesException {
		
		
		
		try {
			File tmpFile = File.createTempFile("import", file.getOriginalFilename());
			file.transferTo(tmpFile);
			importService.importProspect(tmpFile.getPath());
			
			
		} catch (IllegalStateException | IOException e) {
			LOG.error("Error in handleFieImport", e);
			e.printStackTrace();
		}
		
		
		return null;


	}
	
	  
	@GetMapping("/getallfiles")
	public ResponseEntity<List<String>> getListFiles() {
		List<String> fileNames = files
				.stream().map(fileName -> MvcUriComponentsBuilder
						.fromMethodName(UploadController.class, "getFile", fileName).build().toString())
				.collect(Collectors.toList());

		return ResponseEntity.ok().body(fileNames);
	}

	@GetMapping("/files/{filename:.+}")
	@ResponseBody
	public ResponseEntity<Resource> getFile(@PathVariable String fileName) throws BirdnotesException {
		Resource file = storageService.loadFile(fileName);
		return ResponseEntity.ok()
				.header(HttpHeaders.CONTENT_DISPOSITION, "attachment; fileName=\"" + file.getFilename() + "\"")
				.body(file);
	}
}