package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.ReportCron;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.MinimizedUserDto;
import com.intellitech.birdnotes.model.dto.MinimizedUserDtoV1;
import com.intellitech.birdnotes.model.Delegate;


@Repository
public interface DelegateRepository extends JpaRepository<Delegate, Long> {
	
	@Query("SELECT u FROM Delegate u WHERE  u.user.goals IS NULL ")
	List<Delegate> findAllDelegatesWithoutGoal();
	
	@Query("SELECT u FROM Delegate u  ORDER BY u.firstName ASC")
	List<Delegate> findAllDelegates();
	
	
	@Query("SELECT u FROM Delegate u join u.user.superiors s  WHERE s.id =:supervisorId  ORDER BY u.firstName ASC")
	List<Delegate> getDelegatesBySuperior(@Param("supervisorId") Long supervisorId);
	
	@Query("SELECT u.id FROM Delegate u  ")
	List<Long> findAllDelegatesIds();

	

	Delegate findById(Long id);

	@Query("SELECT u from Delegate u WHERE u.user.email = ?1 and u.id != ?2")
	Delegate findByEmailAndId(String email, Long id);

	@Query("SELECT u from Delegate u WHERE u.user.phone = ?1 and u.id != ?2")
	Delegate findByPhoneAndId(String phone, Long id);

	@Query("SELECT u from Delegate u WHERE u.user.username = ?1 and u.id != ?2")
	Delegate findByDelegatenameAndId(String username, Long id);
	
	
	@Query("SELECT u from Delegate u WHERE u.user.username = ?1")
	Delegate findByUsername(String username);

	
	@Override
	@Query("SELECT u from Delegate u where u.user.active = true order by u.firstName ASC")
	List<Delegate> findAll();

	
	@Query("SELECT u.lastSyncro from Delegate u where u.id=:id")
	Date findLastSync(@Param("id") Long id);
	

	@Query("SELECT u from Delegate u WHERE upper(u.firstName) = upper(?1) and upper(u.lastName) = upper(?2) order by u.firstName")
	Delegate findByFirstLastName(String firstName, String lastName);

	
	@Query("Select distinct u from Delegate u join u.user.roles r where r.rank < ?1")
	List<Delegate> getAllSupervisors(Integer rank);
	
	@Query("Select distinct u from Delegate u")
	List<Delegate> getAllUsers();
	
	
	@Query("SELECT u.user.superiors FROM Delegate u where u.id=:id")
	List<Delegate> getSuperviserOfDelegate(@Param("id") Long id);
	
	
	@Query("Select u from Delegate u  where u.id in :ids")
	Set<Delegate> getDelegateByIds(@Param("ids") List<Long> ids);
	
	@Query("SELECT u FROM Delegate u order by u.firstName")
	List<Delegate> getAllDelegates();
	
	@Modifying
	@Query("UPDATE Delegate  set mobileAppVersion =:mobileAppVersion WHERE id =:id")
	void updateMobileAppVersion(@Param("mobileAppVersion") String mobileAppVersion, @Param("id") Long id);
	
	@Modifying
	@Query("UPDATE Delegate set oneSignalUserId =:oneSignalUserId WHERE id =:id")
	void updateOneSignalUserId(@Param("oneSignalUserId") String oneSignalUserId, @Param("id") Long id);
	
	@Modifying
	@Query("UPDATE Delegate set lastLogin =:lastLogin WHERE id =:id")
	void updateLastLogin(@Param("lastLogin") Date lastLogin, @Param("id") Long id);
	
	
	
	@Query("SELECT u.user.goals from Delegate u where u.id =:id")
	Set<Goal> findGoalsOfDelegate(@Param("id") Long id);
	
	@Query("SELECT u from Delegate u join u.user.goals g where g.id =:id ")
	Set<Delegate> findDelegatesOfGoal(@Param("id") Long id);
	
	@Query("SELECT u from Delegate u join u.purchaseOrderTemplates pot where pot.id =:id ")
	Set<Delegate> findDelegatesOfPot(@Param("id") Long id);
	
	@Query("SELECT DISTINCT u from Delegate u join u.user.goals g where (date(g.firstDate) NOT BETWEEN date(:firstDate) "
			+ "AND date(:lastDate)) AND (date(g.lastDate) NOT BETWEEN date(:firstDate) AND date(:lastDate))"
			+ "AND u.user.id in (:subUserIds)")
	List<Delegate> findUsersWithoutGoal(@Param("firstDate") Date firstDate, @Param("lastDate") Date lastDate, @Param("subUserIds") List<Long> subUserIds);

	@Query("SELECT d from Delegate d where d.user.id=:userId ")
	Delegate findDelegateByUserId(@Param("userId") Long userId);
	
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.MinimizedUserDto(d.id,d.firstName,d.lastName) FROM Delegate d join d.user.roles r  order by d.firstName")
	List<MinimizedUserDto> getDelegates();

	@Query("SELECT new com.intellitech.birdnotes.model.dto.MinimizedUserDtoV1 (d.id, d.firstName, d.lastName, d.workType) FROM Delegate d join d.user.roles r order by d.firstName")
	List<MinimizedUserDtoV1> getDelegatesV1();

	@Query("SELECT new com.intellitech.birdnotes.model.dto.MinimizedUserDto (d.id, d.firstName, d.lastName) FROM Delegate d join d.user.roles r WHERE  d.id not in (?1) order by d.firstName")
	List<MinimizedUserDto> getDelegatesExcept(List<Long> ids);
	
	
}
