package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.dto.LabelValueDto;

public interface ActionMarketingRepository extends JpaRepository<ActionMarketing, Long> {

	ActionMarketing findByName(String name);

	ActionMarketing findById(Long id);


	@Query("SELECT p from ActionMarketing p WHERE p.user.id =:id")
	List<ActionMarketing> findActionMarketingByUser(@Param("id") Long id);
	
	@Query("SELECT p from ActionMarketing p WHERE p.user.id IN (:subUsersIds) AND (DATE(p.date) BETWEEN DATE(:startDate) AND DATE(:endDate))")
	List<ActionMarketing> findActionMarketingByDate(@Param("startDate") Date startDate, @Param("endDate") Date endDate, @Param("subUsersIds") List <Long> subUsersIds);

	//@Query("Select a from ActionMarketing a ORDER BY a.name ASC")
	//List<ActionMarketing> findAll();
	
	@Query("Select a from ActionMarketing a ORDER BY a.id DESC")
	List<ActionMarketing> findAll();

	@Modifying
	@Query("DELETE FROM ActionMarketing where id=:id")
	void deleteById(@Param("id") Long id);
	
	@Modifying
	@Query("DELETE ActionMarketing p WHERE p.id in (:actionMarketingReportIds)")
	void deleteByIds(@Param("actionMarketingReportIds") List<Long> actionMarketingReportIds);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(am.user.username, sum(am.budget)) "
			+ "FROM ActionMarketing am WHERE DATE(am.date) >=  DATE(:stardDate)  AND DATE(am.date) <=  DATE(:endDate)  GROUP By am.user.username order by  am.user.username ")
	List<LabelValueDto> getMarketingExpensesByDelegate(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);
	
    
	List<ActionMarketing> findByProspects_Id(Long id);
	
	@Query("SELECT a FROM ActionMarketing a WHERE a.identifier = ?1 And a.user.id = ?2")
	ActionMarketing findByIdentifier(Long identifier, Long userId);
	
	@Modifying
	@Query("Delete FROM ActionMarketing a WHERE a.identifier = ?1 And a.user.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);
	
	@Query ("SELECT a.id FROM ActionMarketing a where a.identifier = ?1 And a.user.id = ?2")
	Long getIdByIdentifier (Long identifier, Long userId);
	
}
