package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class SendRequestDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private List<VisitDto> visits;
	private List<VisitProductDto> visitProducts;
	private List<PurchaseOrderDto> purchaseOrders;
	private List<ProspectDto> prospectsChangeRequests;
	private List<ExpenseReportDto> notefrais;
	private List<ActionMarketingRequestDto> actionMarketings;
	private List<ActivityDto> activities;
	private List<RecoveryDto> recoveries;
	private List<MessageDto> messages;
	private List<AttachmentDto> attachments;
	private List<MessageTagDto> tagedUsers;
	private List<PresentationTimeTrackingDto> presentationTimeTracking;
	private List<OpportunityNoteRequestDto> opportunityNotes;
	private List<LocationDto> locations;
	private List<PlanningDto> plannings;
	private List<PlanningValidationDto> planningValidations;

	private List<Long> visitsToDelete;
	private List<Long> visitProductToDelete;
	private List<Long> purchaseOrderToDelete;
	private List<Long> marketingActionsToDelete;
	private List<Long> opportunityNotesToDelete;
	private List<Long> expenseReportToDelete;
	private List<Long> activitiesToDelete;
	private List<Long> planningsToDelete;
	private List<Long> planningValidationToDelete;
	private List<Long> recoveryToDelete;
	private List<Long> attachmentToDelete;


	public SendRequestDto(List<VisitProductDto> reports, List<ProspectDto> prospectsChangeRequests, 
			List<ExpenseReportDto> notefrais, List<PurchaseOrderDto> purchaseOrders ) {
		super();
		this.visitProducts = reports;
		this.prospectsChangeRequests = prospectsChangeRequests;
		this.notefrais = notefrais;
		this.purchaseOrders = purchaseOrders;

	}
	

	public List<PurchaseOrderDto> getPurchaseOrders() {
		return purchaseOrders;
	}


	public void setPurchaseOrders(List<PurchaseOrderDto> purchaseOrders) {
		this.purchaseOrders = purchaseOrders;
	}


	

	public List<Long> getExpenseReportToDelete() {
		return expenseReportToDelete;
	}


	public void setExpenseReportToDelete(List<Long> expenseReportToDelete) {
		this.expenseReportToDelete = expenseReportToDelete;
	}


	public List<LocationDto> getLocations() {
		return locations;
	}


	public void setLocations(List<LocationDto> locations) {
		this.locations = locations;
	}


	public List<Long> getMarketingActionsToDelete() {
		return marketingActionsToDelete;
	}

	public void setMarketingActionsToDelete(List<Long> marketingActionsToDelete) {
		this.marketingActionsToDelete = marketingActionsToDelete;
	}
	

	public List<ActionMarketingRequestDto> getActionMarketings() {
		return actionMarketings;
	}

	public void setActionMarketings(List<ActionMarketingRequestDto> actionMarketings) {
		this.actionMarketings = actionMarketings;
	}

	public List<PlanningValidationDto> getPlanningValidations() {
		return planningValidations;
	}

	public List<RecoveryDto> getRecoveries() {
		return recoveries;
	}
	
	public void setRecoveries(List<RecoveryDto> recoveries) {
		this.recoveries = recoveries;
	}
	
	
	public List<MessageDto> getMessages() {
		return messages;
	}


	public void setMessages(List<MessageDto> messages) {
		this.messages = messages;
	}

	

	public List<MessageTagDto> getTagedUsers() {
		return tagedUsers;
	}


	public void setTagedUsers(List<MessageTagDto> tagedUsers) {
		this.tagedUsers = tagedUsers;
	}


	public List<AttachmentDto> getAttachments() {
		return attachments;
	}


	public void setAttachments(List<AttachmentDto> attachments) {
		this.attachments = attachments;
	}


	public List<OpportunityNoteRequestDto> getOpportunityNotes() {
		return opportunityNotes;
	}


	public void setOpportunityNotes(List<OpportunityNoteRequestDto> opportunityNotes) {
		this.opportunityNotes = opportunityNotes;
	}

	public List<Long> getOpportunityNotesToDelete() {
		return opportunityNotesToDelete;
	}


	public void setOpportunityNotesToDelete(List<Long> opportunityNotesToDelete) {
		this.opportunityNotesToDelete = opportunityNotesToDelete;
	}


	public void setPlanningValidations(List<PlanningValidationDto> planningValidations) {
		this.planningValidations = planningValidations;
	}

	public SendRequestDto() {
		super();
	}

	public List<PlanningDto> getPlannings() {
		return plannings;
	}

	public void setPlannings(List<PlanningDto> plannings) {
		this.plannings = plannings;
	}

	public List<Long> getPlanningsToDelete() {
		return planningsToDelete;
	}

	public void setPlanningsToDelete(List<Long> planningsToDelete) {
		this.planningsToDelete = planningsToDelete;
	}

	public List<ExpenseReportDto> getNotefrais() {
		return notefrais;
	}

	public void setNotefrais(List<ExpenseReportDto> notefrais) {
		this.notefrais = notefrais;
	}


	public List<ProspectDto> getProspectsChangeRequests() {
		return prospectsChangeRequests;
	}

	public void setProspectsChangeRequests(List<ProspectDto> prospectsChangeRequests) {
		this.prospectsChangeRequests = prospectsChangeRequests;
	}




	public List<ActivityDto> getActivities() {
		return activities;
	}


	public void setActivities(List<ActivityDto> activities) {
		this.activities = activities;
	}


	public List<Long> getActivitiesToDelete() {
		return activitiesToDelete;
	}

	public void setActivitiesToDelete(List<Long> activitiesToDelete) {
		this.activitiesToDelete = activitiesToDelete;
	}


	public List<Long> getPurchaseOrderToDelete() {
		return purchaseOrderToDelete;
	}


	public void setPurchaseOrderToDelete(List<Long> purchaseOrderToDelete) {
		this.purchaseOrderToDelete = purchaseOrderToDelete;
	}


	public List<Long> getPlanningValidationToDelete() {
		return planningValidationToDelete;
	}


	public void setPlanningValidationToDelete(List<Long> planningValidationToDelete) {
		this.planningValidationToDelete = planningValidationToDelete;
	}


	public List<VisitProductDto> getVisitProducts() {
		return visitProducts;
	}


	public void setVisitProducts(List<VisitProductDto> visitProducts) {
		this.visitProducts = visitProducts;
	}


	public List<VisitDto> getVisits() {
		return visits;
	}


	public void setVisits(List<VisitDto> visits) {
		this.visits = visits;
	}


	public List<Long> getVisitProductToDelete() {
		return visitProductToDelete;
	}


	public void setVisitProductToDelete(List<Long> visitProductToDelete) {
		this.visitProductToDelete = visitProductToDelete;
	}


	public List<Long> getVisitsToDelete() {
		return visitsToDelete;
	}


	public void setVisitsToDelete(List<Long> visitsToDelete) {
		this.visitsToDelete = visitsToDelete;
	}


	public List<Long> getRecoveryToDelete() {
		return recoveryToDelete;
	}


	public void setRecoveryToDelete(List<Long> recoveryToDelete) {
		this.recoveryToDelete = recoveryToDelete;
	}


	public List<Long> getAttachmentToDelete() {
		return attachmentToDelete;
	}


	public void setAttachmentToDelete(List<Long> attachmentToDelete) {
		this.attachmentToDelete = attachmentToDelete;
	}


	public List<PresentationTimeTrackingDto> getPresentationTimeTracking() {
		return presentationTimeTracking;
	}


	public void setPresentationTimeTracking(List<PresentationTimeTrackingDto> presentationTimeTracking) {
		this.presentationTimeTracking = presentationTimeTracking;
	}


	


	
	
}