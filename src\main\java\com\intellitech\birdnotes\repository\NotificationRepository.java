package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.User;


public interface NotificationRepository extends JpaRepository<Notification, Long> {

	List<Notification> findByTargetUser(User user);
	
	@Query("Select i from Notification i where i.targetUser=:user AND  (DATE(i.date) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) ORDER BY i.id DESC")
	List<Notification> findAll(@Param("firstDate") Date firstDate, @Param("lastDate") Date lastDate, @Param("user") User user);
	
	@Query("Select count(id) from Notification i where i.targetUser=:user AND  i.status=false")
	int getNotificationNumber(@Param("user") User user);
	
	@Query("Select n from Notification n where n.id >:id AND n.targetUser.id=:userId ")
	List<Notification> findDelegateNotification(@Param("id") Long id, @Param("userId") Long userId);
	
	@Modifying
	@Query("UPDATE Notification SET status=true WHERE targetUser=:user AND status=false")
	void updateNotificationStatus(@Param("user") User user);
}