package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;

public class ReceiveResponsedDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private List<ProspectDto> prospects;
	private List<SendResultDto> mergedProspects;
	private List<ValidationResponse> prospectsValidationResponse;
	private List<PlanningValidationDto> planningValidationResponse;
	private List<ExpenseReportDto> expenseValidationResponse;
	private List<ActionMarketingResponseDto> marketingActionValidationResponse;
	private List<OpportunityNoteResponseDto> opportinityNoteValidationResponse;

	private List<ProductDto> products;

	private List<GiftDto> gadgets;
	private List<RangeDto> ranges;
	private List<WholesalerDto> wholesalers;
	private List<ProductPotentielDto> potentialProducts;
	private List<LocalityDto> localities;
	private List<SectorDto> sectors;
	private List<SpecialityDto> specialities;
	private List<ProspectTypeDto> prospectTypes;
	private List<EstablishmentDto> establishments;
	private List<ExpenseTypeDto> expenseTypes;
	private List<ActivityTypeDto> activityTypes;
	private List<PotentialDto> potential;
	private List<MinimizedChargePlan> loadPlan;
	private List<NotificationDto> notifications;
	private List<GoalItemDto> goalItems;
	private List<GoalDto> goals;
	private List<UserDto> users;
	private List<PurchaseOrderTemplateDto> purchaseOrderTemplates;
	private List<FreeQuantityRuleItemDto> freeQuantityRuleItems;
	private List<NoteDto> notes;
	private List<VisitDto> doubleVisits;
	private List<VisitProductDto> doubleVisitProducts;
	

	private List<VisitProductDto> visitsProducts;
	private List<VisitDto> visits;
	private List<ExpenseReportDto> expenses;
	private List<PurchaseOrderDto> purchaseOrders;
	private List<RecoveryDto> recoveries;
	private List<AttachmentDto> attachments;
	private List<ActionMarketingResponseDto> actionMarketing;
	private List<ActivityDto> activities;
	private List<PlanningDto> plannings;

	private List<Long> deletedProspects;
	private List<Long> refusedProspects;
	private List<Long> deletedProducts;

	private ConfigurationDto config;
	private String recommendedProspectsDate;

	private List<Long> deletedPlannings;
	private List<Long> deletedPlanningValidations;

	List<ProspectOrderPredictionResponse> prospectsOrderPrediction;

	public ReceiveResponsedDto() {
		super();
		this.gadgets = new ArrayList<>();
		this.products = new ArrayList<>();
		this.wholesalers = new ArrayList<>();
		this.potentialProducts = new ArrayList<>();
		this.prospects = new ArrayList<>();
		this.localities = new ArrayList<>();
		this.sectors = new ArrayList<>();
		this.specialities = new ArrayList<>();
		this.prospectTypes = new ArrayList<>();
		this.establishments = new ArrayList<>();
		this.expenses = new ArrayList<>();
		this.purchaseOrders = new ArrayList<>();
		this.recoveries = new ArrayList<>();
		this.attachments = new ArrayList<>();
		this.expenseTypes = new ArrayList<>();
		this.visitsProducts = new ArrayList<>();
		this.deletedProspects = new ArrayList<>();
		this.refusedProspects = new ArrayList<>();
		this.deletedProducts = new ArrayList<>();
		this.activityTypes = new ArrayList<>();
		this.potential = new ArrayList<>();
		this.planningValidationResponse = new ArrayList<>();
		this.expenseValidationResponse = new ArrayList<>();
		this.marketingActionValidationResponse = new ArrayList<>();
		this.opportinityNoteValidationResponse = new ArrayList<>();
		this.loadPlan = new ArrayList<>();
		this.recommendedProspectsDate = null;
		this.plannings = new ArrayList<>();
		this.actionMarketing = new ArrayList<>();
		this.activities = new ArrayList<>();
		this.config = new ConfigurationDto();
		this.goals = new ArrayList<>();
		this.users = new ArrayList<>();
		this.goalItems = new ArrayList<>();
		this.ranges = new ArrayList<>();
		this.visits = new ArrayList<>();
		this.purchaseOrderTemplates = new ArrayList<>();
		this.freeQuantityRuleItems = new ArrayList<>();
		this.notes = new ArrayList<>();
		this.doubleVisits = new ArrayList<>();
		this.doubleVisitProducts = new ArrayList<>();
	}

	public ConfigurationDto getConfig() {
		return config;
	}

	public void setConfig(ConfigurationDto config) {
		this.config = config;
	}

	public List<ActionMarketingResponseDto> getActionMarketing() {
		return actionMarketing;
	}

	public void setActionMarketing(List<ActionMarketingResponseDto> actionMarketing) {
		this.actionMarketing = actionMarketing;
	}

	public List<ActivityDto> getActivities() {
		return activities;
	}

	public void setActivities(List<ActivityDto> activities) {
		this.activities = activities;
	}

	public List<PlanningDto> getPlannings() {
		return plannings;
	}

	public void setPlannings(List<PlanningDto> plannings) {
		this.plannings = plannings;
	}

	public List<MinimizedChargePlan> getChargePlan() {
		return loadPlan;
	}

	public void setChargePlan(List<MinimizedChargePlan> loadPlan) {
		this.loadPlan = loadPlan;
	}

	public String getRecommendedProspectsDate() {
		return recommendedProspectsDate;
	}

	public void setRecommendedProspectsDate(String recommendedProspectsDate) {
		this.recommendedProspectsDate = recommendedProspectsDate;
	}

	public List<ExpenseReportDto> getExpenseValidationResponse() {
		return expenseValidationResponse;
	}

	public void setExpenseValidationResponse(List<ExpenseReportDto> expenseValidationResponse) {
		this.expenseValidationResponse = expenseValidationResponse;
	}

	public List<PlanningValidationDto> getPlanningValidationResponse() {
		return planningValidationResponse;
	}

	public void setPlanningValidationResponse(List<PlanningValidationDto> planningValidationResponse) {
		this.planningValidationResponse = planningValidationResponse;
	}

	public List<ProductDto> getProducts() {
		return products;
	}

	public List<WholesalerDto> getWholesalers() {
		return wholesalers;
	}

	public List<ProductPotentielDto> getPotentialProducts() {
		return potentialProducts;
	}

	public List<GiftDto> getGadgets() {
		return gadgets;
	}

	public void setProducts(List<ProductDto> products) {
		this.products = products;
	}

	public void setWholesalers(List<WholesalerDto> wholesalers) {
		this.wholesalers = wholesalers;
	}

	public void setPotentialProducts(List<ProductPotentielDto> potentialProducts) {
		this.potentialProducts = potentialProducts;
	}

////
	public void setGadgets(List<GiftDto> gadgets) {
		this.gadgets = gadgets;

	}

	public List<ProspectDto> getProspects() {
		return prospects;
	}

	public void setProspects(List<ProspectDto> prospects) {
		this.prospects = prospects;
	}

	public List<LocalityDto> getLocalities() {
		return localities;
	}

	public void setLocalities(List<LocalityDto> localities) {
		this.localities = localities;
	}

	public List<SectorDto> getSectors() {
		return sectors;
	}

	public void setSectors(List<SectorDto> sectors) {
		this.sectors = sectors;
	}

	public List<GoalDto> getGoals() {
		return goals;
	}

	public void setGoals(List<GoalDto> goals) {
		this.goals = goals;
	}

	public List<GoalItemDto> getGoalItems() {
		return goalItems;
	}

	public void setGoalItems(List<GoalItemDto> goalItems) {
		this.goalItems = goalItems;
	}

	public List<UserDto> getUsers() {
		return users;
	}

	public void setUsers(List<UserDto> users) {
		this.users = users;
	}

	public void setPurchaseOrderTemplates(List<PurchaseOrderTemplateDto> purchaseOrderTemplates) {
		this.purchaseOrderTemplates = purchaseOrderTemplates;
	}

	public List<PurchaseOrderTemplateDto> getPurchaseOrderTemplates() {
		return purchaseOrderTemplates;
	}

	public void setFreeQuantityRuleItems(List<FreeQuantityRuleItemDto> freeQuantityRuleItems) {
		this.freeQuantityRuleItems = freeQuantityRuleItems;
	}

	public List<FreeQuantityRuleItemDto> getFreeQuantityRuleItems() {
		return freeQuantityRuleItems;
	}

	public List<SpecialityDto> getSpecialities() {
		return specialities;
	}

	public void setSpecialities(List<SpecialityDto> specialities) {
		this.specialities = specialities;
	}

	public List<ProspectTypeDto> getProspectType() {
		return prospectTypes;
	}

	public void setProspectType(List<ProspectTypeDto> prospectTypes) {
		this.prospectTypes = prospectTypes;
	}

	public List<EstablishmentDto> getEstablishments() {
		return establishments;
	}

	public void setEstablishments(List<EstablishmentDto> establishments) {
		this.establishments = establishments;
	}

	public List<Long> getDeletedProspects() {
		return deletedProspects;
	}

	public void setDeletedProspects(List<Long> deletedProspects) {
		this.deletedProspects = deletedProspects;
	}
	
	

	public List<Long> getRefusedProspects() {
		return refusedProspects;
	}

	public void setRefusedProspects(List<Long> refusedProspects) {
		this.refusedProspects = refusedProspects;
	}

	public List<Long> getDeletedProducts() {
		return deletedProducts;
	}

	public void setDeletedProducts(List<Long> deletedProducts) {
		this.deletedProducts = deletedProducts;
	}

	public List<ActivityTypeDto> getActivityTypes() {
		return activityTypes;
	}

	public void setActivityTypes(List<ActivityTypeDto> activityTypes) {
		this.activityTypes = activityTypes;
	}

	public void setPotential(List<PotentialDto> potential) {
		this.potential = potential;
	}

	public List<PotentialDto> getPotential() {
		return potential;
	}

	public void setNotifications(List<NotificationDto> notifications) {
		this.notifications = notifications;
	}

	public List<NotificationDto> getNotifications() {
		return this.notifications;
	}

	public List<RangeDto> getRanges() {
		return ranges;
	}

	public void setRanges(List<RangeDto> ranges) {
		this.ranges = ranges;
	}

	public List<SendResultDto> getMergedProspects() {
		return mergedProspects;
	}

	public void setMergedProspects(List<SendResultDto> mergedProspects) {
		this.mergedProspects = mergedProspects;
	}

	public List<ValidationResponse> getProspectsValidationResponse() {
		return prospectsValidationResponse;
	}

	public void setProspectsValidationResponse(List<ValidationResponse> prospectsValidationResponse) {
		this.prospectsValidationResponse = prospectsValidationResponse;
	}

	public List<VisitProductDto> getVisitsProducts() {
		return visitsProducts;
	}

	public void setVisitsProducts(List<VisitProductDto> visitsProducts) {
		this.visitsProducts = visitsProducts;
	}

	public List<VisitDto> getVisits() {
		return visits;
	}

	public void setVisits(List<VisitDto> visits) {
		this.visits = visits;
	}

	public List<ActionMarketingResponseDto> getMarketingActionValidationResponse() {
		return marketingActionValidationResponse;
	}

	public void setMarketingActionValidationResponse(
			List<ActionMarketingResponseDto> marketingActionValidationResponse) {
		this.marketingActionValidationResponse = marketingActionValidationResponse;
	}

	public List<OpportunityNoteResponseDto> getOpportinityNoteValidationResponse() {
		return opportinityNoteValidationResponse;
	}

	public void setOpportinityNoteValidationResponse(
			List<OpportunityNoteResponseDto> opportinityNoteValidationResponse) {
		this.opportinityNoteValidationResponse = opportinityNoteValidationResponse;
	}

	public List<ExpenseTypeDto> getExpenseTypes() {
		return expenseTypes;
	}

	public void setExpenseTypes(List<ExpenseTypeDto> expenseTypes) {
		this.expenseTypes = expenseTypes;
	}

	public List<ExpenseReportDto> getExpenses() {
		return expenses;
	}

	public void setExpenses(List<ExpenseReportDto> expenses) {
		this.expenses = expenses;
	}

	public List<PurchaseOrderDto> getPurchaseOrders() {
		return purchaseOrders;
	}

	public void setPurchaseOrders(List<PurchaseOrderDto> purchaseOrders) {
		this.purchaseOrders = purchaseOrders;
	}

	public void setRecoveries(List<RecoveryDto> recoveries) {
		this.recoveries = recoveries;
	}

	public List<RecoveryDto> getRecoveries() {
		return recoveries;
	}

	public List<Long> getDeletedPlannings() {
		return deletedPlannings;
	}

	public void setDeletedPlannings(List<Long> deletedPlannings) {
		this.deletedPlannings = deletedPlannings;
	}

	public List<Long> getDeletedPlanningValidations() {
		return deletedPlanningValidations;
	}

	public void setDeletedPlanningValidations(List<Long> deletedPlanningValidations) {
		this.deletedPlanningValidations = deletedPlanningValidations;
	}

	public List<AttachmentDto> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<AttachmentDto> attachments) {
		this.attachments = attachments;
	}

	public List<ProspectOrderPredictionResponse> getProspectsOrderPrediction() {
		return prospectsOrderPrediction;
	}

	public void setProspectsOrderPrediction(List<ProspectOrderPredictionResponse> prospectsOrderPrediction) {
		this.prospectsOrderPrediction = prospectsOrderPrediction;
	}
	
	public List<NoteDto> getNotes() {
		return notes;
	}

	public void setNotes(List<NoteDto> notes) {
		this.notes = notes;
	}

	public List<VisitDto> getDoubleVisits() {
		return doubleVisits;
	}

	public void setDoubleVisits(List<VisitDto> doubleVisits) {
		this.doubleVisits = doubleVisits;
	}

	public List<VisitProductDto> getDoubleVisitProducts() {
		return doubleVisitProducts;
	}

	public void setDoubleVisitProducts(List<VisitProductDto> doubleVisitProducts) {
		this.doubleVisitProducts = doubleVisitProducts;
	}


}