package com.intellitech.birdnotes.controller;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ProspectActivity;
import com.intellitech.birdnotes.model.dto.ProspectActivityDto;
import com.intellitech.birdnotes.service.ProspectActivityService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/prospectActivity")
public class ProspectActivityController {
	private static final Logger LOG = LoggerFactory.getLogger(RangeController.class);

	@Autowired
	private ProspectActivityService prospectActivityService;
	@Autowired
	UserService userService;
	
	@RequestMapping(value = "/getAllProspectActivity", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectActivityDto>> findAll() {
		try {
			if (userService.checkHasPermission("PROSPECT_ACTIVITY_VIEW")) {
				List<ProspectActivityDto> toReturn = prospectActivityService.findAll();
				return new ResponseEntity<List<ProspectActivityDto>>(toReturn, HttpStatus.OK);

			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting delegate types", e);
			return new ResponseEntity<List<ProspectActivityDto>>(new ArrayList<ProspectActivityDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/saveProspectActivity", method = RequestMethod.POST)
	public ResponseEntity<String> saveProspectActivity(@RequestBody ProspectActivityDto prospectActivityDto) {
	    try {
	        if (userService.checkHasPermission("PROSPECT_ACTIVITY_EDIT")) {
	        	ProspectActivity savedprospectActivity = prospectActivityService.saveprospectActivity(prospectActivityDto);
	            if (savedprospectActivity != null) {
	                return new ResponseEntity<>(savedprospectActivity.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving prospect activity", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving prospect activity", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePreference(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("PROSPECT_ACTIVITY_DELETE")) {
	        	prospectActivityService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); 
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting prospect activity", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the prospect activity with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}
	}
	