package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.enumeration.GroupType;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.DuplicateProspectDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.KeyValueDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectActivityDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.model.dto.ValueTypeDto;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.EstablishmentService;
import com.intellitech.birdnotes.service.LocalityService;
import com.intellitech.birdnotes.service.ExpenseReportService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectActivityService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.ExpenseTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
/**
 * 
 * <AUTHOR> Sellami
 *
 */

@Service("downloadDataService")
@Transactional
public class DownloadDataServiceImpl implements DownloadDataService {
	private DelegateService delegateService;
	private UserService userService;
	private ProspectService prospectService;
	private ExpenseReportService noteFraisService;
	private ExpenseTypeService typeNoteFraisService;
	private SpecialityService specialityService;
	private ProspectTypeService prospectTypeService;
	private EstablishmentService establishmentService;
	private PotentialService potentialService;
	private SectorService sectorService;
	private LocalityService localityService;
	private ProspectActivityService prospectActivityService;
	private ProductService productService;
	private ProspectRepository prospectRepository;
	private ValidationStepService validationStepService;
	
	@Autowired
	public void setProspectRepository( ProspectRepository prospectRepository) {
		this.prospectRepository = prospectRepository;
	}

	@Autowired
	public void setDelegateService(DelegateService delegateService) {
		this.delegateService = delegateService;
	}


	@Autowired
	public void setUserService( UserService userService) {
		this.userService = userService;
	}

	@Autowired
	public void setProspectService( ProspectService prospectService) {
		this.prospectService = prospectService;
	}

	@Autowired
	public void setNoteFraisService( ExpenseReportService noteFraisService) {
		this.noteFraisService = noteFraisService;
	}
	
    @Autowired	
    private ConfigurationService configurationService;

	@Autowired
	public void setTypeNoteFraisService( ExpenseTypeService typeNoteFraisService) {
		this.typeNoteFraisService = typeNoteFraisService;
	}

	@Autowired
	public void setSpecialityService( SpecialityService specialityService) {
		this.specialityService = specialityService;
	}
	

	@Autowired
	public void setProspectTypeService( ProspectTypeService prospectTypeService) {
		this.prospectTypeService = prospectTypeService;
	}

	@Autowired
	public void setEstablishmentService(EstablishmentService establishmentService) {
		this.establishmentService = establishmentService;
	}


	@Autowired
	public void setPotentialService( PotentialService potentialService) {
		this.potentialService = potentialService;
	}

	@Autowired
	public void setSectorService( SectorService sectorService) {
		this.sectorService = sectorService;
	}

	@Autowired
	public void setLocalityService( LocalityService localityService) {
		this.localityService = localityService;
	}
	
	@Autowired
	public void setProspectActivityService( ProspectActivityService prospectActivityService) {
		this.prospectActivityService = prospectActivityService;
	}

	@Autowired
	public void setProductService( ProductService productService) {
		this.productService = productService;
	}
	
	@Autowired
	public void setValidationStepService( ValidationStepService validationStepService) {
		this.validationStepService = validationStepService;
	}
	
	@Autowired
	private MessageSource messageSource;
	
	@Override
	public Map<String, Object> loadDataToVisitHistory(BirdnotesUser birdnotesUser, Locale locale) throws BirdnotesException {
		
        ConfigurationDto config = configurationService.findConfiguration();
        String commentsDictionary = (config != null) ? config.getCommentsDictionary() : null;
        List<String> commentsDictionaryList = new ArrayList<>();
        if (commentsDictionary != null && !commentsDictionary.isEmpty()) {
            commentsDictionaryList = Arrays.asList(commentsDictionary.split("\\n"));
        }


		Map<String, Object> dataVisitHistory = new HashMap<>();
		List<ProductDto> listProductDto = productService.getAllProducts();
		List<SpecialityDto> listSpecialitiesDto = loadDataOfSpeciality();
		List<DelegateDto> listDelegatesDto = loadDataOfDelegate(birdnotesUser);
		//List<ProspectDto> listProspectsDto= loadDataOfProspect();
		List<SectorDto> listSectorsDto = loadDataOfSector();
		List<LocalityDto> listLocalitiessDto = loadDataOfLocality();
		List<ValueTypeDto> listGroupsType = loadDataOfGroupeType(locale);
		List<ValueTypeDto> listValuesType = loadDataOfValueType(locale);
		List<ProspectActivityDto> listprospectActivitiesDto = loadDataOfProspectActivity();
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.DELEGATES, listDelegatesDto);
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.PRODUCTS, listProductDto);
		//dataVisitHistory.put(BirdnotesConstants.VisitHistory.PROSPECTS, listProspectsDto);
		dataVisitHistory.put(BirdnotesConstants.Sector.SECTORS, listSectorsDto);
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.LOCALITIES, listLocalitiessDto);
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.SPECIALTIES, listSpecialitiesDto);
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.GROUPS, listGroupsType);
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.VALUES, listValuesType);
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.PRESENTATIO_ORDER, loadPresentationOrders());
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.PRODUCT_SATISFACTION, loadProductSatisfaction());
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.POTENTIALS, loadDataOfPotential());		
		dataVisitHistory.put(BirdnotesConstants.VisitHistory.COMMENT_RATINGS, loadCommentRating());
		dataVisitHistory.put("commentsDictionary", commentsDictionaryList);
		dataVisitHistory.put(BirdnotesConstants.Prospect.PROSPECT_ACTIVITIES, listprospectActivitiesDto);

		
		return dataVisitHistory;
	}
	
	
	
	
	private List<String> loadCommentRating() {
	
		return Arrays.asList("Présentation", "test en cours" , "Promesse", "Client", "Partenaire",  "Réclamation", "Refus");
	}


	private List<KeyValueDto> loadProductSatisfaction() throws BirdnotesException {
		List<KeyValueDto> presentationOrders = new ArrayList<>();
		KeyValueDto po = new KeyValueDto("Trés Satisfait " , new Long(5));
		presentationOrders.add(po);
		po = new KeyValueDto("Satisfait " , new Long(4));
		presentationOrders.add(po);
		po = new KeyValueDto("Neutre " , new Long(3));
		presentationOrders.add(po);
		po = new KeyValueDto("Confus " , new Long(2));
		presentationOrders.add(po);
		po = new KeyValueDto("Ennuyé " , new Long(1));
		presentationOrders.add(po);
		po = new KeyValueDto("Non Satisfait " , new Long(0));
		presentationOrders.add(po);
		
		
		return presentationOrders;
	}
	
	private List<KeyValueDto> loadPresentationOrders() throws BirdnotesException {
		List<KeyValueDto> presentationOrders = new ArrayList<>();
		List<ProductDto>  productDtoList = productService.getAllProducts();
		
		
		if (productDtoList != null && !productDtoList.isEmpty()) {

			for (int i = 1; i < productDtoList.size() +1;i++) {
				KeyValueDto po = new KeyValueDto("Ordre " + i, new Long(i));
				presentationOrders.add(po);
			}
		}
		
		return presentationOrders;
	}

	private List<ValueTypeDto> loadDataOfValueType(Locale locale) {
		List<ValueTypeDto> listValuesType = new ArrayList<>();
		if (ValueType.getAllValueType(messageSource, locale) != null || !(ValueType.getAllValueType(messageSource, locale).isEmpty())) {
			listValuesType = ValueType.getAllValueType(messageSource, locale);
		}
		return listValuesType;
	}

	private List<ValueTypeDto> loadDataOfGroupeType(Locale locale) {
		List<ValueTypeDto> listGroupsType = new ArrayList<>();
		if (GroupType.getAllGroupType(messageSource, locale) != null || !(GroupType.getAllGroupType(messageSource, locale).isEmpty())) {
			listGroupsType = GroupType.getAllGroupType(messageSource, locale);
		}
		return listGroupsType;
	}
	private List<ValueTypeDto> loadGroupTypeToProspectDistribution(Locale locale) {
		List<ValueTypeDto> listGroupsType = new ArrayList<>();
		if (GroupType.getAllGroupTypeToProspectDistribution(messageSource, locale) != null || !(GroupType.getAllGroupTypeToProspectDistribution(messageSource, locale).isEmpty())) {
			listGroupsType = GroupType.getAllGroupTypeToProspectDistribution(messageSource, locale);
		}
		return listGroupsType;
	}
	
	private List<LocalityDto> loadDataOfLocality() throws BirdnotesException {
		List<LocalityDto> listLocalitiessDto = new ArrayList<>();
		if (localityService.findAll() != null || !(localityService.findAll().isEmpty())) {
			listLocalitiessDto = localityService.findAll();
		}
		return listLocalitiessDto;
	}
	
	private List<ProspectActivityDto> loadDataOfProspectActivity() throws BirdnotesException {
		List<ProspectActivityDto> listProspectActivitiesDto = new ArrayList<>();
		if (prospectActivityService.findAll() != null || !(prospectActivityService.findAll().isEmpty())) {
			listProspectActivitiesDto = prospectActivityService.findAll();
		}
		return listProspectActivitiesDto;
	}

	private List<SectorDto> loadDataOfSector() throws BirdnotesException {
		List<SectorDto> listSectorsDto = new ArrayList<>();
		if (sectorService.findAll() != null || !(sectorService.findAll().isEmpty())) {
			listSectorsDto = sectorService.findAll();
		}
		return listSectorsDto;
	}


	private List<DelegateDto> loadDataOfDelegate(BirdnotesUser birdnotesUser) {
		List<DelegateDto> delegateDtos = new ArrayList<>();
		delegateDtos = delegateService.findAllDelegates();
		return delegateDtos;
	}

	private List<SpecialityDto> loadDataOfSpeciality() throws BirdnotesException {
		List<SpecialityDto> listSpecialitiesDto = new ArrayList<>();
		if (specialityService.findAll() != null || !(specialityService.findAll().isEmpty())) {
			listSpecialitiesDto = specialityService.findAll();
		}
		return listSpecialitiesDto;
	}
	
	private List<ProspectTypeDto> loadDataOfProspectType() throws BirdnotesException {
		List<ProspectTypeDto> listProspectTypeDto = new ArrayList<>();
		if (prospectTypeService.findAll() != null || !(prospectTypeService.findAll().isEmpty())) {
			listProspectTypeDto = prospectTypeService.findAll();
		}
		return listProspectTypeDto;
	}
	
	private List<EstablishmentDto> loadDataOfEstablishment() throws BirdnotesException {
		List<EstablishmentDto> listEstablishmentDto = new ArrayList<>();
		if (establishmentService.findAll() != null || !(establishmentService.findAll().isEmpty())) {
			listEstablishmentDto = establishmentService.findAll();
		}
		return listEstablishmentDto;
	}

	@Override
	public Map<String, Object> getNoteFraisDyDate(Date firstDate, Date lastDate, Long userId) throws BirdnotesException {
		List<ExpenseReportDto> expenseReportDtos = new ArrayList<>();
		
		expenseReportDtos = noteFraisService.findByDate(firstDate, lastDate, userId);
		
		List<ExpenseTypeDto> expenseTypeDtos = new ArrayList<>();
		
		expenseTypeDtos = typeNoteFraisService.findAll();

		List<UserDto> userDtos = new ArrayList<>();
		
		userDtos = userService.getSubUsers();

		HashMap<String, Object> expenseData = new HashMap<>();
		expenseData.put(BirdnotesConstants.ExpenseReport.EXPENSE_REPORT, expenseReportDtos);
		expenseData.put(BirdnotesConstants.ExpenseReport.EXPENSE_TYPE, expenseTypeDtos);
		expenseData.put(BirdnotesConstants.ExpenseReport.DELEGATES, userDtos);

		return expenseData;
	}

	@Override
	public Map<String, Object> findAllDataForProspect(BirdnotesUser birdnotesUser) throws BirdnotesException {
		
 		List<ProspectDto> prospectChangedDtos = loadDataOfProspectChanged();
		Long prospectsCount = loadProspectsCount();
		List<SpecialityDto> specialityDtos = loadDataOfSpeciality();
		List<ProspectTypeDto> prospectTypeDtos = loadDataOfProspectType();
		List<EstablishmentDto> establishmentDtos = loadDataOfEstablishment();
		List<DelegateDto> delegateDtos = loadDataOfDelegate(birdnotesUser);
		List<PotentialDto> potentialsDtos = loadDataOfPotential();
		
		List<SectorDto> sectorDtos = loadDataOfSector();
		List<LocalityDto> localityDtos = loadDataOfLocality();
		List<ProspectActivityDto> prospectActivityDtos = loadDataOfProspectActivity();
		
        ConfigurationDto config = configurationService.findConfiguration();
        Double defaultLatitude = (config != null) ? config.getDefaultLatitude() : null;
        Double defaultLongitude = (config != null) ? config.getDefaultLongitude() : null;
 		HashMap<String, Object> dataProspect = new HashMap<>();
 		dataProspect.put(BirdnotesConstants.Prospect.PROSPECTS_CHANGED, prospectChangedDtos);
		dataProspect.put(BirdnotesConstants.Prospect.PROSPECTS_COUNT, prospectsCount);
		dataProspect.put(BirdnotesConstants.Prospect.SPECIALTYS, specialityDtos);
		dataProspect.put(BirdnotesConstants.Prospect.TYPES, prospectTypeDtos);
		dataProspect.put(BirdnotesConstants.Prospect.ESTABLISHMENTS, establishmentDtos);
		dataProspect.put(BirdnotesConstants.Prospect.DELEGATES, delegateDtos);
		dataProspect.put(BirdnotesConstants.Prospect.POTENTIALS, potentialsDtos);
		dataProspect.put(BirdnotesConstants.Prospect.SECTORS, sectorDtos);
		dataProspect.put(BirdnotesConstants.Prospect.LOCALITYS, localityDtos);
		dataProspect.put(BirdnotesConstants.Prospect.PROSPECT_ACTIVITIES, prospectActivityDtos);
        dataProspect.put("defaultLatitude", defaultLatitude);
        dataProspect.put("defaultLongitude", defaultLongitude);
 		return dataProspect;
	}

	private Long loadProspectsCount() {
		return prospectRepository.count();
	}

	private List<ProspectDto> loadDataOfProspectChanged() throws BirdnotesException {
		
		List<ProspectDto> prospectChangedDtos = new ArrayList<>();
		if (prospectService.findProspectWithStatus() != null || !(prospectService.findProspectWithStatus().isEmpty())) {
			prospectChangedDtos = prospectService.findProspectWithStatus();
		}
		for (ProspectDto prospectDto :prospectChangedDtos) {
			Prospect prospect = prospectRepository.findOne(prospectDto.getId());
			List<ValidationStatusDto> listValidationStatusDto = validationStepService.findByProspect(prospect.getId());
			prospectDto.setValidationStatusDto(listValidationStatusDto);
		}
		return prospectChangedDtos;
	}


	private List<PotentialDto> loadDataOfPotential() throws BirdnotesException {
		
		List<PotentialDto> potentialsDtos = new ArrayList<>();
		if (potentialService.findAll() != null || !(potentialService.findAll().isEmpty())) {
			potentialsDtos = potentialService.findAll();
		}
		return potentialsDtos;
	}


	@Override
	public Map<String, Object> findAllDataForSector() throws BirdnotesException {
		List<UserDto> userDtos = new ArrayList<>();
		if (userService.getSubUsers() != null || !(userService.getSubUsers().isEmpty())) {
			userDtos = userService.getSubUsers();
		}
		List<SectorDto> sectorDtos = new ArrayList<>();
		if (sectorService.findAll() != null || !(sectorService.findAll().isEmpty())) {
			sectorDtos = sectorService.findAll();
		}
		HashMap<String, Object> dataSector = new HashMap<>();
		dataSector.put(BirdnotesConstants.Sector.DELEGATES, userDtos);
		dataSector.put(BirdnotesConstants.Sector.SECTORS, sectorDtos);
		return dataSector;
	}

	@Override
	public Map<String, Object> findAllDataForLocality() throws BirdnotesException {
		List<LocalityDto> localityDtos = new ArrayList<>();
		if (localityService.findAll() != null || !(localityService.findAll().isEmpty())) {
			localityDtos = localityService.findAll();
		}
		List<SectorDto> sectorDtos = new ArrayList<>();
		if (sectorService.findAll() != null || !(sectorService.findAll().isEmpty())) {
			sectorDtos = sectorService.findAll();
		}
		HashMap<String, Object> dataLocality = new HashMap<>();
		dataLocality.put(BirdnotesConstants.Locality.LOCALITYS, localityDtos);
		dataLocality.put(BirdnotesConstants.Locality.SECTORS, sectorDtos);
		return dataLocality;
	}

	@Override
	public Map<String, Object> findAllDataForNewProspect() throws BirdnotesException {
		List<SectorDto> sectorDtos = new ArrayList<>();
		if (sectorService.findAll() != null || !(sectorService.findAll().isEmpty())) {
			sectorDtos = sectorService.findAll();
		}
		List<SpecialityDto> specialityDtos = new ArrayList<>();
		if (specialityService.findAll() != null || !(specialityService.findAll().isEmpty())) {
			specialityDtos = specialityService.findAll();
		}
		List<ProspectTypeDto> prospectTypeDtos = new ArrayList<>();
		if (prospectTypeService.findAll() != null || !(prospectTypeService.findAll().isEmpty())) {
			prospectTypeDtos = prospectTypeService.findAll();
		}
		List<EstablishmentDto> establishmentDtos = new ArrayList<>();
		if (establishmentService.findAll() != null || !(establishmentService.findAll().isEmpty())) {
			establishmentDtos = establishmentService.findAll();
		}
		List<LocalityDto>localityDtos=loadDataOfLocality();
		List<PotentialDto>potentialDtos=loadDataOfPotential();
		HashMap<String, Object> dataForNewProspect = new HashMap<>();
		dataForNewProspect.put(BirdnotesConstants.Prospect.SPECIALTYS, specialityDtos);
		dataForNewProspect.put(BirdnotesConstants.Prospect.TYPES, prospectTypeDtos);
		dataForNewProspect.put(BirdnotesConstants.Prospect.SECTORS, sectorDtos);
		dataForNewProspect.put(BirdnotesConstants.Prospect.LOCALITYS, localityDtos);
		dataForNewProspect.put(BirdnotesConstants.Prospect.POTENTIALS, potentialDtos);
		dataForNewProspect.put(BirdnotesConstants.Prospect.ESTABLISHMENTS, establishmentDtos);
		dataForNewProspect.put(BirdnotesConstants.Prospect.PROSPECT_TYPES, prospectTypeDtos);
		return dataForNewProspect;
	}

	@Override
	public Map<String,Object> getProspectUpdateRequests() throws BirdnotesException {
		HashMap<String, Object> prospectData = new HashMap<>();
		List<ProspectDto> prospectChangedDtos;
		prospectChangedDtos = loadDataOfProspectChanged();
		List<DuplicateProspectDto> duplicateProspectDtos = findDuplicateProspects(prospectChangedDtos);
		prospectData.put(BirdnotesConstants.Prospect.PROSPECTS_CHANGED, duplicateProspectDtos);

		return prospectData;
	}
	
	private List<DuplicateProspectDto> findDuplicateProspects(List<ProspectDto> prospectChangedDtos)throws BirdnotesException{
		
		ArrayList<DuplicateProspectDto> duplicateProspectsList=new ArrayList<>();		
		if(!prospectChangedDtos.isEmpty()) {
			for(ProspectDto prospectChangedDto:prospectChangedDtos) {
				
					DuplicateProspectDto duplicateProspectDto=new DuplicateProspectDto();
					duplicateProspectDto.setProspect(prospectChangedDto);
					if(prospectChangedDto.getStatus().equals(UserValidationStatus.NEW)) {
						List<ProspectDto> existingProspects=prospectService.findSimilarProspects(prospectChangedDto);
						if(!existingProspects.isEmpty()) {
							duplicateProspectDto.setSimilarProspectDtos(existingProspects);
						}					
					}
					duplicateProspectsList.add(duplicateProspectDto);
				
			}
		}
		return duplicateProspectsList;
	}


	@Override
	public Map<String, Object> loadDataForProspectDistribution(BirdnotesUser birdnotesUser, Locale locale) throws BirdnotesException {
		
		Map<String, Object> dataProspectDistribution = new HashMap<>();
		List<SpecialityDto> listSpecialityDtos = loadDataOfSpeciality();
		List<SectorDto> listSectorDtos = loadDataOfSector();
		List<LocalityDto> listLocalityDtos = loadDataOfLocality();
		List<ValueTypeDto> listGroupsType = loadGroupTypeToProspectDistribution(locale);
		List<PotentialDto> listPotentialDtos = loadDataOfPotential();
		List<DelegateDto> listUserDtos = loadDataOfDelegate(birdnotesUser);
		List<ProspectActivityDto> listProspectActivityDtos = loadDataOfProspectActivity();
		dataProspectDistribution.put(BirdnotesConstants.Prospect.DELEGATES, listUserDtos);
		dataProspectDistribution.put(BirdnotesConstants.Prospect.SECTORS, listSectorDtos);
		dataProspectDistribution.put(BirdnotesConstants.Prospect.LOCALITYS, listLocalityDtos);
		dataProspectDistribution.put(BirdnotesConstants.Prospect.SPECIALTYS, listSpecialityDtos);
		dataProspectDistribution.put(BirdnotesConstants.VisitHistory.GROUPS, listGroupsType);
		dataProspectDistribution.put(BirdnotesConstants.Prospect.POTENTIALS, listPotentialDtos);
		dataProspectDistribution.put(BirdnotesConstants.Prospect.PROSPECT_ACTIVITIES, listProspectActivityDtos);
		return dataProspectDistribution;
	}


	
}
