package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.ContactType;


@Repository
public interface ContactTypeRepository extends JpaRepository<ContactType, Long> {
	
	
	
	@Override
	@Query("SELECT ct from ContactType ct order by ct.name ASC")
	List<ContactType> findAll();

	ContactType findByName(String name);

	@Modifying
	@Query("DELETE FROM ContactType where id=:id")
	void deleteById(@Param ("id") Long id);
    @Query("SELECT ct.id from ContactType ct")
	List<Long> getAllContactTypeIds();
    
	@Query("SELECT ct from ContactType ct where  LOWER(name) = LOWER(?1) AND id != ?2")
	ContactType findByNameAndAnotherId(String name, Long id);

	
	
}