package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class TotalCountDto implements Serializable {

	private static final long serialVersionUID = 1L;
	protected long count = 0;
	protected long total = 0;
	

	public TotalCountDto() {
		super();
	}


	public long getTotal() {
		return total;
	}

	public void cummulate(long quantity) {
		this.total+= quantity;
	}

	public void increment() {
		count++;
	}
	
	public float getAverage()
	{
		if(count != 0) {
			return (float) total / (count  );
		}else {
			return 0f;
		}
	}
	
	

}
