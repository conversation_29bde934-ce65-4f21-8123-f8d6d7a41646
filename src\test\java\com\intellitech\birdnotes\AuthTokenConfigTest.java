package com.intellitech.birdnotes;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;

import java.util.HashMap;

@RunWith(MockitoJUnitRunner.class)
public class AuthTokenConfigTest {

    @Mock
    private UserDetailsService mockBirdnotesUserDetailsService;

    @InjectMocks
    private AuthTokenConfig authTokenConfigUnderTest;

    @Test
    public void testConfigure() throws Exception {
        // Setup
        //final HttpSecurity http = new HttpSecurity(null, new AuthenticationManagerBuilder(null), new HashMap<>());

        // Run the test
        //authTokenConfigUnderTest.configure(http);

        // Verify the results
    }

    @Test(expected = Exception.class)
    public void testConfigure_ThrowsException() throws Exception {
        // Setup
        final HttpSecurity http = new HttpSecurity(null, new AuthenticationManagerBuilder(null), new HashMap<>());

        // Run the test
        authTokenConfigUnderTest.configure(http);
    }
}
