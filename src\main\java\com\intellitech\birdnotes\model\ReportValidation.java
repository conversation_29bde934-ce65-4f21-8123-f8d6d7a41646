package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.REPORT_VALIDATION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ReportValidation implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.REPORT_VALIDATION_SEQUENCE, sequenceName = Sequences.REPORT_VALIDATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.REPORT_VALIDATION_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	private Delegate delegate;

	@Column(name = Columns.INVALID_VISITS_NUMBER)
	private Integer invalidVisitsNumber;

	@Column(name = Columns.VALID_VISITS_NUMBER)
	private Integer validVisitsNumber;

	@Column(name = Columns.VALID_GEOLOCATED_VISITS_NUMBER)
	private Integer validGeolocatedVisitsNumber;

	@Column(name = Columns.INVALID_GEOLOCATED_VISITS_NUMBER)
	private Integer invalidGeolocatedVisitsNumber;

	@Column(name = Columns.VISIT_DATE)
	private Date visitDate;
	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	private UserValidationStatus status;

	public ReportValidation() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}

	public Integer getInvalidVisitsNumber() {
		return invalidVisitsNumber;
	}

	public void setInvalidVisitsNumber(Integer invalidVisitsNumber) {
		this.invalidVisitsNumber = invalidVisitsNumber;
	}

	public Integer getValidVisitsNumber() {
		return validVisitsNumber;
	}

	public void setValidVisitsNumber(Integer validVisitsNumber) {
		this.validVisitsNumber = validVisitsNumber;
	}

	public Integer getValidGeolocatedVisitsNumber() {
		return validGeolocatedVisitsNumber;
	}

	public void setValidGeolocatedVisitsNumber(Integer validGeolocatedVisitsNumber) {
		this.validGeolocatedVisitsNumber = validGeolocatedVisitsNumber;
	}

	public Integer getInvalidGeolocatedVisitsNumber() {
		return invalidGeolocatedVisitsNumber;
	}

	public void setInvalidGeolocatedVisitsNumber(Integer invalidGeolocatedVisitsNumber) {
		this.invalidGeolocatedVisitsNumber = invalidGeolocatedVisitsNumber;
	}

	public Date getVisitDate() {
		return visitDate;
	}

	public void setVisitDate(Date visitDate) {
		this.visitDate = visitDate;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

}
