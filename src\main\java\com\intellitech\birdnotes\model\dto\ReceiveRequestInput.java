package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class ReceiveRequestInput implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private List<Long> existingProspects;
	private List<Long> existingPlannings;
	private List<Long> existingPlanningValidations;
	private List<Long> newProspectWaitingForValidation;
	private List<Long> updatedProspectWaitingForValidation;
	private List<Long> planningWaitingValidation;
	private List<Long> expenseWaitingValidation;
	
	private List<IdVersionDto> existingProducts;
	
	private Long lastNotificationId;
	private boolean firstSync;
    private Date maxPredictionDate;
    private Date currentWeekDate;
    private Date lastReceiveDate;



	public boolean isFirstSync() {
		return firstSync;
	}

	public void setFirstSync(boolean firstSync) {
		this.firstSync = firstSync;
	}

	public List<Long> getNewProspectWaitingForValidation() {
		return newProspectWaitingForValidation;
	}
	
	public void setNewProspectWaitingForValidation(List<Long> prospectWaitingForValidation) {
		this.newProspectWaitingForValidation = prospectWaitingForValidation;
	}
	
	public List<IdVersionDto> getExistingProducts() {
		return existingProducts;
	}
	
	public void setExistingProducts(List<IdVersionDto> existingProducts) {
		this.existingProducts = existingProducts;
	}

	public Long getLastNotificationId() {
		return lastNotificationId;
	}

	public void setLastNotificationId(Long lastNotificationId) {
		this.lastNotificationId = lastNotificationId;
	}

	public List<Long> getExistingProspects() {
		return existingProspects;
	}

	public void setExistingProspects(List<Long> existingProspects) {
		this.existingProspects = existingProspects;
	}

	public List<Long> getUpdatedProspectWaitingForValidation() {
		return updatedProspectWaitingForValidation;
	}

	public void setUpdatedProspectWaitingForValidation(List<Long> updatedProspectWaitingForValidation) {
		this.updatedProspectWaitingForValidation = updatedProspectWaitingForValidation;
	}

	public List<Long> getPlanningWaitingValidation() {
		return planningWaitingValidation;
	}
	

	public void setPlanningWaitingValidation(List<Long> planningWaitingValidation) {
		this.planningWaitingValidation = planningWaitingValidation;
	}

	public List<Long> getExpenseWaitingValidation() {
		return expenseWaitingValidation;
	}

	public void setExpenseWaitingValidation(List<Long> expenseWaitingValidation) {
		this.expenseWaitingValidation = expenseWaitingValidation;
	}

	public List<Long> getExistingPlannings() {
		return existingPlannings;
	}

	public void setExistingPlannings(List<Long> existingPlanning) {
		this.existingPlannings = existingPlanning;
	}

	public Date getCurrentWeekDate() {
		return currentWeekDate;
	}

	public void setCurrentWeekDate(Date currentWeekDate) {
		this.currentWeekDate = currentWeekDate;
	}

	public Date getMaxPredictionDate() {
		return maxPredictionDate;
	}

	public void setMaxPredictionDate(Date maxPredictionDate) {
		this.maxPredictionDate = maxPredictionDate;
	}

	public Date getLastReceiveDate() {
		return lastReceiveDate;
	}

	public void setLastReceiveDate(Date lastReceiveDate) {
		this.lastReceiveDate = lastReceiveDate;
	}
	
	public List<Long> getExistingPlanningValidations() {
		return existingPlanningValidations;
	}

	public void setExistingPlanningValidations(List<Long> existingPlanningValidations) {
		this.existingPlanningValidations = existingPlanningValidations;
	}
	
}