package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.DelegateType;
import com.intellitech.birdnotes.model.dto.DelegateTypeDto;
import com.intellitech.birdnotes.repository.DelegateTypeRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.DelegateTypeService;
import com.intellitech.birdnotes.service.UserService;

@Service("delegateTypeService")
@Transactional
public class DelegateTypeServiceImpl implements DelegateTypeService {
	
	@Autowired
	private DelegateTypeRepository delegateTypeRepository;
	
	@Autowired
	UserRepository userRepository;
	
	@Autowired
	UserService userService;
	
	@Override
	public List<DelegateType> findAll() throws BirdnotesException {
		List<DelegateType> back = new ArrayList<>();
		List<DelegateType> listOfdelegateType = delegateTypeRepository.findAll();
		if (listOfdelegateType != null && !listOfdelegateType.isEmpty()) {
			for (DelegateType delegateType : listOfdelegateType) {
				back.add(delegateType);
			}
		}

		return back;

	}
	
	@Override
	public DelegateType savedelegateType(DelegateTypeDto delegateTypeDto) throws BirdnotesException {

		DelegateType existingdelegateType = delegateTypeRepository.findByName(delegateTypeDto.getName());
	    if (existingdelegateType != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    DelegateType delegateType = null;
	    if (delegateTypeDto.getId() != null) {
	    	delegateType = delegateTypeRepository.findOne(delegateTypeDto.getId());
	    }
	    if (delegateType == null) {
	    	delegateType = new DelegateType();
	    }

	    delegateType.setId(delegateTypeDto.getId().longValue());
	    delegateType.setName(delegateTypeDto.getName());
	    return delegateTypeRepository.save(delegateType);
	}
	
	@Override
	public void delete (long id)throws BirdnotesException {
		delegateTypeRepository.delete(id);
	}
	

}
	
	
