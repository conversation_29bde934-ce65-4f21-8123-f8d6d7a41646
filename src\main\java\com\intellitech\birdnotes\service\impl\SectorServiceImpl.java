package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.context.MessageSource;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesUtils;






import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.convertor.ConvertSectorToDto;
import com.intellitech.birdnotes.model.dto.LocalityRequestDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SectorRequestDto;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("sectorService")
@Transactional
public class SectorServiceImpl implements SectorService {
	
	@Autowired
	private ConfigurationService configurationService;
	private SectorRepository sectorRepository;
	private ProspectRepository prospectRepository;
	private UserRepository userRepository;
	private ConvertSectorToDto convertSectorToDto;
	private LocalityRepository localityRepository;
	
	@Autowired
	UserService userService;

	
	@Autowired
	SectorServiceImpl(SectorRepository sectorRepository, ProspectRepository prospectRepository, UserRepository userRepository, ConvertSectorToDto convertSectorToDto, LocalityRepository localityRepository){
		this.sectorRepository = sectorRepository;
		this.prospectRepository = prospectRepository;
		this.userRepository = userRepository;
		this.convertSectorToDto = convertSectorToDto;
		this.localityRepository = localityRepository;
		
	}
	
	@Override
	public List<SectorDto> findAll() throws BirdnotesException {
		List<SectorDto> back = new ArrayList<>();
		List<Sector> allSectors = sectorRepository.findAll();
		for(Sector sector : allSectors) {
			back.add(convertSectorToDto.convert(sector));
		}
		return back;
	}


	@Override
	public Sector addSector(LocalityRequestDto localityRequestDto) throws BirdnotesException {
		Sector sector = new Sector();
		if(localityRequestDto.getSectorName()== null || localityRequestDto.getSectorName().equals("")) {
			throw new BirdnotesException(Exceptions.EMPTY_SECTOR);
		}
		Sector result = sectorRepository.findByName(localityRequestDto.getSectorName());
		if(result!=null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}
				
		sector.setName(localityRequestDto.getSectorName());
	return	sectorRepository.save(sector);
		}
	

	
	@Override
	public void addAll(List<SectorRequestDto> sectorDtos) throws BirdnotesException {
		for(SectorRequestDto sectorDto:sectorDtos) {
		Sector sector = new Sector();
		sector.setName(sectorDto.getName());
		sectorRepository.save(sector);
		}
	}
	
	@Override
	public void add(SectorRequestDto sectorDto) throws BirdnotesException {
		
		Sector sector = new Sector();
		if(sectorDto.getName()== null || "".equals(sectorDto.getName())) {
			throw new BirdnotesException(Exceptions.EMPTY_SECTOR);
		}
		Sector result = sectorRepository.findFirstByNameIgnoreCase(sectorDto.getName());
		if(result!=null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}
				
		sector.setName(sectorDto.getName());
		sector.setLatitude(sectorDto.getLatitude());
		sector.setLongitude(sectorDto.getLongitude());
		sectorRepository.save(sector);
	}
	
	@Override
	public List<SectorRequestDto> saveAll(List<SectorRequestDto> sectorRequestDtos) throws BirdnotesException {
		for(SectorRequestDto sectorRequestDto:sectorRequestDtos){
			add(sectorRequestDto);
		}
		return sectorRequestDtos;
	}
	

	@Override
	public void delete(Long idSector) throws BirdnotesException{
		
		sectorRepository.deleteByID(idSector);}




	@Override
	public Sector saveSector(SectorDto sectorDto) throws BirdnotesException {
		
		if (sectorDto == null || sectorDto.getId() == null) {
			throw new BirdnotesException(Exceptions.SECTOR_DTO_NULL);
		}
		
		Sector result = sectorRepository.findByNameAndAnotherId(sectorDto.getName(), sectorDto.getId());
		if(result!=null) {
			
			throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
		}

		Sector sector = null;
		if(sectorDto.getId() != null) {
			sector = sectorRepository.findOne(sectorDto.getId());
		}
		if (sector == null) {
			sector = new Sector();
		}

		sector.setName(sectorDto.getName());
		sector.setLatitude(sectorDto.getLatitude());
		sector.setLongitude(sectorDto.getLongitude());
		return sectorRepository.save(sector);

	}



	@Override
	public List<SectorDto> findSectorByUserId(Long idUser) throws BirdnotesException {
		List<SectorDto> back = new ArrayList<>();
		List<Sector> allSectors = sectorRepository.findByUserId(idUser);
		for(Sector sector : allSectors) {
			back.add(convertSectorToDto.convert(sector));
		}
		return back;
	}

	@Override
	public SectorDto findSectorByName(String name) throws BirdnotesException {
		Sector sector=sectorRepository.findByName(name);
	SectorDto sectorDto=convertSectorToDto.convert(sector);
	return sectorDto;
		
	}
	@Override
	public SectorDto findSectorDto(String sectorName,List<SectorDto>sectorDtos) {
		for (SectorDto sectorDto : sectorDtos) {
			if (sectorDto.getName().equalsIgnoreCase(sectorName)) {
				return sectorDto;
			}
		}
		return null;
	}
	
	/*@Override
	public List<SectorDto> findSectorByLocalityName(String localityName) {
		List<SectorDto> sectorDtos = new ArrayList<>();
		List<Sector> sectors = sectorRepository.findSectorByLocalityName(localityName);
		for(Sector sector : sectors) {
			sectorDtos.add(convertSectorToDto.convert(sector));
		}
		return sectorDtos;
	}*/
}
