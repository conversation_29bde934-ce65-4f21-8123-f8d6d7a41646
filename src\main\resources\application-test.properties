# DATA SOURCE
spring.datasource.url=***********************************************
spring.datasource.username=postgres
spring.datasource.password=intellitech
spring.datasource.driverClassName=org.postgresql.Driver

#upload
uploadUrl=/attachments
uploadPath=/opt/apache-tomcat-9.0.41/webapps/attachments
mobileAppLogsPath=/mobile_logs
logoPath=/logo
expenseReportPath=/expense_report
purchaseOrderPath=/purchase_order
opportunityNotePath=/opportunityNotePath
commentsRatesFilePath=/ml_data/comments_rates.txt
laboName=LABORATOIRE Test

productPath=/product
specialityPath=/speciality

serveurPath=http://***************:9990

spring.mail.username=<EMAIL>
spring.mail.password=wxR10u1JD%

ml.serverUrl=http://***************:9980/comment
ml.serverToken=sdfghjkloerdtfyguhiopfghjkl;fghjkl
ml.laboName=test
ml.enabled=false