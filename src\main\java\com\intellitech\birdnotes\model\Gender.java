package com.intellitech.birdnotes.model;

import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum Gender {
	
	MEN(BirdnotesConstants.Gender.MEN),
	WOMEN(BirdnotesConstants.Gender.WOMEN);
	
	private String name; //homme ou femme
	
	Gender(String name) {
		this.name = name;
	}
	
	public String getName() {
		return name;
	}
	
	@Override
	public String toString() {
		if(BirdnotesConstants.Gender.MEN.equals(name)) {
			return BirdnotesConstants.Gender.MEN;
		}
		return BirdnotesConstants.Gender.WOMEN;
	}
}
