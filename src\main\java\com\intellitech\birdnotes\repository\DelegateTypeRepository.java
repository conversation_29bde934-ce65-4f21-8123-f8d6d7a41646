package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.DelegateType;
@Repository
public interface DelegateTypeRepository extends JpaRepository<DelegateType, Long> {
		
	@Override
	@Query("SELECT dt from DelegateType dt order by dt.name ASC")
	List<DelegateType> findAll();
	
	@Query("SELECT dt from DelegateType dt where  LOWER(name) = LOWER(?1)")
	DelegateType findByName(String name);

}