package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class LocalityRequestDto implements Serializable {
	
		private static final long serialVersionUID = 1L;
	
		private String name;
		private Long sectorId;
		private String sectorName;
		
		public LocalityRequestDto() {
			super();
		}
	
		public LocalityRequestDto(String name, Long sectorId, String sectorName) {
			super();
			this.name = name;
			this.sectorId = sectorId;
			this.sectorName = sectorName;
		}
	
		public String getName() {
			return name;
		}
	
		public void setName(String name) {
			this.name = name;
		}
	
		public Long getSectorId() {
			return sectorId;
		}
	
		public void setSectorId(Long sectorId) {
			this.sectorId = sectorId;
		}
	
		public String getSectorName() {
			return sectorName;
		}
	
		public void setSectorName(String sectorName) {
			this.sectorName = sectorName;
		}
	}

