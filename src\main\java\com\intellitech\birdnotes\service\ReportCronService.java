package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ReportCron;
import com.intellitech.birdnotes.model.dto.ReportCronDto;
import com.intellitech.birdnotes.model.request.ReportCronRequest;

public interface ReportCronService {

	void add(ReportCronRequest reportCronRequest) throws BirdnotesException;

	List<ReportCronDto> findAll() ;

	void delete(Integer id) throws BirdnotesException;

	ReportCron saveReportCron(ReportCronDto reportCronDto) throws BirdnotesException;
}
