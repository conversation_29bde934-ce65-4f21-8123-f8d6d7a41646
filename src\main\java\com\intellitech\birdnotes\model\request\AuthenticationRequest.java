package com.intellitech.birdnotes.model.request;

public class AuthenticationRequest {

	private String username;
	private String password;
	private String oneSignalUserId;
	private String mobileAppVersion;
	
	
	public AuthenticationRequest() {
		super();
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getOneSignalUserId() {
		return oneSignalUserId;
	}

	public void setOneSignalUserId(String oneSignalUserId) {
		this.oneSignalUserId = oneSignalUserId;
	}


	public String getMobileAppVersion() {
		return mobileAppVersion;
	}

	public void setMobileAppVersion(String mobileAppVersion) {
		this.mobileAppVersion = mobileAppVersion;
	}

}
