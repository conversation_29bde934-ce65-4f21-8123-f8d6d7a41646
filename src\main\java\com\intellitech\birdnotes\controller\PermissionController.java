package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.Module;
import com.intellitech.birdnotes.service.PermissionService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/modules")
public class PermissionController {
	private static final Logger LOG = LoggerFactory.getLogger(PermissionController.class);

	@Autowired
	private PermissionService permissionService;
	@Autowired
	private UserService userService;

	@RequestMapping(value = "/findAllModules", method = RequestMethod.GET)
	public ResponseEntity<List<Module>> getXml() {
		try {
			
				List<Module> modules = permissionService.getXml();
				return new ResponseEntity<>(modules, HttpStatus.OK);
			
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all modules ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllRolesPermissions(@RequestBody List<Module> modules) {
		try {
			
				permissionService.saveAllRolesPermissions(modules);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			
		} catch (Exception e) {
			LOG.error("An exception occurred while adding rolesPermissions", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

}
