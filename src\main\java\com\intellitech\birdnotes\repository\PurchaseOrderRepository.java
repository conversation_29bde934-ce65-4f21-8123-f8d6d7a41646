package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.data.dto.PurchaseOrderDataDto;
import com.intellitech.birdnotes.data.dto.PurchaseOrderItemDto;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrder;

public interface PurchaseOrderRepository extends JpaRepository<PurchaseOrder, Long> {

	PurchaseOrder findById(Long id);

	
	@Modifying
	@Query("UPDATE PurchaseOrder set attachmentName =:nameAttachment, attachmentBase64=:attachmentBase64 WHERE id=:id")
	void update(@Param("nameAttachment") String nameAttachment, @Param("attachmentBase64") String attachmentBase64, @Param("id") Long id);

	@Modifying
	@Query("DELETE PurchaseOrder po WHERE po.id in (:purchaseOrderIds)")
	void deleteById(@Param("purchaseOrderIds") List<Long> purchaseOrderIds);

	@Query("SELECT po FROM PurchaseOrder po WHERE po.identifier = ?1 And po.visit.delegate.id = ?2")
	PurchaseOrder findByIdentifier(Long identifier, Long userId);
	
	@Query("SELECT po FROM PurchaseOrder po WHERE po.identifier in (:identifiers)")
	List<PurchaseOrder> findByIdentifiers(@Param("identifiers") List<Long> identifiers);
	
	@Modifying
	@Query("Delete FROM PurchaseOrder po WHERE po.identifier = ?1 And po.visit.id IN (Select id from Visit v where v.delegate.id = ?2)")
	void deleteByIdentifier(Long identifier, Long userId);
	
	@Modifying
	@Query("DELETE PurchaseOrder po WHERE po.visit.id = ?1")
	void deleteByVisitId(Long visitId);
	
	@Query("SELECT po from PurchaseOrder po WHERE po.visit.delegate.id  = :userId ")
	List<PurchaseOrder> findByUser(@Param("userId") Long userId);


	@Query("SELECT new com.intellitech.birdnotes.data.dto.PurchaseOrderDataDto(po.id, "+
			" CONCAT (po.wholesaler.firstName, ' ',po.wholesaler.lastName),  "+
			" po.wholesaler.address, po.wholesaler.email," + 
			" CONCAT (po.visit.prospect.firstName, ' ',po.visit.prospect.lastName),  "+
			" CONCAT (po.visit.prospect.address,', ', po.visit.prospect.locality.name, ', ',po.visit.prospect.sector.name),"+
			" CONCAT(po.visit.delegate.firstName,' ',po.visit.delegate.lastName), po.visit.visitDate) "+ 
			" from PurchaseOrder po WHERE po.id  = :purchaseOrderId ")
			PurchaseOrderDataDto findPurchaseOrderData(@Param("purchaseOrderId") Long purchaseOrderId);
	
	@Modifying
	@Query("UPDATE PurchaseOrder SET status =:status WHERE id=:purchaseOrderId")
	void updateStatus( @Param("purchaseOrderId") Long purchaseOrderId, @Param("status") UserValidationStatus status);


	@Modifying
	@Query("UPDATE PurchaseOrder p  set p.wholesaler =:oldProspect WHERE p.wholesaler =:newProspect ")
	void updateProspect(@Param("oldProspect") Prospect oldProspect,@Param("newProspect")  Prospect newProspect);
	
	
	/*@Query("SELECT new com.intellitech.birdnotes.data.dto.PurchaseOrderDataDto(po.id, "+
			" po.wholesaler.name, CONCAT (po.visit.prospect.firstName, ' ',po.visit.prospect.lastName),  "+
			" CONCAT(po.visit.user.firstName,' ',po.visit.user.lastName), po.visit.visitDate "+ 
			" po.visit.prospect.speciality.name, po.visit.prospect.activity, po.visit.prospect.sector.name, po.visit.prospect.locality.name, po.purchaseOrderTemplate.id) "+
			" from PurchaseOrder po WHERE (date(po.visit.visitDate) between date(?1) and date(?2)) and po.visit.user.id = ?3")
			List<PurchaseOrderDataDto> findPurchaseOrderByDateAndUser(Date firstDate, Date lastDate, Long userId);*/

	/*@Override
	@Query("Select n from NoteFrais n ")
	List<NoteFrais> findAll();
	
	@Query("SELECT n from NoteFrais n WHERE n.typeNoteFrais.id=:id ")
	List<NoteFrais> findByTypeNoteFraisId(@Param("id") Long id);

	@Modifying
	@Query("DELETE NoteFrais n WHERE n.id = ?1")
	void deleteById(Long idNoteFrais);

	@Query("SELECT t.id from NoteFrais t WHERE t.id in (:noteFrais) ")
	List<Long> findWhereIdIn(@Param("noteFrais") List<Long> noteFrais);
	
	@Query("SELECT t from NoteFrais t WHERE t.id in (:noteFrais) and t.user.id  = :userId ")
	List<NoteFrais> findWhereIdInByUser(@Param("noteFrais") List<Long> noteFrais,@Param("userId") Long userId);
*/
}
