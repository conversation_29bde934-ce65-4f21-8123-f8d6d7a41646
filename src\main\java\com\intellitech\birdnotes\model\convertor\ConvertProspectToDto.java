package com.intellitech.birdnotes.model.convertor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ContactType;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Preference;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.util.BirdnotesUtils;

@Component("convertProspectToDto")
public class ConvertProspectToDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private static final Logger LOG = LoggerFactory.getLogger(ConvertProspectToDto.class);

	private static final String RED = "#ff0000";

	private static final String BLACK = "#000000";

	private ConfigurationRepository configureRepository;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${specialityPath}")
	private String specialityPath;

	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}

	public ProspectDto convert(Prospect prospect) throws BirdnotesException {

		if (prospect == null) {
			LOG.error("prospect is null");
			throw new BirdnotesException("prospect is null");
		}
		ProspectDto prospectDto;
		prospectDto = convertToProspectDto(prospect);
		fillSpecialityDto(prospect, prospectDto);
		fillProspectTypeDto(prospect, prospectDto);
		fillEstablishmentDto(prospect, prospectDto);
		fillSectorDto(prospect, prospectDto);
		fillLocalityDto(prospect, prospectDto);
		fillPotentialDto(prospect, prospectDto);
		return prospectDto;
	}

	private void fillPotentialDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		PotentialDto potentialDto = new PotentialDto();
		Potential potential = prospect.getPotential();
		if (potential != null) {
			potentialDto.setName(potential.getName());
			potentialDto.setId(potential.getId());
		} else {
			LOG.error("potential is null");
			throw new BirdnotesException("potential is null");
		}
		prospectDto.setPotentialDto(potentialDto);
	}

	private void fillLocalityDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		LocalityDto localityDto = new LocalityDto();
		Locality locality = prospect.getLocality();
		if (locality != null) {
			localityDto.setName(locality.getName());
			localityDto.setId(locality.getId());
			localityDto.setSectorName(prospect.getSector().getName());
			localityDto.setSectorId(prospect.getSector().getId());

		} else {
			LOG.error("locality is null");
			throw new BirdnotesException("locality is null");
		}
		prospectDto.setLocalityDto(localityDto);

	}

	private void fillSpecialityDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		Speciality speciality = prospect.getSpeciality();
		SpecialityDto specialityDto = new SpecialityDto();

		if (speciality != null) {
			specialityDto.setId(speciality.getId());
			specialityDto.setName(speciality.getName());
			if (speciality.getIcon() != null) {

				specialityDto.setIcon(BirdnotesUtils.configuration.getBackendUrl() + uploadUrl + specialityPath + "/"
						+ speciality.getId() + "/" + speciality.getIcon());
			}

		} /*else {
			LOG.error("speciality is null");
			throw new BirdnotesException("speciality is null");
		}*/
		prospectDto.setSpecialityDto(specialityDto);
	}

	private void fillProspectTypeDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		ProspectType prospectType = prospect.getProspectType();
		ProspectTypeDto prospectTypeDto = new ProspectTypeDto();

		if (prospectType != null) {
			prospectTypeDto.setId(prospectType.getId());
			prospectTypeDto.setName(prospectType.getName());

		} else {
			LOG.error("prospect type is null");
			throw new BirdnotesException("prospect type is null");
		}
		prospectDto.setProspectTypeDto(prospectTypeDto);
	}

	private void fillEstablishmentDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		Establishment establishment = prospect.getEstablishment();
		EstablishmentDto establishmentDto = new EstablishmentDto();

		if (establishment != null && establishment.getId() != null) {
			establishmentDto.setId(establishment.getId());
			establishmentDto.setName(establishment.getName());

		}
		prospectDto.setEstablishmentDto(establishmentDto);
	}

	private void fillSectorDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		SectorDto sectorDto = new SectorDto();
		Sector sector = prospect.getSector();
		if (sector != null) {
			sectorDto.setName(sector.getName());
			sectorDto.setId(sector.getId());
		} else {
			LOG.error("sector is null");
			throw new BirdnotesException("sector is null");
		}
		prospectDto.setSectorDto(sectorDto);
	}

	private ProspectDto convertToProspectDto(Prospect prospect) throws BirdnotesException {
		ProspectDto prospectDto = new ProspectDto();
		prospectDto.setId(prospect.getId());
		prospectDto.setIdentifier(prospect.getIdentifier());
		prospectDto.setFirstName(prospect.getFirstName());
		prospectDto.setLastName(prospect.getLastName());
		prospectDto.setFullName(prospectDto.getFirstName() + ' ' + prospectDto.getLastName());
		prospectDto.setStatus(prospect.getStatus());
		prospectDto.setIdprospect(prospect.getIdprospect());
		prospectDto.setActivity(prospect.getActivity());
		prospectDto.setAddress(prospect.getAddress());
		prospectDto.setGsm(prospect.getGsm());
		prospectDto.setPhone(prospect.getPhone());
		prospectDto.setEmail(prospect.getEmail());
		prospectDto.setNote(prospect.getNote());
		prospectDto.setSecretary(prospect.getSecretary());
		prospectDto.setGrade(prospect.getGrade());
		prospectDto.setActive(prospect.getActive());
		prospectDto.setLatitude(prospect.getLatitude());
		prospectDto.setLongitude(prospect.getLongitude());
		prospectDto.setMapAddress(prospect.getMapAddress());
		prospectDto.setSocialMedia(prospect.getSocialMedia());
		prospectDto.setTaxIdNumber(prospect.getTaxIdNumber());
		prospectDto.setInterestIds(prospect.getInterests().stream()
                .map(Interest::getId) 
                .collect(Collectors.toList())); 
		prospectDto.setContactTypeIds(prospect.getContactTypes().stream()
                .map(ContactType::getId) 
                .collect(Collectors.toList())); 

		prospectDto.setPreferenceIds(
			    prospect.getPreferences().stream()
			        .map(Preference::getId)
			        .collect(Collectors.toList())
			);


		if (prospect.getAppUser() != null) {
			prospectDto.setAppUserId(prospect.getAppUser().getId());
			prospectDto.setPassword(prospect.getAppUser().getPassword());
			prospectDto.setUserName(prospect.getAppUser().getUsername());
			prospectDto.setPhone(prospect.getAppUser().getPhone());
			prospectDto.setEmail(prospect.getAppUser().getEmail());
			prospectDto.setRoleIds(
					prospect.getAppUser().getRoles().stream().map(Role::getId).collect(Collectors.toList()));
			prospectDto.setSupervisorIds(
					prospect.getAppUser().getSuperiors().stream().map(User::getId).collect(Collectors.toList()));
			prospectDto.setRangeIds(
					prospect.getAppUser().getRanges().stream().map(Range::getId).collect(Collectors.toList()));
			prospectDto.setIsUser(true);
		}

		Set<Range> gammes = prospect.getRanges();

		if (gammes == null) {
			LOG.error("gamme of " + prospect.getFirstName() + ' ' + prospect.getLastName() + " is null");
		} else {

			prospectDto.setGammes(gammes);
		}

		if (prospect.getCreationDate() == null) {
			throw new BirdnotesException("creation date is null");
		}
		if (prospect.getCreationDate() != null && prospect.getIdprospect() != null) {
			// DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm");
			prospectDto.setUpdateDate(prospect.getCreationDate());
		}
		if (prospect.getIdprospect() == null) {
			// DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm");
			prospectDto.setUpdateDate(prospect.getUpdateDate());
		}
		prospectDto.setCreationDate(prospect.getCreationDate());

		if (prospect.getUser() == null) {
			throw new BirdnotesException("delegate is null");
		}
		if (prospect.getUser().getDelegate() != null) {
			prospectDto.setCreatorUser(prospect.getUser().getDelegate().getFirstName() + " "
					+ prospect.getUser().getDelegate().getLastName());
		} else {
			prospectDto.setCreatorUser(prospect.getUser().getUsername());
		}
		ArrayList<String> delegateNames = new ArrayList<>();
		ArrayList<Long> delegateIds = new ArrayList<>();
		if (prospect.getProspectsAffectation() != null) {
			for (ProspectsAffectation prospectsAffectation : prospect.getProspectsAffectation()) {
				delegateNames.add(prospectsAffectation.getDelegate().getFirstName() + ' '
						+ prospectsAffectation.getDelegate().getLastName());
				delegateIds.add(prospectsAffectation.getId());
			}
			prospectDto.setDelegateNames(delegateNames);
			prospectDto.setDelegateIds(delegateIds);
		}
		return prospectDto;
	}

	public ProspectDto convertPatient(Prospect patient) throws BirdnotesException {
		ProspectDto prospectDto = new ProspectDto();
		prospectDto.setId(patient.getId());
		prospectDto.setFirstName(patient.getFirstName());
		prospectDto.setLastName(patient.getLastName());
		prospectDto.setAddress(patient.getAddress());
		prospectDto.setGsm(patient.getGsm());
		prospectDto.setPhone(patient.getPhone());
		prospectDto.setEmail(patient.getEmail());
		prospectDto.setNote(patient.getNote());
		if (patient.getDoctor() != null) {
			prospectDto.setDoctorId(patient.getDoctor().getId());
			LabelValueDto prospectName = new LabelValueDto(patient.getDoctor());
			prospectDto.setProspectName(prospectName.getLabel());
		}
		fillSectorDto(patient, prospectDto);
		fillLocalityDto(patient, prospectDto);
		fillPotentialDto(patient, prospectDto);
		return prospectDto;
	}

}
