package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Holiday;
import com.intellitech.birdnotes.model.dto.HolidayDto;
import com.intellitech.birdnotes.service.HolidayService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/holidays")
public class HolidayController {
	private static final Logger LOG = LoggerFactory.getLogger(AutomationRuleController.class);

	@Autowired
	private HolidayService holidayService;
	
	@Autowired
	UserService userService;

	@RequestMapping(value = "/getAllHolidays", method = RequestMethod.GET)
	public ResponseEntity<List<HolidayDto>> getAllHolidays() {
		try {
			List<HolidayDto> result = holidayService.getAllHolidays();
			return new ResponseEntity<>(result, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all holidays ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveHoliday", method = RequestMethod.POST)
	public ResponseEntity<Long> saveHoliday(@RequestBody HolidayDto holidayDto) {
		try {

			Holiday holidaySaved = holidayService.saveHoliday(holidayDto);
			if (holidaySaved != null) {
				return new ResponseEntity<>(holidaySaved.getId(), HttpStatus.OK);
			}

			return new ResponseEntity<>(null, HttpStatus.OK);

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while saving holiday", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while saving holiday ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deleteHoliday/{holidayId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteHoliday(@PathVariable("holidayId") Long holidayId) {
		try {

			holidayService.deleteHoliday(holidayId);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting holiday", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in delete holiday", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
