package com.intellitech.birdnotes.model.dto;

import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Product; // Importer la classe Product

public class LabelValueDto {

    private String label;
    private double value;

    // Constructeur pour Prospect
    public LabelValueDto(Prospect prospect) {
        this.value = prospect.getId();
        this.label = prospect.getFirstName() + " " + prospect.getLastName() + " - " 
                     + prospect.getSector().getName() + " - " + prospect.getSpeciality().getName();
    }

    // Constructeur général
    public LabelValueDto(String label, double value) {
        this.label = label;
        this.value = value;
    }

    // Constructeur pour Product
    public LabelValueDto(Product product) {
        this.value = product.getId();  // Utilisation de l'ID du produit
        this.label = product.getName();  // Utilisation du nom du produit
    }

    // Autres constructeurs existants
    public LabelValueDto(long id, String firstName, String lastName) {
        this.label = firstName + " " + lastName;
        this.value = id;
    }

    public LabelValueDto(String label, long value) {
        this.label = label;
        this.value = value;
    }

    public LabelValueDto(int label, long value) {
        this.label = String.valueOf(label);
        this.value = value;
    }

    // Getters and Setters
    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public double getValue() {
        return value;
    }

    public void setValue(double value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "LabelValueDto { label='" + label + "', value=" + value + " }";
    }
}
