package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.intellitech.birdnotes.data.dto.CheckedValueDto;
import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.OrdersPredictionResponse;
import com.intellitech.birdnotes.model.dto.PlanifiedProspectDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PlanningValidationDto;
import com.intellitech.birdnotes.model.dto.PostponePlanningRequest;
import com.intellitech.birdnotes.model.dto.PlannedActivityRequest;

public interface PlanningService {

	//public List<PlanifiedProspectDto> findPlanificationByUserAndDate(Long userId, Date date) throws BirdnotesException;

	public List<PlanningDto> findPlanningByUser(Long userId) throws BirdnotesException;

	public PlanningValidationDto findPlanningValidationByUserAndDate(Long userId, Date date) throws BirdnotesException;

	boolean delete(Long id);

	public List<DelegateDto> findDelegatesWaitingForValidationByWeek(Date date) throws BirdnotesException;

	public List<PlanningValidationDto> findPlanningValidationByUser(List<Long> planningWaitingValidation, Long User)
			throws BirdnotesException;

	void acceptValidationStep(PlanningValidationDto planningValidationDto) throws BirdnotesException;

	void refuseValidationStep(PlanningValidationDto planningValidationDto) throws BirdnotesException;

	void reviseValidationStep(PlanningValidationDto planningValidationDto) throws BirdnotesException;

	public List<PlanifiedProspectDto> findPlanningById(Long id) throws BirdnotesException;

	public List<PlanningValidationDto> findAllPlanningValidationByUser(Long userId) throws BirdnotesException;

	public void save(PlanningValidationDto planningValidationDto);

	public void deletePlanning(Long id);

	public List<PlanningDto> findNewPlanning(Delegate delegate, Date currentWeekDate);

	public void savePlanningValidation(PlanningValidationDto planningValidationDto);

	public boolean deletePlanningByDateAndUser(Long UserId, Date date) throws BirdnotesException;

	public void processProspectsOrdersPrediction(Date date, Long prospectId, Long productId) throws BirdnotesException;

	public void postponePlanning(PostponePlanningRequest postponePlanningRequest);

	public OrdersPredictionResponse getOrdersPredictions(Date date, Long prospectId, Long productId, int first, int row);
	void deleteOrdersPredictions(Date date, Long prospectId, Long productId);

	List<Long> findPlanningOfWeeksToShow(Long userId, Date firstDay, List<CheckedValueDto> weekToShow)
			throws BirdnotesException;

	public String getCommentClassification(String comment);

	Map<String, List<PlanningDto>> importPlanning(String filePath, Long userId) throws BirdnotesException;

	List<PlanningValidationDto> findNewPlanningValidation(Delegate delegate, Date currentWeekDate);

	public void updatePlannedActivity(PlannedActivityRequest statusRequest);
	
	void sendOrdersPredictionDoneEmail(Integer month);


	List<PlanifiedProspectDto> findPlanificationByUserAndDate(Long userId, Date firstDay, Date lastDay)
			throws BirdnotesException;

}
