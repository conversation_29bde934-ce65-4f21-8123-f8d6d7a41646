package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class ProspectsToMoveRequestDto {
private List<ProspectDto> prospects;
private Long localityId;
public ProspectsToMoveRequestDto() {
	super();
}
public List<ProspectDto> getProspects() {
	return prospects;
}
public void setProspects(List<ProspectDto> prospects) {
	this.prospects = prospects;
}
public Long getLocalityId() {
	return localityId;
}
public void setLocalityId(Long localityId) {
	this.localityId = localityId;
}

}
