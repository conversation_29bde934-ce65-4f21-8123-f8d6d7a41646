package com.intellitech.birdnotes.data.dto;

import java.util.Set;
import java.util.SortedSet;

public  class UserAffectationDetailsDto{

	
	public SortedSet<String> sectors;
    public SortedSet<String> localities;
    public SortedSet<String> specialities;
    public SortedSet<String> activities;
    public SortedSet<String> establishments;
    public SortedSet<String> prospectTypes;
	public SortedSet<String> getSectors() {
		return sectors;
	}
	public void setSectors(SortedSet<String> sectors) {
		this.sectors = sectors;
	}
	public SortedSet<String> getLocalities() {
		return localities;
	}
	public void setLocalities(SortedSet<String> localities) {
		this.localities = localities;
	}
	public SortedSet<String> getSpecialities() {
		return specialities;
	}
	public void setSpecialities(SortedSet<String> specialities) {
		this.specialities = specialities;
	}
	public SortedSet<String> getActivities() {
		return activities;
	}
	public void setActivities(SortedSet<String> activities) {
		this.activities = activities;
	}
	public SortedSet<String> getEstablishments() {
		return establishments;
	}
	public void setEstablishments(SortedSet<String> establishments) {
		this.establishments = establishments;
	}
	public SortedSet<String> getProspectTypes() {
		return prospectTypes;
	}
	public void setProspectTypes(SortedSet<String> prospectTypes) {
		this.prospectTypes = prospectTypes;
	}
    

	public UserAffectationDetailsDto(){
		 
	}
}