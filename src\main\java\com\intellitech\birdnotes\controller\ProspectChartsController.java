package com.intellitech.birdnotes.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.ProspectDistributionList;
import com.intellitech.birdnotes.model.dto.ProspectDistributionRequest;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/prospectCharts")
public class ProspectChartsController {
	@Autowired
	private ProspectService prospectService;
	@Autowired
	UserService userService;
	private static final Logger LOG = LoggerFactory.getLogger(ProspectChartsController.class);

	@RequestMapping(value = "getProspectsDistribution", method = RequestMethod.POST)
	public ResponseEntity<ProspectDistributionList> getProspectsDistribution(
			@RequestBody ProspectDistributionRequest prospectDistributionRequest) {
		try {
			if (userService.checkHasPermission("PROSPECT_MAPPING_VIEW")) {

				ProspectDistributionList back = prospectService.getProspectsDistribution(prospectDistributionRequest);
				// List<ProspectDto> prospectList =
				// prospectService.findAll(prospectDistributionRequest.getProspectListRequest());
				// back.setProspectList(prospectList);

				return new ResponseEntity<>(back, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getProspectDistribution", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
