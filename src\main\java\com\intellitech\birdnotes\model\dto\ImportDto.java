package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class ImportDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer id;
	private AvatarDto avatar;
	private String pathFile;

	public ImportDto() {
		super();

	}
	
	public ImportDto(Integer id,AvatarDto avatar, String pathFile) {
		super();
		this.id = id;
		this.avatar = avatar;
		this.pathFile = pathFile;
	}


	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}



	public AvatarDto getAvatar() {
		return avatar;
	}

	public void setAvatar(AvatarDto avatar) {
		this.avatar = avatar;
	}

	public String getPathFile() {
		return pathFile;
	}

	public void setPathFile(String pathFile) {
		this.pathFile = pathFile;
	}

}
