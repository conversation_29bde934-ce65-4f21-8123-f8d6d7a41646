package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.intellitech.birdnotes.enumeration.HolidayType;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.HOLIDAY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Holiday implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Integer day;
	private Integer month;
	private HolidayType holidayType;
	private Date date;

	public Holiday() {
		super();
	}

	@Id
	@SequenceGenerator(name = Sequences.HOLIDAY_SEQUENCE, sequenceName = Sequences.HOLIDAY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.HOLIDAY_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = Columns.DAY)
	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	@Column(name = Columns.MONTH)
	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.HOLIDAY_TYPE)
	public HolidayType getHolidayType() {
		return holidayType;
	}

	public void setHolidayType(HolidayType holidayType) {
		this.holidayType = holidayType;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = BirdnotesConstants.Columns.HOLIDAY_DATE)
	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

}
