package com.intellitech.birdnotes.model.convertor;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;

@Component("configurationToDtoConvertor")
public class ConfigurationToDtoConvertor {
	@Value("${uploadUrl}")
	private String uploadUrl;
	@Value("${logoPath}")
	private String logoPath;

	public ConfigurationDto convert(Configuration configuration) {

		if (configuration == null) {
			return null;
		}
		ConfigurationDto configurationDto = new ConfigurationDto();
		if (configuration.getId() != null) {
			configurationDto.setId(configuration.getId());
		}

		if (configuration.getName() != null && !configuration.getName().isEmpty()) {
			configurationDto.setName(configuration.getName());
		}

		if (configuration.getPackageName() != null && !configuration.getPackageName().isEmpty()) {
			configurationDto.setPackageName(configuration.getPackageName());
		}

		if (configuration.getLogo() != null && !configuration.getLogo().isEmpty()) {

			configurationDto.setLogo(configuration.getLogo());

			configurationDto.setPathFile(uploadUrl + logoPath);
		}

		if (configuration.getSyncro() != null) {
			configurationDto.setSyncro(configuration.getSyncro());
		}

		if (configuration.getAutoSync() != null) {
			configurationDto.setAutoSync(configuration.getAutoSync());
		}

		if (configuration.getAutoExpense() != null) {
			configurationDto.setAutoExpense(configuration.getAutoExpense());
		}

		if (configuration.getOpenReportPeriod() != null) {
			configurationDto.setOpenReportPeriod(configuration.getOpenReportPeriod());
		}
		if (configuration.getOpenExpensePeriod() != null) {
			configurationDto.setOpenExpensePeriod(configuration.getOpenExpensePeriod());
		}

		if (configuration.getCycle() != null) {
			configurationDto.setCycle(configuration.getCycle());
		}
		if (configuration.getSyncCycle() != null) {
			configurationDto.setSyncCycle(configuration.getSyncCycle());
		}
		if (configuration.getAcceptedPointingDistance() != null) {
			configurationDto.setAcceptedPointingDistance(configuration.getAcceptedPointingDistance());
		}
		if (configuration.getOrderValidation() != null) {
			configurationDto.setOrderValidation(configuration.getOrderValidation().toString());
		}
		if (configuration.getMultiWholesaler() != null) {
			configurationDto.setMultiWholesaler(configuration.getMultiWholesaler());
		}

		if (configuration.getSendSyncReminder() != null) {
			configurationDto.setSendSyncReminder(configuration.getSendSyncReminder());
		}

		if (configuration.getSyncReminderPeriod() != null) {
			configurationDto.setSyncReminderPeriod(configuration.getSyncReminderPeriod());
		}

		if (configuration.getHolidays() != null) {
			configurationDto.setHolidays(configuration.getHolidays());
		}

		if (configuration.getCommentsDictionary() != null) {
			configurationDto.setCommentsDictionary(configuration.getCommentsDictionary());
		}

		if (configuration.getBiPanels() != null) {
			configurationDto.setBiPanels(configuration.getBiPanels());
		}
		if (configuration.getErpParams() != null) {
			configurationDto.setErpParams(configuration.getErpParams());
		}
		if (configuration.getLockAfterSync() != null) {
			configurationDto.setLockAfterSync(configuration.getLockAfterSync());
		}

		if (configuration.getServerPath() != null && !configuration.getServerPath().isEmpty()) {
			configurationDto.setServerPath(configuration.getServerPath());
		}

		if (configuration.getMlServerUrl() != null && !configuration.getMlServerUrl().isEmpty()) {
			configurationDto.setMlServerUrl(configuration.getMlServerUrl());
		}
		
		if (configuration.getBackendUrl() != null && !configuration.getBackendUrl().isEmpty()) {
			configurationDto.setBackendUrl(configuration.getBackendUrl());
		}
		if (configuration.getFieldParameters() != null && !configuration.getFieldParameters().isEmpty()) {
			configurationDto.setFieldParameters(configuration.getFieldParameters());
		}
		
		if (configuration.getDefaultLatitude() != null ) {
			configurationDto.setDefaultLatitude(configuration.getDefaultLatitude());
		}
		if (configuration.getDefaultLongitude() != null ) {
			configurationDto.setDefaultLongitude(configuration.getDefaultLongitude());
		}

		if (configuration.getBiServerUrl() != null && !configuration.getBiServerUrl().isEmpty()) {
			configurationDto.setBiServerUrl(configuration.getBiServerUrl());
		}

		if (configuration.getCommentsRatingNotification() != null
				&& !configuration.getCommentsRatingNotification().isEmpty()) {
			configurationDto.setCommentsRatingNotification(configuration.getCommentsRatingNotification());
		}

		if (configuration.getOrderPredictionCron() != null && !configuration.getOrderPredictionCron().isEmpty()) {
			configurationDto.setOrderPredictionCron(configuration.getOrderPredictionCron());
		}
		
		if (configuration.getDisplacementCron() != null && !configuration.getDisplacementCron().isEmpty()) {
			configurationDto.setDisplacementCron(configuration.getDisplacementCron());
		}
		
		if (configuration.getErpSyncCron() != null && !configuration.getErpSyncCron().isEmpty()) {
			configurationDto.setErpSyncCron(configuration.getErpSyncCron());
		}
		
		if (configuration.getReportValidationCron() != null && !configuration.getReportValidationCron().isEmpty()) {
			configurationDto.setReportValidationCron(configuration.getReportValidationCron());
		}
		
		if (configuration.getReportingReminderCron() != null && !configuration.getReportingReminderCron().isEmpty()) {
			configurationDto.setReportingReminderCron(configuration.getReportingReminderCron());
		}
		
		if (configuration.getExpenseDistanceCron() != null && !configuration.getExpenseDistanceCron().isEmpty()) {
			configurationDto.setExpenseDistanceCron(configuration.getExpenseDistanceCron());
		}
		
		if (configuration.getErpType() != null && !configuration.getErpType().isEmpty()) {
			configurationDto.setErpType(configuration.getErpType());
		}
		
		if (configuration.getErpUrl() != null && !configuration.getErpUrl().isEmpty()) {
			configurationDto.setErpUrl(configuration.getErpUrl());
		}
		

		if (configuration.getReportingStartingTime() != null ) {
			configurationDto.setReportingStartingTime(configuration.getReportingStartingTime());
		}
		if (configuration.getReportingEndingTime() != null ) {
			configurationDto.setReportingEndingTime(configuration.getReportingEndingTime());
		}
		if (configuration.getDelayedReportingTolerence() != null) {
			configurationDto.setDelayedReportingTolerence(configuration.getDelayedReportingTolerence());
		}

		if (configuration.getNoGeolocationTolerence() != null) {
			configurationDto.setNoGeolocationTolerence(configuration.getNoGeolocationTolerence());
		}
		configurationDto.setHost(configuration.getHost());
		configurationDto.setPort(configuration.getPort());
		configurationDto.setEmail(configuration.getEmail());
		configurationDto.setEmailPassword(configuration.getEmailPassword());
		configurationDto.setLanguage(configuration.getLanguage());

		return configurationDto;
	}
}
