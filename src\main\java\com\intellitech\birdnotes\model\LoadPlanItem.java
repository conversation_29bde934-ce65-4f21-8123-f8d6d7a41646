package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.LOAD_PLAN_ITEM, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class LoadPlanItem implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private Long id;
	private Speciality speciality;
	private Integer rank;
    private Product product;
    private LoadPlan loadPlan;
	
    

	@Id
	@SequenceGenerator(name = Sequences.LOAD_PLAN_ITEM_SEQUENCE, sequenceName = Sequences.LOAD_PLAN_ITEM_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.LOAD_PLAN_ITEM_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.SPECIALITY_ID)
	public Speciality getSpeciality() {
		   return speciality;
	}
	
    public void setSpeciality(Speciality speciality) {
			this.speciality = speciality;
		}
    
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID)
    public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}
    
	@Column(name = Columns.RANK)
	 public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}
	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.LOAD_PLAN_ID, unique = false)
	public LoadPlan getLoadPlan() {
		return loadPlan;
	}

	public void setLoadPlan(LoadPlan loadPlan) {
		this.loadPlan = loadPlan;
	}
	
}
