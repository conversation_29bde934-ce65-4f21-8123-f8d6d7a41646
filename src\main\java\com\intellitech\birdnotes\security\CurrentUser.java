package com.intellitech.birdnotes.security;


import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.context.annotation.Scope;
import org.springframework.context.annotation.ScopedProxyMode;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.model.Role;


@Component("currentUser")

@Scope(value = "session", proxyMode = ScopedProxyMode.TARGET_CLASS)

public class CurrentUser {

	private BirdnotesUser birdnotesUser;

	public BirdnotesUser getBirdnotesUser() {

		if (SecurityContextHolder.getContext().getAuthentication().getPrincipal() instanceof BirdnotesUser) {
			birdnotesUser = (BirdnotesUser) SecurityContextHolder.getContext().getAuthentication().getPrincipal();
		} else {
			birdnotesUser = null;
		}
		return birdnotesUser;
	}
	
	public List<String> getUserRoles(){
		return getBirdnotesUser().getUserDto().getRoles().stream().map(Role::getName).collect(Collectors.toList());
	}

	public void setBirdnotesUser(BirdnotesUser birdnotesUser) {
		this.birdnotesUser = birdnotesUser;
	}

}
