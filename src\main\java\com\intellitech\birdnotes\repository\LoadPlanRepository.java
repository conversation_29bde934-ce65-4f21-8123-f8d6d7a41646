package com.intellitech.birdnotes.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.LoadPlan;

@Repository
public interface LoadPlanRepository extends JpaRepository<LoadPlan, Long> {
	
	@Modifying
	@Query("UPDATE LoadPlan SET active = false where id not in (?1) ")
	public void setAllPlanInactive(Long loadPlanId);

}
