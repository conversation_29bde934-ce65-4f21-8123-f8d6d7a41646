package com.intellitech.birdnotes.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.ReportMessageRequestDto;
import com.intellitech.birdnotes.data.dto.OrderPredictionRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.ActivityCalanderRequestDto;
import com.intellitech.birdnotes.model.dto.ActivityDataDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.MessageDto;
import com.intellitech.birdnotes.model.dto.PlanifiedProspectDto;
import com.intellitech.birdnotes.model.dto.PresentationTimeTrackingDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.model.dto.VisitHistorySummaryDto;
import com.intellitech.birdnotes.model.dto.VisitRequestDto;
import com.intellitech.birdnotes.service.ActivityService;
import com.intellitech.birdnotes.service.PlanningService;
import com.intellitech.birdnotes.service.PresentationTimeTrackingService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.VisitService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/visits")
public class VisitController {

	@Autowired
	private VisitService visitService;

	@Autowired
	private PresentationTimeTrackingService presentationTimeTrackingService;
	
	@Autowired
	private PurchaseOrderService purchaseOrderService;

	private static final Logger LOG = LoggerFactory.getLogger(LocalityController.class);
	@Autowired
	UserService userService;

	@Autowired
	PlanningService planningService;

	@Value("${generatedDocumentPath}")
	private String generatedDocumentPath;
	
	@Value("${uploadPath}")
	private String uploadPath;
	
	@RequestMapping(value = "query", method = RequestMethod.POST)
	public ResponseEntity<VisitHistorySummaryDto> visitHistory(@RequestBody VisitRequestDto visitRequestDto) {
		try {
			if (userService.checkHasPermission("ORDER_VIEW") || userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				return new ResponseEntity<>(visitService.getVisitHistory(visitRequestDto), HttpStatus.OK);
			}else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred to find visit", e);
			return new ResponseEntity<>(VisitHistorySummaryDto.SINGLE_INSTANCE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	
	@RequestMapping(value = "getVisitReport", method = RequestMethod.POST)
	public ResponseEntity<List<VisitDto>> getVisitReport(@RequestBody ReportMessageRequestDto reportMessageRequestDto) {
		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				List<VisitDto> visitReportDto  = visitService.getVisitReport(reportMessageRequestDto);
				List<PlanifiedProspectDto> planifiedProspectDto = planningService.findPlanificationByUserAndDate(reportMessageRequestDto.getUserId(), reportMessageRequestDto.getStartDate(), reportMessageRequestDto.getLastDate());
				return new ResponseEntity<>(visitReportDto, HttpStatus.OK);
			}else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred to find visit", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "coverage", method = RequestMethod.POST)
	public ResponseEntity coverage(@RequestBody VisitRequestDto coverageRequestDto) {
		try {
			if (userService.checkHasPermission("COUVERTURE_VIEW")) {
				return new ResponseEntity<>(visitService.getCoverage(coverageRequestDto), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		}catch (BirdnotesException e) {
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}catch (Exception e) {

			LOG.error("An exception occurred ", e);
			return new ResponseEntity<>(VisitHistorySummaryDto.SINGLE_INSTANCE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/*@RequestMapping(value = "activity", method = RequestMethod.POST)

	public ResponseEntity<ActivityDataDto> getStatisticActivity(
			@RequestBody ActivityCalanderRequestDto activityCalanderRequestDto) {
		try {
			if (userService.checkHasPermission("RELATED_ACTIVITIES_VIEW")) {
				ActivityDataDto response = activityService.getStatisticActivity(activityCalanderRequestDto);

				return new ResponseEntity<>(response, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred ", e);
			return new ResponseEntity<>(ActivityDataDto.SINGLE_INSTANCE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}*/



	@RequestMapping(value = "sendPurchaseOrderMail", method = RequestMethod.POST)
	public ResponseEntity<String> sendPurchaseOrderMail(
			@RequestBody PurchaseOrderDto sendPurchaseOrderMail) {

		try {
			if (userService.checkHasPermission("SEND_EMAIL")) {
				purchaseOrderService.sendPurchaseOrderMail(sendPurchaseOrderMail);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
	
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

	} catch (Exception e) {
		LOG.error("An exception occurred while validating purchase order status", e);
		return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
	
		}
	}
	
	@RequestMapping(value = "validatePurchaseOrder", method = RequestMethod.POST)
	public ResponseEntity<String> validatePurchaseOrder(@RequestBody PurchaseOrderDto  purchaseOrderDto) {

		try {
			if (userService.checkHasPermission("ORDER_VALIDATION")) {
			visitService.validatePurchaseOrder(purchaseOrderDto.getId());
			purchaseOrderService.sendPurchaseOrderMail(purchaseOrderDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while validating purchase order status", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/generatePurchaseOrder", method = RequestMethod.POST)
	public ResponseEntity<Long> generatePurchaseOrder(@RequestBody PurchaseOrderDto purchaseOrderDto) {
		try {
			if (userService.checkHasPermission("ORDER_GENERATE")) {
				String poPath = uploadPath + generatedDocumentPath + File.separator + purchaseOrderDto.getId()+ "/" + "generatedPurchaseOrder.pdf";
				purchaseOrderService.generatePurchaseOrder(poPath, purchaseOrderDto.getId(), "Bon de commande");
				//purchaseOrderService.sendPurchaseOrderMail(purchaseOrderDto);
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		}
		 catch (Exception e) {
			LOG.error("An exception occurred when generating po and sending email", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	
	@RequestMapping(value = "getTimeTracking/{visitProductId}/{productId}", method = RequestMethod.GET)
	public ResponseEntity<List<PresentationTimeTrackingDto>> getTimeTracking(@PathVariable("visitProductId") Long visitProductId, @PathVariable("productId") Long productId) {

		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				List<PresentationTimeTrackingDto> presentationTimeTrackingDto = presentationTimeTrackingService.getTimeTracking(visitProductId, productId);
				return new ResponseEntity<>(presentationTimeTrackingDto, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getTimeTracking", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	
	@RequestMapping(value = "/refusePurchaseOrder/{purchaseOrderId}", method = RequestMethod.PUT)
	public ResponseEntity<String> refusePurchaseOrder(@PathVariable("purchaseOrderId") Long purchaseOrderId) {

		try {
			if (userService.checkHasPermission("ORDER_VALIDATION")) {
			visitService.refusePurchaseOrder(purchaseOrderId);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while refusing purchase order status", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
}
