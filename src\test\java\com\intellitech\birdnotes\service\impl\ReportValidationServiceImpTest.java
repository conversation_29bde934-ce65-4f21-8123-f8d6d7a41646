package com.intellitech.birdnotes.service.impl;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.*;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ReportValidationDto;
import com.intellitech.birdnotes.model.dto.StatusReportValidation;
import com.intellitech.birdnotes.model.request.ReportValidationRequest;
import com.intellitech.birdnotes.repository.ReportValidationRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReportValidationServiceImpTest {

    @Mock
    private ReportValidationRepository mockReportValidationRepository;
    @Mock
    private VisitRepository mockVisitRepository;
    @Mock
    private ConfigurationService mockConfigurationService;
    @Mock
    private NotificationService mockNotificationService;
    @Mock
    private ValidationStepService mockValidationStepService;
    @Mock
    private NotificationMessageBuilder mockNotificationMessageBuilder;

    @InjectMocks
    private ReportValidationServiceImp reportValidationServiceImpUnderTest;

    @Test
    public void testGetReportsValidationByDateAndUser() throws Exception {
        // Setup
        final ReportValidationRequest reportValidationRequest = new ReportValidationRequest();
        reportValidationRequest.setSelectedUser(0L);
        reportValidationRequest.setVisitDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Configure ReportValidationRepository.findReportsValidationByDateAndUser(...).
        final ReportValidation reportValidation = new ReportValidation();
        reportValidation.setId(0L);
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        final User user = new User();
        delegate.setUser(user);
        reportValidation.setDelegate(delegate);
        reportValidation.setInvalidVisitsNumber(0);
        reportValidation.setValidVisitsNumber(0);
        reportValidation.setValidGeolocatedVisitsNumber(0);
        reportValidation.setInvalidGeolocatedVisitsNumber(0);
        reportValidation.setVisitDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        reportValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
        final List<ReportValidation> reportValidations = Arrays.asList(reportValidation);
        when(mockReportValidationRepository.findReportsValidationByDateAndUser(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L)).thenReturn(reportValidations);

        // Run the test
        final List<ReportValidationDto> result = reportValidationServiceImpUnderTest.getReportsValidationByDateAndUser(
                reportValidationRequest);

        // Verify the results
    }

    @Test
    public void testGetReportsValidationByDateAndUser_ReportValidationRepositoryReturnsNull() throws Exception {
        // Setup
        final ReportValidationRequest reportValidationRequest = new ReportValidationRequest();
        reportValidationRequest.setSelectedUser(0L);
        reportValidationRequest.setVisitDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockReportValidationRepository.findReportsValidationByDateAndUser(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L)).thenReturn(null);

        // Run the test
        final List<ReportValidationDto> result = reportValidationServiceImpUnderTest.getReportsValidationByDateAndUser(
                reportValidationRequest);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetReportsValidationByDateAndUser_ReportValidationRepositoryReturnsNoItems() throws Exception {
        // Setup
        final ReportValidationRequest reportValidationRequest = new ReportValidationRequest();
        reportValidationRequest.setSelectedUser(0L);
        reportValidationRequest.setVisitDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        when(mockReportValidationRepository.findReportsValidationByDateAndUser(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L)).thenReturn(Collections.emptyList());

        // Run the test
        final List<ReportValidationDto> result = reportValidationServiceImpUnderTest.getReportsValidationByDateAndUser(
                reportValidationRequest);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testUpdateStatusReportValidation() {
        // Setup
        final StatusReportValidation statusReportValidation = new StatusReportValidation();
        statusReportValidation.setId(0L);
        statusReportValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);

        // Configure ReportValidationRepository.findOne(...).
        final ReportValidation reportValidation = new ReportValidation();
        reportValidation.setId(0L);
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        final User user = new User();
        delegate.setUser(user);
        reportValidation.setDelegate(delegate);
        reportValidation.setInvalidVisitsNumber(0);
        reportValidation.setValidVisitsNumber(0);
        reportValidation.setValidGeolocatedVisitsNumber(0);
        reportValidation.setInvalidGeolocatedVisitsNumber(0);
        reportValidation.setVisitDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        reportValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
        when(mockReportValidationRepository.findOne(0L)).thenReturn(reportValidation);

        // Configure NotificationMessageBuilder.Build(...).
        final User user1 = new User();
        user1.setId(0L);
        user1.setEmail("email");
        user1.setUsername("username");
        user1.setPassword("password");
        user1.setPhone("phone");
        final Notification notification = new Notification("text", false, user1);
        when(mockNotificationMessageBuilder.Build()).thenReturn(notification);

        // Run the test
        reportValidationServiceImpUnderTest.updateStatusReportValidation(statusReportValidation);

        // Verify the results
/*        verify(mockReportValidationRepository).save(any(ReportValidation.class));
        verify(mockNotificationMessageBuilder).setMessageType("messageType");
        verify(mockNotificationMessageBuilder).setUser(any(User.class));
        verify(mockNotificationMessageBuilder).setTagetUser(any(User.class));
        verify(mockNotificationMessageBuilder).setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockNotificationMessageBuilder).setStatus("status");
        verify(mockNotificationService).generateUsingAllNotificationMethods(any(Notification.class));
        verify(mockNotificationService).sendNotificationUsingWorkflow(any(User.class), eq("REPORT_VALIDATION"),
                eq(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()), eq("status"));*/
    }

    @Test
    public void testUpdateStatusReportValidation_ReportValidationRepositoryFindOneReturnsNull() {
        // Setup
        final StatusReportValidation statusReportValidation = new StatusReportValidation();
        statusReportValidation.setId(0L);
        statusReportValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);

        when(mockReportValidationRepository.findOne(0L)).thenReturn(null);

        // Run the test
        reportValidationServiceImpUnderTest.updateStatusReportValidation(statusReportValidation);

        // Verify the results
    }

    @Test
    public void testValidateVisitReport() {
        // Setup
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setFirstName("firstName");
        delegate.setHiringDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        delegate.setLastName("lastName");
        final User user = new User();
        delegate.setUser(user);

        // Configure VisitRepository.findVisitReport(...).
        final Prospect prospect = new Prospect();
        final Range range = new Range();
        range.setId(0);
        range.setName("name");
        prospect.setRanges(new HashSet<>(Arrays.asList(range)));
        prospect.setLatitude(0.0);
        prospect.setLongitude(0.0);
        final Delegate delegate1 = new Delegate();
        delegate1.setId(0L);
        delegate1.setFirstName("firstName");
        delegate1.setHiringDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        delegate1.setLastName("lastName");
        final User user1 = new User();
        delegate1.setUser(user1);
        final List<Visit> visits = Arrays.asList(
                new Visit(0L, new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), prospect, delegate1,
                        "generalNote"));
        when(mockVisitRepository.findVisitReport(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L)).thenReturn(visits);

        // Configure ConfigurationService.findConfiguration(...).
        final ConfigurationDto configurationDto = new ConfigurationDto();
        configurationDto.setAcceptedPointingDistance(0);
        configurationDto.setReportingStartingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        configurationDto.setDelayedReportingTolerence(0);
        configurationDto.setNoGeolocationTolerence(0);
        when(mockConfigurationService.findConfiguration()).thenReturn(configurationDto);

        // Configure NotificationMessageBuilder.Build(...).
        final User user2 = new User();
        user2.setId(0L);
        user2.setEmail("email");
        user2.setUsername("username");
        user2.setPassword("password");
        user2.setPhone("phone");
        final Notification notification = new Notification("text", false, user2);
        when(mockNotificationMessageBuilder.Build()).thenReturn(notification);

        // Run the test
        reportValidationServiceImpUnderTest.validateVisitReport(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), delegate);

/*        // Verify the results
        verify(mockReportValidationRepository).save(any(ReportValidation.class));
        verify(mockNotificationMessageBuilder).setMessageType("messageType");
        verify(mockNotificationMessageBuilder).setUser(any(User.class));
        verify(mockNotificationMessageBuilder).setTagetUser(any(User.class));
        verify(mockNotificationMessageBuilder).setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockNotificationMessageBuilder).setStatus("status");
        verify(mockNotificationService).generateUsingAllNotificationMethods(any(Notification.class));
        verify(mockNotificationService).sendNotificationUsingWorkflow(any(User.class), eq("REPORT_VALIDATION"),
                eq(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()), eq("status"));*/
    }

    @Test
    public void testValidateVisitReport_VisitRepositoryReturnsNoItems() {
        // Setup
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setFirstName("firstName");
        delegate.setHiringDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        delegate.setLastName("lastName");
        final User user = new User();
        delegate.setUser(user);

        when(mockVisitRepository.findVisitReport(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L)).thenReturn(Collections.emptyList());

        // Configure ConfigurationService.findConfiguration(...).
        final ConfigurationDto configurationDto = new ConfigurationDto();
        configurationDto.setAcceptedPointingDistance(0);
        configurationDto.setReportingStartingTime(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        configurationDto.setDelayedReportingTolerence(0);
        configurationDto.setNoGeolocationTolerence(0);
        when(mockConfigurationService.findConfiguration()).thenReturn(configurationDto);

        // Configure NotificationMessageBuilder.Build(...).
        final User user1 = new User();
        user1.setId(0L);
        user1.setEmail("email");
        user1.setUsername("username");
        user1.setPassword("password");
        user1.setPhone("phone");
        final Notification notification = new Notification("text", false, user1);
        when(mockNotificationMessageBuilder.Build()).thenReturn(notification);

        // Run the test
        reportValidationServiceImpUnderTest.validateVisitReport(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), delegate);

        // Verify the results
/*        verify(mockReportValidationRepository).save(any(ReportValidation.class));
        verify(mockNotificationMessageBuilder).setMessageType("messageType");
        verify(mockNotificationMessageBuilder).setUser(any(User.class));
        verify(mockNotificationMessageBuilder).setTagetUser(any(User.class));
        verify(mockNotificationMessageBuilder).setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        verify(mockNotificationMessageBuilder).setStatus("status");
        verify(mockNotificationService).generateUsingAllNotificationMethods(any(Notification.class));
        verify(mockNotificationService).sendNotificationUsingWorkflow(any(User.class), eq("REPORT_VALIDATION"),
                eq(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime()), eq("status"));*/
    }
}
