package com.intellitech.birdnotes.service;
import java.util.List;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.NextActionRule;
import com.intellitech.birdnotes.model.dto.NextActionRuleDto;


public interface NextActionRuleService {
	
	List<NextActionRuleDto> findAll() throws BirdnotesException;
	
	NextActionRule saveNextActionRule(NextActionRuleDto nextActionRuleDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;
	
	NextActionRuleDto findNextActionRuleDto(Long id) throws BirdnotesException;



	

}
