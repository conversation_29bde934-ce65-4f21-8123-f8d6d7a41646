# Exceptions
		EXCEPTION_MESSAGE = This entity can not be deleted because it is linked to the table {0}
		LIST_OF_NOTE_FRAIS_IS_NULL = The list of expense notes is null or empty
		TOKEN_IS_EMPTY = Token is empty
		NO_USER_WITH_ID = no user with id =
		INVALID_CSV_FILE_FORMAT = CSV file format is unexpected
		PROSPECT_DATA_NOT_FOUND_IN_CSV_FILE = Import was aborted. An error is encountered due to missing prospect data on the line
		USER_DTO_IS_NULL = userDto is null
		POTENTIAL_DTO_IS_NULL = potentialDto is null
		POTENTIAL_NAME_IS_EMPTY = potential name is empty
		TYPE_NOTE_FRAIS_NAME_IS_EMPTY = The name of the expense note type is empty
		ACTIVITYTYPE_NAME_IS_EMPTY = Activity name is empty
		SESSION_EXPIRE = Your session has expired
		sendSurveyEmailHtmlBody = Hello,<br/><br/> A new response to the satisfaction survey {0} has been sent
		sendSurveyEmailSubject = Evaluation
		TYPE_IS_NULL = typeNoteFrais is mandatory
		NOTE_FRAIS_COULD_NOT_DELETED = Unable to delete the expense note
		ACTIVITY_COULD_NOT_DELETED = Unable to delete activity
		PlANNING_VALIDATION_COULD_NOT_DELETED = Unable to delete planning validation
		RECOVERY_COULD_NOT_DELETED = Unable to clear overlay
		PURCHASE_ORDER_COULD_NOT_DELETED = Unable to delete purchase order
		ACTIONMARKETING_COULD_NOT_DELETED = Unable to delete actionMarketing
		OPPORTUNITYNOTE_COULD_NOT_DELETED = Unable to delete OpportunityNote
		

		PLANNING_COULD_NOT_DELETED = Unable to delete the planning
		TYPE_NOTE_FRAIS_COULD_NOT_DELETED = Unable to delete the expense note type
		NOTE_FRAIS_DTO_ID_NULL = noteFraisDto is null
		ACTIVITY_DTO_ID_NULL = activityDto is null
		ALREADY_EXIST = Name already exists
		SECTOR_TO_UPDATE_ALREADY_DELETED = The sector you want to modify is already deleted
		SECTOR_TO_DELETE_ALREADY_DELETED = The sector cannot be deleted
		GADGET_TO_UPDATE_ALREADY_DELETED = The gadget you want to modify is already deleted
		GADGET_TO_DELETE_ALREADY_DELETED = This gift cannot be deleted, it is attached to visits
		ACTIONMARKETING_TO_DELETE_ALREADY_DELETED = The marketing action cannot be deleted
		OPPORTUNITYNOTE_TO_DELETE_ALREADY_DELETED = opportunity note cannot be deleted
		ACTIONMARKETING_TO_UPDATE_ALREADY_DELETED = The marketing action you want to modify is already deleted
		OPPORTUNITYNOTE_TO_UPDATE_ALREADY_DELETED = The OPPORTUNITY NOTE you want to modify is already deleted

		NOTEFRAIS_TO_UPDATE_ALREADY_DELETED = The expense note you want to modify is already deleted
		ACTIVIYUTO_UPDATE_ALREADY_DELETED = The activity you want to modify is already deleted
		GONFIGURATION_TO_UPDATE_ALREDY_DELETED = The configuration you want to modify is already deleted
		
		OK = OK
		KO = KO
		LOCALITY_TO_DELETE_ALREADY_DELETED = The location cannot be deleted, it is attached to a prospect
		PROSPECT_TYPE_TO_DELETE_ALREADY_DELETED = The prospect type cannot be deleted, it is attached to a prospect
		POTENTIAL_TO_DELETE_ALREADY_DELETED = The potential type cannot be deleted, it is attached to a prospect
		ESTABLISHMENT_TO_DELETE_ALREADY_DELETED = The establishement cannot be deleted, it is attached to a prospect
		SPECIALITY_TO_DELETE_ATTACHED_PROSPECT = The speciality cannot be deleted, it is attached to a prospect
		TYPE_NOTE_FRAIS_TO_DELETE_ATTACHED_NOTE_FRAIS = This type could not be deleted
		EMAIL_ALREADY_EXIST = Email address already exists
		PRODUCT_NAME_ALREADY_EXIST = Product name already exists
		WHOLESALER_NAME_ALREADY_EXIST = Wholesale site name already exists
		NETWORK_NAME_ALREADY_EXIST = The network name already exists
		PHONE_ALREADY_EXIST = Phone already exists
		USERNAME_ALREADY_EXIST = Identifier already exists
		PROSPECT_TO_UPDATE_ALREADY_DELETED = The prospect you want to modify is already deleted
		GSM_ALREADY_EXIST = GSM already exists
		NAME_ALREADY_EXIST= name already exists
		EMAIL_ALREADY_EXIST= Email already exists
		USERNAME_ALREADY_EXIST= User name already exists
		PHONE_ALREADY_EXIST= Phone already exists
		
		GAMME_TO_DELETE_ALREADY_DELETED = The range cannot be deleted
		SPECIALITY_TO_DELETE_ALREADY_DELETED = The specialty cannot be deleted
		ROLE_TO_DELETE_ALREADY_DELETED = The role cannot be deleted

		 LOCALITY_TO_UPDATE_ALREADY_DELETED = The locality you want to modify is already deleted
		CANNOT_DELETE_PROSPECT = The selected prospect has visits, therefore cannot be deleted
		CANNOT_DELETE_DELEGATE = The delegate cannot be deleted because there are sectors that are linked to this delegate
		CANNOT_DELETE_PRODUCT = The product cannot be deleted because there are visits associated with this product
		CANNOT_DELETE_GAMME = The range is already used and cannot be deleted
		CANNOT_DELETE_WHOLESALER = The wholesaler is already in use and cannot be deleted
		POTENTIAL_TO_UPDATE_ALREADY_DELETED = The Potential you want to modify is already deleted
		EMPTY_PROSPECTSAFFECTATION_USER = User assignment is empty
		EMPTY_PLANNING_VALIDATION = Planning validation is empty
		EMPTY_PRODUCT_POTENTIAL = Product potential is empty
		EMPTY_PROSPECTSAFFECTATION_PROSPECT = Prospect assignment is empty
		EMPTY_SECTOR = Sector name is empty
		EMPTY_SECTOR_USER = Sector user is empty
		USER_ALREADY_DELETED = User already deleted
		PROSPECT_ALREADY_DELETED = prospect already deleted
		AFFECTATION_ALREADY_DELETED = Assignment already deleted
		PRODUCT_ALREADY_DELETED = product already deleted
		SECTOR_LINKED_TO_PROSPECT = The sector cannot be deleted because there are prospects linked to this sector
		SECTOR_LINKED_TO_LOCALITY = The sector cannot be deleted because there are localities that are linked to this sector
		SECTOR_DTO_NULL = sectorDto is null
		CHOOSE_DELEGATE = Choose a delegate
		EMPTY_LOCALITY_NAME = Locality name is empty
		EMPTY_SECTOR_ID = Sector id is empty
		SECTOR_ALREADY_DELETED = sector already deleted
		LOCALITY_DTO_NULL = localityDto is null
		EMPTY_NAME = name is empty!
		 SECTOR_DELETED = The sector is deleted
		EMPTY_OR_NULL_SPECIALITIES = Specialties is null or empty
		EMPTY_SPECIALITY = Specialty name is empty
		NULL_PRODUCT = product is null
		NULL_WHOLESALER = wholesaler is null
		NULL_DTO_PRODUCT = productDto is null
		NULL_DTO_WHOLESALER = wholesalerDto is null
		NULL_DTO_PRODUCT_POTENTIAL = productsPotentialDto is null
		NULL_CHARGE_PLAN = chargePlan is null
		NULL_NOTIFICATION = notifications is null
		NO_PRODUCT_WITH_ID = no product with id =
		NO_WHOLESALER_WITH_ID = no wholesaler with id =
		VALUE_OF_CYCLE_NULL = Cycle value is null
		NULL_AFFECTATION = Assignment is null
		NULL_SPECIALTY = Specialty dto is null
		NULL_SECTOR = Sector dto is null
		NULL_LOCALITY = Locality dto is null
		NULL_TYPE = Prospect type dto is null
		RANGE_TO_UPDATE_ALREADY_DELETED = The Range you want to modify is already deleted
		SPECIALITY_TO_UPDATE_ALREADY_DELETED = The specialty you want to modify is already deleted
		EMPTY_ACTIONMARKETING_VALIDATION = empty_actionmarketing_validation
		NULL_POTENTIAL = potential dto is null
		NULL_GOALS = goals request dto is null
		ROLE_NAME_IS_EMPTY = role name is required
		ROLE_DTO_IS_NULL = RoleDto is null
		ROLE_TO_UPDATE_ALREADY_DELETED = the role you want to modify is already deleted
		GOAL_TO_UPDATE_ALREADY_DELETED = The goal you want to modify is already deleted
		GOAL_NAME_IS_EMPTY = goal name is required
		 
		GOAL_DTO_IS_NULL = GoalDto is null
		GOAL_DELEGATE_IS_EMPTY = Delegate is required
		GOAL_ITEM_IS_EMPTY = Goals are mandatory
		PRODUCT_PURCHASE_ORDER_TEMPLATE_ITEM_IS_EMPTY = products are required
		ROLE_NAME_IS_NUMBER = the role name is a character string
		GOAL_NAME_IS_NUMBER = name is a number
		VALIDATION_TYPE_IS_EMPTY = validation type is required
		VALIDATION_STEPS_IS_EMPTY = validation steps are mandatory
		EVENT_IS_EMPTY = events are mandatory
		NOTIFICATIONS_IS_EMPTY = notifications are required
		 
		 VALIDATION_STATUS_IS_EMPTY = validation steps are mandatory
		NUMBER_OF_VALIDATORS_IS_EMPTY = the number of validators is mandatory
		VALIDATION_INPROGRESS = You cannot modify the workflow because it still has validations in progress
		USED_EXPENSE_TYPE = The type you want to delete is referenced by expense reports
		USED_PRSOPECT = The prospect you want to delete has visits
		NULL_SAMPLE = sample is null
		PRODUCT_IS_EMPTY = Product is required
		USER_IS_EMPTY = Delegate is required
		QUANTITY_IS_EMPTY = quantity is required
		DELIVERY_DATE_IS_EMPTY = Delivery date is mandatory
		PERIOD_IS_EMPTY = The period is mandatory
		LINK_IS_EMPTY = Report link is required
		REPORTCRON_USER_IS_EMPTY = Users are required
		REPORT_CRON_ID_DTO_IS_NULL = reportCron is empty
		REPORT_CRON_PERIOD_IS_EMPTY = the period is mandatory
		REPORT_CRON_LINK_IS_EMPTY = report link is required
		REPORT_CRON_TO_UPDATE_ALREADY_DELETED = The report is already deleted
		REPORT_CRON_USERS_IS_EMPTY = users are required
		VISIT_PRODUCT_COULD_NOT_DELETED = Could not delete visit product
		NULL_PROSPECT_TYPE = Prospect type is null
		 
# ValueType

		NUMBER_OF_VISITS = Number of visits
		REPORT_FILLING_RATE = Report filling rate
		BLANKET = Rate R/P
		SALES = Stock
		SAMPLE_NUMBER = Samples
		NUMBER_OF_ORDERS = Orders
		PROSPECT_SATISFACTION = Satisfaction per comment
		GROSS_SALES = Turnover
		GADGET = Number of gadgets
		PROSPECT_PRODUCT_SATISFACTION = Product satisfaction
		PRODUCT_ORDER_PRESENTATION = Presentation order
		VISITS_AVERAGE = Average visits
		SYNCHRONISATION_DELAY = Synchronization delay
		 
# GroupType

		DELEGUE = Delegate
		PROSPECT = Prospect
		SECTOR = Sector
		LOCALITY = Locality
		ACTIVITY = Activity
		POTENTIAL = Potential
		SPECIALITY = Specialty
		PRODUCT = Product
		SATISFACTION = satisfaction
		PRESENTATION_ORDER = Presentation order
		 
# email and notif 		 
		  
		sendRememberSynchronizationSubject= Synchronization
		sendRememberSynchronizationHtmlBody= Hello,<br/><br/> Please synchronize your data from BirdNotes mobile \n\n \
		
		sendMailReportSubject=Statistics Dashboards
		sendMailReportHtmlBody= Hello,<br/><br/> You can consult the statistics dashboards from the last period on this link: \n\n \
		
		sendWholesalerMailSubject = Prospect command {0} / {2}
		sendWholesalerMailHtmlBodyWithAttachment = Hello,<br/><br/> We have a new order for prospect {0}. <br/> The prospect's address is {1} (Sector: {4}, Location: {5}). <br/>Order status: <span style="color:red;">{6}</span>. <br/>Order details are included in the attachment. <br/> Please confirm receipt of the email. <br/><br/>{3}<br/>Sincerely<br/>CRM BirdNotes\n\n \
		
		sendWholesalerMailHtmlBodyWithoutAttachment = Hello,<br/><br/> You have a new purchase order for prospect {0}. <br/> Prospect address {1} (Sector: {4}, Locality: {5}). <br/>Order status: <span style="color:red;">{6}</span>. <br/><br/>{3}<br/>Sincerely<br/>CRM BirdNotes\n\n \
		
		sendCancellationMailSubject = Cancellation of prospect order {0} / {2}
		sendCancellationMailHtmlBody = Hello,<br/><br/> We regret to inform you that the order for prospect {0} is canceled. <br/> The prospect's address is {1} (Sector: {4}, Location: {5}). <br/><br/>{3}<br/>Sincerely<br/>CRM BirdNotes\n\n \
		
		sendCredentialsEmailSubject = Your account on Birdnotes
		sendPredictionEmailHtmlBody = Hello,<br/><br/> The order prediction for the month {0} has been completed successfully. You can view the recommended prospects by clicking the following link: <a href="{1}">Order Prediction Page</a><br/><br/>.
		sendPredicionEmailSubject = Order Predictions
		sendCredentialsEmailHtmlBody=Hello,<br/><br/> Birdnotes has created an account for you with the following parameters:<br/> \n\n \
		<strong> Username: </strong> {0} <br/> <strong> Password: </strong> {1} <br/> <br/> Kind regards <br/> Birdnotes.
		newIssueEmailHtmlBody=Hello Admin ,<br/><br/> You have new issue:<br/> \n\n \
		<strong> Delegate: </strong> {0} <br/> <strong> Issue: </strong> {1} <br/> <br/> Kind regards <br/> Birdnotes.
		editPasswordEmailSubject=Changing Password on Birdnotes
		editPasswordEmailHtmlBody=Hello,<br/><br/> Birdnotes has modified your account:<br/> \n\n \
		<strong> Your password becomes: </strong> {0} <br/> <br/> Kind regards <br/> Birdnotes.
		sendInvestigationSubject = Satisfaction survey
		sendInvestigationHtmlBody = {0},<br/>{1}<br/>{2}<br/><br/>Sincerely<br/>{3}<br/>
		editUsernameEmailSubject = Modification of identifier on Birdnotes
		editUsernameEmailHtmlBody = Hello,<br/><br/> Birdnotes has modified your account:<br/> \n\n \
		<strong> Your identifier becomes: </strong> {0} <br/> <br/> Kind regards <br/> Birdnotes.
		editUsernamePasswordEmailSubject = Modification of username and password on Birdnotes
		editUsernamePasswordEmailHtmlBody = Hello,<br/><br/> Birdnotes has modified your account:<br/> \n\n \
		<strong> Your username becomes: </strong> {0} <br/> <strong> Your password becomes: </strong> {1} <br/> <br/> Kind regards <br/> Birdnotes.
		
		planningNotificationMessage = Delegate {0} has sent <a href="{5}/#/planning/{2}"> a new planning</a>
		noteFraisNotificationMessage = Delegate {0} has sent <a href="{5}/#/note-fras/{2}" > a new expense report</a>
		actionMarketingNotificationMessage = Delegate {0} has sent <a href="{5}/#/action-marketing/{2}" > a new marketing action</a>
		opportunityNoteNotificationMessage = Delegate {0} has sent a new opportunity note
		locationNotificationMessage = a new location
		addProspectNotificationMessage = Delegate {0} added <a href="{5}/#/change-prospect/{2}" > new prospect {1}</a>
		updateProspectNotificationMessage = Delegate {0} has modified prospect {1}		
		activityNotificationMessage = Delegate {0} added <a href="{5}/#/activity/{2}" >a new activity
		
		planificationValidationNotificationMessage = {0} has validated your planning
		planificationReviseNotificationMessage = {0} requested to revise the planning
		planificationRefusNotificationMessage = {0} refused the planning
		
		actionMarketingValidationNotificationMessage = {0} has validated your marketing action
		actionMarketingRefusNotificationMessage = {0} has refused your marketing action
		opportunityNoteValidationNotificationMessage = {0} validated your opportunity note
		opportunitynoteRefusNotificationMessage = {0} declined your opportunity note
		
		noteFraisValidationNotificationMessage = {0} has validated your expense report
		noteFraisRefusNotificationMessage = {0} refused your expense report
		
		
		delegateCommissionValidationNotificationMessage = {0} has validated your commission
		delegateCommissionRefusNotificationMessage = {0} has refused your commission
		
		prospectsAcceptationNotificationMessage = {0} accepted prospect {1}
		prospectsModificationAcceptationNotificationMessage = {0} accepted the changes from prospect {1}
		prospectsRefuseNotificationMessage = {0} refused prospect {1}
		
		prospectsAffectationNotificationMessage = {0} has assigned you the new prospect {1}
		prospectsAffectationDeleteNotificationMessage = {0} you deleted prospect {1}
				 
		visitMessageTag = {3}: Delegate {0} visited prospect {1} and tagged you in the following message: {4}
		importantMessageTag =Delegate {0} left an important comment during his visit on {3} to prospect {1}: \n\n {4}
		urgentMessageTag =Delegate {0} left an urgent comment during his visit on {3} to prospect {1}: {4} | product: {6}
		reportValidation =The report of delegate {0} dated {3} is {7}
		
		planningValidationRequest=You are invited to validate a <a href="{5}/#/planning/{2}" >planning</a> created by {0}
		prospectValidationRequest=You are invited to validate a <a href="{5}/#/change-prospect/{2}" >prospect</a> created by {0}
		delegateCommissionValidationRequest=You are invited to validate a <a href="{5}/#/delegate-commission/{2}" >delegate commission</a> {0}
		actionMarketingValidationRequest=You are invited to validate the <a href="{5}/#/action-marketing/{2}" >new marketing action</a> created by {0}
		noteFraisValidationRequest=You are invited to validate the <a href="{5}/#/expense-report/{2}" >new expense report</a> created by {0}
				 
				 
		
			
