package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

public interface WholesalerService {
	
	//List<WholesalerDto> getAllWholesalers() throws BirdnotesException;
	
	//boolean checkWholesalerNameIsUnique(String wholesalerName);

	Wholesaler saveWholesaler(WholesalerDto WholesalerRequest) throws BirdnotesException;

	//List<Wholesaler> saveAll(List<WholesalerDto> WholesalerRequests) throws BirdnotesException;

	void deleteWholesaler(Long wholesalerId) throws BirdnotesException;

	Wholesaler updateWholesaler(WholesalerDto WholesalerDto) throws BirdnotesException;
	
	List<WholesalerDto> getWholesalersByUser(Long userId) throws BirdnotesException;

	List<WholesalerDto> getWholesalersByStatus() throws BirdnotesException;
}
