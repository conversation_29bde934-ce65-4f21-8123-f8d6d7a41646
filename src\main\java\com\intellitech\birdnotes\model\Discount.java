package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.DISCOUNT, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Discount implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.DISCOUNT_SEQUENCE, sequenceName = Sequences.DISCOUNT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.DISCOUNT_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, unique = false)
	private Product product;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_TYPE_ID, unique = false)
	private ProspectType prospectType;

	@Column(name = Columns.PERCENTAGE)
	private Float percentage;

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.WHOLESALER_ID, unique = false)
	private Prospect wholesaler;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	public Float getPercentage() {
		return percentage;
	}

	public void setPercentage(Float percentage) {
		this.percentage = percentage;
	}

	public ProspectType getProspectType() {
		return prospectType;
	}

	public void setProspectType(ProspectType prospectType) {
		this.prospectType = prospectType;
	}

	public Prospect getWholesaler() {
		return wholesaler;
	}

	public void setWholesaler(Prospect wholesaler) {
		this.wholesaler = wholesaler;
	}

	public Discount() {
		super();

	}
}
