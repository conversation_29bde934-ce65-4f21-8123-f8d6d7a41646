package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class FreeQuantityRuleItemDto implements Serializable {


	private static final long serialVersionUID = 1L;
	private Long id;
	private Long freeQuantityRuleId;
	private Integer orderQuantity;
	private Integer freeQuantity;
	private Integer labGratuity;
	private Long productId;
	private Long potentialId;
	private Long prospectTypeId;


	public FreeQuantityRuleItemDto(Long id, Integer orderQuantity, Integer freeQuantity, Integer labGratuity, Long productId,
			Long potentialId, Long prospectTypeId) {
		super();
		this.id = id;
		this.orderQuantity = orderQuantity;
		this.freeQuantity = freeQuantity;
		this.labGratuity = labGratuity;
		this.productId = productId;
		this.potentialId = potentialId;
		this.prospectTypeId = prospectTypeId;
	}


	public Long getProductId() {
		return productId;
	}


	public void setProductId(Long productId) {
		this.productId = productId;
	}


	public Long getPotentialId() {
		return potentialId;
	}


	public void setPotentialId(Long potentialId) {
		this.potentialId = potentialId;
	}


	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	

	public Integer getOrderQuantity() {
		return orderQuantity;
	}


	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}


	public Integer getFreeQuantity() {
		return freeQuantity;
	}


	public void setFreeQuantity(Integer freeQuantity) {
		this.freeQuantity = freeQuantity;
	}


	public Integer getLabGratuity() {
		return labGratuity;
	}


	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}


	public FreeQuantityRuleItemDto() {
		super();
		
	}


	public Long getFreeQuantityRuleId() {
		return freeQuantityRuleId;
	}


	public void setFreeQuantityRuleId(Long freeQuantityRuleId) {
		this.freeQuantityRuleId = freeQuantityRuleId;
	}


	public Long getProspectTypeId() {
		return prospectTypeId;
	}


	public void setProspectTypeId(Long prospectTypeId) {
		this.prospectTypeId = prospectTypeId;
	}


	
}
