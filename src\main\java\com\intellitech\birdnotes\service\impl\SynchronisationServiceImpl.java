package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.nio.file.DirectoryNotEmptyException;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import javax.swing.text.html.ListView;

import org.apache.commons.lang3.StringUtils;
import org.apache.xmlrpc.client.XmlRpcClient;
import org.apache.xmlrpc.client.XmlRpcClientConfigImpl;
import org.codehaus.jettison.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.intellitech.birdnotes.builder.ReceiveResponseDtoBuilder;
import com.intellitech.birdnotes.data.dto.CommentClassification;
import com.intellitech.birdnotes.data.dto.PredictionResponse;
import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.enumeration.EmailType;
import com.intellitech.birdnotes.enumeration.MessageType;
import com.intellitech.birdnotes.enumeration.OrderValidation;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.Attachment;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Issue;
import com.intellitech.birdnotes.model.LoadPlanItem;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.Message;
import com.intellitech.birdnotes.model.MessageTag;
import com.intellitech.birdnotes.model.Note;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.OpportunityNote;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.PresentationTimeTracking;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.WorkTypeConverter;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToProspect;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToPurchaseOrder;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.DelegateToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ActionMarketingRequestDto;
import com.intellitech.birdnotes.model.dto.ActionMarketingResponseDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.AttachmentDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.model.dto.MessageDto;
import com.intellitech.birdnotes.model.dto.MessageTagDto;
import com.intellitech.birdnotes.model.dto.MinimizedChargePlan;
import com.intellitech.birdnotes.model.dto.NoteDto;
import com.intellitech.birdnotes.model.dto.NotificationDto;
import com.intellitech.birdnotes.model.dto.OpportunityNoteRequestDto;
import com.intellitech.birdnotes.model.dto.OpportunityNoteResponseDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PlanningValidationDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.PresentationTimeTrackingDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateDto;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.ReceiveRequestInput;
import com.intellitech.birdnotes.model.dto.ReceiveResponsedDto;
import com.intellitech.birdnotes.model.dto.RecoveryDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SendRequestDto;
import com.intellitech.birdnotes.model.dto.SendResponseDto;
import com.intellitech.birdnotes.model.dto.SendResultDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationResponse;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.model.dto.VisitProductDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.ActionMarketingRepository;
import com.intellitech.birdnotes.repository.ActivityRepository;
import com.intellitech.birdnotes.repository.ActivityTypeRepository;
import com.intellitech.birdnotes.repository.AttachmentRepository;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.ExpenseReportRepository;
import com.intellitech.birdnotes.repository.ExpenseTypeRepository;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.repository.IssueRepository;
import com.intellitech.birdnotes.repository.LoadPlanItemRepository;
import com.intellitech.birdnotes.repository.LoadPlanRepository;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.LocationRepository;
import com.intellitech.birdnotes.repository.MessageRepository;
import com.intellitech.birdnotes.repository.MessageTagRepository;
import com.intellitech.birdnotes.repository.NotificationRuleRepository;
import com.intellitech.birdnotes.repository.OpportunityNoteRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.PlanningValidationRepository;
import com.intellitech.birdnotes.repository.PresentationTimeTrackingRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProductsPotentialRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.ProspectsAffectationRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderTemplateRepository;
import com.intellitech.birdnotes.repository.RecoveryRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.repository.ValidationStepRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.repository.WholesalerRepository;
import com.intellitech.birdnotes.service.ActionMarketingService;
import com.intellitech.birdnotes.service.ActivityService;
import com.intellitech.birdnotes.service.ActivityTypeService;
import com.intellitech.birdnotes.service.AttachmentService;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.DistanceService;
import com.intellitech.birdnotes.service.EstablishmentService;
import com.intellitech.birdnotes.service.ExpenseReportService;
import com.intellitech.birdnotes.service.ExpenseTypeService;
import com.intellitech.birdnotes.service.FreeQuantityRuleService;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.GoalsService;
import com.intellitech.birdnotes.service.NoteService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.OpportunityNoteService;
import com.intellitech.birdnotes.service.PlanningService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProductsPotentialService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.ProspectsAffectationService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.service.PurchaseOrderTemplateService;
import com.intellitech.birdnotes.service.RangeService;
import com.intellitech.birdnotes.service.RecoveryService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.SynchronisationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.service.WholesalerService;
import com.intellitech.birdnotes.thread.SaveAndMailBase64File;
import com.intellitech.birdnotes.thread.SaveAndSendMailBase64File;
import com.intellitech.birdnotes.thread.ThreadSendEmail;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

import net.sf.jasperreports.engine.JRException;

@Service
public class SynchronisationServiceImpl implements SynchronisationService {

	private PlanningService planningService;
	private static final String IMGBALISE = " <br/> <img src='";

	private ActionMarketingService actionMarketingService;

	private ActivityService activityService;

	private OpportunityNoteService opportunityNoteService;

	private UserToDtoConvertor userToDtoConvertor;

	private DelegateToDtoConvertor delegateToDtoConvertor;

	private ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder;

	private WholesalerToDtoConvertor wholesalerToDtoConvertor;

	private ConvertProspectToDto convertProspectToDto;

	private PlanningValidationRepository planningValidationRepository;

	private LoadPlanRepository loadPlanRepository;

	private LoadPlanItemRepository loadPlanItemRepository;

	private PlanningRepository planningRepository;

	private ActionMarketingRepository actionMarketingRepository;

	private OpportunityNoteRepository opportunityNoteRepository;

	private LocationRepository locationRepository;

	private ConfigurationRepository configurationRepository;

	private ProspectsAffectationService prospectsAffectationService;

	private ProspectsAffectationRepository prospectsAffectationRepository;
	
	private PresentationTimeTrackingRepository presentationTimeTrackingRepository;

	private ProductService productService;

	private ConfigurationService configurationService;

	private WholesalerService wholesalerService;

	private ProductsPotentialService potentialProductService;

	private GadgetService gadgetService;

	private ProspectService prospectService;

	private SpecialityService specialityService;

	private ProspectTypeService prospectTypeService;

	private EstablishmentService establishmentService;

	private ExpenseTypeService typeNoteFraisService;

	private PurchaseOrderService purchaseOrderService;

	private RecoveryService recoveryService;

	private AttachmentService attachmentService;

	private ActivityTypeService activityTypeService;

	private PotentialService potentialService;

	private GoalsService goalService;

	private PurchaseOrderTemplateService purchaseOrderTemplateService;

	private FreeQuantityRuleService freeQuantityRuleService;

	private UserService userService;
	
	private NoteService noteService;

	private DistanceService distanceService;

	private ExpenseReportService noteFraisService;

	private UserRepository userRepository;

	private ProspectRepository prospectRepository;

	private VisitRepository visitRepository;

	private PurchaseOrderTemplateRepository purchaseOrderTemplateRepository;

	private ProductRepository productRepository;

	private WholesalerRepository wholesalerRepository;

	private ProductsPotentialRepository potentialProductRepository;

	private GiftRepository gadgetRepository;

	private VisitsProductsRepository visitsProductsRepository;

	private ConvertDtoToProspect convertDtoToProspect;

	private SectorRepository sectorRepository;

	private LocalityRepository localityRepository;

	private SpecialityRepository specialityRepository;

	private ExpenseTypeRepository expenseTypeRepository;

	private ExpenseReportRepository noteFraisRepository;

	private PurchaseOrderRepository purchaseOrderRepository;

	private IssueRepository issueRepository;

	private ActivityRepository activityRepository;

	private RecoveryRepository recoveryRepository;

	private AttachmentRepository attachmentRepository;

	private MessageRepository messageRepository;

	private MessageTagRepository messageTagRepository;

	private ActivityTypeRepository activityTypeRepository;

	private NotificationService notificationService;

	private JavaMailSender javaMailSender;

	private RangeService gammeService;

	private NotificationMessageBuilder notificationMessageBuilder;

	private ValidationStepService validationStepService;

	private ValidationStatusRepository validationStatusRepository;

	private ValidationStepRepository validationStepRepository;

	private ConfigurationRepository configureRepository;

	private DelegateRepository delegateRepository;

	private NotificationRuleRepository notificationRuleRepository;

	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}
	
	@Autowired	
	public void setNoteService(NoteService noteService) {
		this.noteService = noteService;
	}

	@Autowired
	private MessageSource messageSource;


	@Autowired
	private RestTemplate restTemplate;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${logoPath}")
	private String logoPath;

	@Value("${generatedDocumentPath}")
	private String generatedDocumentPath;

	@Value("${generatedDoPath}")
	private String generatedDoPath;

	@Value("${mobileAppLogsPath}")
	private String mobileAppLogsPath;

	@Value("${expenseReportPath}")
	private String expenseReportPath;

	@Value("${opportunityNotePath}")
	private String opportunityNotePath;

	@Value("${purchaseOrderPath}")
	private String purchaseOrderPath;

	@Value("${recoveryPath}")
	private String recoveryPath;

	@Value("${uploadUrl}")
	private String uploadUrl;

	/*@Value("${newIssueEmailHtmlBody}")
	private String newIssueEmailHtmlBody;

	@Value("${sendCancellationMailSubject}")
	private String sendCancellationMailSubject;

	@Value("${sendCancellationMailHtmlBody}")
	private String sendCancellationMailHtmlBody;

	@Value("${sendWholesalerMailSubject}")
	private String sendWholesalerMailSubject;

	@Value("${sendWholesalerMailHtmlBodyWithAttachment}")
	private String sendWholesalerMailHtmlBodyWithAttachment;*/

	
	private String mlServerUrl;

	@Value("${ml.serverToken}")
	private String mlServerToken;

	private Marker logSyncMarker = MarkerFactory.getMarker("SYNC");
	private String[] commentRatingToNotify; 
	
	private static final Logger LOG = LoggerFactory.getLogger(SynchronisationServiceImpl.class);
	

	@Override
	@Transactional
	public SendResponseDto send(SendRequestDto sendRequestDto, Long userId) throws BirdnotesException {

		User user = userRepository.findOne(userId);
		Delegate delegate = delegateRepository.findDelegateByUserId(userId);
		Set<VisitDto> doubleVisitList = new HashSet<>();
		Set<VisitProductDto> doubleVisitProductList = new HashSet<>();
		
		LOG.info(logSyncMarker, "Start sending data user id = " + userId + " User name " + delegate.getFirstName());

		delegate.setLastSyncro(new Date());

		Configuration config = configureRepository.findById(1);
		mlServerUrl = config.getMlServerUrl();

		if (mlServerUrl != null && !mlServerUrl.isEmpty() && config.getCommentsRatingNotification() != null
				&& !config.getCommentsRatingNotification().isEmpty()) {
			commentRatingToNotify = config.getCommentsRatingNotification().split("\n");
		}

		List<Long> prospectChangesIds = saveProspects(sendRequestDto.getProspectsChangeRequests(), delegate);

		List<Long> savedVisitIds = saveVisits(sendRequestDto.getVisits(), doubleVisitList, delegate);

		List<Long> savedPurchaseOrderIds = savePurchaseOrder(sendRequestDto.getPurchaseOrders(), delegate);

		List<Long> savedVisitProductIds = saveVisitProduct(sendRequestDto.getVisitProducts(),doubleVisitList, doubleVisitProductList, delegate);
		
		saveDoubleVisits(doubleVisitList, doubleVisitProductList);

		List<Long> savedMarketingActionIds = saveMarketingAction(sendRequestDto.getActionMarketings(), delegate);

		List<Long> savedActivityIds = saveActivity(sendRequestDto.getActivities(), delegate);

		List<Long> savedExpenseReportIds = saveExpenseReports(sendRequestDto.getNotefrais(), delegate);

		List<Long> savedPlanningIds = savePlanning(sendRequestDto.getPlannings(), delegate);

		List<Long> savedPlanningValidationIds = savePlanningValidation(sendRequestDto.getPlanningValidations(),
				delegate);

		List<Long> savedAttachmentIds = saveAttachment(sendRequestDto.getAttachments(), delegate);

		List<Long> savedRecoveryIds = saveRecovery(sendRequestDto.getRecoveries(), delegate);

		List<Long> savedMessageIds = saveMessage(sendRequestDto.getMessages(), user);

		List<Long> savedMessageTagIds = saveMessageTag(sendRequestDto.getTagedUsers(), delegate);
		
		List<Long> savedTrackingTimeIds = savePresentationTimeTracking(sendRequestDto.getPresentationTimeTracking(), delegate);

		List<Long> deletedVisitProducts = deleteVisitProducts(sendRequestDto.getVisitProductToDelete(), delegate);

		List<Long> deletedAttachment = deleteAttachment(sendRequestDto.getAttachmentToDelete(), delegate);

		List<Long> deletedRecovery = deleteRecovery(sendRequestDto.getRecoveryToDelete(), delegate);

		List<Long> deletedPurchaseOrders = deletePurchaseOrder(sendRequestDto.getPurchaseOrderToDelete(),
				sendRequestDto.getPurchaseOrders(), delegate);

		List<Long> deletedVisits = deleteVisits(sendRequestDto.getVisitsToDelete(), delegate);

		List<Long> deletedExpenseReports = deleteExpenseReport(sendRequestDto.getExpenseReportToDelete(), delegate);

		List<Long> deletedMarketingActions = deleteMarketingAction(sendRequestDto.getMarketingActionsToDelete(),
				delegate);

		List<Long> deletedActivities = deleteActivity(sendRequestDto.getActivitiesToDelete(), delegate);

		List<Long> deletedPlannings = deletePlanning(sendRequestDto.getPlanningsToDelete(), delegate);

		List<Long> deletedPlanningValidations = deletePlanningValidation(sendRequestDto.getPlanningValidationToDelete(),
				delegate);

		// List<Long> opportunityNotesToDelete = deleteOpportunityNote(sendRequestDto,
		// delegate);

		insertLocation(sendRequestDto.getLocations(), delegate);

		generateSaveSendEmailPO(sendRequestDto.getPurchaseOrders());
		
		sendPurchaseOrderToErpAsync(savedPurchaseOrderIds, config);
		
		/*
		 * Map<Long, Long> deletedOpportunityNotes = new HashMap<>(); if
		 * (opportunityNotesToDelete != null) { for (Long opportunityNote :
		 * opportunityNotesToDelete) { deletedOpportunityNotes.put(opportunityNote,
		 * opportunityNote); } } userRepository.save(delegate);
		 */

		List<SendResultDto> sendOpportunityNoteIds = new ArrayList<>();
		List<Long> sendOpportunityNoteDeletedIds = new ArrayList<>();

		LOG.info(logSyncMarker, "End sending data user id = " + userId + " User name " + delegate.getFirstName());

		return new SendResponseDto(prospectChangesIds, savedVisitIds, savedVisitProductIds, savedPurchaseOrderIds,
				savedExpenseReportIds, savedMarketingActionIds, savedActivityIds, savedPlanningIds,
				savedPlanningValidationIds, savedRecoveryIds, savedAttachmentIds, savedMessageIds, savedMessageTagIds, savedTrackingTimeIds,

				deletedVisits, deletedVisitProducts, deletedPurchaseOrders, deletedExpenseReports,
				deletedMarketingActions, deletedActivities, deletedPlannings, deletedPlanningValidations,
				deletedRecovery, deletedAttachment, sendOpportunityNoteIds, sendOpportunityNoteDeletedIds);
	}

	@Override
	@Transactional
	public ReceiveResponsedDto receive(Long userId, ReceiveRequestInput receiveRequestInput) throws BirdnotesException {

		ReceiveResponseDtoBuilder receiveResponseDtoBuilder = new ReceiveResponseDtoBuilder();

		User user = userRepository.findOne(userId);
		Delegate delegate = delegateRepository.findDelegateByUserId(userId);
		
		if (delegate != null) {
			LOG.info(logSyncMarker,
					"Start receiving data user id = " + userId + " User name " + delegate.getFirstName());
			
			/* get Data with validation */
			List<Long> deletedProspects = new ArrayList<>();
			List<Long> refusedProspects = new ArrayList<>();
			List<Long> deletedPlannings = new ArrayList<>();
			List<Long> deletedPlanningValidations = new ArrayList<>();
			List<PlanningDto> planningDtoList = new ArrayList<>();
			List<SendResultDto> mergedProspectWatingValidation = new ArrayList<>();
			List<ValidationResponse> prospectsValidationResponse = new ArrayList<>();
			List<PlanningValidationDto> planningValidations = new ArrayList<>();
			if (receiveRequestInput.getNewProspectWaitingForValidation().size() > 0) {

				List<Prospect> newProspectsWaitingForValidation = prospectRepository
						.getByIdentifiers(receiveRequestInput.getNewProspectWaitingForValidation());
				for (Prospect newProspectWaitingForValidation : newProspectsWaitingForValidation) {
					if (newProspectWaitingForValidation.getStatus().equals(UserValidationStatus.REFUSED)) {
						refusedProspects.add(newProspectWaitingForValidation.getIdentifier());
					} else if (newProspectWaitingForValidation.getStatus().equals(UserValidationStatus.MERGED)) {
						SendResultDto mergedProspectMapping = new SendResultDto(
								newProspectWaitingForValidation.getIdentifier(),
								newProspectWaitingForValidation.getIdprospect());
						mergedProspectWatingValidation.add(mergedProspectMapping);
					}
				}
			}
			List<Long> updatedProspectsWaitingForValidation = new ArrayList<>();

			if (receiveRequestInput.getUpdatedProspectWaitingForValidation().size() > 0) {
				updatedProspectsWaitingForValidation = prospectRepository.getUpdatedProspectsWaitingForApproval(
						receiveRequestInput.getUpdatedProspectWaitingForValidation());
			}

			List<ProspectDto> prospects = prospectsAffectationService.findprospectsByUser(delegate.getId());

			for (Long alreadyExistingProspectId : receiveRequestInput.getExistingProspects()) {
				boolean exist = false;
				for (ProspectDto affectedProspect : prospects) {
					if (affectedProspect.getIdentifier().equals(alreadyExistingProspectId)) {
						exist = true;
						break;
					}
				}
				if (!exist) {
					deletedProspects.add(alreadyExistingProspectId);						
				}
			}

			if (updatedProspectsWaitingForValidation != null && !updatedProspectsWaitingForValidation.isEmpty()) {

				for (Long prospectWaitingForValidation : updatedProspectsWaitingForValidation) {

					for (ProspectDto prospect : prospects) {
						if (prospectWaitingForValidation != null
								&& prospectWaitingForValidation.equals(prospect.getIdentifier())) {
							prospects.remove(prospect);
							break;
						}
					}
				}
			}

			List<SendResultDto> mergedProspects = new ArrayList<>();

			if (receiveRequestInput.getExistingProspects().size() > 0) {
				List<Prospect> mergedProspect = prospectRepository
						.getMergedProspects(receiveRequestInput.getExistingProspects());

				for (Prospect prospect : mergedProspect) {
					SendResultDto mergedProspectMapping = new SendResultDto(prospect.getIdentifier(),
							prospect.getIdprospect());
					mergedProspects.add(mergedProspectMapping);
				}
			}

			mergedProspects.addAll(mergedProspectWatingValidation);

			if (receiveRequestInput.isFirstSync()) {

				List<ProspectDto> newAndUpdatedProspects = prospectService.getNewAndUpdated(userId);

				for (ProspectDto newAndUpdatedProspect : newAndUpdatedProspects) {

					prospects.removeIf(
							prospect -> prospect.getIdentifier().equals(newAndUpdatedProspect.getIdprospect()));

				}

				prospects.addAll(newAndUpdatedProspects);

			}

			// get response for entities waiting for validation
			List<ExpenseReportDto> validationOfExpense = noteFraisService
					.findExpenseValidationByUser(receiveRequestInput.getExpenseWaitingValidation(), delegate.getId());
			List<ActionMarketingResponseDto> validationOfActionMarketing = actionMarketingService
					.findActionMarketingByUser(userId);
			List<OpportunityNoteResponseDto> validationOfOpportunityNote = opportunityNoteService
					.findOpportunityNoteByUser(userId);

			receiveResponseDtoBuilder
					.buildProspectDto(prospects, mergedProspects, deletedProspects, refusedProspects, prospectsValidationResponse)

					.buildValidationOfExpense(validationOfExpense)
					.buildValidationOfActionMarketing(validationOfActionMarketing)
					.buildValidationOfOpportunityNote(validationOfOpportunityNote);

			/* Get only updated data on server */
			List<ProductDto> products = productService.getNewVersionProducts(receiveRequestInput.getExistingProducts(),
					delegate.getUser().getId());

			/* Get the rest of the data */

			Date predictionDate = new Date();
			if (receiveRequestInput.getMaxPredictionDate() != null) {
				predictionDate = receiveRequestInput.getMaxPredictionDate();
				predictionDate.setMonth(predictionDate.getMonth() + 1);
			}
			predictionDate.setDate(1);
			List<ProspectOrderPredictionResponse> prospectsOrderPredictionResponse = planningRepository
					.getProspectsOrderPrediction(delegate.getId(), predictionDate);
			WorkTypeConverter workTypeConverter = new WorkTypeConverter();
			List<Sector> sectors = prospectsAffectationRepository.findDelegateSectors(delegate.getId());
			List<SectorDto> sectorDtos = convertSectorsToDto(sectors);
			List<LocalityDto> localityDtos = convertLocalitiesToDto(sectors);
			List<SpecialityDto> specialities = specialityService.findAll();
			List<ProspectTypeDto> prospectTypes = prospectTypeService.findAll();
			List<EstablishmentDto> establishments = establishmentService
					.findEstablishmentByAffectedSectors(delegate.getId());
			List<ActivityTypeDto> activityTypes = activityTypeService.findAll();
			List<PotentialDto> potentials = potentialService.findAll();
			List<RangeDto> gammesDtoList = gammeService.findByUserRange(delegate.getUser().getId());
			// List<GammeDto> gammesDtoList = gammeService.findAll();
			// List<WholesalerDto> wholesalers = wholesalerService.getWholesalersByStatus();
			List<WholesalerDto> wholesalers = prospectService.getWholesalersByStatus(true);
			List<ProductPotentielDto> potentialProducts = potentialProductService.getAllPotentialProducts();
			List<ExpenseTypeDto> expenseTypes = typeNoteFraisService.findByUser(delegate.getId());
			List<GiftDto> gadgets = gadgetService.findAll();
			List<MinimizedChargePlan> loadPlanToSend = buildChargePlanList();
			List<NotificationDto> notificationsDtos = notificationService
					.findDelegateNotification(receiveRequestInput.getLastNotificationId(), delegate.getUser().getId());
			ConfigurationDto config = configurationService.findConfiguration();
			List<GoalDto> goalDto = goalService.getGoalByUser(userId);
			List<GoalItemDto> goalItemDto = goalService.getGoalItemByUser(userId);
			List<PurchaseOrderTemplateDto> purchaseOrderTemplateDto = purchaseOrderTemplateService
					.getPurchaseOrderTemplateByUser(delegate.getId());
			List<FreeQuantityRuleItemDto> freeQuantityRuleItemDto = freeQuantityRuleService.findFreeQuantityRules();
			List<UserDto> userDto = userService.findUsers();
			List<NoteDto> notes = noteService.findAll();
			if(receiveRequestInput.getLastReceiveDate() == null) {
				Calendar cal = Calendar.getInstance();
				//Date today = cal.getTime();
				cal.add(Calendar.YEAR, -1); // to get previous year add -1
				receiveRequestInput.setLastReceiveDate(cal.getTime());
			}
			List<VisitDto> doubleVisits = getDoubleVisits(delegate, receiveRequestInput.getLastReceiveDate());
			List<VisitProductDto> doubleVisitProducts = getDoubleVisitProducts(delegate, receiveRequestInput.getLastReceiveDate());
			
			config.setWorkType(workTypeConverter.convertToDatabaseColumn(delegate.getWorkType()));
			config.setWorkingDays(delegate.getWorkingDaysPerWeek());

			receiveResponseDtoBuilder.buildSectors(sectorDtos).buildLocalities(localityDtos)
					.buildSpecialities(specialities).buildProspectTypes(prospectTypes)
					.buildEstablishments(establishments).buildActivitysDto(activityTypes)
					.buildPotentialDtoDto(potentials).buildProductDto(products).buildGammeDto(gammesDtoList)
					.buildWholesalerDto(wholesalers).buildPotentialProductDto(potentialProducts)
					.buildExpenseTypeDto(expenseTypes).buildGadgetDto(gadgets).buildChargePlanDto(loadPlanToSend)
					.buildNotifications(notificationsDtos)
					.buildProspectsOrderPrediction(prospectsOrderPredictionResponse).buildConfig(config)
					.buildGoal(goalDto).buildGoalItems(goalItemDto).buildUsers(userDto)
					.buildPurchaseOrderTemplates(purchaseOrderTemplateDto)
					.buildFreeQuantityRuleItems(freeQuantityRuleItemDto)
					.buildDoubleNotes(notes)
					.buildDoubleVisits(doubleVisits)
					.buildDoubleVisitProducts(doubleVisitProducts);

			/* Prepare data on first Sync */
			if (receiveRequestInput.isFirstSync()) {

				List<VisitDto> visitsDtoList = new ArrayList<>();
				List<VisitProductDto> visitsProductsDtoList = new ArrayList<>();
				List<Visit> visits = getProspectVisits(receiveRequestInput, delegate.getId());
				visitsDtoList = convertVisitsList(visits, user);
				List<VisitsProducts> visitsProducts = findVisitProduct(receiveRequestInput, delegate.getId());
				if (visitsProducts != null) {
					visitsProductsDtoList = convertVisitsProductList(visitsProducts, user);
				}
				planningDtoList = planningService.findPlanningByUser(delegate.getId());
				planningValidations = planningService.findAllPlanningValidationByUser(delegate.getId());
				List<ActionMarketingResponseDto> actionMarketing = actionMarketingService
						.findActionMarketingByUser(userId);
				List<ActivityDto> activities = activityService.findActivityByUser(delegate.getId());

				List<ExpenseReportDto> noteFrais = noteFraisService.getAllByUser(delegate.getId());
				List<PurchaseOrderDto> purchaseOrders = purchaseOrderService.getAllByUser(delegate.getId());
				List<RecoveryDto> recoveries = recoveryService.getAllByUser(delegate.getId());
				List<AttachmentDto> attachments = attachmentService.getAllByUser(delegate.getId());

				receiveResponseDtoBuilder.buildVisitsDto(visitsDtoList, visitsProductsDtoList)

						.buildActionMarketing(actionMarketing).buildActivity(activities).buildExpensesDto(noteFrais)
						.buildPurchaseOrderDto(purchaseOrders).buildRecoveryDto(recoveries)
						.buildAttachmentDto(attachments);

			}
			// get collegue same affected prospects visits
			else {
				// TODO enable this using config and correct insertion in mobile (using
				// identifier and user)
				/*
				 * List<Visit> visits =
				 * visitRepository.getCollegueSameAffectedProspectVisits(userId);
				 * 
				 * if(visits != null && visits.size() > 0) {
				 * 
				 * List<VisitDto> visitsDtoList = convertVisitsList(visits, delegate);
				 * List<VisitsProducts> visitsProducts =
				 * visitsProductsRepository.findCollegueSameProspectVisits(userId);
				 * List<VisitProductDto> visitsProductsDtoList =
				 * convertVisitsProductList(visitsProducts, delegate);
				 * receiveResponseDtoBuilder.buildVisitsDto(visitsDtoList,
				 * visitsProductsDtoList);
				 * 
				 * }
				 */

				for (Long alreadyExistingPlanningId : receiveRequestInput.getExistingPlannings()) {
					Planning p = planningRepository.findByIdentifier(alreadyExistingPlanningId, delegate.getId());
					if (p == null) {
						deletedPlannings.add(alreadyExistingPlanningId);
					}
				}
				if(receiveRequestInput.getExistingPlanningValidations() != null) {
					for (Long alreadyExistingPlanningValidationId : receiveRequestInput.getExistingPlanningValidations()) {
						PlanningValidation pv = planningValidationRepository
								.findByIdentifier(alreadyExistingPlanningValidationId, delegate.getId());
						if (pv == null) {
							deletedPlanningValidations.add(alreadyExistingPlanningValidationId);
						}
					}
				}

				planningValidations = planningService
						.findPlanningValidationByUser(receiveRequestInput.getPlanningWaitingValidation(), userId);
				List<PlanningValidationDto> newPlanningValidation = planningService.findNewPlanningValidation(delegate,
						receiveRequestInput.getCurrentWeekDate());
				planningValidations.addAll(newPlanningValidation);
				planningDtoList = planningService.findNewPlanning(delegate, receiveRequestInput.getCurrentWeekDate());

			}

			receiveResponseDtoBuilder.buildPlanningDto(planningDtoList);
			receiveResponseDtoBuilder.buildValidationOfPlanning(planningValidations, deletedPlannings,
					deletedPlanningValidations);

			// List<Integer> recommendedProspects = new ArrayList<>();
			// String recommendedProspectsDate = null;
			// recommendedProspectsDate =
			// getRecommendedProspectsDate(synchronisationReceiveInput.getRecommendedProspectsDate());
			// recommendedProspects =
			// getRecommendedProspects(synchronisationReceiveInput.getRecommendedProspectsDate(),userId);
			// receiveResponseDtoBuilder.buildRecommendedProspects(recommendedProspects);
			// receiveResponseDtoBuilder.buildRecommendedProspectsDate(recommendedProspectsDate);
			LOG.info(logSyncMarker, "End receiving data user id = " + userId + " User name " + delegate.getFirstName());

			return receiveResponseDtoBuilder.getReceiveResponseDto();

		} else {
			return new ReceiveResponsedDto();
		}

	}

	private Integer getMinRolesRank(User user) {

		Integer min = Collections.min(user.getRoles(), Comparator.comparing(s -> s.getRank())).getRank();
		return min;
	}

	private List<VisitDto> convertVisitsList(List<Visit> visits, User user) {

		List<VisitDto> reportDtoList = new ArrayList<>();
		DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

		for (Visit visit : visits) {

			if (user.getId() == visit.getDelegate().getUser().getId()
					|| getMinRolesRank(user) <= getMinRolesRank(visit.getDelegate().getUser())) {

				VisitDto visitDto = new VisitDto();
				visitDto.setIdentifier(visit.getIdentifier());
				visitDto.setDay(dateFormat.format(visit.getVisitDate()));
				visitDto.setVisitDate(visit.getVisitDate());
				visitDto.setGeneralNote(visit.getGeneralNote());
				if (visit.getGadget() != null) {
					visitDto.setGadgetId(visit.getGadget().getId());
					visitDto.setGadgetQuantity(visit.getGadgetQuantity());
				}
				visitDto.setPatientNumber(visit.getPatientNumber());
				visitDto.setProspectId(visit.getProspect().getIdentifier());
				visitDto.setUserId(visit.getDelegate().getUser().getId());
				visitDto.setUserName(visit.getDelegate().getFirstName() + ' ' + visit.getDelegate().getLastName());
				if(visit.getDoubleVisit() != null && visit.getDoubleVisit().getDelegate().getUser() != null) {
					visitDto.setCompanionId(visit.getDoubleVisit().getDelegate().getUser().getId());
				}
				reportDtoList.add(visitDto);
			}

		}
		return reportDtoList;
	}

	private List<VisitProductDto> convertVisitsProductList(List<VisitsProducts> visitsProducts, User user) {

		List<VisitProductDto> visitProductDtoList = new ArrayList<>();

		for (VisitsProducts visitProduct : visitsProducts) {

			if (user.getId() == visitProduct.getVisit().getDelegate().getUser().getId()
					|| getMinRolesRank(user) <= getMinRolesRank(visitProduct.getVisit().getDelegate().getUser())) {

				VisitProductDto visitProductDto = new VisitProductDto();

				visitProductDto.setComment(visitProduct.getComment());
				visitProductDto.setPrescriptionQuantity(visitProduct.getPrescriptionQuantity());
				visitProductDto.setOrderQuantity(visitProduct.getOrderQuantity());
				visitProductDto.setProductId(visitProduct.getProduct().getId());
				visitProductDto.setRank(visitProduct.getRank());
				visitProductDto.setSaleQuantity(visitProduct.getSaleQuantity());
				visitProductDto.setSampleQuantity(visitProduct.getSampleQuantity());
				visitProductDto.setFreeOrder(visitProduct.getFreeOrder());
				visitProductDto.setLabGratuity(visitProduct.getLabGratuity());
				visitProductDto.setSmily(visitProduct.getSmily());
				visitProductDto.setUrgent(visitProduct.isUrgent());
				visitProductDto.setIdentifier(visitProduct.getIdentifier());
				visitProductDto.setVisitId(visitProduct.getVisit().getIdentifier());
				if (visitProduct.getPurchaseOrder() != null)
					visitProductDto.setPurchaseOrderId(visitProduct.getPurchaseOrder().getIdentifier());
				visitProductDtoList.add(visitProductDto);
			}

		}
		return visitProductDtoList;
	}

	private List<MinimizedChargePlan> buildChargePlanList() {

		List<MinimizedChargePlan> loadPlanDtos = new ArrayList<>();
		List<LoadPlanItem> loadPlanList = loadPlanItemRepository.findItemOfActiveLoadPlan();
		if (loadPlanList != null) {
			for (LoadPlanItem loadPlan : loadPlanList) {
				MinimizedChargePlan loadPlanDto = new MinimizedChargePlan();
				loadPlanDto.setProductId(loadPlan.getProduct().getId());
				loadPlanDto.setSpecialityId(loadPlan.getSpeciality().getId());
				loadPlanDto.setRank(loadPlan.getRank());

				loadPlanDtos.add(loadPlanDto);
			}
		}

		return loadPlanDtos;
	}

	private String getRecommendedProspectsDate(String ReceivedDate) {
		String recommendedProspectsDate = null;
		Date currentDate = new Date();
		Integer currentMonth = currentDate.getMonth();
		DateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");
		String strDate = dateFormat.format(currentDate);
		try {
			if (ReceivedDate != null && !ReceivedDate.isEmpty()) {
				Date predictionDate = new SimpleDateFormat("dd/MM/yyyy").parse((ReceivedDate));
				Integer recommendedProspectsMonth = predictionDate.getMonth();

				if (recommendedProspectsMonth != currentMonth) {

					recommendedProspectsDate = strDate;
				}
			} else {
				recommendedProspectsDate = strDate;
			}

		} catch (ParseException e) {

			LOG.error(e.getMessage(), e);

		}
		return recommendedProspectsDate;
	}

	private List<Visit> getProspectVisits(ReceiveRequestInput synchronisationReceiveInput, Long userId) {

		// List<Visit> visits = visitRepository.getAffectedProspectVisits(userId);
		List<Visit> visits = visitRepository.getDelegateVisits(userId);
		if (visits == null) {
			visits = new ArrayList<Visit>();
		}
		visits.addAll(visitRepository.getNewProspectWaitingForValidationVisits(userId));
		return visits;

	}

	private List<VisitDto> getDoubleVisits(Delegate delegate, Date lastReceiveDate) {

		// List<Visit> visits = visitRepository.getAffectedProspectVisits(userId);
		List<Visit> visits = visitRepository.getDoubleVisits(delegate.getId(), lastReceiveDate);
		List<VisitDto> visitDtoList = convertVisitsList(visits, delegate.getUser());
		return visitDtoList;

	}
	
	private List<VisitProductDto> getDoubleVisitProducts(Delegate delegate, Date lastReceiveDate) {

		// List<Visit> visits = visitRepository.getAffectedProspectVisits(userId);
		List<VisitsProducts> visitProducts = visitsProductsRepository.getDoubleVisitProducts(delegate.getId(), lastReceiveDate);
		List<VisitProductDto> visitProductList = convertVisitsProductList(visitProducts, delegate.getUser());
		return visitProductList;

	}
	
	private List<VisitsProducts> findVisitProduct(ReceiveRequestInput synchronisationReceiveInput, Long userId) {

		List<VisitsProducts> visitsProducts = visitsProductsRepository.findProspectVisits(userId);
		if (visitsProducts == null) {
			visitsProducts = new ArrayList<VisitsProducts>();
		}
		visitsProducts.addAll(visitsProductsRepository.getNewProspectWaitingForValidationVisitsProducts(userId));
		return visitsProducts;

	}

	private List<SectorDto> convertSectorsToDto(List<Sector> sectors) {

		List<SectorDto> sectorDtos = new ArrayList<>();

		if (sectors != null && !sectors.isEmpty()) {
			for (Sector sector : sectors) {
				SectorDto dto = new SectorDto();
				dto.setId(sector.getId());
				dto.setName(sector.getName());
				sectorDtos.add(dto);
			}

		}

		return sectorDtos;
	}

	private List<LocalityDto> convertLocalitiesToDto(List<Sector> sectors) {

		List<LocalityDto> localityDtos = new ArrayList<>();
		for (Sector sector : sectors) {
			Set<Locality> localities = sector.getLocalities();
			if (localities != null && !localities.isEmpty()) {
				for (Locality locality : localities) {
					LocalityDto localityDto = new LocalityDto();
					localityDto.setId(locality.getId());
					localityDto.setName(locality.getName());
					localityDto.setSectorId(locality.getSector().getId());
					localityDtos.add(localityDto);
				}
			}
		}
		return localityDtos;

	}

	public List<Long> saveProspects(List<ProspectDto> prospectDtoList, Delegate delegate) throws BirdnotesException {

		List<Long> prospectChangesIds = new ArrayList<>();

		if (prospectDtoList != null && !prospectDtoList.isEmpty()) {

			for (ProspectDto prospectDto : prospectDtoList) {

				LOG.info(logSyncMarker,
						"Start saving prospect of user name " + delegate.getFirstName() + " prospect identifier "
								+ prospectDto.getIdentifier() + " prospect status " + prospectDto.getStatus());

				if (UserValidationStatus.NEW.equals(prospectDto.getStatus())) {

					LOG.info(logSyncMarker, "ProspectDto identifier: " + prospectDto.getIdentifier() + " Name :"
							+ prospectDto.getFullName() + " Status :" + prospectDto.getStatus());

					Prospect prospect = prospectRepository.findValidOrNewProspects(prospectDto.getIdentifier(),
							UserValidationStatus.NEW, UserValidationStatus.VALID);

					if (prospect == null) {
						LOG.info(logSyncMarker,
								"Prospect result of findValidOrNewProspects is null prospectDto identifier: "
										+ prospectDto.getIdentifier() + " prospectDto Status :"
										+ prospectDto.getStatus());
						prospect = new Prospect();
						prospectService.validateAndConvertProspect(prospectDto, prospect);
						prospect.setUser(delegate.getUser());
						prospect.setCreationDate(new Date());
						prospect.setIdentifier(prospectDto.getIdentifier());

						Prospect mergedProspect = prospectRepository.save(prospect);
						notificationService.sendNotificationUsingWorkflow(delegate.getUser(), mergedProspect.getId(),
								"ADD_UPDATE_PROSPECT", prospect);
						validationStepService.addValidationStatus("NEW_UPDATE_PROSPECT", mergedProspect.getId(),
								delegate.getUser().getId());
					} else {
						LOG.info(logSyncMarker,
								"Prospect result of findValidOrNewProspects is not null identifier : "
										+ prospect.getIdentifier() + ", Name :" + prospect.getFirstName() + " "
										+ prospect.getLastName() + ", Status :" + prospect.getStatus());
						if (prospect.getStatus().equals(UserValidationStatus.VALID)) {

							setterOfProspectToUpdate(prospect, prospectDto);
							prospect.setStatus(UserValidationStatus.NEW);
							notificationService.sendNotificationUsingWorkflow(delegate.getUser(), prospect.getId(),
									"ADD_UPDATE_PROSPECT", prospect);
							validationStatusRepository.deleteByProspectId(prospect.getId());
							validationStepService.addValidationStatus("NEW_UPDATE_PROSPECT", prospect.getId(),
									delegate.getUser().getId());
							prospectRepository.save(prospect);
							// this.createProspectUpdate(prospectDto, delegate);
						} else if (prospect.getStatus().equals(UserValidationStatus.NEW)) {
							setterOfProspectToUpdate(prospect, prospectDto);
							prospectRepository.save(prospect);
						}

					}

					prospectChangesIds.add(prospectDto.getIdentifier());

				}

				if (UserValidationStatus.UPDATE.equals(prospectDto.getStatus())) {

					Prospect prospect = prospectRepository.findByIdprospectAndStatus(prospectDto.getIdentifier(),
							UserValidationStatus.UPDATE);

					if (prospect == null) {

						this.createProspectUpdate(prospectDto, delegate);

					} else {
						setterOfProspectToUpdate(prospect, prospectDto);
						prospect.setIdentifier(null);
						prospectRepository.save(prospect);
					}
					prospectChangesIds.add(prospectDto.getIdentifier());

				}
				LOG.info(logSyncMarker, "End saving prospect");

			}

		}
		return prospectChangesIds;
	}

	private void createProspectUpdate(ProspectDto prospectDto, Delegate delegate) throws BirdnotesException {

		Prospect prospect = new Prospect();
		prospectService.validateAndConvertProspect(prospectDto, prospect);
		prospect.setUser(delegate.getUser());
		prospect.setCreationDate(new Date());

		prospect = prospectRepository.save(prospect);
		prospect.setIdprospect(prospectDto.getIdentifier());
		prospect.setIdentifier(null);
		prospectRepository.save(prospect);

		notificationService.sendNotificationUsingWorkflow(delegate.getUser(), prospect.getId(), "ADD_UPDATE_PROSPECT",
				prospect);
		validationStepService.addValidationStatus("NEW_UPDATE_PROSPECT", prospect.getId(), delegate.getUser().getId());
	}

	private List<Long> saveVisits(List<VisitDto> visitDtoList, Set<VisitDto>doubleVisitList, Delegate user) throws BirdnotesException {

		List<Long> savedVisitIds = new ArrayList<>();
		for (VisitDto visitDto : visitDtoList) {
			Prospect prospect = null;

			Visit visit = visitRepository.findByIdentifier(visitDto.getIdentifier(), user.getId());

			if (visit == null) {
				visit = new Visit();
				visit.setIdentifier(visitDto.getIdentifier());
				prospect = prospectService.getProspect(visitDto.getProspectId());
				if (prospect == null) {
					LOG.error(logSyncMarker,
							"Prospect of visit is null, prospect identifier = " + visitDto.getProspectId()
									+ " user name : " + user.getFirstName() + " user id : " + user.getId());
					continue;

				}
				visit.setProspect(prospect);
				visit.setDelegate(user);

			} else if (!visit.getProspect().getIdentifier().equals(visitDto.getProspectId())) {
				LOG.error(logSyncMarker,
						"Prospect of existing visit is different from prospect of visit sent "
						+ " | saved_prospect_identifier: " + visit.getProspect().getIdentifier() 
						+ " | saved_visit_id : "+ visit.getId()
						+ " | new_prospect_identifier: " +visitDto.getProspectId() 
						+ " | user_name : " + user.getFirstName() 
						+ " | user_id : " + user.getId());
				throw new BirdnotesException(
						"Prospect of existing visit is different from prospect of visit sent "
						+ " | saved_prospect_identifier: " + visit.getProspect().getIdentifier() 
						+ " | saved_visit_id : "+ visit.getId()
						+ " | new_prospect_identifier: " +visitDto.getProspectId() 
						+ " | user_name : " + user.getFirstName() 
						+ " | user_id : " + user.getId());
			}
			if (visitDto.getGadgetId() != null) {
				Gift gadget = gadgetRepository.findOne(visitDto.getGadgetId());
				visit.setGadget(gadget);
				visit.setGadgetQuantity(visitDto.getGadgetQuantity());
			}			
			if (visitDto.getLng() != null && visitDto.getLat() != null) {
				visit.setLocation(new Location());
				visit.getLocation().setLatitude(visitDto.getLat());
				visit.getLocation().setLongitude(visitDto.getLng());
				visit.getLocation().setDate(visitDto.getVisitDate());
				visit.getLocation().setDelegate(user);
				locationRepository.save(visit.getLocation());
			}

			visit.setGeneralNote(visitDto.getGeneralNote());
			visit.setPatientNumber(visitDto.getPatientNumber());
			visit.setSynchronisationDate(new Date());
			visit.setVisitDate(visitDto.getVisitDate());
			
			Visit savedVisit = visitRepository.save(visit);
			if (visitDto.getCompanionId() != null) {
				visitDto.setId(savedVisit.getId());
				doubleVisitList.add(visitDto);				
			}
			savedVisitIds.add(visitDto.getIdentifier());

		}
		return savedVisitIds;
	}
	
	
	
	List<Long> saveVisitProduct(List<VisitProductDto> visitProductDtos, Set<VisitDto> doubleVisitList, Set<VisitProductDto> doubleVisitProductDtos, Delegate delegate) {

		List<Long> savedVisitProductId = new ArrayList<>();

		List<VisitsProducts> VisitsProductsWithComment = new ArrayList<>();

		
		for (VisitProductDto visitProductDto : visitProductDtos) {

			try {
				VisitsProducts visitsProducts = visitsProductsRepository
						.findByIdentifier(visitProductDto.getIdentifier(), delegate.getId());
				Visit visit = visitRepository.findByIdentifier(visitProductDto.getVisitId(), delegate.getId());
				if (visitsProducts == null) {
					visitsProducts = new VisitsProducts();
					visitsProducts.setIdentifier(visitProductDto.getIdentifier());

					if (visit == null) {
						LOG.error(logSyncMarker,
								"Error visit of visit product is null, visit identifier = "
										+ visitProductDto.getVisitId() + ", user name : " + delegate.getFirstName()
										+ ", user id : " + delegate.getId());
						continue;
					}
					visitsProducts.setVisit(visit);

					Product product = productRepository.findOne(visitProductDto.getProductId());
					visitsProducts.setProduct(product);
				}
				PurchaseOrder purchaseOrder = purchaseOrderRepository
						.findByIdentifier(visitProductDto.getPurchaseOrderId(), delegate.getId());
				/*
				 * else if
				 * (!visitsProducts.getVisit().getIdentifier().equals(visitProductDto.getVisitId
				 * ())) { visitsProducts =
				 * visitsProductsRepository.findByVisitAndProduct(visitProductDto.getVisitId(),
				 * visitProductDto.getProductId(), user.getId()); LOG.error(logSyncMarker,
				 * "Existing  Visit product visitProduct_identifier: "+visitsProducts.
				 * getIdentifier()
				 * +"-- is different from visit product sent, visitProduct_identifier: "
				 * +visitProductDto.getIdentifier()
				 * +"-- user_name : "+user.getFirstName()+", user_id : "+user.getId() ); //throw
				 * new BirdnotesException("Existing Visit product visitProduct_identifier: "
				 * +visitsProducts.getIdentifier()
				 * +"-- is different from visit product sent, visitProduct_identifier: "
				 * +visitProductDto.getIdentifier()
				 * +"-- user_name : "+user.getFirstName()+", user_id : "+user.getId() ); }
				 */

				visitsProducts.setPurchaseOrder(purchaseOrder);
				visitsProducts.setComment(visitProductDto.getComment());
				visitsProducts.setOrderQuantity(visitProductDto.getOrderQuantity());
				visitsProducts.setSampleQuantity(visitProductDto.getSampleQuantity());
				visitsProducts.setRank(visitProductDto.getRank());
				visitsProducts.setSmily(visitProductDto.getSmily());
				visitsProducts.setSaleQuantity(visitProductDto.getSaleQuantity());
				visitsProducts.setUrgent(visitProductDto.isUrgent());
				visitsProducts.setPrescriptionQuantity(visitProductDto.getPrescriptionQuantity());
				visitsProducts.setFreeOrder(visitProductDto.getFreeOrder());
				visitsProducts.setLabGratuity(visitProductDto.getLabGratuity());
				VisitsProducts savedVisitProduct = visitsProductsRepository.save(visitsProducts);

				if (!visitProductDto.getComment().isEmpty()) {
					VisitsProductsWithComment.add(visitsProducts);
				}
				for(VisitDto doubleVisit : doubleVisitList) {
					if(visitProductDto.getVisitId() == doubleVisit.getIdentifier()) {
						doubleVisitProductDtos.add(visitProductDto);
					}
				}
				
				savedVisitProductId.add(visitProductDto.getIdentifier());

				if (visitProductDto.isUrgent()) {
					notificationService.sendNotificationUsingWorkflow(delegate.getUser(), savedVisitProduct.getId(),
							"URGENT_COMMENT", visit.getProspect(), visit.getVisitDate(), visitProductDto.getComment(),
							savedVisitProduct.getProduct().getName());
				}

			} catch (Exception e) {
				LOG.error(logSyncMarker, "Error creating Visit Product, identifier = " + visitProductDto.getIdentifier()
						+ ", user name : " + delegate.getFirstName() + ", user id : " + delegate.getId(), e);
			}
		}
		if (VisitsProductsWithComment.size() > 0) {
			rateVisitProductsComments(VisitsProductsWithComment);
		}

		return savedVisitProductId;
	}
	
	private List<Visit> createDoubleVisit(Set<VisitDto> doubleVisitDto) throws BirdnotesException {
		List<Visit> savedVisits = new ArrayList<>();
		for (VisitDto visitDto : doubleVisitDto) {
			Prospect prospect = null;
			Visit visit = null;
			Visit originalVisit = visitRepository.findById(visitDto.getId());
			Delegate companion = delegateRepository.findDelegateByUserId(visitDto.getCompanionId());
			if(originalVisit.getDoubleVisit() != null) {
				visit = visitRepository.findById(originalVisit.getDoubleVisit().getId());
			}
			

			if (visit == null) {
				visit = new Visit();
				visit.setIdentifier(visitDto.getIdentifier());
				prospect = prospectService.getProspect(visitDto.getProspectId());
				if (prospect == null) {
					LOG.error(logSyncMarker,
							"Prospect of visit is null, prospect identifier = " + visitDto.getProspectId()
									+ " user name : " + companion.getFirstName() + " user id : " + companion.getId());
					continue;

				}
				ProspectsAffectation prospectsAffectation = prospectsAffectationRepository.findByDelegateAndProspect(companion.getId(), prospect.getId());
				if(prospectsAffectation == null) {
					prospectsAffectation = new ProspectsAffectation();
					prospectsAffectation.setDelegate(companion);
					prospectsAffectation.setProspect(prospect);
					prospectsAffectationRepository.save(prospectsAffectation);
				}
				visit.setProspect(prospect);
				visit.setDelegate(companion);

			} else if (!visit.getProspect().getIdentifier().equals(visitDto.getProspectId())) {
				LOG.error(logSyncMarker,
						"Prospect of existing visit is different from prospect of visit sent "
						+ " | saved_prospect_identifier: " + visit.getProspect().getIdentifier() 
						+ " | saved_visit_id : "+ visit.getId()
						+ " | new_prospect_identifier: " +visitDto.getProspectId() 
						+ " | user_name : " + companion.getFirstName() 
						+ " | user_id : " + companion.getId());
				throw new BirdnotesException(
						"Prospect of existing visit is different from prospect of visit sent "
						+ " | saved_prospect_identifier: " + visit.getProspect().getIdentifier() 
						+ " | saved_visit_id : "+ visit.getId()
						+ " | new_prospect_identifier: " +visitDto.getProspectId() 
						+ " | user_name : " + companion.getFirstName() 
						+ " | user_id : " + companion.getId());
			}
			if (visitDto.getGadgetId() != null) {
				Gift gadget = gadgetRepository.findOne(visitDto.getGadgetId());
				visit.setGadget(gadget);
				visit.setGadgetQuantity(visitDto.getGadgetQuantity());
			}			
			if (visitDto.getLng() != null && visitDto.getLat() != null) {
				visit.setLocation(new Location());
				visit.getLocation().setLatitude(visitDto.getLat());
				visit.getLocation().setLongitude(visitDto.getLng());
				visit.getLocation().setDate(visitDto.getVisitDate());
				visit.getLocation().setDelegate(companion);
				locationRepository.save(visit.getLocation());
			}

			visit.setGeneralNote(visitDto.getGeneralNote());
			visit.setPatientNumber(visitDto.getPatientNumber());
			visit.setSynchronisationDate(new Date());
			visit.setVisitDate(visitDto.getVisitDate());
			
			
			visit.setDoubleVisit(originalVisit);
			Visit savedVisit = visitRepository.save(visit);
			
			originalVisit.setDoubleVisit(savedVisit);
			visitRepository.save(originalVisit);
			
			savedVisits.add(savedVisit);
		}
		return savedVisits;
			
	}
	
	private void createDoubleVisitProduct(List<Visit> doubleVisitList)
			throws BirdnotesException {
		for (Visit doubleVisit : doubleVisitList) {
			Visit originalVisit = visitRepository.findByDoubleVisit(doubleVisit.getId());
			List<VisitsProducts> originalVisitProducts = visitsProductsRepository.findByVisitId(originalVisit.getId());
			List<VisitsProducts> doubleVisitProducts = visitsProductsRepository.findByVisitId(doubleVisit.getId());
			for (VisitsProducts originalVisitProduct : originalVisitProducts) {
				//doubleVisitProducts not yes created for the first time
				if (doubleVisitProducts.size() == 0) {
					VisitsProducts doubleVisitProduct = new VisitsProducts();
					doubleVisitProduct.setVisit(doubleVisit);
					doubleVisit.setIdentifier(new Date().getTime());
					setVisitProduct(originalVisitProduct, doubleVisitProduct);
				} else {
					//doubleVisitProducts are already created
					boolean vpExist = checkVisitProductExist(originalVisitProduct, doubleVisitProducts);
					//if there is a new visit product
					if (vpExist == false) {
						VisitsProducts addedVisitProduct = new VisitsProducts();
						addedVisitProduct.setVisit(doubleVisit);
						addedVisitProduct.setIdentifier(new Date().getTime());
						setVisitProduct(originalVisitProduct, addedVisitProduct);
					}else {
						for (VisitsProducts doubleVisitProduct : doubleVisitProducts) {
							//check if vp exist 
							 if(doubleVisitProduct.getProduct().getId() == originalVisitProduct.getProduct().getId()) {
								setVisitProduct(originalVisitProduct, doubleVisitProduct);
							}
						}
					}
				}
			}
		}

	}
	
	private boolean checkVisitProductExist(VisitsProducts visitProductToFind, List<VisitsProducts>visitProductList) {
		boolean vpExist = false;
		for(VisitsProducts visitProduct : visitProductList) {
			if(visitProduct.getPurchaseOrder() == null && visitProduct.getProduct().getId() == visitProductToFind.getProduct().getId()) {
				vpExist = true;
			}
		}
		return vpExist;
	}

	private void setVisitProduct(VisitsProducts originalVisitProduct, VisitsProducts newVisitProduct) {
		newVisitProduct.setIdentifier(new Date().getTime());
		newVisitProduct.setComment(originalVisitProduct.getComment());
		newVisitProduct.setCommentRating(originalVisitProduct.getCommentRating());
		newVisitProduct.setFreeOrder(originalVisitProduct.getFreeOrder());
		newVisitProduct.setLabGratuity(originalVisitProduct.getLabGratuity());
		newVisitProduct.setOrderQuantity(originalVisitProduct.getOrderQuantity());
		newVisitProduct.setPrescriptionQuantity(originalVisitProduct.getPrescriptionQuantity());
		newVisitProduct.setProduct(originalVisitProduct.getProduct());		
		newVisitProduct.setPurchaseOrder(originalVisitProduct.getPurchaseOrder());
		newVisitProduct.setRank(originalVisitProduct.getRank());
		newVisitProduct.setSaleQuantity(originalVisitProduct.getSaleQuantity());
		newVisitProduct.setSampleQuantity(originalVisitProduct.getSampleQuantity());
		newVisitProduct.setSmily(originalVisitProduct.getSmily());
		newVisitProduct.setUrgent(originalVisitProduct.isUrgent());
		
		visitsProductsRepository.save(newVisitProduct);
	}
	
	private void saveDoubleVisits(Set<VisitDto> doubleVisitList, Set<VisitProductDto> doubleVisitProductList) throws BirdnotesException {	
		List<Visit>savedDoubleVisits = createDoubleVisit(doubleVisitList);
		createDoubleVisitProduct(savedDoubleVisits);
	}

	private void rateVisitProductsComments(List<VisitsProducts> visitsProductsWithComment) {

		Thread commentThread = new Thread(new Runnable() {

			@Override
			public void run() {

				try {
					StringBuilder text = new  StringBuilder();
					URI uri = new URI(mlServerUrl + "/comment");
					HttpHeaders headers = new HttpHeaders();
					headers.set("Authorization", "Bearer " + mlServerToken);
					headers.set("Content-Type", "application/json");
					int index = 0;
					CommentClassification[] commentsClassificationiList = new CommentClassification[visitsProductsWithComment
							.size()];
					for (VisitsProducts visitsProducts : visitsProductsWithComment) {
						commentsClassificationiList[index] = new CommentClassification(visitsProducts.getComment(),
								visitsProducts.getId());
						index++;
					}
					PredictionResponse predictionRequest = new PredictionResponse();
					predictionRequest.setComments(commentsClassificationiList);
					HttpEntity<PredictionResponse> request = new HttpEntity<>(predictionRequest, headers);
					ResponseEntity<PredictionResponse> response = restTemplate.postForEntity(uri, request,
							PredictionResponse.class);

					Map<Long, String> classifications = new HashMap<Long, String>();

					for (int i = 0; i < response.getBody().getReuslts().length; i++) {
						classifications.put(response.getBody().getReuslts()[i].getVisitProductId(),
								response.getBody().getReuslts()[i].getScore());
					}
					
					
					for (VisitsProducts visitsProduct : visitsProductsWithComment) {

						visitsProduct.setCommentRating(classifications.get(visitsProduct.getId()));
						visitsProductsRepository.updateCommentRating(visitsProduct.getId(), visitsProduct.getCommentRating());
						
						if ( commentRatingToNotify != null && commentRatingToNotify.length > 0 && Arrays.stream(commentRatingToNotify).anyMatch(visitsProduct.getCommentRating()::equals)) {
							text.append("- ");
							text.append(visitsProduct.getProduct().getName());
							text.append(" : ");
							text.append(visitsProduct.getComment());
							text.append("\n");
					
							
						}
					}
					
					Visit visit = visitsProductsWithComment.get(0).getVisit();
					
					
					notificationService.sendNotificationUsingWorkflow(
							visit.getDelegate().getUser(), visit.getId(),
							"IMPORTANT_COMMENT", visit.getProspect(),
							visit.getVisitDate(), text.toString(), "");


				} catch (Exception e) {
					LOG.error(logSyncMarker ,"Error rating comment :  " , e);

				}
			}
		});

		commentThread.start();

	}

	List<Long> savePurchaseOrder(List<PurchaseOrderDto> purchaseOrderDtoList, Delegate user) throws BirdnotesException {

		List<Long> savedPurchaseOdersIds = new ArrayList<>();
		List<PurchaseOrderDto> purchaseOrderToCancelList = new ArrayList<>();

		for (PurchaseOrderDto purchaseOrderDto : purchaseOrderDtoList) {
			PurchaseOrder purchaseOrder = purchaseOrderRepository.findByIdentifier(purchaseOrderDto.getIdentifier(),
					user.getId());
			Visit visit = visitRepository.findByIdentifier(purchaseOrderDto.getVisitId(), user.getId());
			if (visit == null) {
				LOG.error(logSyncMarker,
						"Error visit is null of purchase order , visit identifier = " + purchaseOrderDto.getVisitId()
								+ ", user name : " + user.getFirstName() + ", user id : " + user.getId());
				continue;
			}
			Prospect wholesaler = prospectService.getProspect(purchaseOrderDto.getWholesalerId());

			if (purchaseOrder != null) {
				PurchaseOrderDto purchaseOrderDtoToCancel = new PurchaseOrderDto(purchaseOrder);
				purchaseOrderDtoToCancel.setEmailType(EmailType.CANCELLATION.toString());
				purchaseOrderDtoToCancel.setMailSent(false);
				purchaseOrder.setMailSent(false);
				
				purchaseOrderDtoToCancel.setDelegate(delegateToDtoConvertor.convert(purchaseOrder.getVisit().getDelegate()));
				purchaseOrderDtoToCancel.setWholesaler(convertProspectToDto.convert(wholesaler));
				try {
					purchaseOrderDtoToCancel.setOldWholesaler(convertProspectToDto.convert(purchaseOrder.getWholesaler()));
				} catch (BirdnotesException e) {
					LOG.error(logSyncMarker,
							"Old Wholesaler of purchase order is null, purchaseOrder id = " + purchaseOrder.getId()
									+ ", user name : " + user.getFirstName() + ", user id : " + user.getId());
				}
				try {
					purchaseOrderDtoToCancel.setProspect(convertProspectToDto.convert(purchaseOrder.getVisit().getProspect()));
				} catch (BirdnotesException e) {
					LOG.error(logSyncMarker,
							"purchase to cancel is null, visit id = " + purchaseOrder.getVisit().getId()
									+ ", user name : " + user.getFirstName() + ", user id : " + user.getId());
				}
				if (wholesaler != null && (purchaseOrder.getWholesaler().getId() != wholesaler.getId()
						|| (purchaseOrderDto.getAttachmentName() != null && purchaseOrder.getAttachmentName()
								.equals(purchaseOrderDto.getAttachmentName()) == false))) {

				}

				purchaseOrderToCancelList.add(purchaseOrderDtoToCancel);
				/*
				 * saveAndMailPurchaseOrder(purchaseOrder, true, purchaseOrder.getWholesaler());
				 * purchaseOrder.setMailSent(false);
				 */
			}

			if (purchaseOrder == null) {
				purchaseOrder = new PurchaseOrder();
				purchaseOrder.setIdentifier(purchaseOrderDto.getIdentifier());
				purchaseOrder.setVisit(visit);
				purchaseOrder.setMailSent(false);
			}
			if (purchaseOrderDto.getPurchaseOrderTemplateId() != null) {
				PurchaseOrderTemplate purchaseOrderTemplate = purchaseOrderTemplateRepository
						.findOne(purchaseOrderDto.getPurchaseOrderTemplateId());
				purchaseOrder.setPurchaseOrderTemplate(purchaseOrderTemplate);
			}

			purchaseOrder.setWholesaler(wholesaler);
			purchaseOrder.setAttachmentName(purchaseOrderDto.getAttachmentName());
			purchaseOrder.setAttachmentBase64(purchaseOrderDto.getAttachmentBase64());
			purchaseOrder.setPlacementMethod(purchaseOrderDto.getPlacementMethod());
			ConfigurationDto config = configurationService.findConfiguration();
			if (config.getOrderValidation().equals(OrderValidation.DELEGATE.toString())) {
				purchaseOrder.setStatus(UserValidationStatus.valueOf(purchaseOrderDto.getStatus()));
			} else {
				purchaseOrder.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			}
			PurchaseOrder mergedPurchaseOrder = purchaseOrderRepository.save(purchaseOrder);

			// set properties of purchaseOrderDto to generate, save and send purchase order
			Prospect prospect = mergedPurchaseOrder.getVisit().getProspect();
			Delegate delegate = mergedPurchaseOrder.getVisit().getDelegate();
			purchaseOrderDto.setId(mergedPurchaseOrder.getId());
			try {
				purchaseOrderDto.setWholesaler(convertProspectToDto.convert(wholesaler));
			} catch (BirdnotesException e) {
				LOG.error(logSyncMarker,
						"Wholesaler of new purchase order is null " + purchaseOrderDto.getWholesalerId()
								+ ", user name : " + user.getFirstName() + ", user id : " + user.getId());
			}
			try {
				purchaseOrderDto.setProspect(convertProspectToDto.convert(prospect));
			} catch (BirdnotesException e) {
				LOG.error(logSyncMarker,
						"Prospect of new purchase order is null " + purchaseOrderDto.getWholesalerId());
			}
			purchaseOrderDto.setDelegate(delegateToDtoConvertor.convert(delegate));
			if (!mergedPurchaseOrder.getMailSent()) {
				purchaseOrderDto.setEmailType(EmailType.VALIDATION.toString());
				/* saveAndMailPurchaseOrder(mergedPurchaseOrder, false, null); */

			}

			savedPurchaseOdersIds.add(purchaseOrderDto.getIdentifier());

		}
		purchaseOrderDtoList.addAll(purchaseOrderToCancelList);
		return savedPurchaseOdersIds;
	}

	/*
	 * public void sendCancellationMail(PurchaseOrder mergedPurchaseOrder,
	 * Wholesaler oldWholesaler) {
	 * 
	 * try { String attachmentPath = createFolderPurchaseOrder(mergedPurchaseOrder);
	 * ConfigurationDto config = configurationService.findConfiguration(); String
	 * pathLogo = config.getServerPath() + uploadUrl + logoPath + "/" +
	 * config.getLogo(); MessageFormat subjectFormat = new
	 * MessageFormat(sendCancellationMailSubject); MessageFormat messageFormat = new
	 * MessageFormat(sendCancellationMailHtmlBody);
	 * 
	 * 
	 * Prospect prospect = mergedPurchaseOrder.getVisit().getProspect(); User
	 * delegate = mergedPurchaseOrder.getVisit().getUser(); List<User> supervisors =
	 * userRepository.getSuperviserOfUser(delegate.getId()); Set<String> to = new
	 * HashSet<String>();
	 * 
	 * if (delegate.getEmail() != null && !"".equals(delegate.getEmail())) {
	 * to.add(delegate.getEmail()); } else {
	 * LOG.error("Delegate  email is null, user id = " + delegate.getId()); } for
	 * (User supervisor : supervisors) { if (supervisor.getEmail() != null) {
	 * to.add(supervisor.getEmail()); } }
	 * 
	 * for (User topUser : userRepository.getTopUsers()) { if (topUser.getEmail() !=
	 * null) { to.add(topUser.getEmail()); } }
	 * 
	 * 
	 * String[] args = { prospect.getFirstName() + " " + prospect.getLastName(),
	 * prospect.getAddress(), oldWholesaler.getName(), config.getName(),
	 * prospect.getSector().getName(), prospect.getLocality().getName() }; String
	 * subject = subjectFormat.format(args); String fileUrl = config.getServerPath()
	 * + uploadUrl + purchaseOrderPath + "/" + mergedPurchaseOrder.getId() + "/" +
	 * mergedPurchaseOrder.getAttachmentName(); String htmlBody =
	 * messageFormat.format(args);
	 * 
	 * if(!"".equals(mergedPurchaseOrder.getAttachmentName()) &&
	 * !"".equals(mergedPurchaseOrder.getAttachmentBase64())) { htmlBody = htmlBody
	 * + " <br>  <a href = '" + fileUrl +
	 * "' target ='_blank' >Cliquez ici pour voir le bon de commande en ligne </a> <br> "
	 * + IMGBALISE + pathLogo + "'>"; }
	 * 
	 * if (oldWholesaler.getEmail() != null && !"".equals(oldWholesaler.getEmail()))
	 * { to.add(oldWholesaler.getEmail()); } SaveAndMailBase64File base64EmailSender
	 * = new SaveAndMailBase64File(mergedPurchaseOrder.getAttachmentBase64(),
	 * mergedPurchaseOrder.getAttachmentName(), attachmentPath, true,
	 * javaMailSender, null, config.getName(), to.stream().toArray(String[]::new),
	 * subject, htmlBody);
	 * 
	 * Thread thread = new Thread(base64EmailSender); thread.start();
	 * 
	 * 
	 * 
	 * } catch (BirdnotesException e) {
	 * 
	 * LOG.error("Error creating folder for purchase order", e); }
	 * 
	 * }
	 */

	public void generateSaveSendEmailPO(List<PurchaseOrderDto> purchaseOrderDtoList) {

		if(purchaseOrderDtoList.size()>0) {
			String sendWholesalerMailHtmlBodyWithoutAttachment =  userService.getTranslatedLabel("sendWholesalerMailHtmlBodyWithoutAttachment");
			String sendCancellationMailSubject =  userService.getTranslatedLabel("sendCancellationMailSubject");
			String sendWholesalerMailSubject =  userService.getTranslatedLabel("sendWholesalerMailSubject");
			String sendWholesalerMailHtmlBodyWithAttachment =  userService.getTranslatedLabel("sendWholesalerMailHtmlBodyWithAttachment");
			String sendCancellationMailHtmlBody =  userService.getTranslatedLabel("sendCancellationMailHtmlBody");
		SaveAndSendMailBase64File base64EmailSender = new SaveAndSendMailBase64File(
				 javaMailSender,  purchaseOrderDtoList, configurationService, userRepository,  purchaseOrderService,
				 purchaseOrderRepository,  convertDtoToPurchaseOrder,
				 uploadUrl,  logoPath, sendWholesalerMailSubject, sendCancellationMailSubject,
				 sendCancellationMailHtmlBody, sendWholesalerMailHtmlBodyWithAttachment,
				 sendWholesalerMailHtmlBodyWithoutAttachment,  purchaseOrderPath, generatedDocumentPath, generatedDoPath, uploadPath, notificationRuleRepository);
		Thread thread = new Thread(base64EmailSender);
		thread.start();
		}

	}

	/*public void saveAndMailPurchaseOrder(PurchaseOrder mergedPurchaseOrder, Boolean isCancellation,
			Prospect oldWholesaler) {

		try {
			String attachmentPath = createFolderPurchaseOrder(mergedPurchaseOrder);
			ConfigurationDto config = configurationService.findConfiguration();
			String pathLogo = config.getServerPath() + uploadUrl + logoPath + "/" + config.getLogo();
			MessageFormat subjectFormat = new MessageFormat(sendWholesalerMailSubject);

			MessageFormat messageFormat;
			if (isCancellation == true) {
				subjectFormat = new MessageFormat(sendCancellationMailSubject);
				messageFormat = new MessageFormat(sendCancellationMailHtmlBody);
			} else {
				subjectFormat = new MessageFormat(sendWholesalerMailSubject);

				if (!"".equals(mergedPurchaseOrder.getAttachmentBase64())
						&& !"".equals(mergedPurchaseOrder.getAttachmentName())) {
					messageFormat = new MessageFormat(sendWholesalerMailHtmlBodyWithAttachment);
				} else {
					messageFormat = new MessageFormat(sendWholesalerMailHtmlBodyWithoutAttachment);
				}
			}

			Prospect prospect = mergedPurchaseOrder.getVisit().getProspect();
			User user = mergedPurchaseOrder.getVisit().getDelegate().getUser();
			List<User> supervisors = userRepository.getSuperviserOfUser(user.getId());
			Set<String> to = new HashSet<String>();

			if (user.getEmail() != null && !"".equals(user.getEmail())) {
				to.add(user.getEmail());
			} else {
				LOG.error(logSyncMarker, "Delegate email is null, user id = " + user.getId());
			}
			for (User supervisor : supervisors) {
				if (supervisor.getEmail() != null) {
					to.add(supervisor.getEmail());
				}
			}

			for (User topUser : userRepository.getTopUsers()) {
				if (topUser.getEmail() != null) {
					to.add(topUser.getEmail());
				}
			}

			Prospect wholesaler = null;
			if (isCancellation == true) {
				wholesaler = oldWholesaler;
			} else {
				wholesaler = mergedPurchaseOrder.getWholesaler();
			}

			wholesaler = mergedPurchaseOrder.getWholesaler();
			String[] args = { prospect.getFirstName() + " " + prospect.getLastName(), prospect.getAddress(),
					wholesaler.getFirstName() + " " + wholesaler.getLastName(), config.getName(),
					prospect.getSector().getName(), prospect.getLocality().getName() };

			String subject = subjectFormat.format(args);
			String fileUrl = config.getServerPath() + uploadUrl + purchaseOrderPath + "/" + mergedPurchaseOrder.getId()
					+ "/" + mergedPurchaseOrder.getAttachmentName();
			String htmlBody = messageFormat.format(args);

			if (!"".equals(mergedPurchaseOrder.getAttachmentName())
					&& !"".equals(mergedPurchaseOrder.getAttachmentBase64())) {
				htmlBody = htmlBody + " <br>  <a href = '" + fileUrl
						+ "' target ='_blank' >Cliquez ici pour voir le bon de commande en ligne </a> <br> " + IMGBALISE
						+ pathLogo + "'>";
			}

			if (wholesaler.getEmail() != null && !"".equals(wholesaler.getEmail())) {
				to.add(wholesaler.getEmail());
			}

			String documentPath = createFolder(mergedPurchaseOrder.getId(), generatedDocumentPath) + "/"
					+ "generatedPurchaseOrder.pdf";
			try {
				purchaseOrderService.generatePurchaseOrder(documentPath, mergedPurchaseOrder.getId(), "Bon de commande");
			} catch (FileNotFoundException e) {
				e.printStackTrace();
			} catch (JRException e) {
				e.printStackTrace();
			}

			SaveAndMailBase64File base64EmailSender = new SaveAndMailBase64File(
					mergedPurchaseOrder.getAttachmentBase64(), mergedPurchaseOrder.getAttachmentName(), attachmentPath,
					true, javaMailSender, null, config.getName(), to.stream().toArray(String[]::new), subject,
					htmlBody);

			Thread thread = new Thread(base64EmailSender);
			thread.start();

			if (isCancellation != true) {
				mergedPurchaseOrder.setMailSent(true);
				purchaseOrderRepository.save(mergedPurchaseOrder);
			}

		} catch (BirdnotesException e) {

			LOG.error(logSyncMarker,
					"Error creating folder for purchase order, identifier : " + mergedPurchaseOrder.getIdentifier(), e);
		}

	}*/

	private void setterOfProspectToUpdate(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {

		prospect.setFirstName(prospectDto.getFirstName());
		prospect.setLastName(prospectDto.getLastName());
		prospect.setActivity(prospectDto.getActivity());
		prospect.setAddress(prospectDto.getAddress());
		prospect.setGsm(prospectDto.getGsm());
		prospect.setPhone(prospectDto.getPhone());
		prospect.setEmail(prospectDto.getEmail());
		prospect.setNote(prospectDto.getNote());
		prospect.setSecretary(prospectDto.getSecretary());

		prospect.setGrade(prospectDto.getGrade());
		if (prospectDto.getLatitude() != null) {
			prospect.setLatitude(prospectDto.getLatitude());
		}
		if (prospectDto.getLongitude() != null) {
			prospect.setLongitude(prospectDto.getLongitude());
		}
		if (prospectDto.getMapAddress() != null) {
			prospect.setMapAddress(prospectDto.getMapAddress());
		}
		Speciality speciality = new Speciality();

		if (prospectDto.getSpecialityDto() != null && prospectDto.getSpecialityDto().getId() != null) {
			speciality.setId(prospectDto.getSpecialityDto().getId());
		} else {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_SPECIALTY);
		}
		prospect.setSpeciality(speciality);

		setProspectObject(prospectDto, prospect);

	}

	private void setProspectObject(ProspectDto prospectDto, Prospect prospect) throws BirdnotesException {
		Sector sector = new Sector();
		if (prospectDto.getSectorDto() != null && prospectDto.getSectorDto().getId() != null) {
			sector.setId(prospectDto.getSectorDto().getId());

		} else {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_SECTOR);
		}
		prospect.setSector(sector);

		Locality locality = new Locality();
		if (prospectDto.getLocalityDto() != null && prospectDto.getLocalityDto().getId() != null) {
			locality.setId(prospectDto.getLocalityDto().getId());
		} else {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_LOCALITY);
		}
		prospect.setLocality(locality);

		ProspectType prospectType = new ProspectType();
		if (prospectDto.getProspectTypeDto() != null && prospectDto.getProspectTypeDto().getId() != null) {
			prospectType.setId(prospectDto.getProspectTypeDto().getId());
		} else {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_TYPE);
		}
		prospect.setProspectType(prospectType);

		if (prospectDto.getEstablishmentDto() != null && prospectDto.getEstablishmentDto().getId() != null) {
			Establishment establishment = new Establishment();
			establishment.setId(prospectDto.getEstablishmentDto().getId());
			prospect.setEstablishment(establishment);
		}

		PotentialDto potentialDto = prospectDto.getPotentialDto();
		if (potentialDto != null && potentialDto.getName() != null) {
			Potential potential = new Potential();
			potential.setId(prospectDto.getPotentialDto().getId());
			prospect.setPotential(potential);
		} else {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_POTENTIAL);
		}

	}

	/*
	 * public Map<Long, Long> insertNoteFrais(SendRequestDto reportListDto, User
	 * delegate) throws BirdnotesException {
	 * 
	 * Map<Long, Long> mapIdsReturn = new HashMap<>(); if (reportListDto != null &&
	 * reportListDto.getNotefrais() != null &&
	 * !reportListDto.getNotefrais().isEmpty()) { for (NoteFraisDto noteFraisDto :
	 * reportListDto.getNotefrais()) {
	 * 
	 * insertOrUpdateNoteFrais(noteFraisDto, delegate, mapIdsReturn); } } return
	 * mapIdsReturn; }
	 */

	/*
	 * public Map<Long, Long> insertActivity(SendRequestDto reportListDto, User
	 * delegate) throws BirdnotesException {
	 * 
	 * Map<Long, Long> mapIdsReturn = new HashMap<>(); if (reportListDto != null &&
	 * reportListDto.getActivities() != null &&
	 * !reportListDto.getActivities().isEmpty()) { for (ActivityDto activityDto :
	 * reportListDto.getActivities()) {
	 * 
	 * insertOrUpdateActivity(activityDto, delegate, mapIdsReturn); } } return
	 * mapIdsReturn; }
	 */

	public List<Long> deleteVisits(List<Long> visitsToDelete, Delegate user) {

		List<Long> deletedVisits = new ArrayList<>();
		if (visitsToDelete != null && !visitsToDelete.isEmpty()) {

			for (Long visitId : visitsToDelete) {
				try {
					Visit visit = visitRepository.findByIdentifier(visitId, user.getId());
 					if (visit != null) {
						messageTagRepository.deleteByVisitId(visit.getId());
						if(visit.getDoubleVisit() != null) {
							Visit doubleVisit = visitRepository.findById(visit.getDoubleVisit().getId());
							visit.setDoubleVisit(null);
							visitRepository.delete(doubleVisit);
						}
					}
					
					visitRepository.deleteByIdentifier(visitId, user.getId());
					deletedVisits.add(visitId);
				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting visit , visitId = " + visitId, e);
				}
			}

		}
		return deletedVisits;
	}

	public List<Long> deleteVisitProducts(List<Long> visitProductToDelete, Delegate user) {

		List<Long> deletedVisitProducts = new ArrayList<>();
		if (visitProductToDelete != null && !visitProductToDelete.isEmpty()) {

			for (Long visitProductId : visitProductToDelete) {
				try {
					
					VisitsProducts originalVisitProductToDelete = visitsProductsRepository.findByIdentifier(visitProductId, user.getId());
					if(originalVisitProductToDelete != null && originalVisitProductToDelete.getVisit().getDoubleVisit() != null) {
						List<VisitsProducts> doubleVisitProducts = visitsProductsRepository.findByVisitId(originalVisitProductToDelete.getVisit().getDoubleVisit().getId());
						for(VisitsProducts doubleVisitProduct : doubleVisitProducts) {
							if(doubleVisitProduct.getProduct().getId() == originalVisitProductToDelete.getProduct().getId()) {
								visitsProductsRepository.delete(doubleVisitProduct);
							}
						}
					}
					visitsProductsRepository.deleteByIdentifier(visitProductId, user.getId());
					deletedVisitProducts.add(visitProductId);
				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting visit product, visitProductId = " + visitProductId,
							e);
				}
			}

		}
		return deletedVisitProducts;
	}

	public List<Long> deletePurchaseOrder(List<Long> purchaseOrdersToDelete,
			List<PurchaseOrderDto> purchaseOrderDtoList, Delegate user) {

		List<Long> deletedPurchaseOrders = new ArrayList<>();
		List<PurchaseOrderDto> purchaseOrderToCancelList = new ArrayList<>();
		if (purchaseOrdersToDelete != null && !purchaseOrdersToDelete.isEmpty()) {

			for (Long purchaseOrderId : purchaseOrdersToDelete) {
				try {
					PurchaseOrder purchaseOrder = purchaseOrderRepository.findByIdentifier(purchaseOrderId,
							user.getId());
					/*
					 * List<Recovery> recoveries =
					 * recoveryRepository.findByPurchaseOrder(purchaseOrderId); for(Recovery
					 * recovery : recoveries) {
					 * attachmentRepository.delete(recovery.getAttachment().getId());
					 * recoveryRepository.delete(recovery.getId()); }
					 */
					// ************* add the purchaseOrderDto to the list of po to send cancel email
					PurchaseOrderDto purchaseOrderDtoToCancel = new PurchaseOrderDto(purchaseOrder);
					purchaseOrderDtoToCancel.setEmailType(EmailType.CANCELLATION.toString());
					purchaseOrderDtoToCancel.setMailSent(false);
					purchaseOrder.setMailSent(false);
					purchaseOrderDtoToCancel
							.setDelegate(delegateToDtoConvertor.convert(purchaseOrder.getVisit().getDelegate()));
					try {
						purchaseOrderDtoToCancel
								.setOldWholesaler(convertProspectToDto.convert(purchaseOrder.getWholesaler()));
					} catch (BirdnotesException e) {
						LOG.error(logSyncMarker,
								"Old Wholesaler of purchase order is null, purchaseOrder id = " + purchaseOrder.getId()
										+ ", user name : " + user.getFirstName() + ", user id : " + user.getId());
					}
					try {
						purchaseOrderDtoToCancel
								.setProspect(convertProspectToDto.convert(purchaseOrder.getVisit().getProspect()));
					} catch (BirdnotesException e) {
						LOG.error(logSyncMarker,
								"purchase to cancel is null, visit id = " + purchaseOrder.getVisit().getId()
										+ ", user name : " + user.getFirstName() + ", user id : " + user.getId());
					}
					purchaseOrderToCancelList.add(purchaseOrderDtoToCancel);
					// *************

					visitsProductsRepository.removePurchaseOrder(purchaseOrdersToDelete);
					purchaseOrderRepository.deleteByIdentifier(purchaseOrderId, user.getId());

					deletedPurchaseOrders.add(purchaseOrderId);
				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting Purchase Order , purchaseOrderId = " + purchaseOrderId
							+ ", user name : " + user.getFirstName() + ", user id : " + user.getId(), e);
				}
			}

		}
		purchaseOrderDtoList.addAll(purchaseOrderToCancelList);
		return deletedPurchaseOrders;
	}

	private void deletedPurchaseOrderFile(Long purchaseOrderId) {

		String pathToFolder = uploadPath + purchaseOrderPath + File.separator + purchaseOrderId;
		File fileName = new File(pathToFolder + File.separator);

		try {
			Files.delete(fileName.toPath());
		} catch (NoSuchFileException x) {
			LOG.error(logSyncMarker, "Error when delete file : no such file or directory", x);
		} catch (DirectoryNotEmptyException x) {
			LOG.error(logSyncMarker, "Error when delete file : file or directory  not empty", x);
		} catch (IOException e) {
			LOG.error(logSyncMarker, "Error when delete file ", e);
		}
	}

	public List<Long> deleteExpenseReport(List<Long> expenseReportToDelete, Delegate user) {

		List<Long> deletedExpenseReport = new ArrayList<>();
		if (expenseReportToDelete != null && !expenseReportToDelete.isEmpty()) {

			for (Long expenseReportIdentifier : expenseReportToDelete) {
				try {
					Long expenseId = noteFraisRepository.getIdByIdentifier(expenseReportIdentifier, user.getId());
					validationStatusRepository.deleteByExpenseValidation(expenseId);
					noteFraisRepository.deleteByIdentifier(expenseReportIdentifier, user.getId());

					deletedExpenseReport.add(expenseReportIdentifier);
				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting expanse report, id = " + expenseReportIdentifier, e);
				}
			}

		}
		return deletedExpenseReport;
	}

	public List<Long> deleteActivity(List<Long> activitiesToDelete, Delegate user) throws BirdnotesException {

		List<Long> deletedActivies = new ArrayList<>();
		if (activitiesToDelete != null && !activitiesToDelete.isEmpty()) {

			for (Long activityId : activitiesToDelete) {
				try {
					activityRepository.deleteByIdentifier(activityId, user.getId());

					deletedActivies.add(activityId);
				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting activity report, id = " + activityId, e);
				}
			}

		}
		return deletedActivies;
	}

	public List<Long> deleteMarketingAction(List<Long> marketingActionToDelete, Delegate user)
			throws BirdnotesException {

		List<Long> deletedMarketingActions = new ArrayList<>();

		if (marketingActionToDelete != null && !marketingActionToDelete.isEmpty()) {
			for (Long marketingActionIdentifier : marketingActionToDelete) {
				try {
					Long actionMarketingId = actionMarketingRepository.getIdByIdentifier(marketingActionIdentifier,
							user.getId());
					validationStatusRepository.deleteByActionMarketingId(actionMarketingId);
					actionMarketingRepository.deleteByIdentifier(marketingActionIdentifier, user.getId());
					deletedMarketingActions.add(marketingActionIdentifier);

				} catch (Exception e) {
					LOG.error(logSyncMarker,
							"error when deleting marketing action report, id = " + marketingActionIdentifier, e);
					throw new BirdnotesException(Exceptions.ACTIONMARKETING_COULD_NOT_DELETED);
				}
			}
		}
		return deletedMarketingActions;
	}

	public List<Long> deletePlanning(List<Long> planningToDelete, Delegate user) throws BirdnotesException {

		List<Long> deletedPlannings = new ArrayList<>();

		if (planningToDelete != null && !planningToDelete.isEmpty()) {

			for (Long planningId : planningToDelete) {
				try {

					planningRepository.deleteByIdentifier(planningId, user.getId());
					deletedPlannings.add(planningId);

				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting planning, id = " + planningId, e);
					throw new BirdnotesException(Exceptions.PLANNING_COULD_NOT_DELETED);
				}
			}
		}
		return deletedPlannings;
	}

	public List<Long> deletePlanningValidation(List<Long> planningValidationToDelete, Delegate user)
			throws BirdnotesException {

		List<Long> deletedPlanningValidations = new ArrayList<>();

		if (planningValidationToDelete != null && !planningValidationToDelete.isEmpty()) {

			for (Long planningValidationIdentifier : planningValidationToDelete) {
				try {
					Long planningValidationId = planningValidationRepository
							.getIdByIdentifier(planningValidationIdentifier, user.getId());
					validationStatusRepository.deleteByPlanningValidation(planningValidationId);
					planningValidationRepository.deleteByIdentifier(planningValidationIdentifier, user.getId());

					deletedPlanningValidations.add(planningValidationIdentifier);

				} catch (Exception e) {
					LOG.error(logSyncMarker,
							"error when deleting planning validation, identifier = " + planningValidationIdentifier, e);
					throw new BirdnotesException(Exceptions.PlANNING_VALIDATION_COULD_NOT_DELETED);
				}
			}
		}
		return deletedPlanningValidations;
	}

	public List<Long> deleteRecovery(List<Long> recoveryToDelete, Delegate user) throws BirdnotesException {

		List<Long> deletedRecovery = new ArrayList<>();

		if (recoveryToDelete != null && !recoveryToDelete.isEmpty()) {

			for (Long recoveryIdentifier : recoveryToDelete) {
				try {
					Recovery recovery = recoveryRepository.findByIdentifier(recoveryIdentifier, user.getId());
					recoveryRepository.delete(recovery.getId());
					deletedRecovery.add(recoveryIdentifier);

				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting recovery, identifier = " + recoveryIdentifier, e);
					throw new BirdnotesException(Exceptions.RECOVERY_COULD_NOT_DELETED);
				}
			}
		}
		return deletedRecovery;
	}

	public List<Long> deleteAttachment(List<Long> attachmentToDelete, Delegate user) throws BirdnotesException {

		List<Long> deletedAttachment = new ArrayList<>();

		if (attachmentToDelete != null && !attachmentToDelete.isEmpty()) {

			for (Long attachmentIdentifier : attachmentToDelete) {
				try {
					Attachment attachment = attachmentRepository.findByIdentifier(attachmentIdentifier, user.getId());
					attachmentRepository.delete(attachment.getId());
					deletedAttachment.add(attachmentIdentifier);

				} catch (Exception e) {
					LOG.error(logSyncMarker, "error when deleting attachment, identifier = " + attachmentIdentifier, e);
					throw new BirdnotesException(Exceptions.RECOVERY_COULD_NOT_DELETED);
				}
			}
		}
		return deletedAttachment;
	}

	/*
	 * public Map<Long, Long> insertPlanning(SendRequestDto reportListDto, User
	 * delegate) throws BirdnotesException {
	 * 
	 * Map<Long, Long> mapIdsReturn = new HashMap<>(); if (reportListDto != null &&
	 * reportListDto.getPlannings() != null &&
	 * !reportListDto.getPlannings().isEmpty()) { for (PlanningDto planningDto :
	 * reportListDto.getPlannings()) {
	 * 
	 * insertOrUpdatePlanning(planningDto, delegate, mapIdsReturn);
	 * 
	 * } } return mapIdsReturn; }
	 */

	/*
	 * public Map<Long, Long> insertActionMarketing(SendRequestDto reportListDto,
	 * User delegate) throws BirdnotesException {
	 * 
	 * Map<Long, Long> mapIdsReturn = new HashMap<>(); if (reportListDto != null &&
	 * reportListDto.getActionMarketings() != null &&
	 * !reportListDto.getActionMarketings().isEmpty()) { for
	 * (ActionMarketingRequestDto actionMarketingRequestDto :
	 * reportListDto.getActionMarketings()) {
	 * 
	 * insertOrUpdateActionMarketing(actionMarketingRequestDto, delegate,
	 * mapIdsReturn);
	 * 
	 * } } return mapIdsReturn; }
	 */
	// insert part

	public Map<Long, Long> insertOpportunityNote(SendRequestDto reportListDto, User delegate)
			throws BirdnotesException {

		Map<Long, Long> mapIdsReturn = new HashMap<>();
		if (reportListDto != null && reportListDto.getOpportunityNotes() != null
				&& !reportListDto.getOpportunityNotes().isEmpty()) {
			for (OpportunityNoteRequestDto opportuntiyNoteRequestDto : reportListDto.getOpportunityNotes()) {

				insertOrUpdateOpportunityNote(opportuntiyNoteRequestDto, delegate, mapIdsReturn);

			}
		}
		return mapIdsReturn;
	}
	// insert location data

	public Map<Long, Long> insertLocation(List<LocationDto> locations, Delegate delegate) throws BirdnotesException {
		Map<Long, Long> mapIdsReturn = new HashMap<>();

		if (locations != null && !locations.isEmpty()) {
			locationToAdd(locations.get(0), 0, 0, delegate);
			/*
			 * DistanceCalculation distanceCalculation = new DistanceCalculation(locations,
			 * delegate, distanceService, locationRepository); Thread thread = new
			 * Thread(distanceCalculation); thread.start();
			 */

		}
		return mapIdsReturn;
	}

	/*
	 * public Map<Long, Long> insertPlanningValidation(SendRequestDto reportListDto,
	 * User delegate) throws BirdnotesException {
	 * 
	 * Map<Long, Long> mapIdsReturn = new HashMap<>(); if (reportListDto != null &&
	 * reportListDto.getPlanningValidation() != null &&
	 * !reportListDto.getPlanningValidation().isEmpty()) { for
	 * (PlanningValidationDto planningvalidationDto :
	 * reportListDto.getPlanningValidation()) { PlanningValidation
	 * mergedPlanningValidation = planningValidationToAdd(planningvalidationDto,
	 * delegate); mapIdsReturn.put(planningvalidationDto.getId(),
	 * mergedPlanningValidation.getId()); if (mergedPlanningValidation != null) {
	 * notificationMessageBuilder.setMessageType("planningNotificationMessage");
	 * notificationMessageBuilder.setUser(userToDtoConvertor.convert(delegate));
	 * notificationMessageBuilder.setEntityId(mergedPlanningValidation.getId()); for
	 * (User superior : delegate.getSuperiors()) {
	 * notificationMessageBuilder.setTagetUser(superior); Notification notification
	 * = notificationMessageBuilder.Build();
	 * notificationService.generate(notification); } Notification notification =
	 * notificationMessageBuilder.Build();
	 * notificationService.generate(notification, "ADD_PLANIFICATION"); }
	 * 
	 * }
	 * 
	 * } return mapIdsReturn; }
	 */

	public List<Long> deleteOpportunityNote(SendRequestDto reportListDto, User delegate) throws BirdnotesException {

		if (reportListDto != null && reportListDto.getOpportunityNotesToDelete() != null
				&& !reportListDto.getOpportunityNotesToDelete().isEmpty()) {

			try {
				opportunityNoteRepository.deleteByIds(reportListDto.getOpportunityNotesToDelete());

			} catch (Exception e) {
				e.printStackTrace();
				LOG.error("error when delete note d'opportunité ", e);
				throw new BirdnotesException(Exceptions.OPPORTUNITÉNOTE_COULD_NOT_DELETED);
			}

			return reportListDto.getOpportunityNotesToDelete();
		} else {
			return null;
		}

	}

	/*
	 * private void insertOrUpdateNoteFrais(NoteFraisDto noteFraisDto, User
	 * delegate, Map<Long, Long> mapIdsReturn) throws BirdnotesException { if
	 * (noteFraisDto.getId() < 0) { NoteFrais mergedNoteFrais =
	 * noteFraisToAdd(noteFraisDto, delegate);
	 * mapIdsReturn.put(noteFraisDto.getId(), mergedNoteFrais.getId());
	 * notificationMessageBuilder.setMessageType("noteFraisNotificationMessage");
	 * notificationMessageBuilder.setUser(userToDtoConvertor.convert(delegate));
	 * notificationMessageBuilder.setEntityId(mergedNoteFrais.getId()); for (User
	 * superior : delegate.getSuperiors()) {
	 * notificationMessageBuilder.setTagetUser(superior); Notification notification
	 * = notificationMessageBuilder.Build();
	 * notificationService.generate(notification); } Notification notification =
	 * notificationMessageBuilder.Build();
	 * notificationService.generate(notification, "ADD_EXPENSE");
	 * validationStepService.addValidationStatus("NEW_EXPENSE",
	 * mergedNoteFrais.getId(), delegate.getId());
	 * 
	 * } else { try { noteFraisToUpdate(noteFraisDto); } catch (IOException |
	 * BirdnotesException e) { LOG.error("Error when update note frais", e); } } }
	 */

	/*
	 * private void insertOrUpdateActivity(ActivityDto activityDto, User delegate,
	 * Map<Long, Long> mapIdsReturn) throws BirdnotesException { if
	 * (activityDto.getId() < 0) { Activity mergedActivitys =
	 * activityToAdd(activityDto, delegate); mapIdsReturn.put(activityDto.getId(),
	 * mergedActivitys.getId());
	 * notificationMessageBuilder.setMessageType("activityNotificationMessage");
	 * notificationMessageBuilder.setUser(userToDtoConvertor.convert(delegate));
	 * notificationMessageBuilder.setEntityId(mergedActivitys.getId()); for (User
	 * superior : delegate.getSuperiors()) {
	 * notificationMessageBuilder.setTagetUser(superior); Notification notification
	 * = notificationMessageBuilder.Build();
	 * notificationService.generate(notification); } Notification notification =
	 * notificationMessageBuilder.Build();
	 * notificationService.generate(notification, "ADD_ACTIVITY");
	 * 
	 * } else { try { activityToUpdate(activityDto); } catch (IOException |
	 * BirdnotesException e) { LOG.error("Error when update activity", e); } } }
	 */

	/*
	 * private void insertOrUpdatePlanning(PlanningDto planningDto, User delegate,
	 * Map<Long, Long> mapIdsReturn) throws BirdnotesException { if
	 * (planningDto.getId() != null) { if (planningDto.getId() < 0) { Planning
	 * mergedPlannings = planningToAdd(planningDto, delegate);
	 * mapIdsReturn.put(planningDto.getId(), mergedPlannings.getId()); } } else {
	 * try { planningToUpdate(planningDto); } catch (IOException |
	 * BirdnotesException e) { LOG.error("Error when update planning", e); } } }
	 */

	/*
	 * private void insertOrUpdateActionMarketing(ActionMarketingRequestDto
	 * actionMarketingDto, User delegate, Map<Long, Long> mapIdsReturn) throws
	 * BirdnotesException { if (actionMarketingDto.getId() != null) { if
	 * (actionMarketingDto.getId() < 0) { ActionMarketing mergedActionMarketings =
	 * actionMarketingToAdd(actionMarketingDto, delegate);
	 * mapIdsReturn.put(actionMarketingDto.getId().longValue(),
	 * mergedActionMarketings.getId().longValue()); if (mergedActionMarketings !=
	 * null) { notificationMessageBuilder.setMessageType(
	 * "actionMarketingNotificationMessage");
	 * notificationMessageBuilder.setUser(userToDtoConvertor.convert(delegate));
	 * notificationMessageBuilder.setEntityId(mergedActionMarketings.getId()); for
	 * (User superior : delegate.getSuperiors()) {
	 * notificationMessageBuilder.setTagetUser(superior); Notification notification
	 * = notificationMessageBuilder.Build();
	 * notificationService.generate(notification); } Notification notification =
	 * notificationMessageBuilder.Build();
	 * notificationService.generate(notification, "ADD_ACTIONMARKETING");
	 * 
	 * }
	 * 
	 * } } else { try { actionMarketingToUpdate(actionMarketingDto); } catch
	 * (IOException | BirdnotesException e) {
	 * LOG.error("Error when update actionMarketing", e); } } }
	 */

	private void insertOrUpdateOpportunityNote(OpportunityNoteRequestDto opportunityNoteDto, User delegate,
			Map<Long, Long> mapIdsReturn) throws BirdnotesException {
	    ConfigurationDto config = configurationService.findConfiguration();
	    Locale locale = new Locale(config.getLanguage());
		if (opportunityNoteDto.getId() < 0) {
			OpportunityNote mergedOpportunityNotes = opportunityNoteToAdd(opportunityNoteDto, delegate);
			mapIdsReturn.put(opportunityNoteDto.getId().longValue(), mergedOpportunityNotes.getId().longValue());

			for (User superior : delegate.getSuperiors()) {
			    notificationMessageBuilder.setMessageType("opportunityNoteNotificationMessage");
				notificationMessageBuilder.setUser(delegate);
				notificationMessageBuilder.setTagetUser(superior);
				Notification notification = notificationMessageBuilder.Build();
				notificationService.generateUsingAllNotificationMethods(notification);
			}

		} else {
			try {
				opportunityNoteToUpdate(opportunityNoteDto);
			} catch (IOException | BirdnotesException e) {
				LOG.error("Error when update opportunity note", e);
			}
		}
	}

	// insert location data
	/*
	 * private void insertOrUpdateLocation(LocationDto locationDto, User delegate)
	 * throws BirdnotesException { locationToAdd(locationDto, delegate);
	 * 
	 * 
	 * notificationMessageBuilder.setMessageType("locationNotificationMessage");
	 * notificationMessageBuilder.setUser(delegate);
	 * notificationMessageBuilder.setTagetUser(delegate.getSupervisor());
	 * Notification notification = notificationMessageBuilder.Build();
	 * notificationService.save(notification);
	 * 
	 * }
	 */

	private void actionMarketingToUpdate(ActionMarketingRequestDto actionMarketingDto)
			throws IOException, BirdnotesException {
		ActionMarketing actionMarketing = actionMarketingRepository.findOne(actionMarketingDto.getId());
		if (actionMarketing != null) {
			actionMarketing.setDate(actionMarketingDto.getDate());
			Set<Prospect> prospects = prospectRepository.findProspectsByIds(actionMarketingDto.getProspectsIds());
			if (prospects == null) {
				LOG.error(logSyncMarker,
						"prospects not found in Marketing action ids = " + actionMarketingDto.getProspectsIds());

			} else {
				Set<Prospect> prospectsToSave = new HashSet<>();

				for (Prospect prospect : prospects) {
					if (prospect.getStatus() != null && prospect.getStatus().equals(UserValidationStatus.MERGED)) {
						prospect = prospectRepository.findOne(prospect.getIdprospect());
					}
					Set<Product> products = productRepository.findProducts(actionMarketingDto.getProductsIds());
					if (products == null) {
						throw new BirdnotesException("prospect est obligatoire");
					}
					actionMarketing.setProspects(prospectsToSave);
					actionMarketing.setProducts(products);
					actionMarketing.setName(actionMarketingDto.getName());
					actionMarketing.setBudget(actionMarketingDto.getBudget());
					actionMarketing.setDescription(actionMarketingDto.getDescription());

					actionMarketingRepository.save(actionMarketing);
				}
			}
		}
	}

	private void opportunityNoteToUpdate(OpportunityNoteRequestDto opportunityNoteDto)
			throws IOException, BirdnotesException {
		OpportunityNote opportunityNote = opportunityNoteRepository.findOne(opportunityNoteDto.getId());
		if (opportunityNote != null) {
			deletedFileON(opportunityNoteDto, opportunityNote);
			opportunityNote.setDate(opportunityNoteDto.getDate());
			Set<Prospect> prospects = prospectRepository.findProspectsByIds(opportunityNoteDto.getProspectsIds());
			if (prospects == null) {
				throw new BirdnotesException("prospect est obligatoire");
			}
			Set<Product> products = productRepository.findProducts(opportunityNoteDto.getProductsIds());
			if (products == null) {
				throw new BirdnotesException("prospect est obligatoire");
			}
			Set<Prospect> pharmacies = prospectRepository.findProspectsByIds(opportunityNoteDto.getPharmaciesIds());
			if (pharmacies == null) {
				throw new BirdnotesException("pharmacie est obligatoire");
			}

			opportunityNote.setProspects(prospects);
			opportunityNote.setProducts(products);
			opportunityNote.setPharmacies(pharmacies);
			opportunityNote.setAttachmentBase64(opportunityNoteDto.getAttachmentBase64());
			opportunityNote.setPieceJointe(opportunityNoteDto.getNameAttachment());
			opportunityNote.setName(opportunityNoteDto.getName());
			opportunityNote.setBudget(opportunityNoteDto.getBudget());
			opportunityNote.setDescription(opportunityNoteDto.getDescription());
			opportunityNoteRepository.save(opportunityNote);
		}
	}

	/*
	 * private void noteFraisToUpdate(ExpenseReportDto noteFraisDto) throws
	 * IOException, BirdnotesException { NoteFrais expenseReport =
	 * noteFraisRepository.findById(noteFraisDto.getId()); if (expenseReport !=
	 * null) { deletedFile(noteFraisDto, expenseReport); NoteFrais noteFrais = new
	 * NoteFrais(); noteFrais = convert(noteFraisDto, noteFrais);
	 * 
	 * TypeNoteFrais typeNoteFrais =
	 * typeNoteFraisRepository.findOne(noteFraisDto.getTypeNotefrais()); if
	 * (typeNoteFrais == null) { throw new
	 * BirdnotesException("type note frais est obligatoire"); }
	 * noteFrais.setTypeNoteFrais(typeNoteFrais);
	 * noteFraisRepository.update(noteFraisDto.getDate(), noteFraisDto.getMontant(),
	 * noteFraisDto.getDescription(), typeNoteFrais,
	 * noteFraisDto.getAttachmentName(), noteFraisDto.getAttachmentBase64(),
	 * noteFraisDto.getId()); } }
	 */

	private void activityToUpdate(ActivityDto activityDto) throws IOException, BirdnotesException {
		Activity activitys = activityRepository.findById(activityDto.getId());
		if (activitys != null) {

			Activity activity = convert(activityDto, activitys);

			ActivityType activityType = activityTypeRepository.findOne(activityDto.getTypeActivity());
			if (activityType == null) {
				throw new BirdnotesException("type activity est obligatoire");
			}
			activity.setActivityType(activityType);
			activityRepository.update(activityDto.getDate(), activityDto.getHourNumber(), activityType,
					activityDto.getComment(), activityDto.getId());
		}
	}

	private List<Long> saveExpenseReports(List<ExpenseReportDto> noteFraisDtoList, Delegate delegate)
			throws BirdnotesException {

		List<Long> savedExpenseReportsIds = new ArrayList<>();
		for (ExpenseReportDto noteFraisDto : noteFraisDtoList) {
			ExpenseReport noteFrais = noteFraisRepository.findByIdentifier(noteFraisDto.getIdentifier(),
					delegate.getId());
			boolean isNew = false;
			if (noteFrais == null) {
				noteFrais = new ExpenseReport();
				noteFrais.setIdentifier(noteFraisDto.getIdentifier());
				noteFrais.setDelegate(delegate);
				noteFrais.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
				isNew = true;
			}

			noteFrais = convert(noteFraisDto, noteFrais);
			ExpenseType typeNoteFrais = expenseTypeRepository.findOne(noteFraisDto.getExpenseTypeId());
			Activity activity = activityRepository.findByIdentifier(noteFraisDto.getActivityId(), delegate.getId());
			noteFrais.setExpenseType(typeNoteFrais);
			noteFrais.setActivity(activity);
			ExpenseReport mergedNoteFrais = noteFraisRepository.save(noteFrais);
			saveExpenseAttachement(mergedNoteFrais, noteFraisDto);
			savedExpenseReportsIds.add(noteFraisDto.getIdentifier());
			if (isNew) {
				notificationService.sendNotificationUsingWorkflow(delegate.getUser(), mergedNoteFrais.getId(),
						"ADD_EXPENSE");
				validationStepService.addValidationStatus("NEW_EXPENSE", mergedNoteFrais.getId(),
						delegate.getUser().getId());
			}

		}
		return savedExpenseReportsIds;
	}

	private List<Long> saveMarketingAction(List<ActionMarketingRequestDto> actionMarketingDtoList, Delegate delegate)
			throws BirdnotesException {

		boolean isNew = false;
		List<Long> savedMarketingActionIds = new ArrayList<>();
		for (ActionMarketingRequestDto actionMarketingRequestDto : actionMarketingDtoList) {
			ActionMarketing actionMarketing = actionMarketingRepository
					.findByIdentifier(actionMarketingRequestDto.getIdentifier(), delegate.getUser().getId());
			if (actionMarketing == null) {
				actionMarketing = new ActionMarketing();
				actionMarketing.setIdentifier(actionMarketingRequestDto.getIdentifier());
				actionMarketing.setUser(delegate.getUser());
				actionMarketing.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
				isNew = true;
			}

			actionMarketing.setDate(actionMarketingRequestDto.getDate());
			actionMarketing.setName(actionMarketingRequestDto.getName());
			actionMarketing.setDescription(actionMarketingRequestDto.getDescription());
			actionMarketing.setBudget(actionMarketingRequestDto.getBudget());

			Set<Prospect> prospects = prospectRepository
					.findProspectsByIdentifiers(actionMarketingRequestDto.getProspectsIds());
			if (prospects == null) {
				LOG.error(logSyncMarker,
						"prospects not found in Marketing action ids = " + actionMarketingRequestDto.getProspectsIds());
				return null;
			} else {
				Set<Prospect> prospectsToSave = new HashSet<>();

				for (Prospect prospect : prospects) {
					if (prospect.getStatus() != null && prospect.getStatus().equals(UserValidationStatus.MERGED)) {
						prospect = prospectRepository.findByIdentifier(prospect.getIdprospect());
					} else if (prospect.getStatus().equals(UserValidationStatus.REFUSED)) {

						LOG.error(logSyncMarker, "Prospect not found in marketing action, prospect identifier : "
								+ prospect.getIdentifier());
						prospect = null;
						continue;
					}
					prospectsToSave.add(prospect);
				}
				actionMarketing.setProspects(prospectsToSave);

				Set<Product> products = productRepository.findProducts(actionMarketingRequestDto.getProductsIds());
				if (products == null) {
					throw new BirdnotesException("product est obligatoire");
				}
				actionMarketing.setProducts(products);
				ActionMarketing mergedActionMarketing = actionMarketingRepository.save(actionMarketing);
				savedMarketingActionIds.add(actionMarketingRequestDto.getIdentifier());

				if (isNew) {
					notificationService.sendNotificationUsingWorkflow(delegate.getUser(), mergedActionMarketing.getId(),
							"ADD_ACTIONMARKETING");
					validationStepService.addValidationStatus("NEW_MARKETING_ACTION", mergedActionMarketing.getId(),
							delegate.getUser().getId());
				}
			}
		}
		return savedMarketingActionIds;
	}

	private List<Long> saveRecovery(List<RecoveryDto> recoveryDtoList, Delegate user) throws BirdnotesException {

		List<Long> savedRecoveryIds = new ArrayList<>();

		for (RecoveryDto recoveryDto : recoveryDtoList) {
			Recovery recovery = recoveryRepository.findByIdentifier(recoveryDto.getIdentifier(), user.getId());
			if (recovery == null) {
				recovery = new Recovery();
				recovery.setIdentifier(recoveryDto.getIdentifier());
			}
			recovery.setAmount(recoveryDto.getAmount());
			recovery.setDate(recoveryDto.getDate());
			recovery.setDescription(recoveryDto.getDescription());
			recovery.setPayment(recoveryDto.getPayment());

			PurchaseOrder purchaseOrder = purchaseOrderRepository.findByIdentifier(recoveryDto.getPurchaseOrderId(),
					user.getId());
			if (purchaseOrder != null) {
				recovery.setPurchaseOrder(purchaseOrder);
			}
			Attachment attachment = attachmentRepository.findByIdentifier(recoveryDto.getAttachmentId(), user.getId());
			if (attachment != null) {
				recovery.setAttachment(attachment);
			}

			Recovery mergedRecovery = recoveryRepository.save(recovery);
			String attachmentPath = createFolderRecovery(mergedRecovery, recoveryDto);

			savedRecoveryIds.add(recoveryDto.getIdentifier());
		}
		return savedRecoveryIds;
	}

	private List<Long> saveAttachment(List<AttachmentDto> attachmentDtoList, Delegate user) throws BirdnotesException {

		List<Long> savedAttachmentIds = new ArrayList<>();
		for (AttachmentDto attachmentDto : attachmentDtoList) {
			Attachment attachment = attachmentRepository.findByIdentifier(attachmentDto.getIdentifier(), user.getId());
			if (attachment == null) {
				attachment = new Attachment();
				attachment.setIdentifier(attachmentDto.getIdentifier());
				attachment.setDelegate(user);
			}
			attachment.setIdentifier(attachmentDto.getIdentifier());
			attachment.setAttachmentBase64(attachmentDto.getAttachmentBase64());
			attachment.setAttachmentName(attachmentDto.getAttachmentName());

			Attachment mergedAttachment = attachmentRepository.save(attachment);

			savedAttachmentIds.add(attachmentDto.getIdentifier());
		}
		return savedAttachmentIds;
	}

	private List<Long> saveMessage(List<MessageDto> messageDtoList, User user) throws BirdnotesException {

		List<Long> savedMessageIds = new ArrayList<>();
		for (MessageDto messageDto : messageDtoList) {
			Message message = messageRepository.findByIdentifier(messageDto.getIdentifier(), user.getId());
			if (message == null) {
				message = new Message();
				message.setIdentifier(messageDto.getIdentifier());
				message.setUser(user);
			}
			message.setIdentifier(messageDto.getIdentifier());
			message.setText(messageDto.getText());
			message.setDate(messageDto.getDate());
			if (messageDto.getType() != null) {
				message.setType(MessageType.valueOf(messageDto.getType()));
			} else {
				message.setType(MessageType.MESSAGE);
			}

			Message mergedMessage = messageRepository.save(message);

			savedMessageIds.add(messageDto.getIdentifier());
		}
		return savedMessageIds;
	}

	private List<Long> saveMessageTag(List<MessageTagDto> messageTagDtoList, Delegate delegate) throws BirdnotesException {

		List<Long> savedMessageTagIds = new ArrayList<>();
		for (MessageTagDto messageTagDto : messageTagDtoList) {
			MessageTag messageTag = messageTagRepository.findByIdentifier(messageTagDto.getIdentifier(), delegate.getId());
			if (messageTag == null) {
				messageTag = new MessageTag();

			}
			messageTag.setIdentifier(messageTagDto.getIdentifier());
			Visit visit = visitRepository.findByIdentifier(messageTagDto.getVisitId(), delegate.getId());
			if(visit != null) {
				messageTag.setVisit(visit);
				User tagedUser = userRepository.findOne(messageTagDto.getUserId());
				messageTag.setUser(tagedUser);
				MessageTag mergedMessageTag = messageTagRepository.save(messageTag);
	
				savedMessageTagIds.add(messageTagDto.getIdentifier());
			// notify taged users

				notificationMessageBuilder.setMessageType("visitMessageTag");
				notificationMessageBuilder.setUser(delegate.getUser());
				notificationMessageBuilder.setTagetUser(tagedUser);
			
				notificationMessageBuilder.setDate(visit.getVisitDate());
				notificationMessageBuilder.setProspect(visit.getProspect());
				notificationMessageBuilder.setNote(visit.getGeneralNote());
			
				Notification notification = notificationMessageBuilder.Build();
				notificationService.generateUsingAllNotificationMethods(notification);
			}
		}

		return savedMessageTagIds;
	}
	
	private List<Long> savePresentationTimeTracking(List<PresentationTimeTrackingDto>presentationTimeTrackingDtos, Delegate delegate){
		List<Long> presentationTimeTrackingIds = new ArrayList<>();
		if (presentationTimeTrackingDtos != null) {
			if (presentationTimeTrackingDtos != null) {
				for (PresentationTimeTrackingDto presentationTimeTrackingDto : presentationTimeTrackingDtos) {
					PresentationTimeTracking presentationTimeTracking  = new PresentationTimeTracking();
					presentationTimeTracking.setIdentifier(presentationTimeTrackingDto.getIdentifier());
					VisitsProducts visitProduct = visitsProductsRepository.findByVisitAndProduct(presentationTimeTrackingDto.getVisitId(), presentationTimeTrackingDto.getProductId(), delegate.getId());
					presentationTimeTracking.setVisitProduct(visitProduct);
					presentationTimeTracking.setDocumentName(presentationTimeTrackingDto.getDocumentName());
					presentationTimeTracking.setEndTime(presentationTimeTrackingDto.getEndTime());
					presentationTimeTracking.setStartTime(presentationTimeTrackingDto.getStartTime());
					PresentationTimeTracking savedPresentationTimeTracking = presentationTimeTrackingRepository.save(presentationTimeTracking);
		
					presentationTimeTrackingIds.add(presentationTimeTrackingDto.getIdentifier());
				}
			}
		}
		return presentationTimeTrackingIds;
	}

	private List<Long> saveActivity(List<ActivityDto> activityDtoList, Delegate delegate) throws BirdnotesException {

		List<Long> savedActivityIds = new ArrayList<>();
		for (ActivityDto activityDto : activityDtoList) {
			Activity activity = activityRepository.findByIdentifier(activityDto.getIdentifier(), delegate.getId());
			if (activity == null) {
				activity = new Activity();
				activity.setIdentifier(activityDto.getIdentifier());
			}
			activity = convert(activityDto, activity);

			ActivityType activityType = activityTypeRepository.findOne(activityDto.getTypeActivity());
			if (activityType == null) {
				throw new BirdnotesException("type activity est obligatoire");
			}
			activity.setActivityType(activityType);

			if (delegate == null) {
				throw new BirdnotesException("Session est expiré");
			}

			activity.setDelegate(delegate);
			Activity mergedActivity = activityRepository.save(activity);

			savedActivityIds.add(activityDto.getIdentifier());
			if (mergedActivity != null) {
				notificationService.sendNotificationUsingWorkflow(delegate.getUser(), mergedActivity.getId(),
						"ADD_ACTIVITY");
			}

		}
		return savedActivityIds;
	}

	private List<Long> savePlanning(List<PlanningDto> planningDtoList, Delegate user) throws BirdnotesException {

		List<Long> savedPlanningIds = new ArrayList<>();
		for (PlanningDto planningDto : planningDtoList) {
			Planning planning = planningRepository.findByIdentifier(planningDto.getIdentifier(), user.getId());
			if (planning == null) {
				planning = new Planning();
				planning.setIdentifier(planningDto.getIdentifier());
				Prospect prospect = prospectService.getProspect(planningDto.getProspect());
				if (prospect == null) {
					LOG.error(logSyncMarker,
							"Prospect not found in planning, prospect identifier" + planningDto.getProspect());
					continue;
				}
				planning.setProspect(prospect);
				planning.setDate(planningDto.getDate());
				planning.setDelegate(user);
			}

			Planning mergedPlanning = planningRepository.save(planning);

			savedPlanningIds.add(planningDto.getIdentifier());
		}

		return savedPlanningIds;

	}

	private List<Long> savePlanningValidation(List<PlanningValidationDto> planningValidationDtoList, Delegate delegate)

			throws BirdnotesException {

		List<Long> savedPlanningValidationIds = new ArrayList<>();

		for (PlanningValidationDto planningValidationDto : planningValidationDtoList) {
			PlanningValidation planningValidation = null;
			planningValidation = planningValidationRepository.findByUserAndDate(delegate.getId(), planningValidationDto.getDate());
			//planningValidation = planningValidationRepository.findByIdentifier(planningValidationDto.getIdentifier(), delegate.getId());

			boolean isNew = false;
			if (planningValidation == null) {

				planningValidation = new PlanningValidation();
				planningValidation.setIdentifier(planningValidationDto.getIdentifier());
				planningValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
				planningValidation.setDate(planningValidationDto.getDate());
				planningValidation.setDelegate(delegate);
				isNew = true;
			}

			PlanningValidation mergedPlanningValidation = planningValidationRepository.save(planningValidation);
			savedPlanningValidationIds.add(planningValidationDto.getIdentifier());

			if (isNew) {

				notificationService.sendNotificationUsingWorkflow(delegate.getUser(), mergedPlanningValidation.getId(),
						"ADD_PLANIFICATION");
				validationStepService.addValidationStatus("NEW_PLANIFICATION", mergedPlanningValidation.getId(),
						delegate.getUser().getId());

			}

		}

		return savedPlanningValidationIds;

	}

	private OpportunityNote opportunityNoteToAdd(OpportunityNoteRequestDto opportunityNoteDto, User delegate)
			throws BirdnotesException {

		OpportunityNote opportunityNote = new OpportunityNote();
		opportunityNote.setDate(opportunityNoteDto.getDate());
		opportunityNote.setName(opportunityNoteDto.getName());
		opportunityNote.setId(opportunityNoteDto.getId());
		opportunityNote.setDescription(opportunityNoteDto.getDescription());
		opportunityNote.setBudget(opportunityNoteDto.getBudget());
		opportunityNote.setAttachmentBase64(opportunityNoteDto.getAttachmentBase64());
		opportunityNote.setPieceJointe(opportunityNoteDto.getNameAttachment());
		opportunityNote.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);

		Set<Prospect> prospects = prospectRepository.findProspectsByIds(opportunityNoteDto.getProspectsIds());
		if (prospects == null) {
			throw new BirdnotesException("prospect est obligatoire");
		}
		opportunityNote.setProspects(prospects);

		Set<Prospect> pharmacies = prospectRepository.findProspectsByIds(opportunityNoteDto.getPharmaciesIds());
		if (pharmacies == null) {
			throw new BirdnotesException("pharmacie  est obligatoire");
		}
		opportunityNote.setPharmacies(pharmacies);
		Set<Product> products = productRepository.findProducts(opportunityNoteDto.getProductsIds());
		if (products == null) {
			throw new BirdnotesException("product est obligatoire");
		}
		opportunityNote.setProducts(products);

		if (delegate == null) {
			throw new BirdnotesException("Délégué introuvable");
		} else

		{
			opportunityNote.setUser(delegate);
			OpportunityNote mergedOpportunityNote = opportunityNoteRepository.save(opportunityNote);
			createFolderON(mergedOpportunityNote, opportunityNoteDto);
			return mergedOpportunityNote;
		}
	}

	// location to add code data

	private Location locationToAdd(LocationDto locationDto, Integer distance, Integer duration, Delegate delegate)
			throws BirdnotesException {

		Location location = new Location();
		location.setDate(locationDto.getDate());
		location.setLatitude(locationDto.getLatitude());
		location.setLongitude(locationDto.getLongitude());
		location.setDistance(distance);
		location.setDuration(duration);
		if (delegate == null) {
			throw new BirdnotesException("Délégué introuvable");
		} else {
			location.setDelegate(delegate);
			Location mergedLocation = locationRepository.save(location);
			return mergedLocation;
		}
	}

	private void saveExpenseAttachement(ExpenseReport mergedNoteFrais, ExpenseReportDto noteFraisDto)
			throws BirdnotesException {
		String pathUploadAttechment = uploadPath + expenseReportPath + File.separator + mergedNoteFrais.getId();
		File dirName = new File(pathUploadAttechment);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		if (StringUtils.isNotEmpty(noteFraisDto.getAttachmentBase64())) {
			SaveAndMailBase64File threadBase64ToImage = new SaveAndMailBase64File(noteFraisDto.getAttachmentBase64(),
					noteFraisDto.getAttachmentName(), pathUploadAttechment, false, null, null, null, null, null, null);
			Thread thread = new Thread(threadBase64ToImage);
			thread.start();

			/*
			 * BirdnotesUtils.decodeBase64ToImage(noteFraisDto.getAttachmentBase64(),
			 * noteFraisDto.getNameAttachment(), pathUploadAttechment);
			 */
		}
	}

	private void createFolderON(OpportunityNote mergedOpportunityNote, OpportunityNoteRequestDto opportunityNoteDto)
			throws BirdnotesException {
		String pathUploadAttechment = uploadPath + opportunityNotePath + File.separator + mergedOpportunityNote.getId();
		File dirName = new File(pathUploadAttechment);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		if (StringUtils.isNotEmpty(opportunityNoteDto.getAttachmentBase64())) {
			BirdnotesUtils.saveBase64ToImage(opportunityNoteDto.getAttachmentBase64(),
					opportunityNoteDto.getNameAttachment(), pathUploadAttechment);
		}
	}

	private String createFolderPurchaseOrder(PurchaseOrder mergedPurchaseOrder) throws BirdnotesException {

		String pathUploadAttachment = uploadPath + purchaseOrderPath + File.separator + mergedPurchaseOrder.getId();
		File dirName = new File(pathUploadAttachment);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}

		return pathUploadAttachment;
	}

	private String createFolder(Long id, String path) throws BirdnotesException {

		String pathUpload = uploadPath + path + File.separator + id;
		File dirName = new File(pathUpload);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		return pathUpload;
	}

	private String createFolderRecovery(Recovery mergedRecovery, RecoveryDto recoveryDto) throws BirdnotesException {

		String pathUploadAttachment = uploadPath + recoveryPath + File.separator + mergedRecovery.getId();
		File dirName = new File(pathUploadAttachment);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		if (mergedRecovery.getAttachment() != null) {
			Attachment attachment = attachmentRepository.findOne(mergedRecovery.getAttachment().getId());
			if (StringUtils.isNotEmpty(attachment.getAttachmentBase64())) {
				BirdnotesUtils.saveBase64ToImage(attachment.getAttachmentBase64(), attachment.getAttachmentName(),
						pathUploadAttachment);
			}
		}
		return pathUploadAttachment;
	}

	private void deletedFile(ExpenseReportDto noteFraisDto, ExpenseReport expenseReport)
			throws IOException, BirdnotesException {
		String pathToFolder = uploadPath + expenseReportPath + File.separator + expenseReport.getId();
		File fileName = new File(pathToFolder + File.separator + expenseReport.getPieceJointe());
		if (expenseReport.getAttachmentBase64() != null
				&& !expenseReport.getAttachmentBase64().equals(noteFraisDto.getAttachmentBase64())) {
			try {
				Files.delete(fileName.toPath());
			} catch (NoSuchFileException x) {
				LOG.error(logSyncMarker, "Error when delete file : no such file or directory", x);
				throw new BirdnotesException("No such file or directory : " + x.getMessage());
			} catch (DirectoryNotEmptyException x) {
				LOG.error(logSyncMarker, "Error when delete file : file or directory  not empty", x);
				throw new BirdnotesException("File or directory  not empty: " + x.getMessage());
			}
		}
		if (StringUtils.isNotEmpty(noteFraisDto.getAttachmentBase64())) {
			BirdnotesUtils.saveBase64ToImage(noteFraisDto.getAttachmentBase64(), noteFraisDto.getAttachmentName(),
					pathToFolder);
		}
	}

	private void deletedFileON(OpportunityNoteRequestDto opportunityNoteDto, OpportunityNote opportunityNote)
			throws IOException, BirdnotesException {
		String pathToFolder = uploadPath + opportunityNotePath + File.separator + opportunityNote.getId();
		File fileName = new File(pathToFolder + File.separator + opportunityNote.getPieceJointe());
		if (opportunityNote.getAttachmentBase64() != null
				&& !opportunityNote.getAttachmentBase64().equals(opportunityNoteDto.getAttachmentBase64())) {
			try {
				Files.delete(fileName.toPath());
			} catch (NoSuchFileException x) {
				LOG.error(logSyncMarker, "Error when delete file : no such file or directory", x);
				throw new BirdnotesException("No such file or directory : " + x.getMessage());
			} catch (DirectoryNotEmptyException x) {
				LOG.error(logSyncMarker, "Error when delete file : file or directory  not empty", x);
				throw new BirdnotesException("File or directory  not empty: " + x.getMessage());
			}
		}
		if (StringUtils.isNotEmpty(opportunityNoteDto.getAttachmentBase64())) {
			BirdnotesUtils.saveBase64ToImage(opportunityNoteDto.getAttachmentBase64(),
					opportunityNoteDto.getNameAttachment(), pathToFolder);
		}
	}

	private ExpenseReport convert(ExpenseReportDto noteFraisDto, ExpenseReport noteFrais) {
		noteFrais.setDate(noteFraisDto.getDate());
		noteFrais.setMontant(noteFraisDto.getMontant());
		noteFrais.setDescription(noteFraisDto.getDescription());
		noteFrais.setPieceJointe(noteFraisDto.getAttachmentName());
		noteFrais.setAttachmentBase64(noteFraisDto.getAttachmentBase64());
		return noteFrais;
	}

	private Activity convert(ActivityDto activityDto, Activity activity) {
		activity.setActivityDate(activityDto.getDate());
		activity.setComment(activityDto.getComment());
		activity.setHourNumber(activityDto.getHourNumber());

		return activity;
	}

	@Override
	public Long insertIssue(Issue issue) throws BirdnotesException {

		Issue mergedIssue = issueRepository.save(issue);
		createFolderOfIssue(mergedIssue);
		//this.sendEmailIssueToSuperAdmin(issue);
		return mergedIssue.getId();

	}

	private String createFolderOfIssue(Issue mergedIssue) throws BirdnotesException {
		String pathUploadFileLog = uploadPath + mobileAppLogsPath + File.separator + mergedIssue.getId();
		File dirName = new File(pathUploadFileLog);
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		return pathUploadFileLog;
	}

	private void sendEmailIssueToSuperAdmin(Issue issue) {
		ConfigurationDto config = configurationService.findConfiguration();
		Locale locale = new Locale(config.getLanguage());
		String newIssueEmailHtmlBody = messageSource.getMessage("newIssueEmailHtmlBody", null, locale);
		MessageFormat messageFormat = new MessageFormat(newIssueEmailHtmlBody);
		String[] args = { issue.getUsername(), issue.getDescription() };
		String htmlBody = messageFormat.format(args);
		String[] t = { "<EMAIL>", "<EMAIL>" };
		ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, t, "New Issue On Bird Notes", htmlBody);
		Thread thread = new Thread(threadSendEmail);
		thread.start();
	}

	@Autowired
	public void setDelegateToDtoConvertor(DelegateToDtoConvertor delegateToDtoConvertor) {
		this.delegateToDtoConvertor = delegateToDtoConvertor;
	}

	@Autowired
	public void setGammeService(RangeService gammeService) {
		this.gammeService = gammeService;
	}

	@Autowired
	public void setProductService(ProductService productService) {
		this.productService = productService;
	}

	@Autowired
	public void setConfigurationService(ConfigurationService configurationService) {
		this.configurationService = configurationService;
	}

	@Autowired
	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}

	@Autowired
	public void setConvertDtoToPurchaseOrder(ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder) {
		this.convertDtoToPurchaseOrder = convertDtoToPurchaseOrder;
	}

	@Autowired
	public void setWholesalerToDtoConvertor(WholesalerToDtoConvertor wholesalerToDtoConvertor) {
		this.wholesalerToDtoConvertor = wholesalerToDtoConvertor;
	}

	@Autowired
	public void setConvertProspectToDto(ConvertProspectToDto convertProspectToDto) {
		this.convertProspectToDto = convertProspectToDto;
	}

	@Autowired
	public void setWholesalerService(WholesalerService wholesalerService) {
		this.wholesalerService = wholesalerService;
	}

	@Autowired
	public void setPotentialProductService(ProductsPotentialService potentialProductService) {
		this.potentialProductService = potentialProductService;
	}

	@Autowired
	public void setGoalService(GoalsService goalService) {
		this.goalService = goalService;
	}

	@Autowired
	public void setPurchaseOrderTemplateService(PurchaseOrderTemplateService purchaseOrderTemplateService) {
		this.purchaseOrderTemplateService = purchaseOrderTemplateService;
	}

	@Autowired
	public void setFreeQuantityRuleService(FreeQuantityRuleService freeQuantityRuleService) {
		this.freeQuantityRuleService = freeQuantityRuleService;
	}

	@Autowired
	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	@Autowired
	public void setDistanceService(DistanceService distanceService) {
		this.distanceService = distanceService;
	}

	@Autowired
	public void setGadgetService(GadgetService gadgetService) {
		this.gadgetService = gadgetService;
	}

	@Autowired
	public void setChargePlanRepository(LoadPlanRepository loadPlanRepository) {
		this.loadPlanRepository = loadPlanRepository;
	}
	
	@Autowired
	public void setPresentationTimeTrackingRepository(
			PresentationTimeTrackingRepository presentationTimeTrackingRepository) {
		this.presentationTimeTrackingRepository = presentationTimeTrackingRepository;
	}

	@Autowired
	public void loadPlanItemRepository(LoadPlanItemRepository loadPlanItemRepository) {
		this.loadPlanItemRepository = loadPlanItemRepository;
	}

	@Autowired
	public void setPlanningValidationRepository(PlanningValidationRepository planningValidationRepository) {
		this.planningValidationRepository = planningValidationRepository;
	}

	@Autowired
	public void setValidationStepRepository(ValidationStepRepository validationStepRepository) {
		this.validationStepRepository = validationStepRepository;
	}

	@Autowired
	public void setActivityRepository(ActivityRepository activityRepository) {
		this.activityRepository = activityRepository;
	}

	@Autowired
	public void setRecoveryRepository(RecoveryRepository recoveryRepository) {
		this.recoveryRepository = recoveryRepository;
	}

	@Autowired
	public void setAttachmentRepository(AttachmentRepository attachmentRepository) {
		this.attachmentRepository = attachmentRepository;
	}

	@Autowired
	public void setMessageRepository(MessageRepository messageRepository) {
		this.messageRepository = messageRepository;
	}

	@Autowired
	public void setMessageTagRepository(MessageTagRepository messageTagRepository) {
		this.messageTagRepository = messageTagRepository;
	}

	@Autowired
	public void setProspectService(ProspectService prospectService) {
		this.prospectService = prospectService;
	}

	@Autowired
	public void setSpecialityService(SpecialityService specialityService) {
		this.specialityService = specialityService;
	}

	@Autowired
	public void setProspectTypeService(ProspectTypeService prospectTypeService) {
		this.prospectTypeService = prospectTypeService;
	}

	@Autowired
	public void setEstablishmentService(EstablishmentService establishmentService) {
		this.establishmentService = establishmentService;
	}

	@Autowired
	public void setTypeNoteFraisService(ExpenseTypeService typeNoteFraisService) {
		this.typeNoteFraisService = typeNoteFraisService;
	}

	@Autowired
	public void setPurchaseOrderService(PurchaseOrderService purchaseOrderService) {
		this.purchaseOrderService = purchaseOrderService;
	}

	@Autowired
	public void setRecoveryService(RecoveryService recoveryService) {
		this.recoveryService = recoveryService;
	}

	@Autowired
	public void setAttachmentService(AttachmentService attachmentService) {
		this.attachmentService = attachmentService;
	}

	@Autowired
	public void setActivityTypeService(ActivityTypeService activityTypeService) {
		this.activityTypeService = activityTypeService;
	}

	@Autowired
	public void setPotentialService(PotentialService potentialService) {
		this.potentialService = potentialService;
	}

	@Autowired
	public void setNoteFraisService(ExpenseReportService noteFraisService) {
		this.noteFraisService = noteFraisService;
	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	@Autowired
	public void setProspectsAffectationRepository(ProspectsAffectationRepository prospectsAffectationRepository) {
		this.prospectsAffectationRepository = prospectsAffectationRepository;
	}

	@Autowired
	public void setProspectRepository(ProspectRepository prospectRepository) {
		this.prospectRepository = prospectRepository;
	}

	@Autowired
	public void setVisitRepository(VisitRepository visitRepository) {
		this.visitRepository = visitRepository;
	}

	@Autowired
	public void setPurchaseOrderTemplateRepository(PurchaseOrderTemplateRepository purchaseOrderTemplateRepository) {
		this.purchaseOrderTemplateRepository = purchaseOrderTemplateRepository;
	}

	@Autowired
	public void setProductRepository(ProductRepository productRepository) {
		this.productRepository = productRepository;
	}

	@Autowired
	public void setWholesalerRepository(WholesalerRepository wholesalerRepository) {
		this.wholesalerRepository = wholesalerRepository;
	}

	@Autowired
	public void setPotentialProductRepository(ProductsPotentialRepository potentialProductRepository) {
		this.potentialProductRepository = potentialProductRepository;
	}

	@Autowired
	public void setGadgetRepository(GiftRepository gadgetRepository) {
		this.gadgetRepository = gadgetRepository;
	}

	@Autowired
	public void setPlanningRepository(PlanningRepository planningRepository) {
		this.planningRepository = planningRepository;
	}

	@Autowired
	public void setActionMarketingRepository(ActionMarketingRepository actionMarketingRepository) {
		this.actionMarketingRepository = actionMarketingRepository;
	}

	@Autowired
	public void setOpportunityNoteRepository(OpportunityNoteRepository opportunityNoteRepository) {
		this.opportunityNoteRepository = opportunityNoteRepository;
	}

	@Autowired
	public void setLocationRepository(LocationRepository locationRepository) {
		this.locationRepository = locationRepository;
	}

	@Autowired
	public void setConfigurationRepository(ConfigurationRepository configurationRepository) {
		this.configurationRepository = configurationRepository;
	}

	@Autowired
	public void setVisitsProductsRepository(VisitsProductsRepository visitsProductsRepository) {
		this.visitsProductsRepository = visitsProductsRepository;
	}

	@Autowired
	public void setConvertDtoToProspect(ConvertDtoToProspect convertDtoToProspect) {
		this.convertDtoToProspect = convertDtoToProspect;
	}

	@Autowired
	public void setSectorRepository(SectorRepository sectorRepository) {
		this.sectorRepository = sectorRepository;
	}

	@Autowired
	public void setLocalityRepository(LocalityRepository localityRepository) {
		this.localityRepository = localityRepository;
	}

	@Autowired
	public void setSpecialityRepository(SpecialityRepository specialityRepository) {
		this.specialityRepository = specialityRepository;
	}

	@Autowired
	public void setTypeNoteFraisRepository(ExpenseTypeRepository expenseTypeRepository) {
		this.expenseTypeRepository = expenseTypeRepository;
	}

	@Autowired
	public void setPurchaseOrderRepository(PurchaseOrderRepository purchaseOrderRepository) {
		this.purchaseOrderRepository = purchaseOrderRepository;
	}

	@Autowired
	public void setNoteFraisRepository(ExpenseReportRepository noteFraisRepository) {
		this.noteFraisRepository = noteFraisRepository;
	}

	@Autowired
	public void setIssueRepository(IssueRepository issueRepository) {
		this.issueRepository = issueRepository;
	}

	@Autowired
	public void setActivityTypeRepository(ActivityTypeRepository activityTypeRepository) {
		this.activityTypeRepository = activityTypeRepository;
	}

	@Autowired
	public void setNotificationService(NotificationService notificationService) {
		this.notificationService = notificationService;
	}

	@Autowired
	public void setNotificationRuleRepository(NotificationRuleRepository notificationRuleRepository) {
		this.notificationRuleRepository = notificationRuleRepository;
	}

	@Autowired
	public void setValidationStepService(ValidationStepService validationStepService) {
		this.validationStepService = validationStepService;
	}

	@Autowired
	public void setValidationStatusRepository(ValidationStatusRepository validationStatusRepository) {
		this.validationStatusRepository = validationStatusRepository;
	}

	@Autowired
	public void setNotificationMessageBuilder(NotificationMessageBuilder notificationMessageBuilder) {
		this.notificationMessageBuilder = notificationMessageBuilder;
	}

	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	public void setMobileAppLogsPath(String mobileAppLogsPath) {
		this.mobileAppLogsPath = mobileAppLogsPath;
	}

	public void setPurchaseOrderPath(String purchaseOrderPath) {
		this.purchaseOrderPath = purchaseOrderPath;
	}

	public void setRecoveryPath(String recoveryPath) {
		this.recoveryPath = recoveryPath;
	}

	public void setExpenseReportPath(String expenseReportPath) {
		this.expenseReportPath = expenseReportPath;
	}

	public void setOpportunityNotePath(String opportunityNotePath) {
		this.opportunityNotePath = opportunityNotePath;
	}

	@Autowired
	public void setProspectsAffectationService(ProspectsAffectationService prospectsAffectationService) {
		this.prospectsAffectationService = prospectsAffectationService;
	}

	@Autowired
	public void setPlanningService(PlanningService planningService) {
		this.planningService = planningService;
	}

	@Autowired
	public void setActionMarketingService(ActionMarketingService actionMarketingService) {
		this.actionMarketingService = actionMarketingService;
	}

	@Autowired
	public void setActivityService(ActivityService activityService) {
		this.activityService = activityService;
	}

	@Autowired
	public void setOpportunityNoteService(OpportunityNoteService opportunityNoteService) {
		this.opportunityNoteService = opportunityNoteService;
	}

	@Autowired
	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}

	public void sendPurchaseOrderToErpAsync(List<Long> savedPurchaseOrderIds, Configuration config) {
		
		if(!config.getErpUrl().isEmpty()) {
			
			ExecutorService executor = Executors.newSingleThreadExecutor();
		    executor.execute(() -> {
		        try {
		            sendPurchaseOrderToErp(savedPurchaseOrderIds, config);
		        } catch (Exception e) {
		            e.printStackTrace();
		        }
		    });
		    executor.shutdown();
		}
	    
	}

	private void sendPurchaseOrderToErp(List<Long> savedPurchaseOrderIds, Configuration config) throws Exception {
	    
		// Odoo Connection Parameters
	    String url = config.getErpUrl();
	    //String db = "tekum";
	    //String username = "<EMAIL>";
	    String erpParams = config.getErpParams();
	    erpParams = erpParams.replace("'", "\"");
	    JSONObject json = new JSONObject(erpParams);
	    String db = json.getString("db");
	    String username = json.getString("username");
	    

	    // ==================================================================
	    // 1. AUTHENTICATION
	    // ==================================================================
	    XmlRpcClientConfigImpl commonConfig = new XmlRpcClientConfigImpl();
	    commonConfig.setServerURL(new URL(url + "/xmlrpc/2/common"));
	    XmlRpcClient authClient = new XmlRpcClient();
	    authClient.setConfig(commonConfig);
	    
	    String password = "n2*!r@T?-!M9/-%"; // Replace with your actual password
	    Integer uid = (Integer) authClient.execute("authenticate",
	            Arrays.asList(db, username, password, new HashMap<>()));
    
	    /*
	    String apiToken = "your_api_token_here"; // Use the generated API token
	    Integer uid = (Integer) authClient.execute("authenticate", 
	        Arrays.asList(db, username, apiToken, new HashMap<>()));*/

	    System.out.println("Authenticated with UID: " + uid);

	    // ==================================================================
	    // 2. SETUP OBJECT CLIENT
	    // ==================================================================
	    XmlRpcClientConfigImpl objectConfig = new XmlRpcClientConfigImpl();
	    objectConfig.setServerURL(new URL(url + "/xmlrpc/2/object"));

	    XmlRpcClient objectClient = new XmlRpcClient();
	    objectClient.setConfig(objectConfig);

	    // ==================================================================
	    // 3. COLLECT DATA FOR BULK INSERTION
	    // ==================================================================
	    List<PurchaseOrder> purchaseOrders = purchaseOrderRepository.findByIdentifiers(savedPurchaseOrderIds);
	    List<Map<String, Object>> pickingValuesList = new ArrayList<>();
	    Map<Long, Integer> purchaseOrderPickingIds = new HashMap<>();

	    for (PurchaseOrder purchaseOrder : purchaseOrders) {
	        Map<String, Object> pickingValues = new HashMap<>();
	        pickingValues.put("picking_type_id", 10);
	        pickingValues.put("partner_id", purchaseOrder.getVisit().getProspect().getCode()); // 1
	        pickingValues.put("location_id", purchaseOrder.getWholesaler().getCode()); // 5
	        pickingValuesList.add(pickingValues);
	    }

	    // ==================================================================
	    // 4. BULK CREATE PICKINGS
	    // ==================================================================
	    
	    Object[] pickingIdsArray = (Object[]) objectClient.execute("execute_kw", Arrays.asList(
	    	    db, uid, password,
	    	    "stock.picking", "create",
	    	    Arrays.asList(pickingValuesList)));
	    
	    List<Integer> pickingIds = Arrays.stream(pickingIdsArray)
                .map(obj -> (Integer) obj)
                .collect(Collectors.toList());

	    System.out.println("Created pickings with IDs: " + pickingIds);

	    for (int i = 0; i < purchaseOrders.size(); i++) {
	        purchaseOrderPickingIds.put(purchaseOrders.get(i).getId(),  pickingIds.get(i));
	    }

	    // ==================================================================
	    // 5. COLLECT STOCK MOVES DATA
	    // ==================================================================
	    List<Map<String, Object>> moveValuesList = new ArrayList<>();

	    for (PurchaseOrder purchaseOrder : purchaseOrders) {
	        Integer pickingId = purchaseOrderPickingIds.get(purchaseOrder.getId());
	        List<VisitsProducts> visitProducts = visitsProductsRepository.findAllByVisitId(purchaseOrder.getVisit().getId());

	        for (VisitsProducts vp : visitProducts) {
	            Map<String, Object> moveValues = new HashMap<>();
	            moveValues.put("product_id", vp.getProduct().getCode()); // 1
	            moveValues.put("product_uom_qty", vp.getOrderQuantity());
	            moveValues.put("product_uom", 1);
	            moveValues.put("picking_id", pickingId);
	            moveValues.put("name", "Transfer");
	            moveValuesList.add(moveValues);
	        }
	    }

	    // ==================================================================
	    // 6. BULK CREATE STOCK MOVES
	    // ==================================================================
	    Object[] moveIdsArray = (Object[]) objectClient.execute("execute_kw", Arrays.asList(
	            db, uid, password,
	            "stock.move", "create",
	            Arrays.asList(moveValuesList)
	    ));
	    
	    List<Integer> moveIds = Arrays.stream(moveIdsArray)
                .map(obj -> (Integer) obj)
                .collect(Collectors.toList());

	    System.out.println("Created moves with IDs: " + moveIds);
	}

    
}
