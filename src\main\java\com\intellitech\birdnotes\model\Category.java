package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;

@Entity
@Table(name = BirdnotesConstants.Tables.CATEGORY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Category implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer id;
	private String name;

	public Category() {
		super();
	}
	
	public Category(Integer id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.CATEGORY_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.CATEGORY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.CATEGORY_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = BirdnotesConstants.Columns.NAME, length = BirdnotesConstants.Numbers.N_85)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

}
