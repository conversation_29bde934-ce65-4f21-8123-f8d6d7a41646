package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.model.User;

public class OpportunityNoteDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String name;
	private Float budget;
	private Date date;
	private Set<ProductDto> products;
	private Set<ProspectDto> prospects;
	private Set<ProspectDto> pharmacies;
	private String description;
	private String pieceJointe;
	private List<FileNamePath> nameFile;

	


	private String nameAttachment;
	private String attachmentBase64;
	private User user;
	private String userName;


	private String status;

	public OpportunityNoteDto(Long id, String name, Float budget, Date date, Set<ProductDto> products,String nameAttachment,String attachmentBase64,
			Set<ProspectDto> prospects,Set<ProspectDto> pharmacies, User user, String userName, String status, String description) {
		super();
		this.id = id;
		this.name = name;
		this.budget = budget;
		this.date = date;
		this.products = products;
		this.prospects = prospects;
		this.pharmacies=pharmacies;
		this.nameAttachment=nameAttachment;
		this.attachmentBase64=attachmentBase64;
		this.user = user;
		this.userName = userName;
		this.status = status;
		this.description = description;
	}
	
	
	public List<FileNamePath> getNameFile() {
		return nameFile;
	}

	public void setNameFile(List<FileNamePath> nameFile) {
		this.nameFile = nameFile;
	}
	
	

	public String getPieceJointe() {
		return pieceJointe;
	}

	public void setPieceJointe(String pieceJointe) {
		this.pieceJointe = pieceJointe;
	}

	public String getNameAttachment() {
		return nameAttachment;
	}

	public void setNameAttachment(String nameAttachment) {
		this.nameAttachment = nameAttachment;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public Set<ProspectDto> getPharmacies() {
		return pharmacies;
	}

	public void setPharmacies(Set<ProspectDto> pharmacies) {
		this.pharmacies = pharmacies;
	}

	public OpportunityNoteDto() {
		super();
	}

	
	

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Float getBudget() {
		return budget;
	}

	public void setBudget(Float budget) {
		this.budget = budget;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Set<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(Set<ProductDto> products) {
		this.products = products;
	}

	public Set<ProspectDto> getProspects() {
		return prospects;
	}

	public void setProspects(Set<ProspectDto> prospects) {
		this.prospects = prospects;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public void setUserName(String findUserById) {
		this.userName = findUserById;

	}

	public String getUserName() {
		return userName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
