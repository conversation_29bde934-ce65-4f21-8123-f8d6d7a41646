package com.intellitech.birdnotes.controller;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.SampleSupplyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Mission;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.MissionDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyItemDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.SampleSupplyService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/samples")
public class SampleSupplyController {
	private static final Logger LOG = LoggerFactory.getLogger(SampleSupplyController.class);
	@Autowired
	private SampleSupplyService sampleSupplyService;
	@Autowired
	UserService userService;
	@Autowired
	private ProductService productService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	DelegateService delegateService;
	@Autowired
	private SectorService sectorService;

	@RequestMapping(value = "/saveSampleSupply", method = RequestMethod.POST)
	public ResponseEntity<Long> saveSampleSupply(@RequestBody SampleSupplyDto sampleSupplyDto) {
		try {
			if (userService.checkHasPermission("SAMPLE_ADD")) {
				SampleSupply sampleSupplySaved = sampleSupplyService.saveSampleSupply(sampleSupplyDto);
				if (sampleSupplySaved != null) {
					return new ResponseEntity<>(sampleSupplySaved.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving sampleSupplySaved", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving sampleSupplySaved", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "saveMission", method = RequestMethod.POST, produces = MediaType.APPLICATION_PDF_VALUE
			+ "; charset=utf-8")
	public ResponseEntity<InputStreamResource> saveMission(@RequestBody MissionDto missionDto,
			HttpServletResponse response) {
		try {
			if (userService.checkHasPermission("SAMPLE_ADD")) {
				Mission missionSaved = sampleSupplyService.saveMission(missionDto);
				// String fileName = "missionExport" + missionSaved.getId() + ".pdf";
				String fileName = sampleSupplyService.generateMissionPDF(missionSaved);
				sampleSupplyService.generateMissionReport(fileName, missionSaved);
				// response.setContentType("application/pdf");
				response.setHeader("Content-Disposition", "attachment;" + fileName);
				response.setHeader("filename", "generatedMissionReport"+((new Date()).getTime()));
				response.setHeader("Access-Control-Expose-Headers", "filename");

				if (missionSaved != null) {
					InputStream fileInputStream = new FileInputStream(fileName);
					InputStreamResource inputStream = new InputStreamResource(fileInputStream);
					return ResponseEntity.ok().contentType(MediaType.APPLICATION_PDF).body(inputStream);

				}

				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving sampleSupplySaved", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving sampleSupplySaved", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllDataSampleSupplyForm", method = RequestMethod.GET)
	public ResponseEntity<SampleSupplyFormData> getAllDataSampleSupplyForm() {
		try {
			SampleSupplyFormData result = sampleSupplyService.getAllDataSampleSupplyForm();
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getItemsBySampleSupplyId/{sampleSupplyId}", method = RequestMethod.GET)
	public ResponseEntity<List<SampleSupplyItemDto>> getItemsBySampleSupplyId(
			@PathVariable("sampleSupplyId") Long sampleSupplyId) {
		try {
			List<SampleSupplyItemDto> result = sampleSupplyService.getItemsBySampleSupplyId(sampleSupplyId);
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getSampleByUserProductAndDate", method = RequestMethod.POST)

	public ResponseEntity<List<SampleSupplyDto>> getSampleByUserProductAndDate(
			@RequestBody SampleRequestDto sampleRequest) {

		try {
			if (userService.checkHasPermission("SAMPLE_VIEW")) {
				List<SampleSupplyDto> samplesSupplyDto = sampleSupplyService
						.getSampleByUserProductAndDate(sampleRequest);
				return new ResponseEntity<>(samplesSupplyDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting all samplesSupply", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("SAMPLE_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("SAMPLE_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllSectors", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> getAllSectors() {
		try {
			if (userService.checkHasPermission("SAMPLE_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<SectorDto> sectorDto = sectorService.findAll();
				return new ResponseEntity<>(sectorDto, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "deleteSampleSupply/{sampleSupplyId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteSampleSupply(@PathVariable("sampleSupplyId") Long sampleSupplyId) {
		try {
			if (userService.checkHasPermission("SAMPLE_DELETE")) {
				sampleSupplyService.deleteSampleSupply(sampleSupplyId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sample supply", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in deleteSampleSupply", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "updateSampleSupply", method = RequestMethod.PUT)
	public ResponseEntity<String> updateSampleSupply(@RequestBody SampleSupplyDto sampleSupplyDto) {
		try {
			if (userService.checkHasPermission("SAMPLE_EDIT")) {
				sampleSupplyService.saveSampleSupply(sampleSupplyDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException fe) {
			LOG.error("An exception occurred :Non-Authoritative Information when update sample supply", fe);
			return new ResponseEntity<>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in updateSampleSupply", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
