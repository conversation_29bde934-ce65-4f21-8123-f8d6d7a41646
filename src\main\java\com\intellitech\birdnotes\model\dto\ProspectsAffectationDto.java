package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;



public class ProspectsAffectationDto implements Serializable {
	

	private static final long serialVersionUID = 1L;
	private Long id;
	private String userName;
	private Long userId;
	private String prospectName;
	private Long prospectId;

	

	public ProspectsAffectationDto(Long id, String userName, Long userId,String prospectName,Long prospectId) {
		super();
		this.id = id;
		this.userName = userName;
		this.userId = userId;
		this.prospectName = prospectName;
		this.prospectId = prospectId;
	}

	public ProspectsAffectationDto() {super();}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public String getProspectName() {
		return prospectName;
	}
	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}
	public Long getProspectId() {
		return prospectId;
	}
	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}

}

