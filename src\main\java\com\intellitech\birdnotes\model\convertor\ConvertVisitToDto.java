package com.intellitech.birdnotes.model.convertor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;


@Component("convertVisitToDto")
public class ConvertVisitToDto {

	
	@Autowired
	private UserRepository userRepository;

	@Autowired
	private CurrentUser currentUser;
	
	
	public VisitDto convert(Visit visit) throws BirdnotesException {
		if(visit == null) {
			throw new BirdnotesException("visit is null");
		}
		
		VisitDto visitDto = new VisitDto();
		
		visitDto.setGadgetQuantity(visit.getGadgetQuantity());
		visitDto.setPatientNumber(visit.getPatientNumber());
		visitDto.setVisitDate(visit.getVisitDate());
		Prospect prospect = visit.getProspect();
		visitDto.setProspectId(prospect.getId());
		visitDto.setGeneralNote(visit.getGeneralNote());
		visitDto.setIdentifier(visit.getIdentifier());
		visitDto.setId(visit.getId());
		Delegate delegate = visit.getDelegate();
		//visitDto.setUserName(user.getFirstName()+" "+user.getLastName());
	
		
		return visitDto;
	}
}

