package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.RoleRequestDto;

public interface RoleService {

	List<RoleDto> findAll() throws BirdnotesException;

	List<Role> add(RoleRequestDto roleRequestDto) throws BirdnotesException;

	void delete(Integer id) throws BirdnotesException;

	void update(RoleDto roleDto) throws BirdnotesException;

}
