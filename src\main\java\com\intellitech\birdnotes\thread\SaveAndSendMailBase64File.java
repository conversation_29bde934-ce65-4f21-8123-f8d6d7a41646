package com.intellitech.birdnotes.thread;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.MessageFormat;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.mail.javamail.JavaMailSender;


import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.enumeration.EmailType;
import com.intellitech.birdnotes.enumeration.OrderValidation;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToPurchaseOrder;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.NotificationRuleRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.util.BirdnotesUtils;

import net.sf.jasperreports.engine.JRException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SaveAndSendMailBase64File implements Runnable {

	
	private JavaMailSender javaMailSender;

	private List<PurchaseOrderDto> purchaseOrderList;
	private static final String IMGBALISE = " <br/> <img src='";
	
	PurchaseOrderDto mergedPurchaseOrder;
	ConfigurationService configurationService;
	UserRepository userRepository;
	PurchaseOrderService purchaseOrderService;
	PurchaseOrderRepository purchaseOrderRepository;
	ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder;
	NotificationRuleRepository notificationRuleRepository;

	String uploadUrl;
	String logoPath;
	String sendWholesalerMailSubject;
	String sendCancellationMailSubject; 
	String sendCancellationMailHtmlBody;
	String sendWholesalerMailHtmlBodyWithAttachment;
	String sendWholesalerMailHtmlBodyWithoutAttachment;
	String purchaseOrderPath;
	
	String generatedDocumentPath;
	String generatedDoPath;
	String uploadPath;
	
	
	private static final Logger LOG = LoggerFactory.getLogger(SaveAndSendMailBase64File.class);

	public JavaMailSender getJavaMailSender() {
		return javaMailSender;
	}

	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}

	public NotificationRuleRepository getNotificationRuleRepository() {
		return notificationRuleRepository;
	}

	public void setNotificationRuleRepository(NotificationRuleRepository notificationRuleRepository) {
		this.notificationRuleRepository = notificationRuleRepository;
	}

	
	public List<PurchaseOrderDto> getPurchaseOrderList() {
		return purchaseOrderList;
	}

	public void setPurchaseOrderList(List<PurchaseOrderDto> purchaseOrderList) {
		this.purchaseOrderList = purchaseOrderList;
	}
	


	public String getGeneratedDoPath() {
		return generatedDoPath;
	}

	public void setGeneratedDoPath(String generatedDoPath) {
		this.generatedDoPath = generatedDoPath;
	}

	public PurchaseOrderDto getMergedPurchaseOrder() {
		return mergedPurchaseOrder;
	}

	public void setMergedPurchaseOrder(PurchaseOrderDto mergedPurchaseOrder) {
		this.mergedPurchaseOrder = mergedPurchaseOrder;
	}

	public ConfigurationService getConfigurationService() {
		return configurationService;
	}

	public void setConfigurationService(ConfigurationService configurationService) {
		this.configurationService = configurationService;
	}

	public UserRepository getUserRepository() {
		return userRepository;
	}

	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	public PurchaseOrderService getPurchaseOrderService() {
		return purchaseOrderService;
	}

	public void setPurchaseOrderService(PurchaseOrderService purchaseOrderService) {
		this.purchaseOrderService = purchaseOrderService;
	}

	public PurchaseOrderRepository getPurchaseOrderRepository() {
		return purchaseOrderRepository;
	}

	public void setPurchaseOrderRepository(PurchaseOrderRepository purchaseOrderRepository) {
		this.purchaseOrderRepository = purchaseOrderRepository;
	}

	public ConvertDtoToPurchaseOrder getConvertDtoToPurchaseOrder() {
		return convertDtoToPurchaseOrder;
	}

	public void setConvertDtoToPurchaseOrder(ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder) {
		this.convertDtoToPurchaseOrder = convertDtoToPurchaseOrder;
	}

	public String getUploadUrl() {
		return uploadUrl;
	}

	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}

	public String getLogoPath() {
		return logoPath;
	}

	public void setLogoPath(String logoPath) {
		this.logoPath = logoPath;
	}

	public String getSendWholesalerMailSubject() {
		return sendWholesalerMailSubject;
	}

	public void setSendWholesalerMailSubject(String sendWholesalerMailSubject) {
		this.sendWholesalerMailSubject = sendWholesalerMailSubject;
	}

	public String getSendCancellationMailSubject() {
		return sendCancellationMailSubject;
	}

	public void setSendCancellationMailSubject(String sendCancellationMailSubject) {
		this.sendCancellationMailSubject = sendCancellationMailSubject;
	}

	public String getSendCancellationMailHtmlBody() {
		return sendCancellationMailHtmlBody;
	}

	public void setSendCancellationMailHtmlBody(String sendCancellationMailHtmlBody) {
		this.sendCancellationMailHtmlBody = sendCancellationMailHtmlBody;
	}

	public String getSendWholesalerMailHtmlBodyWithAttachment() {
		return sendWholesalerMailHtmlBodyWithAttachment;
	}

	public void setSendWholesalerMailHtmlBodyWithAttachment(String sendWholesalerMailHtmlBodyWithAttachment) {
		this.sendWholesalerMailHtmlBodyWithAttachment = sendWholesalerMailHtmlBodyWithAttachment;
	}

	public String getSendWholesalerMailHtmlBodyWithoutAttachment() {
		return sendWholesalerMailHtmlBodyWithoutAttachment;
	}

	public void setSendWholesalerMailHtmlBodyWithoutAttachment(String sendWholesalerMailHtmlBodyWithoutAttachment) {
		this.sendWholesalerMailHtmlBodyWithoutAttachment = sendWholesalerMailHtmlBodyWithoutAttachment;
	}

	public String getPurchaseOrderPath() {
		return purchaseOrderPath;
	}

	public void setPurchaseOrderPath(String purchaseOrderPath) {
		this.purchaseOrderPath = purchaseOrderPath;
	}



	public String getGeneratedDocumentPath() {
		return generatedDocumentPath;
	}

	public void setGeneratedDocumentPath(String generatedDocumentPath) {
		this.generatedDocumentPath = generatedDocumentPath;
	}

	public String getUploadPath() {
		return uploadPath;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}


	
	public SaveAndSendMailBase64File(JavaMailSender javaMailSender, List<PurchaseOrderDto> purchaseOrderList,
			ConfigurationService configurationService,
			UserRepository userRepository, PurchaseOrderService purchaseOrderService,
			PurchaseOrderRepository purchaseOrderRepository, ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder,
			String uploadUrl, String logoPath, String sendWholesalerMailSubject, String sendCancellationMailSubject,
			String sendCancellationMailHtmlBody, String sendWholesalerMailHtmlBodyWithAttachment,
			String sendWholesalerMailHtmlBodyWithoutAttachment, String purchaseOrderPath, String generatedDocumentPath, String generatedDoPath,
			String uploadPath, NotificationRuleRepository notificationRuleRepository) {
		super();
	
		this.javaMailSender = javaMailSender;
		
		this.purchaseOrderList = purchaseOrderList;
		
		this.configurationService = configurationService;
		this.userRepository = userRepository;
		this.purchaseOrderService = purchaseOrderService;
		this.purchaseOrderRepository = purchaseOrderRepository;
		this.convertDtoToPurchaseOrder = convertDtoToPurchaseOrder;
		this.uploadUrl = uploadUrl;
		this.logoPath = logoPath;
		this.sendWholesalerMailSubject = sendWholesalerMailSubject;
		this.sendCancellationMailSubject = sendCancellationMailSubject;
		this.sendCancellationMailHtmlBody = sendCancellationMailHtmlBody;
		this.sendWholesalerMailHtmlBodyWithAttachment = sendWholesalerMailHtmlBodyWithAttachment;
		this.sendWholesalerMailHtmlBodyWithoutAttachment = sendWholesalerMailHtmlBodyWithoutAttachment;
		this.purchaseOrderPath = purchaseOrderPath;
		this.generatedDocumentPath = generatedDocumentPath;
		this.generatedDoPath = generatedDoPath;
		this.uploadPath = uploadPath;
		this.notificationRuleRepository = notificationRuleRepository;
	}

	private String createFolder(Long id, String path, String uploadPath) throws BirdnotesException {
		
		String pathUpload = uploadPath + path + File.separator + id;
		File dirName = new File(pathUpload);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		return pathUpload;
	}
	
	
	/*public void saveAndMailPurchaseOrder(PurchaseOrderDto mergedPurchaseOrder, ConfigurationDto config, UserRepository userRepository, 
			PurchaseOrderService purchaseOrderService, PurchaseOrderRepository purchaseOrderRepository, ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder, 
			String uploadUrl, String logoPath, String sendWholesalerMailSubject, String sendCancellationMailSubject, 
			String sendCancellationMailHtmlBody, String sendWholesalerMailHtmlBodyWithAttachment, String sendWholesalerMailHtmlBodyWithoutAttachment, 
			String purchaseOrderPath, String generatedDocumentPath, String uploadPath , String attachmentPath) {

		try {
			
			
			String pathLogo = config.getServerPath() + uploadUrl + logoPath + "/" + config.getLogo();
			
			MessageFormat subjectFormat = new MessageFormat(sendWholesalerMailSubject);

			MessageFormat messageFormat;
			if(mergedPurchaseOrder.getEmailType().equals(EmailType.CANCELLATION.toString())) {
				subjectFormat = new MessageFormat(sendCancellationMailSubject);
				messageFormat = new MessageFormat(sendCancellationMailHtmlBody);
			}else{
				subjectFormat = new MessageFormat(sendWholesalerMailSubject);
				
				if(!"".equals(mergedPurchaseOrder.getAttachmentBase64()) && !"".equals(mergedPurchaseOrder.getAttachmentName())){
					messageFormat = new MessageFormat(sendWholesalerMailHtmlBodyWithAttachment);
				}else {
					messageFormat = new MessageFormat(sendWholesalerMailHtmlBodyWithoutAttachment);
				}
			}

			List<User> supervisors = userRepository.getSuperviserOfUser(mergedPurchaseOrder.getUser().getId());
			Set<String> to = new HashSet<String>();

			if (mergedPurchaseOrder.getUser().getEmail() != null && !"".equals(mergedPurchaseOrder.getUser().getEmail())) {
				to.add(mergedPurchaseOrder.getUser().getEmail());
			} else {
				LOG.error("Delegate  email is null, user id = " + mergedPurchaseOrder.getUser().getId());
			}
			for (User supervisor : supervisors) {
				if (supervisor.getEmail() != null) {
					to.add(supervisor.getEmail());
				}
			}
			
			for (User topUser : userRepository.getTopUsers()) {
				if (topUser.getEmail() != null) {
					to.add(topUser.getEmail());
				}
			}
			
			WholesalerDto wholesaler = null;
			if( mergedPurchaseOrder.getEmailType().equals(EmailType.CANCELLATION.toString())) {
				wholesaler = mergedPurchaseOrder.getOldWholesaler();
			}else {
				wholesaler = mergedPurchaseOrder.getWholesaler();
			}
			


			String[] args = { mergedPurchaseOrder.getProspect().getFirstName() + " " + mergedPurchaseOrder.getProspect().getLastName(), mergedPurchaseOrder.getProspect().getAddress(), 
					wholesaler.getName(), config.getName(), 
					mergedPurchaseOrder.getProspect().getSectorDto().getName(), mergedPurchaseOrder.getProspect().getLocalityDto().getName() };

			String subject = subjectFormat.format(args);
			String fileUrl = config.getServerPath() + uploadUrl + purchaseOrderPath + "/" + mergedPurchaseOrder.getId() + "/" + mergedPurchaseOrder.getAttachmentName();
			String htmlBody = messageFormat.format(args);
			
			if(!"".equals(mergedPurchaseOrder.getAttachmentName()) && !"".equals(mergedPurchaseOrder.getAttachmentBase64())) {
				htmlBody = htmlBody + " <br>  <a href = '" + fileUrl
						+ "' target ='_blank' >Cliquez ici pour voir le bon de commande en ligne </a> <br> " + IMGBALISE
						+ pathLogo + "'>";
			}
			
			if (wholesaler.getEmail() != null && !"".equals(wholesaler.getEmail())) {
				to.add(wholesaler.getEmail());
			}
			//Generate PO as pdf
			String documentPath = createFolder(mergedPurchaseOrder.getId(), generatedDocumentPath, uploadPath) + "/" + "generatedPurchaseOrder.pdf" ;
			try {
				purchaseOrderService.generatePurchaseOrder(documentPath, mergedPurchaseOrder.getId());
			} catch (FileNotFoundException e) {
				e.printStackTrace();
			} catch (JRException e) {
				e.printStackTrace();
			}
			
				
			if(mergedPurchaseOrder.getEmailType().equals(EmailType.VALIDATION.toString()) ) {
				mergedPurchaseOrder.setMailSent(true);
				PurchaseOrder purchaseOrder = convertDtoToPurchaseOrder.convert(mergedPurchaseOrder);
				purchaseOrderRepository.save(purchaseOrder);
			}
			
			if(mergedPurchaseOrder.getAttachmentBase64()==null || mergedPurchaseOrder.getAttachmentName()==null || 
					"".equals(mergedPurchaseOrder.getAttachmentName()) || "".equals(mergedPurchaseOrder.getAttachmentBase64())) {
				if(to != null && to.stream().toArray(String[]::new).length > 0) {
					BirdnotesUtils.sendEmail(javaMailSender, to.stream().toArray(String[]::new), subject, htmlBody);
				}
			}		
			else {
					if(mergedPurchaseOrder.getAttachmentBase64()!=null && mergedPurchaseOrder.getAttachmentName()!=null) {
						
						if(to != null && to.stream().toArray(String[]::new).length > 0) {

							BirdnotesUtils.sendEmailWithAttachment(javaMailSender, config.getName(), to.stream().toArray(String[]::new),null, subject, 
									htmlBody, attachmentPath, mergedPurchaseOrder.getAttachmentName());
						
					}
					}
				} 
				

		} catch (BirdnotesException e) {

			LOG.error("Error creating folder for purchase order", e);
		}

	}*/
	
	public void saveAttachment(PurchaseOrderDto mergedPurchaseOrder, String attachmentPath, String documentPath ) {

		if((mergedPurchaseOrder.getAttachmentBase64()!=null && mergedPurchaseOrder.getAttachmentName()!=null ) && 
				(!mergedPurchaseOrder.getAttachmentName().equals("") && !mergedPurchaseOrder.getAttachmentBase64().equals(""))) {	
				BirdnotesUtils.saveBase64ToImage(mergedPurchaseOrder.getAttachmentBase64(), mergedPurchaseOrder.getAttachmentName(), attachmentPath);	
			
				
		}
		
		
	}
	@Override
	public void run() {
		
		for(PurchaseOrderDto purchaseOrder : this.purchaseOrderList){
			String attachmentPoPath ="";
			String attachmentDoPath ="";
			String poDocumentPath = "";
			String doDocumentPath = "";
			try {
				
					attachmentPoPath = createFolder(purchaseOrder.getId(),this.purchaseOrderPath, this.uploadPath );
				
				
				
			} catch (BirdnotesException e) {
				LOG.error("Error creating folder for purchase order", e);
			}
			try {
				poDocumentPath = createFolder(purchaseOrder.getId(), this.generatedDocumentPath, this.uploadPath);
				doDocumentPath = createFolder(purchaseOrder.getId(), this.generatedDoPath, this.uploadPath);
			} catch (BirdnotesException e) {
				LOG.error("Error creating folder for generated purchase order", e);
			}
			
				saveAttachment(purchaseOrder, attachmentPoPath, poDocumentPath);
			
			
			//Generate PO as pdf
			if(!purchaseOrder.getEmailType().equals(EmailType.CANCELLATION.toString())){
				try {
						if(purchaseOrder.getGeneratePo() != null && purchaseOrder.getGeneratePo()== true) {
							this.purchaseOrderService.generatePurchaseOrder(poDocumentPath+ "/" + "generatedPurchaseOrder.pdf", purchaseOrder.getId(), "Bon de commande");
						}
						if(purchaseOrder.getGenerateDo() != null && purchaseOrder.getGenerateDo()== true) {
							this.purchaseOrderService.generatePurchaseOrder(doDocumentPath+ "/" + "generatedDeliveryOrder.pdf", purchaseOrder.getId(), "Bon de livraison");
						}else {
							this.purchaseOrderService.generatePurchaseOrder(poDocumentPath+ "/" + "generatedPurchaseOrder.pdf", purchaseOrder.getId(), "Bon de commande");
						}
					} catch(FileNotFoundException e) {
					LOG.error("error in generating purchase order : " + e);
				} catch (JRException e) {
					LOG.error("error in generating purchase order : " + e);
				}
			}
			ConfigurationDto config = this.configurationService.findConfiguration();
			if(config.getOrderValidation().equals(OrderValidation.DELEGATE.toString())) {
				if(purchaseOrder.getEmailType().equals(EmailType.CANCELLATION.toString())) {
					/*BirdnotesUtils.sendMailPurchaseOrder(purchaseOrder, config, this.userRepository, 
							this.purchaseOrderService, this.purchaseOrderRepository, this.convertDtoToPurchaseOrder, 
							this.uploadUrl, this.logoPath, this.sendWholesalerMailSubject, this.sendCancellationMailSubject, 
							this.sendCancellationMailHtmlBody, this.sendWholesalerMailHtmlBodyWithAttachment, this.sendWholesalerMailHtmlBodyWithoutAttachment, 
							this.purchaseOrderPath, null, this.uploadPath , attachmentPath, null, javaMailSender);*/

				}else {
					BirdnotesUtils.sendMailPurchaseOrder(purchaseOrder, config, this.userRepository, 
							this.purchaseOrderService, this.purchaseOrderRepository, this.convertDtoToPurchaseOrder, 
							this.uploadUrl, this.logoPath, this.sendWholesalerMailSubject, this.sendCancellationMailSubject, 
							this.sendCancellationMailHtmlBody, this.sendWholesalerMailHtmlBodyWithAttachment, this.sendWholesalerMailHtmlBodyWithoutAttachment, 
							this.purchaseOrderPath, this.generatedDocumentPath, this.uploadPath , attachmentPoPath, poDocumentPath,doDocumentPath, javaMailSender, this.notificationRuleRepository);

				}
							}
		}	
		
	}
	}

	




