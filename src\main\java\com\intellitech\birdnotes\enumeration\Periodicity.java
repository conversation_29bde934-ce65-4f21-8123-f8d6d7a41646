package com.intellitech.birdnotes.enumeration;

import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum Periodicity {
	ONCE(BirdnotesConstants.Periodicity.ONCE), PERIODIC(BirdnotesConstants.Periodicity.PERIODIC);

	private String name;

	Periodicity(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public String toString() {
		if (BirdnotesConstants.Periodicity.ONCE.equals(name)) {
			return BirdnotesConstants.Periodicity.ONCE;
		}
		return BirdnotesConstants.Periodicity.PERIODIC;

	}

}
