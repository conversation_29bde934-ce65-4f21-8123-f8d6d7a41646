package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ReportCron;
import com.intellitech.birdnotes.model.dto.ReportCronDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.request.ReportCronRequest;
import com.intellitech.birdnotes.service.ReportCronService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/report-cron")
public class ReportCronController {
	private static final Logger LOG = LoggerFactory.getLogger(ReportCronController.class);

	@Autowired
	private ReportCronService reportCronService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/save", method = RequestMethod.POST)
	public ResponseEntity<String> saveReportCron(@RequestBody ReportCronRequest reportCronRequest) {
		try {
			if (userService.checkHasPermission("REPORT_CRON_ADD")) {
				reportCronService.add(reportCronRequest);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add reportCron", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new reportCron", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/findAll", method = RequestMethod.GET)
	public ResponseEntity<List<ReportCronDto>> findAllReportCron() {

		try {
			if (userService.checkHasPermission("REPORT_CRON_VIEW")) {
				List<ReportCronDto> reportCronDto = reportCronService.findAll();
				return new ResponseEntity<>(reportCronDto, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all reportCrons", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteReportCron(@PathVariable("id") Integer id) {
	    try {
	        if (userService.checkHasPermission("REPORT_CRON_DELETE")) {
	            reportCronService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting reportCron", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the reportCron with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/saveReportCron", method = RequestMethod.POST)
	public ResponseEntity<Integer> saveReportCron(@RequestBody ReportCronDto reportCronDto) {
		try {
			if (userService.checkHasPermission("REPORT_CRON_EDIT") || userService.checkHasPermission("REPORT_CRON_ADD")) {
				ReportCron savedReportCron = reportCronService.saveReportCron(reportCronDto);
				if (savedReportCron != null) {
					return new ResponseEntity<>(savedReportCron.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving savedReportCron", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving savedReportCron", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "getAllUsers", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllUsersWithRoles() {
		try {

			if (userService.checkHasPermission("REPORT_CRON_VIEW")) {
				List<UserDto> userDtos = userService.getSubUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
