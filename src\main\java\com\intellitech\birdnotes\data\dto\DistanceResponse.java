package com.intellitech.birdnotes.data.dto;

import java.util.ArrayList;
import java.util.List;

public class DistanceResponse {
	
	
		 ArrayList <Object> destination_addresses = new ArrayList <Object> ();
		 ArrayList <Object> origin_addresses = new ArrayList <Object> ();
		 ArrayList <Object> rows = new ArrayList <Object> ();
		 private String status;

		 public List<Object> getDestination_addresses() {
			return destination_addresses;
		}

		public void setDestination_addresses(ArrayList<Object> destination_addresses) {
			this.destination_addresses = destination_addresses;
		}

		public List<Object> getOrigin_addresses() {
			return origin_addresses;
		}

		public void setOrigin_addresses(ArrayList<Object> origin_addresses) {
			this.origin_addresses = origin_addresses;
		}

		public ArrayList<Object> getRows() {
			return rows;
		}

		public void setRows(ArrayList<Object> rows) {
			this.rows = rows;
		}

		public String getStatus() {
		  return status;
		 }


		 public void setStatus(String status) {
		  this.status = status;
		 }
		

}
