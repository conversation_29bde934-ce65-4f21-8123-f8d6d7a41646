package com.intellitech.birdnotes.data.dto;

public class ProspectFilterDto {
	
	public long sectorId;
	public long specialityId;
	public long potentialId;
	public String activity;
	public long localityId;
	public boolean plannedProspects;
	public boolean visitedProspects;
	
	
	
	public ProspectFilterDto() {
		super();
		// TODO Auto-generated constructor stub
	}
	
	public ProspectFilterDto(long sectorId, long specialityId, long potentialId, long activityId, long localityId,
			boolean plannedProspects, boolean visitedProspects) {
		super();
		this.sectorId = sectorId;
		this.specialityId = specialityId;
		this.potentialId = potentialId;
		this.activity = activity;
		this.localityId = localityId;
		this.plannedProspects = plannedProspects;
		this.visitedProspects = visitedProspects;
	}



	public long getSectorId() {
		return sectorId;
	}
	public void setSectorId(long sectorId) {
		this.sectorId = sectorId;
	}
	public long getSpecialityId() {
		return specialityId;
	}
	public void setSpecialityId(long specialityId) {
		this.specialityId = specialityId;
	}
	public long getPotentialId() {
		return potentialId;
	}
	public void setPotentialId(long potentialId) {
		this.potentialId = potentialId;
	}
	
	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}

	public long getLocalityId() {
		return localityId;
	}
	public void setLocalityId(long localityId) {
		this.localityId = localityId;
	}
	public boolean isPlannedProspects() {
		return plannedProspects;
	}
	public void setPlannedProspects(boolean plannedProspects) {
		this.plannedProspects = plannedProspects;
	}
	public boolean isVisitedProspects() {
		return visitedProspects;
	}
	public void setVisitedProspects(boolean visitedProspects) {
		this.visitedProspects = visitedProspects;
	}
	
	
	
	
	
	
	
	
	
	

}
