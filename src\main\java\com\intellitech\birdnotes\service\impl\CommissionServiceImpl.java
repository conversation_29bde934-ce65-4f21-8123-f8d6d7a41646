package com.intellitech.birdnotes.service.impl;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.ProductRevenueByMonthDto;
import com.intellitech.birdnotes.enumeration.CommissionType;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.CommissionItem;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.DelegateCommission;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.convertor.ConvertGiftToDto;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.CommissionDto;
import com.intellitech.birdnotes.model.dto.CommissionItemDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.DelegateCommissionDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.model.dto.ThresholdValueDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.CommissionItemRepository;
import com.intellitech.birdnotes.repository.CommissionRepository;
import com.intellitech.birdnotes.repository.DelegateCommissionRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.CommissionItemRepository;
import com.intellitech.birdnotes.repository.CommissionRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.WholesalerRepository;
import com.intellitech.birdnotes.service.CommissionService;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.service.CommissionService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("CommissionService")
@Transactional
public class CommissionServiceImpl implements CommissionService {
	@Autowired
	private CommissionRepository commissionRepository;
	@Autowired
	private UserRepository userRepository;
	@Autowired
	private CommissionItemRepository commissionItemRepository;
	@Autowired
	private	ProductRepository productRepository;
	@Autowired
	private	WholesalerRepository wholesalerRepository;
	@Autowired
	private	VisitRepository visitRepository;
	@Autowired
	private ValidationStatusRepository validationStatusRepository;
	@Autowired
	private ValidationStepService validationStepService;
	@Autowired
	private NotificationService notificationService;
	@Autowired
	private DynamicQueries dynamicQueries;
	@Autowired
	private ProspectRepository prospectRepository;
	
	@Autowired
	private DelegateCommissionRepository delegateCommissionRepository;
	
	private UserService userService;

	private DelegateRepository delegateRepository;
	
	private UserToDtoConvertor userToDtoConvertor ;
	
	private ProductToDtoConvertor productToDtoConvertor ;
	
	private WholesalerToDtoConvertor wholesalerToDtoConvertor ;
	
	private ConvertProspectToDto convertProspectToDto; 
	
	private ConfigurationService configurationService;
	
	Logger LOG = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	public void setUserService(UserService userService) {
		this.userService = userService;
		
	}
	
	@Autowired
	private MessageSource messageSource;
	
	@Autowired
	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}
	@Autowired
	public void setProductToDtoConvertor(ProductToDtoConvertor productToDtoConvertor) {
		this.productToDtoConvertor = productToDtoConvertor;
	}
	
	@Autowired
	public void setConvertProspectToDto(ConvertProspectToDto convertProspectToDto) {
		this.convertProspectToDto = convertProspectToDto;
	}
	@Autowired
	public void setWholesalerToDtoConvertor(WholesalerToDtoConvertor wholesalerToDtoConvertor) {
		this.wholesalerToDtoConvertor = wholesalerToDtoConvertor;
	}
	
	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}
	
    
	@Override
	public Commission saveCommission(CommissionDto commissionDto) throws BirdnotesException {
	    
	    Commission existingCommission = commissionRepository.findByNameAndAnotherId(commissionDto.getName(), commissionDto.getId());
	    if (existingCommission != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    Commission commission = null;
	    if (commissionDto.getId() != null) {
	        commission = commissionRepository.findOne(commissionDto.getId());
	    }
	    if (commission == null) {
	        commission = new Commission();
	    }

	    commission.setFirstDate(commissionDto.getFirstDate());
	    commission.setLastDate(commissionDto.getLastDate());
	    commission.setName(commissionDto.getName());
	    commission.setType(CommissionType.valueOf(commissionDto.getType()));

	    List<Product> products = productRepository.findAll(commissionDto.getProductsId());
	    if (products != null) {
	        commission.setProducts(products);
	    }

	    if (commissionDto.getWholesalersId() != null && !commissionDto.getWholesalersId().isEmpty()) {
	        List<Prospect> wholesalers = prospectRepository.getWholesalersByIds(commissionDto.getWholesalersId());
	        if (wholesalers != null) {
	            commission.setWholesalers(wholesalers);
	        }
	    }

	    List<User> users = userRepository.findAll(commissionDto.getUsersId());
	    if (users != null) {
	        commission.setUsers(users);
	    }

	    Commission savedCommission = commissionRepository.save(commission);

	    for (CommissionItemDto commissionItemDto : commissionDto.getCommissionItem()) {
	        CommissionItem commissionItem = null;
	        if (commissionItemDto.getId() != null) {
	            commissionItem = commissionItemRepository.findOne(commissionItemDto.getId());
	        }
	        if (commissionItem == null) {
	            commissionItem = new CommissionItem();
	        }

	        commissionItem.setCommission(savedCommission);
	        commissionItem.setThreshold(commissionItemDto.getThreshold());
	        commissionItem.setValue(commissionItemDto.getValue());
	        commissionItemRepository.save(commissionItem);
	    }

	    return savedCommission;
	}

	
	
	@Override
	public List<CommissionDto> getCommissionByUserProductAndDate(
			CommissionDto commissionRequestDto) throws BirdnotesException, ParseException{
		StringBuilder query = new StringBuilder();
		List<CommissionDto> commissionDtoList = new ArrayList<>();
		List<Commission> commissionList;
		Map<String, Object> parameters = new HashMap<>();
		query.append(
				"SELECT distinct com from Commission com ");
		if(commissionRequestDto.getSelectedUserId()!= null && commissionRequestDto.getSelectedUserId()!= 0) {
		    query.append(" join com.users u ");		
		}
		
		if(commissionRequestDto.getSelectedProductId()!= null && commissionRequestDto.getSelectedProductId()!= 0) {
			query.append(" join com.products p  ");
		}
		query.append(" where (date(com.firstDate) >= date(:firstDate) and date(com.firstDate) <= date(:lastDate)) OR (date(com.lastDate) >= date(:firstDate) and date(com.lastDate) <=  date(:lastDate))");
		parameters.put(BirdnotesConstants.QueryBuild.FIRST_DATE, commissionRequestDto.getFirstDate());
		parameters.put(BirdnotesConstants.QueryBuild.LAST_DATE, commissionRequestDto.getLastDate());
		if(commissionRequestDto.getSelectedUserId()!= null && commissionRequestDto.getSelectedUserId()!= 0) {
			query.append(" And u.id =:userId ");
			parameters.put("userId", commissionRequestDto.getSelectedUserId());	
		}
		if(commissionRequestDto.getSelectedProductId()!= null && commissionRequestDto.getSelectedProductId()!= 0) {
			query.append(" And p.id =:productId  ");
			parameters.put("productId", commissionRequestDto.getSelectedProductId());
		}
		/*List<Long> subUsersIds = userService.getSubUsersIds();
		query.append(" And u.id IN (:subUsersIds) ");
		parameters.put("subUsersIds", subUsersIds);*/

		
		commissionList = dynamicQueries.findCommission(query.toString(), parameters);
		
		for(Commission commission : commissionList) {
			List<ProductDto> products = new ArrayList<>();
			List<ProspectDto> wholesalers = new ArrayList<>();
			List<Long> productIds = new ArrayList<>();
			List<Long> userIds = new ArrayList<>();
			List<Long> wholesalerIds = new ArrayList<>();
			List<UserDto>users = new ArrayList<>();
			CommissionDto commissionDto = new CommissionDto();
			commissionDto.setId(commission.getId());
			commissionDto.setFirstDate(commission.getFirstDate());
			commissionDto.setLastDate(commission.getLastDate());
			commissionDto.setName(commission.getName());
			for(Product product :commission.getProducts()) {
				products.add(productToDtoConvertor.convert(product));
				productIds.add(product.getId());
			}
			for(Prospect wholesaler :commission.getWholesalers()) {
				wholesalers.add(convertProspectToDto.convert(wholesaler));
				wholesalerIds.add(wholesaler.getId());
			}
			for(User user : commission.getUsers()) {
				users.add(userToDtoConvertor.convert(user));
				userIds.add(user.getId());
			}
			commissionDto.setProducts(products);
			commissionDto.setProductsId(productIds);
			commissionDto.setWholesalers(wholesalers);
			commissionDto.setUsersId(userIds);
			commissionDto.setWholesalersId(wholesalerIds);
			commissionDto.setUsers(users);
			commissionDto.setType(commission.getType().name().toString());	
			commissionDtoList.add(commissionDto);
		}
		

		return commissionDtoList;
		
	}
	
	
	@Override
	public List<DelegateCommissionDto> getDelegateCommissionByDate(
			CommissionDto commissionRequestDto) throws BirdnotesException, ParseException{
		
		List<CommissionDto> commissionDtoList = new ArrayList<>();
			
		commissionDtoList = getCommissionByUserProductAndDate(commissionRequestDto);
		/****** Convert CommissionDto to DelegateCommissionDto ******/
		List<DelegateCommissionDto> delegatecommissionDtoList = new ArrayList<>();
		for(CommissionDto commissionDto : commissionDtoList) {
			commissionDto.setCommissionItem(getCommissionItem(commissionDto));
			delegatecommissionDtoList.add(new DelegateCommissionDto(commissionDto));
		}
				
		for(DelegateCommissionDto delegateCommissionDto : delegatecommissionDtoList) {
			/*** Search months between commission's dates ***/
			getMonthOfCommission(delegateCommissionDto);
			/**** Calculate delegate revenue of the products commission ****/
			getProductRevenueByMonth(delegateCommissionDto, commissionRequestDto.getSelectedUserId());
			/****** Calculate delegate commissions  *****/
			
			for(ProductRevenueByMonthDto productRevenueByMonth : delegateCommissionDto.getProductRevenueByMonth()) {
				if(productRevenueByMonth.getProductsRevenue() != null) {
					Float commissionAmount = calculateDelegateCommission(delegateCommissionDto, productRevenueByMonth.getProductsRevenue());
					delegateCommissionDto.setMonth(productRevenueByMonth.getMonth());
					delegateCommissionDto.setProductRevenue(productRevenueByMonth.getProductsRevenue());
					DelegateCommission delegateCommission = delegateCommissionRepository.findCommissionByUserAndDate(delegateCommissionDto.getFirstDate(), 
							delegateCommissionDto.getLastDate(), commissionRequestDto.getSelectedUserId(), delegateCommissionDto.getId());
					Commission commission = commissionRepository.findOne(delegateCommissionDto.getId());
					User user = userRepository.findOne(commissionRequestDto.getSelectedUserId());
					boolean isNew = false;
					if(delegateCommission == null) {
						delegateCommission = new DelegateCommission();
						delegateCommission.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
						isNew= true;
					}
								
					delegateCommissionDto.setStatus(delegateCommission.getStatus().toString());
					delegateCommissionDto.setDelegateCommission(commissionAmount);
					DateFormat formater = new SimpleDateFormat("dd-MM-yyyy");
					delegateCommission.setAmount(commissionAmount);
					delegateCommission.setCommission(commission);
					delegateCommission.setUser(user);
					delegateCommission.setMonth(formater.parse(productRevenueByMonth.getMonth()));
					DelegateCommission savedDelegateCommission = delegateCommissionRepository.save(delegateCommission);
					if(isNew) {
						validationStepService.addValidationStatus("NEW_DELEGATE_COMMISSION", savedDelegateCommission.getId(), user.getId());
					}
					List<ValidationStatusDto> listValidationStatusDto = validationStepService
							.findByDelegateCommission(savedDelegateCommission);
						delegateCommissionDto.setValidationStatusDto(listValidationStatusDto);
				}			
			}	
		}
		

		return delegatecommissionDtoList;
		
	}
	
	public void getMonthOfCommission(DelegateCommissionDto commissionDto) {
		/*** Search months between commission's dates ***/
		 Calendar beginDate = Calendar.getInstance();
	     Calendar finishDate = Calendar.getInstance();
	     beginDate.setTime(commissionDto.getFirstDate());
		 finishDate.setTime(commissionDto.getLastDate());
		 DateFormat formater = new SimpleDateFormat("dd-MM-yyyy");
		 List <ProductRevenueByMonthDto> productRevenueByMonthList = new ArrayList<>();
		 
		 while (beginDate.before(finishDate)) {
	            // add one month to date per loop
	            String month = formater.format(beginDate.getTime()).toUpperCase();
	            ProductRevenueByMonthDto productRevenueByMonth = new ProductRevenueByMonthDto();
	            productRevenueByMonth.setMonth(month);
	            beginDate.add(Calendar.MONTH, 1);
	            productRevenueByMonthList.add(productRevenueByMonth);
	            	            
	     }
		 commissionDto.setProductRevenueByMonth(productRevenueByMonthList);	
	}
	
	public void getProductRevenueByMonth(DelegateCommissionDto commissionDto, Long userId) throws ParseException {
		 DateFormat formater = new SimpleDateFormat("dd-MM-yyyy");
		 for(ProductRevenueByMonthDto productRevenueByMonth : commissionDto.getProductRevenueByMonth()) {
			 Date firstDateOfMonth = formater.parse(productRevenueByMonth.getMonth());
			 Calendar date = Calendar.getInstance();
			 date.setTime(firstDateOfMonth);
			 date.set(Calendar.DAY_OF_MONTH, date.getActualMaximum(Calendar.DAY_OF_MONTH));
			 Date lastDateOfMonth = date.getTime();
			 Float productsRevenue = visitRepository.getProductCommissionRevenue(firstDateOfMonth, lastDateOfMonth, commissionDto.getProductsId(), userId);
			 productRevenueByMonth.setProductsRevenue(productsRevenue);
		 }
	}
	
	public Float calculateDelegateCommission(DelegateCommissionDto commissionDto , Float productsRevenue) {
		/****** Get thresholds and values and sort it  *****/
		// commissionDto.setCommissionItem(getCommissionItem(commissionDto));
		 List<ThresholdValueDto>thresholdValues = new ArrayList<>();
		
		 for(CommissionItemDto commissionItem : commissionDto.getCommissionItem()) {
			 ThresholdValueDto thresholdValueDto = new ThresholdValueDto();
			 thresholdValueDto.setThreshold(commissionItem.getThreshold());
			 thresholdValueDto.setValue(commissionItem.getValue());
			 thresholdValues.add(thresholdValueDto);
		 }
		 ThresholdValueDto thresholdValueDto = new ThresholdValueDto();
		 thresholdValueDto.setThreshold((float) 0);
		 thresholdValueDto.setValue((float) 0);
		 thresholdValues.add(thresholdValueDto);
		 Collections.sort(thresholdValues, new Comparator<ThresholdValueDto>(){
		     public int compare(ThresholdValueDto t1, ThresholdValueDto t2){
		    	 return t1.getThreshold().compareTo(t2.getThreshold());
		     }});
		 
		 /****** Calculate delegate commissions  *****/
		 float delegateCommission = 0 ;
		 /*for(int i = 0 ; i<thresholdValues.size() ; i++) {
			 if(productsRevenue > thresholdValues.get(i).getThreshold()) {
				 if(commissionDto.getType().toString().equals("PERCENTAGE")) {
					 delegateCommission = productsRevenue * thresholdValues.get(i).getValue()/100;
				 }else {
					 delegateCommission = thresholdValues.get(i).getValue();
				 }
				 commissionDto.setDelegateCommission(delegateCommission);
				 break;
			 }
		 }*/
		 //PERCENTAGE
		 if(productsRevenue != null) {
			 if(commissionDto.getType().toString().equals("PERCENTAGE")) {
				int i = thresholdValues.size()-1 ; 
				while (i>=0) {
					if(productsRevenue < thresholdValues.get(i).getThreshold()) {
						i--;
					}else {
						delegateCommission += (productsRevenue - thresholdValues.get(i).getThreshold()) * (thresholdValues.get(i).getValue()/100);
						break;
					}
				 }
				 /*for(int j = 0; j<=i ; j++) {
					 delegateCommission += thresholdValues.get(j).getThreshold() * (thresholdValues.get(j).getValue()/100);
				 }*/
				 while(i-1>=0) {
					 delegateCommission += (thresholdValues.get(i).getThreshold() - thresholdValues.get(i-1).getThreshold())
							 * (thresholdValues.get(i).getValue()/100);
					 i--;
				 }
				//VALUE
			 }else {
				 for(int i = 0 ; i<thresholdValues.size() ; i++) {
					 if(productsRevenue > thresholdValues.get(i).getThreshold()) {
						 delegateCommission += thresholdValues.get(i).getValue();
					 }
				}
				 
			 }
		 }
		return delegateCommission;
		
	}
		
	@Override
	public void delete (Long id)throws BirdnotesException {

			Commission commission = commissionRepository.findOne(id);
			
			for (CommissionItem commissionItem : commissionItemRepository.findByCommission(commission) )
			{
				commissionItemRepository.delete(commissionItem);
			}
			
			for ( User user : userRepository.findUsersOfCommission(commission.getId()) ) 
			{
				if(user.getCommissions()!=null) {
					user.getCommissions().remove(commission);
			}	
				
				userRepository.save(user);
			}
			
			for ( Product product : productRepository.findProductOfCommission(commission.getId()) ) 
			{
				if(product.getCommissions()!=null) {
					product.getCommissions().remove(commission);
			}	
				
				productRepository.save(product);
			}
			commissionRepository.delete(id);



	}
		@Override
		public void deleteCommissionItem (Long id) throws BirdnotesException {


				commissionItemRepository.delete(id);
			
	}
		
	@Override
	public List<CommissionItemDto> getCommissionItem(
			CommissionDto commissionRequestDto) {
		
		List<CommissionItemDto> commissionItemDtoList = new ArrayList<>();
		
		Commission commission = commissionRepository.findOne(commissionRequestDto.getId());
		
		Set<CommissionItem> commissionItems = commissionItemRepository.findByCommission(commission);
		
		for(CommissionItem commissionItem : commissionItems) {
			CommissionItemDto commissionItemDto = new CommissionItemDto();
			commissionItemDto.setId(commissionItem.getId());
			commissionItemDto.setThreshold(commissionItem.getThreshold());
			commissionItemDto.setValue(commissionItem.getValue());
			commissionItemDtoList.add(commissionItemDto);
		}
		return commissionItemDtoList;
	}
	
	
	@Override
	public void acceptValidationStep(long commissionValidationId) throws BirdnotesException {
		
	
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(commissionValidationId);
		List<ValidationStatus> allValidationStatus =validationStatusRepository.findByDelegateCommissionOrderByRankAsc (userValidationStatus.getDelegateCommission());	
		
		
		boolean isWorkflowFinished = this.validationStepService.accept(allValidationStatus, commissionValidationId, userValidationStatus.getDelegateCommission().getId());
		
		if(isWorkflowFinished) {
			userValidationStatus.getDelegateCommission().setStatus(UserValidationStatus.ACCEPTED);
			delegateCommissionRepository.save( userValidationStatus.getDelegateCommission());
			notificationService.sendSingleNotification( userValidationStatus.getDelegateCommission().getUser(), null, "delegateCommissionValidationNotificationMessage");
		}else {
			
			 userValidationStatus.getDelegateCommission().setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			 delegateCommissionRepository.save( userValidationStatus.getDelegateCommission());
		}	
				
	}

	@Override
	public void refuseValidationStep (long commissionValidationId) throws BirdnotesException {
		this.validationStepService.refuse(commissionValidationId);
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(commissionValidationId);
		userValidationStatus.getDelegateCommission().setStatus(UserValidationStatus.REFUSED);
		delegateCommissionRepository.save(userValidationStatus.getDelegateCommission());
		notificationService.sendSingleNotification(userValidationStatus.getDelegateCommission().getUser(), null, "delegateCommissionRefusNotificationMessage");
	}

	
	
}
