package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/potentials")
public class PotentialController {

	private static final Logger LOG = LoggerFactory.getLogger(PotentialController.class);

	@Autowired
	private PotentialService potentialService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllPotentials(@RequestBody List<PotentialDto> potentialRequestDtos) {
		try {
			potentialService.addAll(potentialRequestDtos);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add potentials", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding potentials", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addPotential(@RequestBody PotentialDto potentialRequestDto) {
		try {
			if (userService.checkHasPermission("POTENTIEL_ADD")) {
				potentialService.add(potentialRequestDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add potential", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new potential", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "findAllPotentials", method = RequestMethod.GET)
	public ResponseEntity<List<PotentialDto>> findAllPotentials() {

		try {
			if (userService.checkHasPermission("POTENTIEL_VIEW")) {
				List<PotentialDto> potentialsDto = potentialService.findAll();
				return new ResponseEntity<>(potentialsDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all potentials", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePotential(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("POTENTIEL_DELETE")) {
	            potentialService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting potential", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the potential with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}





	@RequestMapping(value = "/savePotential", method = RequestMethod.POST)
	public ResponseEntity<String> savePotential(@RequestBody PotentialDto potentialDto) {

	    try {
	        if (userService.checkHasPermission("POTENTIEL_EDIT")) {
	            Potential savedPotential = potentialService.savePotential(potentialDto);
	            if (savedPotential != null) {
	                return new ResponseEntity<>(savedPotential.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving potential", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving potential", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

}
