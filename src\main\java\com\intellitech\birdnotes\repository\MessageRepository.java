package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Attachment;
import com.intellitech.birdnotes.model.Message;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;

public interface MessageRepository extends JpaRepository<Message, Long> {

	@Query("SELECT m FROM Message m WHERE m.identifier = ?1 And m.user.id = ?2")
	Message findByIdentifier(Long identifier, Long userId);
	
		
	@Query("SELECT m from Message m WHERE m.user.id  = :userId ")
	List<Message> findByUser(@Param("userId") Long userId);
	
	
	@Query("SELECT m from Message m WHERE DATE(m.date) >=  DATE(:stardDate) AND DATE(m.date) <=  DATE(:endDate) ")
	List<Message> findByDate(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);
	
	@Query("SELECT m from Message m WHERE DATE(m.date) >=  DATE(:stardDate) AND DATE(m.date) <=  DATE(:endDate)  and m.user.id=:userId")
	List<Message> findByDateAndUser(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate , @Param("userId") Long userId);
}
