package com.intellitech.birdnotes.service;

import org.springframework.http.ResponseEntity;

import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Issue;
import com.intellitech.birdnotes.model.dto.ReceiveRequestInput;
import com.intellitech.birdnotes.model.dto.ReceiveResponsedDto;
import com.intellitech.birdnotes.model.dto.SendRequestDto;
import com.intellitech.birdnotes.model.dto.SendResponseDto;

public interface SynchronisationService {
	
	ReceiveResponsedDto receive(Long userId, ReceiveRequestInput synchronisationReceiveInput) throws BirdnotesException;
	SendResponseDto send(SendRequestDto reportListDto, Long userId) throws BirdnotesException;
	Long insertIssue(Issue issueDto)throws BirdnotesException;
	
}

