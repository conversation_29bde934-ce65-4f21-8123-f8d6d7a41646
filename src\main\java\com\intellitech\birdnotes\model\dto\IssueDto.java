package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class IssueDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;

	private String username;

	private String description;

	private Date date;
	
	private String filePath;

	public IssueDto(Long id, String username, String description, Date date) {
		super();
		this.id = id;
		this.username = username;
		this.description = description;
		this.date = date;
	}

	public IssueDto(String username, String description, Date date) {
		super();
		this.username = username;
		this.description = description;
		this.date = date;
	}

	public IssueDto() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

}
