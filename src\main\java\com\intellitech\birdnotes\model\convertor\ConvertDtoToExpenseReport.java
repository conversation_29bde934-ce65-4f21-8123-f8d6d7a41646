package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;

@Component("convertDtoToNoteFrais")
public class ConvertDtoToExpenseReport {
	
	private static final Logger LOG = LoggerFactory.getLogger(ConvertDtoToExpenseReport.class);

	public ExpenseReport convert(ExpenseReportDto expenseReportDto) throws BirdnotesException {

		if (expenseReportDto == null) {
			LOG.error("expenseReportDto is null");
			throw new BirdnotesException("expenseReportDto is null");
		}

		ExpenseReport expenseReport = new ExpenseReport();
		expenseReport.setDescription(expenseReportDto.getDescription());
		expenseReport.setPieceJointe(expenseReportDto.getPieceJointe());
		expenseReport.setAttachmentBase64(expenseReportDto.getAttachmentBase64());
		expenseReport.setDate(expenseReportDto.getDate());
		ExpenseTypeDto expenseTypeDto = expenseReportDto.getExpenseTypeDto();
		ExpenseType expenseType = new ExpenseType();
		if (expenseTypeDto != null && expenseTypeDto.getId() != null) {
			expenseType.setId(expenseTypeDto.getId());
		} else {
			LOG.error("typeNoteFrais is null");
			throw new BirdnotesException("typeNoteFrais dto is null");
		}
		expenseReport.setExpenseType(expenseType);
		return expenseReport;

	}

}
