package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.ReportValidation;

public interface ReportValidationRepository extends JpaRepository<ReportValidation, Long> {
	@Query("SELECT r from ReportValidation r  where (date(r.visitDate) between date(?1) and date(?2)) AND r.delegate.id= ?3 ")
	List<ReportValidation> findReportsValidationByDateAndUser(Date firstDate, Date endDate, Long userId);
	
	@Modifying
	@Query("DELETE FROM ReportValidation r WHERE (date(r.visitDate) BETWEEN date(?1) AND date(?2)) AND r.delegate.id = ?3")
	void deleteReportsValidationByDateAndUser(Date firstDate, Date endDate, Long userId);

}
