package com.intellitech.birdnotes.model.request;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;

public class ActionMarquetingRequest {
	private Long id;
	private UserValidationStatus status;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}
}
