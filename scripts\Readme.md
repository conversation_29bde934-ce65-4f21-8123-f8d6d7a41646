- Import from K8s database pod to postgresql/db-init-scripts folder

          example : sudo ./import-k8s-to-dump.sh demo


- Import from K8s database pod to local database

          example : sudo ./import-k8s-to-local-db.sh demo


- Export from local database to postgresql/db-init-scripts folder

		example : sudo ./export-from-local-db.sh demo


- Import database from  postgresql/db-init-scripts folder to local database :

        example : sudo ./import-to-local-db.sh demo


- Import from docker container to local database

         example : sudo ./import-docker-to-local-db.sh demo









