package com.intellitech.birdnotes.model;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.REPORT_CRON, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ReportCron {

	@Id
	@SequenceGenerator(name = Sequences.REPORT_CRON_SEQUENCE, sequenceName = Sequences.REPORT_CRON_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.REPORT_CRON_SEQUENCE)
	@Column(name = Columns.ID)
	private Integer id;
	
	@Column(name = Columns.PERIOD)
	private String period;
	
	@Column(name = Columns.LINK)
	private String link;
	
	@JsonIgnore
	@OneToMany (mappedBy = "reportCron")
	private List<User> users;

	public ReportCron() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public List<User> getUsers() {
		return users;
	}

	public void setUsers(List<User> users) {
		this.users = users;
	}
	
	
}
