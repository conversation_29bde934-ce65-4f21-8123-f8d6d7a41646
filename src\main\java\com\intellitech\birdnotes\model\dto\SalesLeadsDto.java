package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Set;
public class SalesLeadsDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Set<Long> userId;
	private Set<Long> prospectId;
	private Set<Integer> quantity;
	public SalesLeadsDto() {
		super();
	}
	public SalesLeadsDto(Set<Long> userId, Set<Long> prospectId, Set<Integer> quantity) {
		super();
		this.userId = userId;
		this.prospectId = prospectId;
		this.quantity = quantity;
	}
	public Set<Long> getUserId() {
		return userId;
	}
	public void setUserId(Set<Long> userId) {
		this.userId = userId;
	}
	public Set<Long> getProspectId() {
		return prospectId;
	}
	public void setProspectId(Set<Long> prospectId) {
		this.prospectId = prospectId;
	}
	public Set<Integer> getQuantity() {
		return quantity;
	}
	public void setQuantity(Set<Integer> quantity) {
		this.quantity = quantity;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	

}
