package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class CoverageRequestDto  implements Serializable {

	private static final long serialVersionUID = 1L;
	private String firstDate;
	private String lastDate;
	private Long selectedGroup;
	private Long selectedUser;
	
	public CoverageRequestDto() {
		super();
	}
	public CoverageRequestDto(String firstDate, String lastDate, Long selectedGroup, Long selectedUser) {
		super();
		this.firstDate = firstDate;
		this.lastDate = lastDate;
		this.selectedGroup = selectedGroup;
		this.selectedUser = selectedUser ;
	}
	public String getFirstDate() {
		return firstDate;
	}
	public void setFirstDate(String firstDate) {
		this.firstDate = firstDate;
	}
	public String getLastDate() {
		return lastDate;
	}
	public void setLastDate(String lastDate) {
		this.lastDate = lastDate;
	}
	public Long getSelectedGroup() {
		return selectedGroup;
	}
	public void setSelectedGroup(Long selectedGroup) {
		this.selectedGroup = selectedGroup;
	}
	public Long getSelectedUser() {
		return selectedUser;
	}
	public void setSelectedUser(Long selectedUser) {
		this.selectedUser = selectedUser;
	}
	

}
