package com.intellitech.birdnotes.security;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.service.UserService;

@Service("birdnotesUserDetailsService")
public class BirdnotesUserDetailsService implements UserDetailsService {

	@Autowired
	private UserService userService;
 

	@Override
	public UserDetails loadUserByUsername(String username) {

		if (username.trim().isEmpty()) {
			throw new UsernameNotFoundException("username is empty");
		}

		UserDto userDto = userService.findByUsername(username);

		if (userDto == null) {
			throw new UsernameNotFoundException("User " + username + " not found");
		}

		BirdnotesUser birdnotesUser = new BirdnotesUser(userDto.getUsername(), userDto.getPassword(),
				getGrantedAuthorities(userDto));

		birdnotesUser.setUserDto(userDto);
		return birdnotesUser;
	}

	private List<GrantedAuthority> getGrantedAuthorities(UserDto userDto) {

		List<GrantedAuthority> authorities = new ArrayList<>();
		Set<Role> roles = userDto.getRoles();
		for (Role role : roles) {
			authorities.add(new SimpleGrantedAuthority(role.getName()));
		}

		return authorities;
	}
}
