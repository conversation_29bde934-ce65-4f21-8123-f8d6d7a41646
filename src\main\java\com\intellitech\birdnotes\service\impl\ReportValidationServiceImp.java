package com.intellitech.birdnotes.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.enumeration.DelegateProspectLocationStatus;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.ReportValidation;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ReportValidationDto;
import com.intellitech.birdnotes.model.dto.StatusReportValidation;
import com.intellitech.birdnotes.model.request.ReportValidationRequest;
import com.intellitech.birdnotes.repository.ReportValidationRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.ReportValidationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;


@Service("reportValidationService")
@Transactional
public class ReportValidationServiceImp implements ReportValidationService {
	@Autowired
	private ReportValidationRepository reportValidationRepository;
	@Autowired
	private VisitRepository visitRepository;
	@Autowired
	private  ConfigurationService configurationService;
	@Autowired
	private NotificationService notificationService;
	@Autowired
	private ValidationStepService validationStepService;
	@Autowired
	private NotificationMessageBuilder notificationMessageBuilder;
	@Autowired
	private MessageSource messageSource;
	@Autowired
	private UserService userService;
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy"); 
	
   
 
	@Override
	public List<ReportValidationDto> getReportsValidationByDateAndUser(ReportValidationRequest reportValidationRequest)
			throws BirdnotesException {

		List<ReportValidation> reportsValidation = reportValidationRepository.findReportsValidationByDateAndUser(reportValidationRequest.getVisitDate(),
				reportValidationRequest.getVisitDate(), reportValidationRequest.getSelectedUser());
		List<ReportValidationDto> result = new ArrayList<>();

		if (reportsValidation != null && !reportsValidation.isEmpty()) {
			for (ReportValidation reportValidation : reportsValidation) {
				ReportValidationDto reportValidationDto = new ReportValidationDto();
				reportValidationDto.setId(reportValidation.getId());
				reportValidationDto.setDelegateId(reportValidation.getDelegate().getId());
				reportValidationDto.setDelegateName(
						reportValidation.getDelegate().getFirstName() +" "+ reportValidation.getDelegate().getLastName());
				reportValidationDto.setValidVisitsNumber(reportValidation.getValidVisitsNumber());
				reportValidationDto.setInvalidVisitsNumber(reportValidation.getInvalidVisitsNumber());
				reportValidationDto.setValidGeolocatedVisitsNumber(reportValidation.getValidGeolocatedVisitsNumber());
				reportValidationDto
						.setInvalidGeolocatedVisitsNumber(reportValidation.getInvalidGeolocatedVisitsNumber());
				reportValidationDto.setVisitDate(reportValidation.getVisitDate());
				reportValidationDto.setStatus(reportValidation.getStatus());
				result.add(reportValidationDto);

			}
		}

		return result;
	}

	@Override
	public void updateStatusReportValidation(StatusReportValidation statusReportValidation) {
		ReportValidation reportValidation = reportValidationRepository.findOne(statusReportValidation.getId());
		if (reportValidation != null) {
			reportValidation.setStatus(statusReportValidation.getStatus());
			reportValidationRepository.save(reportValidation);
			sendNotification(reportValidation.getDelegate(), reportValidation.getVisitDate(), reportValidation.getStatus().toString());
		}

	}
	

	@Override
	public void validateVisitReport(Date visitDate, Delegate delegate) {

		List<Visit> visits = visitRepository.findVisitReport(visitDate, visitDate, delegate.getId());
		ConfigurationDto configuration = configurationService.findConfiguration();
		Integer validVisitNumber =  0 ;
		Integer invalidVisitNumber =  0 ;
		Integer validGeolocatedVisitsNumber = 0;
		Integer inValidGeolocatedVisitsNumber = 0;

		for(Visit visit : visits) {
			if(BirdnotesUtils.getHoursfromDate(visit.getSynchronisationDate()) > BirdnotesUtils.getHoursfromDate(configuration.getReportingStartingTime()) || 
					(BirdnotesUtils.getHoursfromDate(visit.getSynchronisationDate()) == BirdnotesUtils.getHoursfromDate(configuration.getReportingStartingTime() )
					&& BirdnotesUtils.getMinutesfromDate(visit.getSynchronisationDate()) > BirdnotesUtils.getHoursfromDate(configuration.getReportingStartingTime()))) {
				validVisitNumber ++;
			}else {
				invalidVisitNumber ++;
			}			
			if (visit.getLocation() != null && visit.getLocation().getLatitude() != null
					&& visit.getLocation().getLatitude() != 0
					&& visit.getProspect().getLatitude() != null
					&& visit.getProspect().getLatitude() != 0) {
				double distance = BirdnotesUtils.distanceBetweenTwoPositions(
						visit.getProspect().getLongitude(), visit.getProspect().getLatitude(),
						visit.getLocation().getLongitude(), visit.getLocation().getLatitude());
				if (distance < configuration.getAcceptedPointingDistance()) {
					validGeolocatedVisitsNumber ++;
				} else {
					inValidGeolocatedVisitsNumber ++ ;
				}
			} else {
				inValidGeolocatedVisitsNumber ++ ;
			}
		}	
		if(visits.size() > 0) {
			ReportValidation reportValidation = new ReportValidation();
			reportValidation.setInvalidGeolocatedVisitsNumber(inValidGeolocatedVisitsNumber);
			reportValidation.setValidGeolocatedVisitsNumber(validGeolocatedVisitsNumber);
			reportValidation.setInvalidVisitsNumber(invalidVisitNumber);
			reportValidation.setValidVisitsNumber(validVisitNumber);
			reportValidation.setDelegate(delegate);
			reportValidation.setVisitDate(visitDate);
			reportValidation.setStatus(UserValidationStatus.VALID);
			if(invalidVisitNumber > configuration.getDelayedReportingTolerence()) {
				reportValidation.setStatus(UserValidationStatus.INVALID);
			}
			else if(inValidGeolocatedVisitsNumber > configuration.getNoGeolocationTolerence()) {
				reportValidation.setStatus(UserValidationStatus.INVALID);
			}
			reportValidationRepository.deleteReportsValidationByDateAndUser(visitDate, visitDate, delegate.getId());
			reportValidationRepository.save(reportValidation);
			
			sendNotification(delegate, visitDate, reportValidation.getStatus().toString());
		}
	}
	
	private void sendNotification(Delegate delegate, Date visitDate, String status) {
		//send notif to delegate | If I put the delegate in the notification rules, he will recieve all notifs that's why the notif must be sent alone
		notificationMessageBuilder.setMessageType("reportValidation");
		notificationMessageBuilder.setUser(delegate.getUser());
		notificationMessageBuilder.setTagetUser(delegate.getUser());
		notificationMessageBuilder.setDate(visitDate);
		if(status != null && status.equals("VALID")) {
			notificationMessageBuilder.setStatus("valide");
		}else if(status != null && status.equals("INVALID")) {
			notificationMessageBuilder.setStatus("invalide");
		}
		Notification notification = notificationMessageBuilder.Build();
		notificationService.generateUsingAllNotificationMethods(notification);
		//send notif to users in the notif rules
		notificationService.sendNotificationUsingWorkflow(delegate.getUser(), "REPORT_VALIDATION" , visitDate, status);
	}
	
}
