package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.convertor.ConvertRoleToDto;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.RoleRequestDto;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.service.RoleService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("roleService")
@Transactional
public class RoleServiceImpl implements RoleService {

	private RoleRepository roleRepository;

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	public RoleServiceImpl(RoleRepository roleRepository, ConvertRoleToDto convertRoleToDto) {
		super();
		this.roleRepository = roleRepository;
	}

	@Override
	public List<Role> add(RoleRequestDto roleRequestDto) throws BirdnotesException {
		int rank = 1;
		List roleList = new ArrayList<>();
		for (Role role : roleRequestDto.getRoles()) {
			if (role.getId() == null) {

				if (role.getName() == null || "".equals(role.getName())) {
					throw new BirdnotesException(Exceptions.ROLE_NAME_IS_EMPTY);
				}
				Role result = roleRepository.findFirstByNameIgnoreCase(role.getName());
				if (result != null) {
					throw new BirdnotesException(Exceptions.ALREADY_EXIST);
				}
				Role newRole = new Role();
				newRole.setName(role.getName());
				newRole.setRank(role.getRank());
				roleList.add(roleRepository.save(newRole));
				rank++;
			} else {
				Role existingRole = roleRepository.findOne(role.getId());
				if (existingRole != null) {
					existingRole.setName(role.getName());
					existingRole.setRank(rank);
					roleList.add(roleRepository.save(existingRole));
					rank++;
				}
			}

		}
		return roleList;
	}

	@Override
	public List<RoleDto> findAll() throws BirdnotesException {
		List<RoleDto> result = new ArrayList<>();
		List<Role> allRoles = roleRepository.findAll();
		for (Role role : allRoles) {
			RoleDto r = new RoleDto();
			r.setId(role.getId());
			r.setName(role.getName());
			result.add(r);
		}
		return result;
	}

	/*
	 * @Override public boolean delete (Integer id)throws BirdnotesException { try{
	 * roleRepository.deleteById(id); return true; } catch (Exception e) {
	 * log.error("error when deleted role",e); throw new
	 * BirdnotesException("Could not deleted role!"); } }
	 */
	@Override
	public void delete(Integer id) throws BirdnotesException {

			roleRepository.deleteById(id);



		
	}

	@Override
	public void update(RoleDto roleDto) throws BirdnotesException {
		if (roleDto == null || roleDto.getId() == null) {
			throw new BirdnotesException(Exceptions.ROLE_DTO_IS_NULL);
		}

		if (roleDto.getName() == null || roleDto.getName().isEmpty()) {
			throw new BirdnotesException(Exceptions.ROLE_NAME_IS_EMPTY);
		}
		Role result = roleRepository.findFirstByNameIgnoreCase(roleDto.getName());
		if (result != null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}

		Role roleToUpadte = roleRepository.findOne(roleDto.getId());
		if (roleToUpadte == null) {
			throw new BirdnotesException(Exceptions.ROLE_TO_UPDATE_ALREADY_DELETED);
		}

		roleToUpadte.setName(roleDto.getName());
		roleRepository.save(roleToUpadte);
	}

}
