package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.MISSION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Mission implements Serializable {
	private static final long serialVersionUID = 1L;

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Mission() {
		super();

	}

	// Ajouté pour le test
	public Mission(Long id, Product product, Long quantity, Date endDate) {
		super();
		this.id = id;
		this.endDate = endDate;

	}

	@Id
	@SequenceGenerator(name = Sequences.MISSION_SEQUENCE, sequenceName = Sequences.MISSION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.MISSION_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.END_DATE)
	private Date endDate;

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.SECTOR_ID)
	private Sector sector;

	public Sector getSector() {
		return sector;
	}

	public void setSector(Sector sector) {
		this.sector = sector;
	}

	@JsonIgnore
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "mission", targetEntity = SampleSupply.class)
	private SampleSupply sampleSupply;

	public SampleSupply getSampleSupply() {
		return sampleSupply;
	}

	public void setSampleSupply(SampleSupply sampleSupply) {
		this.sampleSupply = sampleSupply;
	}

}
