package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


import com.intellitech.birdnotes.model.Holiday;

@Repository
public interface HolidayRepository extends JpaRepository<Holiday, Long> {
	
	@Query("Select h from Holiday h where (DATE(h.date) BETWEEN DATE(:startDate) AND DATE(:endDate)) OR h.holidayType='PERIODIC'")
	List<Holiday> findByDate(@Param("startDate")Date firstDate, @Param("endDate")Date lastDate);

}
