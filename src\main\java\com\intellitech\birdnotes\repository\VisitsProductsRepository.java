package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.data.dto.ProductCompareDto;
import com.intellitech.birdnotes.data.dto.PurchaseOrderDataDto;
import com.intellitech.birdnotes.data.dto.PurchaseOrderItemDto;
import com.intellitech.birdnotes.data.dto.SmilyDto;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.dto.OrderProductDto;

@Repository
public interface VisitsProductsRepository extends JpaRepository<VisitsProducts, Long> {

	List<VisitsProducts> findByVisitAndProduct(Visit visit, Product product);

	@Query("SELECT COUNT(*) FROM VisitsProducts vp WHERE  vp.product.id = :productId")
	Long countVisitOfProduct(@Param("productId") Long productId);
	
	//@Query("SELECT COUNT(*) FROM VisitsProducts vp WHERE  vp.wholesaler.id = :wholesalerId")
	//Long countWholesalerPerVisitProduct(@Param("wholesalerId") Long wholesalerId);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.OrderProductDto(vp.rank, count(*)) FROM VisitsProducts vp WHERE  vp.product.id = :productId AND DATE(vp.visit.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:saturdayDate) GROUP BY vp.rank")
	List<OrderProductDto> averageOrderProduct(@Param("productId") Long productId, @Param("mondayDate") Date mondayDate,
			@Param("saturdayDate") Date saturdayDate);
	
	@Query("SELECT new com.intellitech.birdnotes.data.dto.ProductCompareDto(vp.product.id,vp.product.name,COUNT(vp.sampleQuantity),SUM(vp.orderQuantity))"
			+ " FROM VisitsProducts vp WHERE  MONTH(vp.visit.visitDate)=:month AND YEAR(vp.visit.visitDate)=:year "
			+ "GROUP BY vp.product.id,vp.product.name order by vp.product.id")
	List<ProductCompareDto> compareProductsPerMonth(@Param("month") Integer month, @Param("year") Integer year);
	
	@Query("SELECT new com.intellitech.birdnotes.data.dto.SmilyDto(vp.product.id,vp.product.name,vp.smily,COUNT(*))"
			+ " FROM VisitsProducts vp WHERE  MONTH(vp.visit.visitDate)=:month AND YEAR(vp.visit.visitDate)=:year "
			+ "GROUP BY vp.product.id,vp.product.name,vp.smily order by vp.product.id")
	List<SmilyDto> getSmilyPerMonth(@Param("month") Integer month, @Param("year") Integer year);
	
	@Query("SELECT new com.intellitech.birdnotes.data.dto.ProductCompareDto(vp.product.id,vp.product.name,COUNT(vp.sampleQuantity),SUM(vp.orderQuantity))"
			+ " FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:saturdayDate) "
			+ "GROUP BY vp.product.id,vp.product.name order by vp.product.id")
	List<ProductCompareDto> compareProductsPerWeek(@Param("mondayDate") Date mondayDate, @Param("saturdayDate") Date saturdayDate);
	
	@Query("SELECT new com.intellitech.birdnotes.data.dto.SmilyDto(vp.product.id,vp.product.name,vp.smily,COUNT(*))"
			+ " FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:saturdayDate) "
			+ "GROUP BY vp.product.id,vp.product.name,vp.smily order by vp.product.id")
	List<SmilyDto> getSmilyPerWeek(@Param("mondayDate") Date mondayDate, @Param("saturdayDate") Date saturdayDate);
	
	@Query("Select vp from VisitsProducts vp where vp.visit.delegate.id = :userId AND DATE(vp.visit.visitDate) < DATE(:firstDate) ORDER BY vp.id ASC")
	List<VisitsProducts> findByUserId(@Param("userId") Long userId, @Param("firstDate") Date firstDate);
	
	@Query("Select vp from VisitsProducts vp where vp.visit.delegate.id = :userId ORDER BY vp.id ASC")
	List<VisitsProducts> findByUserId(@Param("userId") Long userId);
	
	
	@Modifying
	@Query("DELETE VisitsProducts vp WHERE vp.id = ?1")
	void deleteById(Long visitProductId);
	
	@Modifying
	@Query("DELETE VisitsProducts vp WHERE vp.id IN (:visitProductIds)")
	void deleteByIds(@Param("visitProductIds") List<Long> visitProductIds);
	
	@Modifying
	@Query("DELETE VisitsProducts vp WHERE vp.visit.id = ?1")
	void deleteByVisitId(Long visitId);

	// Find visits of all prospects affected to one delegate which their synchronisationDate upper to the last sync done by him without his visits
	@Query("SELECT vp FROM VisitsProducts vp where vp.visit.prospect.id in (SELECT p.prospect.id FROM  ProspectsAffectation p WHERE p.delegate.id=:id) "
			+ "AND DATE(vp.visit.synchronisationDate) >= DATE(:date) AND vp.visit.delegate.id !=:id ORDER BY vp.id ASC")
	List<VisitsProducts> findProspectVisits(@Param("id") Long id,@Param("date") Date date);
	
	
	@Query("SELECT vp FROM VisitsProducts vp where vp.visit.prospect.id in (SELECT p.prospect.id FROM  ProspectsAffectation p WHERE p.delegate.id=:id) and vp.visit.delegate.id=:id ORDER BY vp.id ASC")
	List<VisitsProducts> findProspectVisits(@Param("id") Long id);
	
	@Query("SELECT vp FROM VisitsProducts vp where vp.visit.delegate.id =:userId and vp.visit.doubleVisit is not null and DATE(vp.visit.visitDate) >=  DATE(:lastReceiveDate)")
	List<VisitsProducts> getDoubleVisitProducts(@Param("userId") Long  userId,  @Param("lastReceiveDate")  Date lastReceiveDate);

	
	@Query("SELECT vp FROM VisitsProducts vp where vp.visit.prospect.id in (SELECT p.prospect.id FROM  "
			+ "ProspectsAffectation p WHERE p.delegate.id=:id) AND vp.visit.delegate.id !=:id ORDER BY vp.id ASC")
	List<VisitsProducts> findCollegueSameProspectVisits(@Param("id") Long id);
	
	@Query("SELECT vp FROM VisitsProducts vp where vp.visit.prospect.user.id = :userId AND vp.visit.prospect.status = 'NEW' ")
	List<VisitsProducts> getNewProspectWaitingForValidationVisitsProducts(@Param("userId") Long  userId);
	
	@Query("Select vp from VisitsProducts vp where vp.visit.id = :visitId and vp.purchaseOrder is null ORDER BY vp.id ASC")
	List<VisitsProducts> findByVisitId(@Param("visitId") Long visitId);
	
	@Query("Select vp from VisitsProducts vp where vp.visit.id = :visitId ORDER BY vp.id ASC")
	List<VisitsProducts> findAllByVisitId(@Param("visitId") Long visitId);


	@Modifying
	@Query("Update VisitsProducts vp set vp.purchaseOrder = null where vp.purchaseOrder.id IN (select id from PurchaseOrder po where po.identifier in (:purchaseOrderIds))")
	void removePurchaseOrder(@Param("purchaseOrderIds") List<Long> purchaseOrderIds);
	
	VisitsProducts findByIdentifier(Long identifier);
	
	@Query("SELECT vp FROM VisitsProducts vp WHERE vp.identifier = ?1 And vp.visit.delegate.id = ?2")
	VisitsProducts findByIdentifier(Long identifier, Long userId);
	
	@Query("SELECT vp FROM VisitsProducts vp WHERE vp.visit.identifier = ?1 And vp.product.id = ?2 And vp.visit.delegate.id = ?3")
	VisitsProducts findByVisitAndProduct(Long visitIdentifier, Long productId, Long userId);
	
	@Modifying
	@Query("Delete FROM VisitsProducts vp WHERE vp.identifier = ?1 And vp.visit.id IN (Select id from Visit v where v.delegate.id = ?2)")
	void deleteByIdentifier(Long identifier, Long userId);
	
	
	@Query("SELECT new com.intellitech.birdnotes.data.dto.PurchaseOrderItemDto(vp.product.name , vp.freeOrder, vp.labGratuity, vp.orderQuantity, vp.product.price, vp.product.vat, vp.product.buyingPrice) "+ 
			" from VisitsProducts vp WHERE vp.purchaseOrder.id  = :purchaseOrderId ")
			List<PurchaseOrderItemDto>findPurchaseOrderById(@Param("purchaseOrderId") Long purchaseOrderId);
	
	@Transactional
	@Modifying
	@Query("UPDATE VisitsProducts vp SET vp.commentRating = :commentRating WHERE vp.id = :id")
	void updateCommentRating(@Param("id") Long id, @Param("commentRating") String commentRating);

	
	}


