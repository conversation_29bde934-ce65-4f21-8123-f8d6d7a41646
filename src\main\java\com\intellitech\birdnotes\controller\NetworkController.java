package com.intellitech.birdnotes.controller;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.dto.NetworkDto;
import com.intellitech.birdnotes.service.NetworkService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/network")
public class NetworkController {

	@Autowired
	private NetworkService networkService;
	@Autowired
	UserService userService;

	private static final Logger LOG = LoggerFactory.getLogger(LocalityController.class);

	@RequestMapping(value = "/getAllNetwork", method = RequestMethod.GET)
	public ResponseEntity<List<Network>> findAll() {
		try {
			if (userService.checkHasPermission("NETWORK_VIEW")) {
				List<Network> toReturn = networkService.findAll();
				return new ResponseEntity<>(toReturn, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting network lists", e);
			return new ResponseEntity<>(new ArrayList<Network>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)

	public ResponseEntity<String> addNetwork(@RequestBody Network network) {
		try {
			if (userService.checkHasPermission("NETWORK_ADD")) {
				networkService.add(network);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add network", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new nnetwork", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteNetwork(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("NETWORK_DELETE")) {
				networkService.delete(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting network", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("An exception occurred while deleting the network with id ", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "saveNetwork", method = RequestMethod.POST)
	public ResponseEntity<?> saveNetwork(@RequestBody NetworkDto networkDto) {

		try {
			if (userService.checkHasPermission("NETWORK_EDIT")) {
				Network savedNetwork = networkService.saveNetwork(networkDto);
				if (savedNetwork != null) {
					return new ResponseEntity<>(savedNetwork.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving network", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving network", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}