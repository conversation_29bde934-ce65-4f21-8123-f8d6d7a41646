package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.RECOVERY, schema = Common.PUBLIC_SCHEMA)
public class Recovery implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String payment;
	private String description;
	private Float amount;
	private Date date;
	private PurchaseOrder purchaseOrder;
    private Long identifier;
    private Attachment attachment;
    
    
	public Recovery() {
		super();
	}

	@Id
	@SequenceGenerator(name = Sequences.VISIT_PRODUCT_SEQUENCE, sequenceName = Sequences.VISIT_PRODUCT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.VISIT_PRODUCT_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.ATTACHMENT_ID, unique = false)
	public Attachment getAttachment() {
		return attachment;
	}

	public void setAttachment(Attachment attachment) {
		this.attachment = attachment;
	}

	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PURCHASEORDER_ID, unique = false)
	public PurchaseOrder getPurchaseOrder() {
		return purchaseOrder;
	}

	public void setPurchaseOrder(PurchaseOrder purchaseOrder) {
		this.purchaseOrder = purchaseOrder;
	}
	
	@Column(name = Columns.PAYMENT, length = BirdnotesConstants.Numbers.N_85)
	public String getPayment() {
		return payment;
	}

	public void setPayment(String payment) {
		this.payment = payment;
	}

	@Column(name = Columns.IDENTIFIER)
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	@Column(name = Columns.DESCRIPTION, length = BirdnotesConstants.Numbers.N_255)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = Columns.AMOUNT)
	public Float getAmount() {
		return amount;
	}

	public void setAmount(Float amount) {
		this.amount = amount;
	}

	@Column(name = Columns.RECOVERY_DATE)
	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	
}
