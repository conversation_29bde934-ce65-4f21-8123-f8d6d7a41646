package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.dto.PotentialDto;

@Component("convertPotentialToDto")
public class ConvertPotentialToDto {
	private static final Logger LOG = LoggerFactory.getLogger(ConvertPotentialToDto.class);

	public PotentialDto convert(Potential potential) throws BirdnotesException {

		if (potential == null) {
			LOG.error("potential is null");
			throw new BirdnotesException("potential is null");
		}
		PotentialDto potentialDto = new PotentialDto();
		potentialDto.setId(potential.getId());
		potentialDto.setName(potential.getName());
		
		return potentialDto;
	}
}
