package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.ReportCron;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.MinimizedUserDto;
import com.intellitech.birdnotes.model.dto.MinimizedUserDtoV1;

@Repository
public interface UserRepository extends JpaRepository<User, Long> {
	
	@Query("SELECT u FROM User u WHERE  u.goals IS NULL ")
	List<User> findAllDelegatesWithoutGoal();
	
	@Query("SELECT u FROM User u  ORDER BY u.username ASC")
	List<User> findAllDelegates();
	
	@Query("SELECT u from User u WHERE LOWER(username) = LOWER(?1) AND id != ?2 ")
	User findByNameWithDiffId(String username, Long id);

	@Query("SELECT u from User u WHERE  LOWER(email) = LOWER(?1) AND id != ?2")
	User findByEmail(String email, Long id);
	
	
	@Query("SELECT u FROM User u join u.superiors s  WHERE s.id =:supervisorId and u.active = true ORDER BY u.username ASC")
	List<User> getUsersBySuperior(@Param("supervisorId") Long supervisorId);
	
	
	@Query("SELECT u.id FROM User u  ")
	List<Long> findAllDelegatesIds();

	@Query("SELECT u from User u WHERE u.phone = ?1 and u.id != ?2")
	User findByPhoneWithDiffId(String phone, Long id);

	
	@Query("SELECT u from User u WHERE u.username = ?1")
	User findByUsername(String username);

	User findByEmail(String email);
	
	User findById(Long id);

	@Query("SELECT u from User u WHERE u.email = ?1 and u.id != ?2")
	User findByEmailAndId(String email, Long id);

	User findByPhone(String phone);

	@Query("SELECT u from User u WHERE u.phone = ?1 and u.id != ?2")
	User findByPhoneAndId(String phone, Long id);

	@Query("SELECT u from User u WHERE u.username = ?1 and u.id != ?2")
	User findByUsernameAndId(String username, Long id);
	
	@Override
	@Query("SELECT u from User u order by u.username ASC")
	List<User> findAll();

	@Query("SELECT u FROM User u ")
	List<User> findAllDelegue();
	
	@Query("SELECT u FROM User u ")
	List<User> findAllLastSynchro();
	
	/*@Query("SELECT u.lastSyncro from User u where u.id=:id")
	Date findLastSync(@Param("id") Long id);
	

	@Query("SELECT u from User u WHERE upper(u.firstName) = upper(?1) and upper(u.lastName) = upper(?2) order by u.firstName")
	User findByFirstLastName(String firstName, String lastName);

	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.MinimizedUserDto(u.id,u.firstName,u.lastName) FROM User u join u.roles r  order by u.username")
	List<MinimizedUserDto> getDelegates();

	@Query("SELECT new com.intellitech.birdnotes.model.dto.MinimizedUserDtoV1 (u.id,u.firstName,u.lastName,u.workType) FROM User u join u.roles r order by u.firstName")
	List<MinimizedUserDtoV1> getDelegatesV1();

	@Query("SELECT new com.intellitech.birdnotes.model.dto.MinimizedUserDto (u.id,u.firstName,u.lastName) FROM User u join u.roles r WHERE  u.id not in (?1) order by u.firstName")
	List<MinimizedUserDto> getDelegatesExcept(List<Long> ids);*/
	
	@Query("Select distinct u from User u join u.roles r where r.rank < ?1")
	List<User> getAllSupervisors(Integer rank);
	
	
	
	@Query("Select u from User u join u.roles r WHERE r.rank = 1")
	List<User> getTopUsers();
	
	@Query("Select distinct u from User u")
	List<User> getAllUsers();
	
	
	@Query("SELECT u.superiors FROM User u where u.id=:id")
	List<User> getSuperviserOfUser(@Param("id") Long id);
	
	
	@Query("Select u from User u  where u.id in :ids")
	Set<User> getUserByIds(@Param("ids") List<Long> ids);
	
	@Query("Select u.id from User u  where u.id in :ids")
	List<Long> getUserIds(@Param("ids") List<Long> ids);
	
	@Query("SELECT u FROM User u order by u.username")
	List<User> getAllDelegates();
	
	
	@Modifying
	@Query("UPDATE User set oneSignalUserId =:oneSignalUserId WHERE id =:id")
	void updateOneSignalUserId(@Param("oneSignalUserId") String oneSignalUserId, @Param("id") Long id);
	
	@Modifying
	@Query("UPDATE User set lastLogin =:lastLogin WHERE id =:id")
	void updateLastLogin(@Param("lastLogin") Date lastLogin, @Param("id") Long id);
	
	//Set<User> findByGoal(Goal goal);

	List<User> findByReportCron(ReportCron reportCron);
	
	@Query("SELECT u.goals from User u where u.id =:id")
	Set<Goal> findGoalsOfUser(@Param("id") Long id);
	
	

	@Query("SELECT u from User u join u.goals g where g.id =:id ")
	Set<User> findUsersOfGoal(@Param("id") Long id);
	
	
	@Query("SELECT u from User u join u.commissions c where c.id =:id ")
	Set<User> findUsersOfCommission(@Param("id") Long id);
	
	@Query("SELECT DISTINCT u from User u join u.goals g where (date(g.firstDate) NOT BETWEEN date(:firstDate) "
			+ "AND date(:lastDate)) AND (date(g.lastDate) NOT BETWEEN date(:firstDate) AND date(:lastDate))"
			+ "AND u.id in (:subUserIds)")
	List<User> findUsersWithoutGoal(@Param("firstDate") Date firstDate, @Param("lastDate") Date lastDate, @Param("subUserIds") List<Long> subUserIds);

	@Query("SELECT u from User u join u.delegate d where d.id =:delegateId ")
	User findUserByDelegateId(@Param("delegateId") Long delegateId);
	
	@Query("select distinct u.id FROM User u join u.roles r where r.id=:id ")
	List<Long> getUsersByRole(@Param("id") Integer id);
	

	
}
