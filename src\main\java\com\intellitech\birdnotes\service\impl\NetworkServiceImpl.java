package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.dto.NetworkDto;
import com.intellitech.birdnotes.repository.NetworkRepository;
import com.intellitech.birdnotes.service.NetworkService;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Service("NetworkService")
@Transactional
public class NetworkServiceImpl implements NetworkService {
	


	private NetworkRepository networkRepository;
	@Autowired
	public void NetworkRepository(NetworkRepository networkRepository) {
		this.networkRepository = networkRepository;
	}
	@Override
	public boolean checkNetworkNameIsUnique(String networkName) {
		Network network = networkRepository.findByName(networkName);

		return network != null;
	}

	
	public Network add(Network network) throws BirdnotesException {
		if (checkNetworkNameIsUnique(network.getName())) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NETWORK_NAME_ALREADY_EXIST);
		}
	
		return networkRepository.save(network);
	}
	private boolean checkNetworkNameIsUnique(String networkName, Long networkId) {
		Network network = networkRepository.findByNameExcludeId(networkName, networkId);

		return network != null;
	}

	@Override
	public void delete(Long id) throws BirdnotesException {

			networkRepository.delete(id);



	}
	
	@Override
	public Network saveNetwork(NetworkDto networkDto) throws BirdnotesException {
		
		Network network = null;
		if (networkDto == null) {
			throw new BirdnotesException("Le réseau que vous voulez modifier est déjà supprimé");
		} else if (checkNetworkNameIsUnique(networkDto.getName(), networkDto.getId())) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NETWORK_NAME_ALREADY_EXIST);
		}	
		if(networkDto.getId() != null) {
			network = networkRepository.findOne(networkDto.getId());
			
		}
		if(network == null) {
			network = new Network();
		}
		network.setName(networkDto.getName());
		return networkRepository.save(network);
	}

	
	@Override
	public List<Network> findAll() throws BirdnotesException {
		List<Network> back = new ArrayList<>();
		List<Network> listOfNetwork = networkRepository.findAll();
		if (listOfNetwork != null && !listOfNetwork.isEmpty()) {
			for (Network network : listOfNetwork) {
				back.add(network);
			}
		}

		return back;

	}

}
