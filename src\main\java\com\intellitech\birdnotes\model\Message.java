package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.MessageType;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.MESSAGE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Message implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.MESSAGE_SEQUENCE, sequenceName = Sequences.MESSAGE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.MESSAGE_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	@Column(name = BirdnotesConstants.Columns.IDENTIFIER)
	private Long identifier;
	
	@Column(name = Columns.MESSAGE_DATE)
	private Date date;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;
	
	@Column(name = Columns.TYPE)
	private MessageType type;
	
	@Lob
	@Column(name = Columns.TEXT)
	private String text;
	
	public Message() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public MessageType getType() {
		return type;
	}

	public void setType(MessageType type) {
		this.type = type;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}


	
}
