package com.intellitech.birdnotes.batchprocessing;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.annotation.PostConstruct;

import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;

import com.google.maps.model.LatLng;
import com.intellitech.birdnotes.enumeration.ImportStep;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ImportProspectDto;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.dto.DuplicateProspectDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.service.EstablishmentService;
import com.intellitech.birdnotes.service.LocalityService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesUtils;

public class ProspectValidationProcess implements ItemProcessor<ImportProspectDto, ProspectDto> {
	@Autowired
	SpecialityService specialityService;
	@Autowired
	LocalityService localityService;
	@Autowired
	PotentialService potentialService;
	@Autowired
	ProspectTypeRepository prospectTypeRepository;
	@Autowired
	SectorService sectorService;
	@Autowired
	ProspectService prospectService;
	@Autowired
	UserService userService;
	@Autowired
	EstablishmentService establishmentService;
	@Autowired
	ProspectTypeService prospectTypeService;


	private List<LocalityDto> localityDtos = new ArrayList<>();
	private List<SectorDto> sectorDtos = new ArrayList<>();
	private List<SpecialityDto> specialityDtos = new ArrayList<>();
	private List<PotentialDto> potentialDtos = new ArrayList<>();
	private List<EstablishmentDto> establishmentDtos = new ArrayList<>();
	private List<ProspectTypeDto> prospectTypeDtos = new ArrayList<>();
	private List<UserDto> userDtos = new ArrayList<>();
	boolean isValid = true;

	private ImportStep importStep;
	private Boolean geocodeAdress;

	public ProspectValidationProcess(ImportStep importStep, Boolean geocodeAdress) {
		this.importStep = importStep;
		this.geocodeAdress = geocodeAdress;
		
	}
	
	@PostConstruct

	public void getData() {
		
		if (!importStep.equals(ImportStep.MISSING_DATA)) {
			try {
				localityDtos = localityService.findAll();
				potentialDtos = potentialService.findAll();
				specialityDtos = specialityService.findAll();
				sectorDtos = sectorService.findAll();
				establishmentDtos = establishmentService.findAll();
				prospectTypeDtos = prospectTypeService.findAll();
				userDtos = userService.getSubUsers();
			} catch (BirdnotesException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
		}

		
		

	}

	@Override
	public ProspectDto process(ImportProspectDto importProspectDto) throws Exception {

		

		ProspectDto prospectDto = new ProspectDto();

		if (importStep.equals(ImportStep.MISSING_DATA)) {

			nameValidation(getValue(importProspectDto, "firstName"), getValue(importProspectDto, "lastName"));
			phoneValidation(getValue(importProspectDto, "phone"));
			phoneValidation(getValue(importProspectDto, "gsm"));
			//addressValidation(getValue(importProspectDto, "address"));
			activityValidation(getValue(importProspectDto, "activity"));
			emailValidation(getValue(importProspectDto, "email"));
			
			if(getValue(importProspectDto, "sector").isEmpty()) {
				isValid = false;
			}
			
			if(getValue(importProspectDto, "locality").isEmpty()) {
				isValid = false;
			}
			
			if(getValue(importProspectDto, "speciality").isEmpty()) {
				isValid = false;
			}
			
			if(getValue(importProspectDto, "type").isEmpty()) {
				isValid = false;
			}
			
			if(getValue(importProspectDto, "potential").isEmpty()) {
				isValid = false;
			}

			if (!isValid) {
				fillProspectDtoData(importProspectDto, prospectDto);
				BatchConfiguration.invalidProspects.add(prospectDto);
				isValid = true;
				
			}
			return null;
		}
		if (importStep.equals(ImportStep.INVALID_TYPES)) {
			BatchConfiguration.allLocalities.add(getValue(importProspectDto, "locality"));
			BatchConfiguration.allSectors.add(getValue(importProspectDto, "sector"));
			BatchConfiguration.allSpecialities.add(getValue(importProspectDto, "speciality"));
			if (BatchConfiguration.localitySectorsMap.get(getValue(importProspectDto, "locality")) == null) {
				Set<String> sectorList = new HashSet<>();
				sectorList.add(getValue(importProspectDto, "sector"));
				BatchConfiguration.localitySectorsMap.put(getValue(importProspectDto, "locality"), sectorList);
			}else {
				BatchConfiguration.localitySectorsMap.get(getValue(importProspectDto, "locality")).add(getValue(importProspectDto, "sector"));
			}
			return null;
		}
		
		
		if (importStep.equals(ImportStep.MISSING_TYPES)) {
			
			checkIfSpecialityExit(getValue(importProspectDto, "speciality"));
			checkIfSectorExist(getValue(importProspectDto, "sector"));
			checkIfLocalityExist(getValue(importProspectDto, "locality"), getValue(importProspectDto, "sector"));
			checkIfPotentialExist(getValue(importProspectDto, "potential"));
			checkIfEstablishmentExist(getValue(importProspectDto, "establishment"));
			checkIfProspectTypeExist(getValue(importProspectDto, "type"));
			
			return null;
		}

		

		if (importStep.equals(ImportStep.FIND_DUPPLICATES)) {
		
			fillProspectDtoData(importProspectDto, prospectDto);
			fillProspectDtoTypes(importProspectDto, prospectDto);

			List<ProspectDto> result = prospectService.findSimilarProspects(prospectDto);

			if (result != null && !result.isEmpty()) {

				DuplicateProspectDto duplicateProspectDto = new DuplicateProspectDto();
				duplicateProspectDto.setProspect(prospectDto);
				duplicateProspectDto.setSimilarProspectDtos(result);
				BatchConfiguration.duplicateProspectsList.add(duplicateProspectDto);
			}
			return null;

		}

		if (importStep.equals(ImportStep.SAVE_DATA)) {
			
			fillProspectDtoData(importProspectDto, prospectDto);
			fillProspectDtoTypes(importProspectDto, prospectDto);

			if (geocodeAdress) {

				LatLng position = BirdnotesUtils.geocode(prospectDto.getAddress());
				if (position != null) {
					prospectDto.setLatitude(position.lat);
					prospectDto.setLongitude(position.lng);
				}

			}
		}

		return prospectDto;

	}

	private String getValue(ImportProspectDto importProspectDto, String key) {

		String value = String.valueOf(importProspectDto.getProspectMap().get(key));
		return value.trim().replaceAll("\\s+", " ");
		
	}
	
	
	private String [] getMultipleValue(ImportProspectDto importProspectDto, String key) {

		String value = String.valueOf(importProspectDto.getProspectMap().get(key));
		String[] valueSplit = value.split("/");
		String [] cleanValues = new String [valueSplit.length];
		
		if(valueSplit.length > 0) {
			
			for (int i = 0; i < valueSplit.length; i++) {
				cleanValues[i] = valueSplit[i].trim().replaceAll("\\s+", " ");
			}
			
		}
		
		return cleanValues;
		
	}

	private void fillProspectDtoData(ImportProspectDto importProspectDto, ProspectDto prospectDto) {
		String firstName = getValue(importProspectDto, "firstName");
		String lastName = getValue(importProspectDto, "lastName");
		String activity = getValue(importProspectDto, "activity");
		String[] phones = getMultipleValue(importProspectDto, "phone");
		String[] addresses = getMultipleValue(importProspectDto, "address");
		String[] gsms = getMultipleValue(importProspectDto, "gsm");
		String[] emails = getMultipleValue(importProspectDto, "email");
		String latitude = getValue(importProspectDto, "latitude");
		String longitude = getValue(importProspectDto, "longitude");
		String note = getValue(importProspectDto, "note");
		String grade = getValue(importProspectDto, "grade");
		String scrappingId = getValue(importProspectDto, "scrappingId");
		
		prospectDto.setFirstName(firstName);
		prospectDto.setLastName(lastName);
		prospectDto.setActivity(activity.toUpperCase());	
		if(!latitude.isEmpty()) {
			prospectDto.setLatitude(Double.valueOf(latitude));
		}
		if(!longitude.isEmpty()) {
			prospectDto.setLongitude(Double.valueOf(longitude));
		}
		
		
		prospectDto.setGrade(grade);
		prospectDto.setScrappingId(scrappingId);
		
		StringBuilder noteSb = new StringBuilder();
		noteSb.append(note);
		
		if(phones.length > 0) {
			prospectDto.setPhone(phones[0].replaceAll("\\s", ""));
			if(phones.length > 1) {
				if(noteSb.length() > 0) {
					noteSb.append(" | ");
				}
				noteSb.append("Téléphone : " );
				for (int i = 1; i < phones.length; i++) {
					noteSb.append(phones[i].trim());
				}
				noteSb.append("");	
			}
		}
		
		if(gsms.length > 0) {
			prospectDto.setGsm(gsms[0].replaceAll("\\s", ""));
			if(gsms.length > 1) {
				if(noteSb.length() > 0) {
					noteSb.append(" | ");
				}
				noteSb.append("GSM : " );
				for (int i = 1; i < gsms.length; i++) {
					noteSb.append(gsms[i].trim());
				}
				noteSb.append("");	
			}
		}
		
		if(emails.length > 0) {
			prospectDto.setEmail(emails[0]);
			if(emails.length > 1) {
				if(noteSb.length() > 0) {
					noteSb.append(" | ");
				}
				noteSb.append("Email : " );
				for (int i = 1; i < emails.length; i++) {
					noteSb.append(emails[i]);
				}
				noteSb.append("");	
			}
		}
		
		if(addresses.length > 0) {
			prospectDto.setAddress(addresses[0]);
			if(addresses.length > 1) {
				if(noteSb.length() > 0) {
					noteSb.append(" | ");
				}
				noteSb.append("Adresse : " );
				for (int i = 1; i < addresses.length; i++) {
					noteSb.append(addresses[i]);
				}
				noteSb.append("");	
			}
		}		
		prospectDto.setNote(noteSb.toString());
		prospectDto.setSector(getValue(importProspectDto, "sector"));
		prospectDto.setLocality(getValue(importProspectDto, "locality"));
		prospectDto.setSpeciality(getValue(importProspectDto, "speciality"));
		prospectDto.setPotential(getValue(importProspectDto, "potential"));
		prospectDto.setProspectType(getValue(importProspectDto, "type"));
		prospectDto.setEstablishment(getValue(importProspectDto, "establishment"));
	}

	private void fillProspectDtoTypes(ImportProspectDto importProspectDto, ProspectDto prospectDto) {

		String specialityName = getValue(importProspectDto, "speciality");
		String sectorName = getValue(importProspectDto, "sector");
		String potentialName = getValue(importProspectDto, "potential");
		String localityName = getValue(importProspectDto, "locality");
		String establishmentName = getValue(importProspectDto, "establishment");
		String type = getValue(importProspectDto, "type");
		String delegates = getValue(importProspectDto, "delegates");

		HashSet<String> userNames = new HashSet<>(Arrays.asList(delegates.split(",")));

		SectorDto sectorExist = sectorService.findSectorDto(sectorName, sectorDtos);
		prospectDto.setSectorDto(sectorExist);
		LocalityDto localityExist = localityService.findLocalityDto(localityName, sectorName, localityDtos);
		prospectDto.setLocalityDto(localityExist);
		SectorDto sectorDto = prospectDto.getSectorDto();
		if(localityExist == null || localityExist.getSectorId()==null) {
			System.out.print(" locality is null");
		}
		sectorDto.setId(localityExist.getSectorId());
		sectorDto.setName(localityExist.getSectorName());
		prospectDto.setSectorDto(sectorDto);
		EstablishmentDto establishmentExist = establishmentService.findEstablishmentDto(establishmentName, establishmentDtos);
		prospectDto.setEstablishmentDto(establishmentExist);
		PotentialDto potentialExist = potentialService.findPotentialDto(potentialName, potentialDtos);
		prospectDto.setPotentialDto(potentialExist);
		ProspectTypeDto prospectTypeDto = new ProspectTypeDto();
		ProspectType prospectType = prospectTypeRepository.findByName(type);
		prospectTypeDto.setId(prospectType.getId());
		prospectTypeDto.setName(prospectType.getName());
		prospectDto.setProspectTypeDto(prospectTypeDto);
		SpecialityDto specialityExist = specialityService.findSpecialityDto(specialityName, specialityDtos);
		prospectDto.setSpecialityDto(specialityExist);

		List<Long> delegateIds = new ArrayList<>();
		List<String> delegateNames = new ArrayList<>();

		if (userNames != null && !userNames.isEmpty()) {
			for (String userName : userNames) {

				if ((!userName.equals(""))) {
					userName = userName.trim().replaceAll("\\s+", " ");
					UserDto userDto = userService.findUserDto(userName, userDtos);

					if (userDto != null) {

						delegateIds.add(userDto.getId());

					} else {
						delegateNames.add(userName);
					}
					prospectDto.setDelegateIds(delegateIds);
					prospectDto.setDelegateNames(delegateNames);
				}
			}

		}
	}

	public void emailValidation(String emails) {
		
		if (!emails.isEmpty()) {

			String[] emailsSplit = emails.split("/");

			for (int i = 0; i < emailsSplit.length; i++) {
				
				if (!prospectService.validateEmail(emailsSplit[i].trim())) {
					isValid = false;
				}
			}
		}
		
	}
	


	public void activityValidation(String activity) {
		if ((!activity.equals(""))) {

			if (!activity.equalsIgnoreCase("h") && !activity.equalsIgnoreCase("p")) {
				isValid = false;
			}
		}
	}

	public void addressValidation(String addresses) {
		
		
		if (!addresses.isEmpty()) {

			String[] adresssSplit = addresses.split("/");

			for (int i = 0; i < adresssSplit.length; i++) {
				if (!prospectService.validateAddress(adresssSplit[i].trim())) {
					isValid = false;
				}
			}
		}
		
	}

	public void phoneValidation(String phones) {

		if (!phones.isEmpty()) {

			String[] phoneSplit = phones.split("/");

			for (int i = 0; i < phoneSplit.length; i++) {
				if (!prospectService.validatePhone(phoneSplit[i].trim().replaceAll("\\s+", ""))) {

					isValid = false;
				}
			}
		}

	}

	public void checkIfLocalityExist(String localityName, String sectorName) {

		LocalityDto localityDto = new LocalityDto();
		if (localityName.isEmpty()) {
			BatchConfiguration.localityDtosNotFound.add(localityDto);
		}
		LocalityDto localityExist = localityService.findLocalityDto(localityName, sectorName, localityDtos);

		if (localityExist == null) {
			isValid = false;
			localityDto.setName(localityName);
			localityDto.setSectorName(sectorName);
			BatchConfiguration.localityDtosNotFound.add(localityDto);	
			
		}
	}
	
	
	

	public void checkIfPotentialExist(String potentialName) {

		PotentialDto potentialDto = new PotentialDto();

		if (potentialName.isEmpty()) {
			BatchConfiguration.potentialDtosNotFound.add(potentialDto);
		}
		PotentialDto potentialExist = potentialService.findPotentialDto(potentialName, potentialDtos);
		if (potentialExist == null) {
			potentialDto.setName(potentialName);
			BatchConfiguration.potentialDtosNotFound.add(potentialDto);
		}

	}
	
	public void checkIfEstablishmentExist(String establishmentName) {

		EstablishmentDto establishmentDto = new EstablishmentDto();

		if (!establishmentName.isEmpty()) {
			BatchConfiguration.establishmentDtosNotFound.add(establishmentDto);		
			EstablishmentDto establishmentExist = establishmentService.findEstablishmentDto(establishmentName, establishmentDtos);
			if (establishmentExist == null) {
				establishmentDto.setName(establishmentName);
				BatchConfiguration.establishmentDtosNotFound.add(establishmentDto);
			}
		}

	}

	public void checkIfProspectTypeExist(String prospectTypeName) {

		ProspectTypeDto prospectTypeDto = new ProspectTypeDto();

		if (prospectTypeName.isEmpty()) {
			BatchConfiguration.prospectTypeDtosNotFound.add(prospectTypeDto);
		}
		ProspectTypeDto prospectTypeExist = prospectTypeService.findProspectTypeDto(prospectTypeName, prospectTypeDtos);
		if (prospectTypeExist == null) {
			prospectTypeDto.setName(prospectTypeName);
			BatchConfiguration.prospectTypeDtosNotFound.add(prospectTypeDto);
		}

	}


	public void checkIfSpecialityExit(String specialityName) {

		SpecialityDto specialityDto = new SpecialityDto();

		if (specialityName.isEmpty()) {
			BatchConfiguration.specialityDtosNotFound.add(specialityDto);
		}
		SpecialityDto specialityExist = specialityService.findSpecialityDto(specialityName, specialityDtos);
		if (specialityExist == null) {
			specialityDto.setName(specialityName);

			BatchConfiguration.specialityDtosNotFound.add(specialityDto);
			
		}
	}

	public void checkIfSectorExist(String sectorName) {

		SectorDto sectorDto = new SectorDto();

		if (sectorName.isEmpty()) {
			BatchConfiguration.sectorDtosNotFound.add(sectorDto);
		}
		SectorDto sectorExist = sectorService.findSectorDto(sectorName, sectorDtos);

		if (sectorExist == null) {
			sectorDto.setName(sectorName);
			BatchConfiguration.sectorDtosNotFound.add(sectorDto);
			
		}

	}

	public boolean nameValidation(String lastName) {
	     if(lastName != null && lastName.length() > 1) {
	    	 return true;
	     }else {
	    	 return false;
	     }
	}


	public void nameValidation(String firstName, String lastName) {
		if (!nameValidation(lastName) || !nameValidation(firstName)) {
			isValid = false;

		}

	}

}
