<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Project>
    <totalNumberOfTestableClasses>1</totalNumberOfTestableClasses>
    <cut>
        <fullNameOfTargetClass>com.intellitech.birdnotes.service.impl.ActivityServiceImpl</fullNameOfTargetClass>
        <fullNameOfTestSuite>com.intellitech.birdnotes.service.impl.ActivityServiceImpl_ESTest</fullNameOfTestSuite>
        <generation>
            <id>0</id>
            <failed>true</failed>
            <modified>true</modified>
            <timeBudgetInSeconds>180</timeBudgetInSeconds>
            <memoryInMB>2000</memoryInMB>
            <std_err_CLIENT>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_47_55/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_err_CLIENT.log</std_err_CLIENT>
            <std_out_CLIENT>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_47_55/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_out_CLIENT.log</std_out_CLIENT>
            <std_err_MASTER>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_47_55/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_err_MASTER.log</std_err_MASTER>
            <std_out_MASTER>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_47_55/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_out_MASTER.log</std_out_MASTER>
        </generation>
        <generation>
            <id>1</id>
            <failed>true</failed>
            <modified>true</modified>
            <timeBudgetInSeconds>180</timeBudgetInSeconds>
            <memoryInMB>2000</memoryInMB>
            <std_err_CLIENT>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_49_38/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_err_CLIENT.log</std_err_CLIENT>
            <std_out_CLIENT>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_49_38/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_out_CLIENT.log</std_out_CLIENT>
            <std_err_MASTER>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_49_38/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_err_MASTER.log</std_err_MASTER>
            <std_out_MASTER>/home/<USER>/workspace/birdnotes/back-end_v5/.evosuite/tmp_2024_01_24_09_49_38/logs/com.intellitech.birdnotes.service.impl.ActivityServiceImpl/std_out_MASTER.log</std_out_MASTER>
        </generation>
    </cut>
</Project>
