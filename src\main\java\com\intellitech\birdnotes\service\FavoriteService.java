package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.FavoriteMenuItem;
import com.intellitech.birdnotes.model.dto.FavoriteDto;
import com.intellitech.birdnotes.model.dto.FavoriteRequestDto;

public interface FavoriteService {

	List<FavoriteDto> findAll() throws BirdnotesException;

	void delete(Long id) throws BirdnotesException;

	List<FavoriteMenuItem> add(FavoriteRequestDto favoriteRequestDto) throws BirdnotesException;

}
