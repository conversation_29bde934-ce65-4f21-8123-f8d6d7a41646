package com.intellitech.birdnotes.model.convertor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.PlanifiedProspectDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;

@Component("convertPlanifiedProspectToDto")
public class ConvertPlanifiedProspectToDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private static final Logger LOG = LoggerFactory.getLogger(ConvertPlanifiedProspectToDto.class);

	public PlanifiedProspectDto convert(Planning planning) throws BirdnotesException {

		if (planning == null) {
			LOG.error("planning is null");
			throw new BirdnotesException("planning is null");
		}
		PlanifiedProspectDto planifiedProspectDto;
		planifiedProspectDto = convertToPlanifiedProspectDto(planning);
		fillSpecialityDto(planning.getProspect(), planifiedProspectDto);
		fillSectorDto(planning.getProspect(), planifiedProspectDto);
		fillLocalityDto(planning.getProspect(), planifiedProspectDto);
		fillPotentialDto(planning.getProspect(), planifiedProspectDto);

		return planifiedProspectDto;
	}

	private void fillPotentialDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		PotentialDto potentialDto = new PotentialDto();
		Potential potential = prospect.getPotential();
		if (potential != null) {
			potentialDto.setName(potential.getName());
			potentialDto.setId(potential.getId());
		} else {
			LOG.error("potential is null");
			throw new BirdnotesException("potential is null");
		}
		prospectDto.setPotentialDto(potentialDto);
	}

	private void fillLocalityDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		LocalityDto localityDto = new LocalityDto();
		Locality locality = prospect.getLocality();
		if (locality != null) {
			localityDto.setName(locality.getName());
			localityDto.setId(locality.getId());
			localityDto.setSectorName(locality.getSector().getName());
			localityDto.setSectorId(locality.getSector().getId());

		} else {
			LOG.error("locality is null");
			throw new BirdnotesException("locality is null");
		}
		prospectDto.setLocalityDto(localityDto);

	}

	private void fillSpecialityDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		Speciality speciality = prospect.getSpeciality();
		SpecialityDto specialityDto = new SpecialityDto();

		if (speciality != null) {
			specialityDto.setId(speciality.getId());
			specialityDto.setName(speciality.getName());
		} else {
			LOG.error("speciality is null");
			throw new BirdnotesException("speciality is null");
		}
		prospectDto.setSpecialityDto(specialityDto);
	}

	private void fillSectorDto(Prospect prospect, ProspectDto prospectDto) throws BirdnotesException {
		SectorDto sectorDto = new SectorDto();
		Sector sector = prospect.getSector();
		if (sector != null) {
			sectorDto.setName(sector.getName());
			sectorDto.setId(sector.getId());
		} else {
			LOG.error("sector is null");
			throw new BirdnotesException("sector is null");
		}
		prospectDto.setSectorDto(sectorDto);
	}

	private PlanifiedProspectDto convertToPlanifiedProspectDto(Planning planning) throws BirdnotesException {
		PlanifiedProspectDto planifiedProspectDto = new PlanifiedProspectDto();
		planifiedProspectDto.setPlanningId(planning.getId());
		planifiedProspectDto.setId(planning.getProspect().getId());
		planifiedProspectDto.setIdentifier(planning.getProspect().getIdentifier());
		planifiedProspectDto.setFirstName(planning.getProspect().getFirstName());
		planifiedProspectDto.setLastName(planning.getProspect().getLastName());
		planifiedProspectDto
				.setFullName(planning.getProspect().getFirstName() + ' ' + planning.getProspect().getLastName());
		planifiedProspectDto.setStatus(planning.getProspect().getStatus());
		planifiedProspectDto.setIdprospect(planning.getProspect().getIdprospect());
		planifiedProspectDto.setActivity(planning.getProspect().getActivity());
		planifiedProspectDto.setAddress(planning.getProspect().getAddress());
		planifiedProspectDto.setGsm(planning.getProspect().getGsm());
		planifiedProspectDto.setPhone(planning.getProspect().getPhone());
		planifiedProspectDto.setEmail(planning.getProspect().getEmail());
		planifiedProspectDto.setNote(planning.getProspect().getNote());
		planifiedProspectDto.setLatitude(planning.getProspect().getLatitude());
		planifiedProspectDto.setLongitude(planning.getProspect().getLongitude());
		planifiedProspectDto.setMapAddress(planning.getProspect().getMapAddress());
		planifiedProspectDto.setPlanningDate(planning.getDate());
		planifiedProspectDto.setSpeciality(planning.getProspect().getSpeciality().getName());
		planifiedProspectDto.setSector(planning.getProspect().getSector().getName());
		planifiedProspectDto.setLocality(planning.getProspect().getLocality().getName());
		planifiedProspectDto.setPotential(planning.getProspect().getPotential().getName());
		if(planning.getActivity() != null && planning.getActivity().getId() != null) {
			planifiedProspectDto.setActivityId(planning.getActivity().getId());
			planifiedProspectDto.setActivityTypeName(planning.getActivity().getActivityType().getName());
		}
		if(planning.getStatus() != null) {
			planifiedProspectDto.setPlannedActivityStatus(planning.getStatus().toString());
		}
		
		Set<Range> gammes = planning.getProspect().getRanges();

		if (gammes == null) {
			LOG.error("gamme of " + planning.getProspect().getFirstName() + ' ' + planning.getProspect().getLastName()
					+ " is null");
		} else {

			planning.getProspect().setRanges(gammes);
		}

		if (planning.getProspect().getCreationDate() == null) {
			throw new BirdnotesException("creation date is null");
		}
		planifiedProspectDto.setCreationDate(planning.getProspect().getCreationDate());

		if (planning.getProspect().getUser() == null) {
			throw new BirdnotesException("user is null");
		}
		// planning.getProspect().setDelegateName(planning.getProspect().getDelegate().getFirstName()
		// + " " + planning.getProspect().getDelegate().getLastName());
		ArrayList<String> delegateNames = new ArrayList<>();
		ArrayList<Long> delegateIds = new ArrayList<>();
		if (planning.getProspect().getProspectsAffectation() != null) {
			for (ProspectsAffectation prospectsAffectation : planning.getProspect().getProspectsAffectation()) {
				delegateNames.add(prospectsAffectation.getDelegate().getFirstName() + ' '
						+ prospectsAffectation.getDelegate().getLastName());
				delegateIds.add(prospectsAffectation.getId());
			}
			planifiedProspectDto.setDelegateNames(delegateNames);
			planifiedProspectDto.setDelegateIds(delegateIds);
		}
		return planifiedProspectDto;
	}

	public PlanningDto convertToPlanningDto(Planning planning) {
		PlanningDto planningDto = new PlanningDto();
		planningDto.setDate(planning.getDate());
		planningDto.setId(planning.getId());
		planningDto.setIdentifier(planning.getIdentifier());
		planningDto.setProspect(planning.getProspect().getIdentifier());

		return planningDto;
	}

}