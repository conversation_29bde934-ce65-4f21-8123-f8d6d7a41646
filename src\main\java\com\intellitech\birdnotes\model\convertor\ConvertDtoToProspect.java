package com.intellitech.birdnotes.model.convertor;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Component("convertDtoToProspect")
public class ConvertDtoToProspect {

    private static final Logger LOG = LoggerFactory.getLogger(ConvertDtoToProspect.class);
	
    private RangeRepository gammeRepository;
    private SectorRepository sectorRepository;
	@Autowired
	public void setGammeRepository(RangeRepository gammeRepository, SectorRepository sectorRepository) {
		this.gammeRepository = gammeRepository;
		this.sectorRepository = sectorRepository;
	}
	public Prospect convert(ProspectDto prospectDto, Prospect prospect) throws BirdnotesException {

		if (prospectDto == null) {
			return null;
		}
		if(prospect == null) {
			prospect = new Prospect();
		}
		prospect.setFirstName(prospectDto.getFirstName());
		prospect.setLastName(prospectDto.getLastName());
		prospect.setStatus(prospectDto.getStatus());
		prospect.setIdprospect(prospectDto.getIdprospect());
		prospect.setActivity(prospectDto.getActivity());
		prospect.setAddress(prospectDto.getAddress());
		prospect.setGsm(prospectDto.getGsm());
		prospect.setPhone(prospectDto.getPhone());
		prospect.setEmail(prospectDto.getEmail());
		prospect.setNote(prospectDto.getNote());
		prospect.setSecretary(prospectDto.getSecretary());
		prospect.setGrade(prospectDto.getGrade());
		prospect.setActive(prospectDto.getActive());
		prospect.setLatitude(prospectDto.getLatitude());
		prospect.setLongitude(prospectDto.getLongitude());
		if(prospectDto.getIdentifier()!= null) {
			prospect.setIdentifier(prospectDto.getIdentifier());
		}
		
		prospect.setScrappingId(prospectDto.getScrappingId());
		
		List<Range> gammes = gammeRepository.findAll();
		if (gammes != null && !gammes.isEmpty()) {
			fillGammes(gammes, prospectDto, prospect);
		}
		getProspect(prospectDto ,prospect);
		return prospect;
	}

	private void fillGammes(List<Range> gammes, ProspectDto prospectDto, Prospect prospect) {

		Set<Range> gammeProspect = new HashSet<>();
		if(prospectDto.getRangeIds()!= null && !prospectDto.getRangeIds().isEmpty() ){
			for (Integer gammeId : prospectDto.getRangeIds()) {
				for (Range gamme : gammes) {
					if (gamme.getId() == gammeId) {
						gammeProspect.add(gamme);
					}
				}
			}
			prospect.setRanges(gammeProspect);
		}
	}
	
	private Prospect getProspect(ProspectDto prospectDto,Prospect prospect) throws BirdnotesException {
		
		getLocalisation(prospectDto,prospect);

		SpecialityDto specialityDto = prospectDto.getSpecialityDto();
		Speciality speciality = new Speciality();
		if (specialityDto != null && specialityDto.getId() != null) {
			speciality.setId(specialityDto.getId());
			speciality.setName(specialityDto.getName());
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_SPECIALTY);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_SPECIALTY);
		}
		prospect.setSpeciality(speciality);

		SectorDto sectorDto = prospectDto.getSectorDto();
		Sector sector = new Sector();
		if (sectorDto != null && sectorDto.getId() != null) {
			sector.setId(sectorDto.getId());
			sector.setName(sectorDto.getName());
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_SECTOR);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_SECTOR);
		}
		prospect.setSector(sector);

		LocalityDto localityDto = prospectDto.getLocalityDto();
		Locality locality = new Locality();
		if (localityDto != null && localityDto.getId() != null) {
			locality.setId(localityDto.getId());
			locality.setName(localityDto.getName());
			locality.setSector(sector);
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_LOCALITY);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_LOCALITY);
		}
		prospect.setLocality(locality);
		
		ProspectTypeDto prospectTypeDto = prospectDto.getProspectTypeDto();
		ProspectType prospectType = new ProspectType();
		if (prospectTypeDto != null && prospectTypeDto.getId() != null) {
			prospectType.setId(prospectTypeDto.getId());
			prospectType.setName(prospectTypeDto.getName());
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_TYPE);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_TYPE);
		}
		prospect.setProspectType(prospectType);
		
		EstablishmentDto establishmentDto = prospectDto.getEstablishmentDto();
		
		if (establishmentDto != null && establishmentDto.getId() != null) {
			Establishment establishment = new Establishment();
			establishment.setId(establishmentDto.getId());
			establishment.setName(establishmentDto.getName());
			/*establishment.setActivity(establishmentDto.getActivity());
			if(establishmentDto.getSectorId() !=null) {
				Sector establishmentSector = sectorRepository.findOne(establishmentDto.getSectorId());
				establishment.setSector(establishmentSector);
			}*/
			
			prospect.setEstablishment(establishment);
		} 
		
		
		PotentialDto potentialDto = prospectDto.getPotentialDto();
		Potential potential = new Potential();
		if (potentialDto != null && potentialDto.getId() != null) {
			potential.setId(potentialDto.getId());
			potential.setName(potentialDto.getName());
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_POTENTIAL);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_POTENTIAL);
		}
		prospect.setPotential(potential);
		
		return prospect;
	}

	private void getLocalisation(ProspectDto prospectDto,Prospect prospect) {
		if (prospectDto.getLatitude() != null) {
			prospect.setLatitude(prospectDto.getLatitude());
		}
		if (prospectDto.getLongitude() != null) {
			prospect.setLongitude(prospectDto.getLongitude());
		}
		if (prospectDto.getMapAddress() != null) {
			prospect.setMapAddress(prospectDto.getMapAddress());
		}
	}

	public Prospect convertToEdit(ProspectDto prospectDto)throws BirdnotesException {
		if (prospectDto == null) {
			return null;
		}

		Prospect prospect = new Prospect();
		prospect.setFirstName(prospectDto.getFirstName());
		prospect.setLastName(prospectDto.getLastName());
		prospect.setStatus(prospectDto.getStatus());
		prospect.setIdprospect(prospectDto.getId());
		prospect.setActivity(prospectDto.getActivity());
		prospect.setAddress(prospectDto.getAddress());
		prospect.setGsm(prospectDto.getGsm());
		prospect.setPhone(prospectDto.getPhone());
		prospect.setEmail(prospectDto.getEmail());
		prospect.setNote(prospectDto.getNote());
		prospect.setSecretary(prospectDto.getSecretary());
		prospect.setGrade(prospectDto.getGrade());
		Date date = new Date();
		prospect.setCreationDate(date);
		getProspect(prospectDto ,prospect);
		return prospect;
	}
}
