package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class ActivityDataDto {
	
	public static final ActivityDataDto SINGLE_INSTANCE = new ActivityDataDto();
	
	private List<VisitHistoryGroupDto> visitHistoryGroupDtoList;
	
	private List <ActivityDto> activitiesList;

	public List<VisitHistoryGroupDto> getVisitHistoryGroupDtoList() {
		return visitHistoryGroupDtoList;
	}

	public void setVisitHistoryGroupDtoList(List<VisitHistoryGroupDto> visitHistoryGroupDtoList) {
		this.visitHistoryGroupDtoList = visitHistoryGroupDtoList;
	}

	public List<ActivityDto> getActivitiesList() {
		return activitiesList;
	}

	public void setActivitiesList(List<ActivityDto> activitiesList) {
		this.activitiesList = activitiesList;
	}
	
	

}
