#!/bin/bash
if [ $# -ne 1 ]; then
echo "Please specify the container name"
exit 1
else

#ssh root@161.97.170.134 "kubectl exec birdnotes-db-'$1'-0 -n birdnotes-'$1' -- bash -c \"pg_dump -U postgres birdnotes\" > database.sql"

ssh root@173.212.205.158 "docker exec -u postgres $1 pg_dump  birdnotes > /root/export-$1.sql"

scp "root@173.212.205.158:/root/export-$1.sql" /var/lib/postgresql/ 

runuser -l  postgres -c "psql -c 'drop database if exists '$1"
runuser -l  postgres -c "psql  -c 'create database '$1"
runuser -l  postgres -c "psql $1 < /var/lib/postgresql/export-$1.sql"

fi
