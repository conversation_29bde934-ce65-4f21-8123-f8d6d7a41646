package com.intellitech.birdnotes.model;

import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.enumeration.GiftType;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.GIFT, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Gift {
	
	@Id
	@SequenceGenerator(name = Sequences.GIFT_SEQUENCE, sequenceName = Sequences.GIFT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.GIFT_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.NAME)
	private String name;
	
	@Column(name = Columns.PRICE)
	private Float price;

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.TYPE)
	private GiftType type;

	@JsonIgnore
	@ManyToMany(mappedBy = "gifts")
	private Set<PurchaseOrderTemplate> purchaseOrderTemplates;
	
	
	public Gift(Long id, String name, Float price) {
		super();
		this.id = id;
		this.name = name;
		this.price = price;
	}

	public Gift() {
		super();
	
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "gadget", targetEntity = Visit.class)
	private Set<Visit> visits;



	
	
	public Float getPrice() {
		return price;
	}

	public void setPrice(Float price) {
		this.price = price;
	}

	@OneToMany(mappedBy = "gift")
	private List<GiftSupply> gadgetsSupply;
	public List<GiftSupply> getGadgetsSupply() {
		return gadgetsSupply;
	}

	public void setGadgetsSupply(List<GiftSupply> gadgetsSupply) {
		this.gadgetsSupply = gadgetsSupply;
	}

	public GiftType getType() {
		return type;
	}

	public void setType(GiftType type) {
		this.type = type;
	}

	
	public Set<PurchaseOrderTemplate> getPurchaseOrderTemplates() {
		return purchaseOrderTemplates;
	}

	public void setPurchaseOrderTemplates(Set<PurchaseOrderTemplate> purchaseOrderTemplates) {
		this.purchaseOrderTemplates = purchaseOrderTemplates;
	}
	
	
}
