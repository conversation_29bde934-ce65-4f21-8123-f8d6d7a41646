package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.RoleRequestDto;
import com.intellitech.birdnotes.service.RoleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/roles")
public class RoleController {
	private static final Logger LOG = LoggerFactory.getLogger(RoleController.class);

	@Autowired
	private RoleService roleService;

	@Autowired
	UserService userService;

	@RequestMapping(value = "/findAllRoles", method = RequestMethod.GET)
	public ResponseEntity<List<RoleDto>> findAllRoles() {
		try {
			if (userService.checkHasPermission("ROLE_VIEW")) {
				List<RoleDto> rolesDtos = roleService.findAll();
				return new ResponseEntity<>(rolesDtos, HttpStatus.OK);
			}
			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all roles", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<?> addRole(@RequestBody RoleRequestDto roleRequestDto) {
		try {
			if (userService.checkHasPermission("ROLE_ADD")) {
				List<Role> roleSaved = roleService.add(roleRequestDto);
				if (roleSaved != null) {
					return new ResponseEntity<List<Role>>(roleSaved, HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add role", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new role", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteRole(@PathVariable("id") Integer id) {
	    try {
	        if (userService.checkHasPermission("ROLE_DELETE")) {
	            roleService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting role", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the role with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/update", method = RequestMethod.PUT)
	public ResponseEntity<String> updateRole(@RequestBody RoleDto roleDto) {

		try {
			if (userService.checkHasPermission("ROLE_EDIT")) {
				roleService.update(roleDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update role", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while updating role", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
