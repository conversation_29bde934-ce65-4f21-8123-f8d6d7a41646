package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class SendResultDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Long oldId;
	private Long newId;
	public SendResultDto(Long oldId, Long newId) {
		this.oldId = oldId;
		this.newId = newId;
	}
	public Long getOldId() {
		return oldId;
	}
	public void setOldId(Long oldId) {
		this.oldId = oldId;
	}
	public Long getNewId() {
		return newId;
	}
	public void setNewId(Long newId) {
		this.newId = newId;
	}
}