package com.intellitech.birdnotes.model.convertor;

import java.text.SimpleDateFormat;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.util.BirdnotesUtils;

@Component("convertActivityToDto")
public class ConvertActivityToDto {
	private static final Logger LOG = LoggerFactory.getLogger(ConvertActivityToDto.class);
	private SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy");

	public ActivityDto convert(Activity activity) throws BirdnotesException {

		if (activity == null) {
			LOG.error("activityTypes is null");
			throw new BirdnotesException("activityTypes is null");
		}
		DelegateDto delegateDto = new DelegateDto();
		Delegate delegate = activity.getDelegate();
		if (delegate != null) {
			delegateDto.setFirstName(delegate.getFirstName());
			delegateDto.setLastName(delegate.getLastName());
			delegateDto.setId(delegate.getId());

		} else {
			LOG.error("user is null");
			throw new BirdnotesException("user is null");
		}

		ActivityTypeDto activityTypeDto = new ActivityTypeDto();
		activityTypeDto.setName(activity.getActivityType().getName());
		ActivityDto activityDto = new ActivityDto();
		activityDto.setComment(activity.getComment());
		activityDto.setStringDate(dateFormatter.format(activity.getActivityDate()));
		activityDto.setDelegateDto(delegateDto);
		activityDto.setActivityTypeDto(activityTypeDto);
		activityDto.setHourNumber(activity.getHourNumber());
		activityDto.setUserName(delegateDto.getFirstName() + " " + delegateDto.getLastName());
		activityDto.setActivityTypeName(activity.getActivityType().getName());
		activityDto.setActivityTypeId(activity.getActivityType().getId());
		activityDto.setDelegateId(activity.getDelegate().getId());
		activityDto.setId(activity.getId());
		activityDto.setDate(activity.getActivityDate());
		if(activity.getProspect() != null) {
			activityDto.setProspectId(activity.getProspect().getId());
			LabelValueDto prospectName = new LabelValueDto(activity.getProspect());
			activityDto.setProspectName(prospectName.getLabel());
		}
		
		if(activity.getPlanning() != null && activity.getPlanning().getStatus() != null) {
			activityDto.setPlanifiedActivityStatus(activity.getPlanning().getStatus().toString());
			activityDto.setPlanifiedActivityDate(dateFormatter.format(activity.getPlanning().getDate()));
			activityDto.setNbDaysPlanifiedVsExecuted(Integer.valueOf(BirdnotesUtils.getWorkingDaysBetweenTwoDates(activity.getPlanning().getDate(), activity.getActivityDate(), true)));
		}
		
		return activityDto;
	}

}
