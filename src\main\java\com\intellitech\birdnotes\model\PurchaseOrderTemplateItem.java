package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.PURCHASE_ORDER_TEMPLATE_ITEM, schema = Common.PUBLIC_SCHEMA)
public class PurchaseOrderTemplateItem implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Integer quantity;
	private Product product;
	private Integer freeOrder;
	private Integer labGratuity;
	private PurchaseOrderTemplate purchaseOrderTemplate;
    
	public PurchaseOrderTemplateItem() {
		super();
	}

	@Id
	@SequenceGenerator(name = Sequences.PURCHASE_ORDER_TEMPLATE_ITEM_SEQUENCE, sequenceName = Sequences.PURCHASE_ORDER_TEMPLATE_ITEM_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PURCHASE_ORDER_TEMPLATE_ITEM_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PURCHASE_ORDER_TEMPLATE_ID, unique = false)
	public PurchaseOrderTemplate getPurchaseOrderTemplate() {
		return purchaseOrderTemplate;
	}

	public void setPurchaseOrderTemplate(PurchaseOrderTemplate pruchaseOrderTemplate) {
		this.purchaseOrderTemplate = pruchaseOrderTemplate;
	}


	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, unique = false)
	public Product getProduct() {
		return product;
	}

	
	public void setProduct(Product product) {
		this.product = product;
	}
	

	@Column(name = Columns.QUANTITY)
	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	@Column(name = Columns.FREE_ORDER)
	public Integer getFreeOrder() {
		return freeOrder;
	}

	public void setFreeOrder(Integer freeOrder) {
		this.freeOrder = freeOrder;
	}

	@Column(name = Columns.LAB_GRATUITY)
	public Integer getLabGratuity() {
		return labGratuity;
	}

	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}



}
