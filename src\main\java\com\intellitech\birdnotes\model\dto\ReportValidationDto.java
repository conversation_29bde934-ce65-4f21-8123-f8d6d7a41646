package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;

public class ReportValidationDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long delegateId;
	private String delegateName;
	private Integer invalidVisitsNumber;
	private Integer validVisitsNumber;
	private Integer validGeolocatedVisitsNumber;
	private Integer invalidGeolocatedVisitsNumber;
	private Date visitDate;
	private UserValidationStatus status;

	public ReportValidationDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getDelegateId() {
		return delegateId;
	}

	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}

	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

	public Integer getInvalidVisitsNumber() {
		return invalidVisitsNumber;
	}

	public void setInvalidVisitsNumber(Integer invalidVisitsNumber) {
		this.invalidVisitsNumber = invalidVisitsNumber;
	}

	public Integer getValidVisitsNumber() {
		return validVisitsNumber;
	}

	public void setValidVisitsNumber(Integer validVisitsNumber) {
		this.validVisitsNumber = validVisitsNumber;
	}

	public Integer getValidGeolocatedVisitsNumber() {
		return validGeolocatedVisitsNumber;
	}

	public void setValidGeolocatedVisitsNumber(Integer validGeolocatedVisitsNumber) {
		this.validGeolocatedVisitsNumber = validGeolocatedVisitsNumber;
	}

	public Integer getInvalidGeolocatedVisitsNumber() {
		return invalidGeolocatedVisitsNumber;
	}

	public void setInvalidGeolocatedVisitsNumber(Integer invalidGeolocatedVisitsNumber) {
		this.invalidGeolocatedVisitsNumber = invalidGeolocatedVisitsNumber;
	}

	public Date getVisitDate() {
		return visitDate;
	}

	public void setVisitDate(Date visitDate) {
		this.visitDate = visitDate;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

}
