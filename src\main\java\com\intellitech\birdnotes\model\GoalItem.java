package com.intellitech.birdnotes.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.GOAL_ITEM, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class GoalItem {
	@Id
	@SequenceGenerator(name = Sequences.GOAL_ITEM_SEQUENCE, sequenceName = Sequences.GOAL_ITEM_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.GOAL_ITEM_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.VALUE)
	private Integer value;
	
	@Column(name = Columns.activity)
	private String activity;
	
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.GOAL_ID)
	private Goal goal;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID)
	private Product product;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.POTENTIAL_ID)
	private Potential potential;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.SECTOR_ID)
	private Sector sector;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.SPECIALITY_ID)
	private Speciality speciality;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_TYPE_ID)
	private ProspectType prospectType;


	public GoalItem() {
		super();
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public Goal getGoal() {
		return goal;
	}
	public void setGoal(Goal goal) {
		this.goal = goal;
	}
	public Product getProduct() {
		return product;
	}
	public void setProduct(Product product) {
		this.product = product;
	}
	public Potential getPotential() {
		return potential;
	}
	public void setPotential(Potential potential) {
		this.potential = potential;
	}
	public Sector getSector() {
		return sector;
	}
	public void setSector(Sector sector) {
		this.sector = sector;
	}
	public Speciality getSpeciality() {
		return speciality;
	}
	public void setSpeciality(Speciality speciality) {
		this.speciality = speciality;
	}
	public String getActivity() {
		return activity;
	}
	public void setActivity(String activity) {
		this.activity = activity;
	}
	
	public ProspectType getProspectType() {
		return prospectType;
	}
	public void setProspectType(ProspectType prospectType) {
		this.prospectType = prospectType;
	}

}
