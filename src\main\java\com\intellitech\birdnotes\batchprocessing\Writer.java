package com.intellitech.birdnotes.batchprocessing;
import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.item.ItemWriter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.DuplicateProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.service.ProspectService;

@Component
public class Writer implements ItemWriter<ProspectDto> {

	
	@Autowired
	private ProspectService prospectService;
	
	BufferedWriter writer;
	
	private static final Logger LOG = LoggerFactory.getLogger(Writer.class);
	
	public Writer() throws IOException {
	
		writer = new BufferedWriter(new FileWriter("import-error.txt"));
	    
	    
	}
	 
	
	@Override
	public void write(List<? extends ProspectDto> items) throws IOException {

		Set<ProspectDto> prospectDtos = new HashSet<>(items);
		for (ProspectDto prospectDto : prospectDtos) {

			try {

				List<Long> potentialIds = new ArrayList<>();
				prospectDto.setPotentialIds(potentialIds);
				prospectService.addImportedProspect(prospectDto);
				BatchConfiguration.savedProspects++;

			} catch (BirdnotesException e) {
				LOG.error("Exception when importing prospect ", e);
				writer.write(prospectDto.toString() + " Error : " + e.getMessage());

			}
		}

		writer.close();

	}

}
