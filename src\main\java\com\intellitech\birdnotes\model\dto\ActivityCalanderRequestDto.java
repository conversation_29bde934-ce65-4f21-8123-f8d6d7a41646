package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class ActivityCalanderRequestDto  implements Serializable {

	private static final long serialVersionUID = 1L;
	private String firstDate;
	private String lastDate;
	private Long selectedUser;
	
	public ActivityCalanderRequestDto() {
		super();
	}
	public ActivityCalanderRequestDto(String firstDate, String lastDate, Long selectedUser) {
		super();
		this.firstDate = firstDate;
		this.lastDate = lastDate;
		this.selectedUser = selectedUser ;
	}
	public String getFirstDate() {
		return firstDate;
	}
	public void setFirstDate(String firstDate) {
		this.firstDate = firstDate;
	}
	public String getLastDate() {
		return lastDate;
	}
	public void setLastDate(String lastDate) {
		this.lastDate = lastDate;
	}

	public Long getSelectedUser() {
		return selectedUser;
	}
	public void setSelectedUser(Long selectedUser) {
		this.selectedUser = selectedUser;
	}
	

}
