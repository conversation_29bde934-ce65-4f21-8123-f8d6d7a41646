package com.intellitech.birdnotes.thread;

import java.text.SimpleDateFormat;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;


import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.repository.LocationRepository;
import com.intellitech.birdnotes.service.DistanceService;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DistanceCalculation implements Runnable {

	private List<LocationDto> locations;
	private DistanceService distanceService;
	private LocationRepository locationRepository;
	private Delegate delegate;
	private static final Logger LOG = LoggerFactory.getLogger(DistanceCalculation.class);
	
	


	@Override
	public void run() {
		for (int i=0; i<locations.size()-1; i++) {
			 SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
			 if(df.format(locations.get(i).getDate()).equals(df.format(locations.get(i+1).getDate()))) {
				ResponseEntity<DistanceResponse> distances = null;
				try {
					distances = distanceService.distance(locations.get(i).getLatitude(), locations.get(i).getLongitude(), locations.get(i+1).getLatitude(), locations.get(i+1).getLongitude());
					
				} catch (BirdnotesException e) {
					LOG.error("error when calculating distance", e);
				}
				
				LinkedHashMap distance = (LinkedHashMap) (((List<HashMap>) ((HashMap) distances.getBody().getRows().get(0)).get("elements")).get(0)).get("distance");
				LinkedHashMap duration = (LinkedHashMap) (((List<HashMap>) ((HashMap) distances.getBody().getRows().get(0)).get("elements")).get(0)).get("duration");
				
				/****** save location distance and duration ******/
				Location location = new Location();
				location.setDate(locations.get(i+1).getDate());
				location.setLatitude(locations.get(i+1).getLatitude());
				location.setLongitude(locations.get(i+1).getLongitude());
				location.setDistance((Integer)distance.get("value"));
				location.setDuration((Integer)duration.get("value"));
				location.setDelegate(delegate);
				locationRepository.save(location);
					
				
			 }else {
				 continue;
			 }
		}
		
	}



	public DistanceCalculation(List<LocationDto> locations, Delegate delegate, DistanceService distanceService,
			LocationRepository locationRepository) {
		super();
		this.locations = locations;
		this.distanceService = distanceService;
		this.locationRepository = locationRepository;
		this.delegate = delegate;
	}










	public List<LocationDto> getLocations() {
		return locations;
	}





	public void setLocations(List<LocationDto> locations) {
		this.locations = locations;
	}





	public DistanceService getDistanceService() {
		return distanceService;
	}





	public void setDistanceService(DistanceService distanceService) {
		this.distanceService = distanceService;
	}





	public Delegate getDelegate() {
		return delegate;
	}





	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}
	}

	




