package com.intellitech.birdnotes.model;

import javax.persistence.*;
import java.io.Serializable;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.BUDGETALLOCATION, schema = Common.PUBLIC_SCHEMA)
public class BudgetAllocation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @SequenceGenerator(name = Sequences.BUDGET_ALLOCATION_SEQUENCE, sequenceName = Sequences.BUDGET_ALLOCATION_SEQUENCE, allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.BUDGET_ALLOCATION_SEQUENCE)
    @Column(name = Columns.ID)
    private Long id;

    @Column(name = Columns.YEAR)
    private Integer year;
    
    @Column(name = Columns.MONTHLY_BUDGET)
    private Float monthlyBudget;
    
    @Column(name = Columns.TYPE)
    private String type;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = Columns.USER_ID, referencedColumnName = "id", nullable = false)
    private User user;

    public BudgetAllocation() {}

    public BudgetAllocation(Long id, Integer year, Float monthlyBudget, String type, User user) {
        this.id = id;
        this.year = year;
        this.monthlyBudget = monthlyBudget;
        this.type = type;
        this.user = user;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Float getMonthlyBudget() {
        return monthlyBudget;
    }

    public void setMonthlyBudget(Float monthlyBudget) {
        this.monthlyBudget = monthlyBudget;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
