package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Category;
import com.intellitech.birdnotes.model.convertor.CategoryToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ValueTypeDto;
import com.intellitech.birdnotes.repository.CategoryRepository;
import com.intellitech.birdnotes.service.CategoryService;

@Service("categoryService")
@Transactional
public class CategoryServiceImpl implements CategoryService {

	private CategoryRepository categoryRepository;
	private CategoryToDtoConvertor categoryToDtoConvertor;
	
	@Autowired
	CategoryServiceImpl(CategoryRepository categoryRepository, CategoryToDtoConvertor categoryToDtoConvertor){
		this.categoryRepository = categoryRepository;
		this.categoryToDtoConvertor = categoryToDtoConvertor;
	}

	@Override
	@Transactional(readOnly = true)
	public List<ValueTypeDto> findAllCategories() throws BirdnotesException{
		List<Category> categories = categoryRepository.findAll();
		List<ValueTypeDto> categoryDtos = new ArrayList<>();
		if (!categories.isEmpty()) {
			for (Category category : categories) {
				ValueTypeDto categoryDto = categoryToDtoConvertor.convert(category);
				categoryDtos.add(categoryDto);
			}
		}
		return categoryDtos;
	}
}
