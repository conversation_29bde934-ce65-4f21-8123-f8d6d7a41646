package com.intellitech.birdnotes.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.ValueTypeDto;
import com.intellitech.birdnotes.service.CategoryService;

@RestController
@RequestMapping("/categories")
public class CategoryController {

	private static final Logger LOG = LoggerFactory.getLogger(CategoryController.class);

	@Autowired
	private CategoryService categoryService;

	@RequestMapping(value = "findAllCategories", method = RequestMethod.GET)
	public ResponseEntity<List<ValueTypeDto>> findAllCategories() {
		try {
			List<ValueTypeDto> categoryDtos = categoryService.findAllCategories();
			return new ResponseEntity<>(categoryDtos, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in findAllCategories", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
}
