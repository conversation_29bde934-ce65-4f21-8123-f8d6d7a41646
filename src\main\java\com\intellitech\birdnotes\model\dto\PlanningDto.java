package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class PlanningDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long identifier;
	
	private UserDto userDto ;

	private Date date;
	
	private Long prospect;
	
	private String message;
	
	private Integer line;
	
	public PlanningDto() {super();}
	
	public PlanningDto(Long id, Long identifier, UserDto userDto, Date date, Long prospect,
			String message, Integer line) {
		super();
		this.id = id;
		this.identifier = identifier;
		this.userDto = userDto;
		this.date = date;
		this.prospect = prospect;
		this.message = message;
		this.line = line;
	}
	
	
	
	public Integer getLine() {
		return line;
	}

	public void setLine(Integer line) {
		this.line = line;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public UserDto getUserDto() {
		return userDto;
	}

	public void setUserDto(UserDto userDto) {
		this.userDto = userDto;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	
	public Long getProspect() {
		return prospect;
	}

	public void setProspect(Long prospect) {
		this.prospect = prospect;
	}


	

}
