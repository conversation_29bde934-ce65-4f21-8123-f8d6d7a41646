package com.intellitech.birdnotes.service;

import java.util.List;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.BudgetAllocation;
import com.intellitech.birdnotes.model.dto.BudgetAllocationDto;


public interface BudgetAllocationService {
	
	List<BudgetAllocationDto> findAll() throws BirdnotesException;
	
	BudgetAllocation saveBudgetAllocation(BudgetAllocationDto budgetAllocationDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;

	BudgetAllocationDto findBudgetAllocationDto(Long id) throws BirdnotesException;


	

}