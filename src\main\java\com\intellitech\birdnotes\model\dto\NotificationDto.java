package com.intellitech.birdnotes.model.dto;


import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.GregorianCalendar;

import com.intellitech.birdnotes.model.Notification;

public class NotificationDto implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private Long id;
	private String text;
	private boolean status;
	private String creationDate;
	private LocalDate localDate;
	private Calendar calendar;
	private SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		
	public NotificationDto(Notification notification) {
		this.id = notification.getId();
		this.text = notification.getText();
		this.status = notification.getStatus();
		
		this.creationDate = format.format (notification.getDate());
		localDate = Instant.ofEpochMilli(notification.getDate().getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
		localDate.getDayOfMonth();
		calendar = GregorianCalendar.getInstance(); // creates a new calendar instance
		calendar.setTime(notification.getDate());   // assigns calendar to given date 
		
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}
	

	public boolean getStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public String getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(String creationDate) {
		this.creationDate = creationDate;
	}

	public int getCreationDay() {
		return localDate.getDayOfMonth();
	}


	public int getCreationMonth() {
		return localDate.getMonthValue();
	}


	public int getCreationYear() {
		return localDate.getYear();
	}	
	
	public int getCreationHour() {
		return calendar.get(Calendar.HOUR_OF_DAY); 	
	}
	
	public int getCreationMinutes() {
		 return calendar.get(Calendar.MINUTE); 		
	}

}
