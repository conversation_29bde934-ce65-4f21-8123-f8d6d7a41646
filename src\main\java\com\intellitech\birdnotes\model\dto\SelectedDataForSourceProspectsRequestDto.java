package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class SelectedDataForSourceProspectsRequestDto {
	private Long selectedUser;
	private List<Long> selectedSectors;
	private List<Long> selectedSpecialities;
	private List<String> selectedActivities;
	private List<Long> selectedPotentials;
	private List<Long> selectedLocalities;
	private List<Long> selectedEstablishments;
	private List<Long>selectedProspectTypes;
	private String selectedAffectationType;
	private String prospectName;

	public SelectedDataForSourceProspectsRequestDto() {
		super();
	}

	public Long getSelectedUser() {
		return selectedUser;
	}

	public void setSelectedUser(Long selectedUser) {
		this.selectedUser = selectedUser;
	}

	public List<Long> getSelectedSectors() {
		return selectedSectors;
	}

	public void setSelectedSectors(List<Long> selectedSectors) {
		this.selectedSectors = selectedSectors;
	}

	public List<Long> getSelectedSpecialities() {
		return selectedSpecialities;
	}

	public void setSelectedSpecialities(List<Long> selectedSpecialities) {
		this.selectedSpecialities = selectedSpecialities;
	}

	public List<String> getSelectedActivities() {
		return selectedActivities;
	}

	public void setSelectedActivities(List<String> selectedActivities) {
		this.selectedActivities = selectedActivities;
	}

	public List<Long> getSelectedPotentials() {
		return selectedPotentials;
	}

	public void setSelectedPotentials(List<Long> selectedPotentials) {
		this.selectedPotentials = selectedPotentials;
	}

	public List<Long> getSelectedLocalities() {
		return selectedLocalities;
	}

	public void setSelectedLocalities(List<Long> selectedLocalities) {
		this.selectedLocalities = selectedLocalities;
	}

	public String getSelectedAffectationType() {
		return selectedAffectationType;
	}

	public void setSelectedAffectationType(String selectedAffectationType) {
		this.selectedAffectationType = selectedAffectationType;
	}

	public String getProspectName() {
		return prospectName;
	}

	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}
	public List<Long> getSelectedEstablishments() {
		return selectedEstablishments;
	}
	public void setSelectedEstablishments(List<Long> selectedEstablishments) {
		this.selectedEstablishments = selectedEstablishments;
	}

	public List<Long> getSelectedProspectTypes() {
		return selectedProspectTypes;
	}
	public void setSelectedProspectTypes(List<Long> selectedProspectTypes) {
		this.selectedProspectTypes = selectedProspectTypes;
	}

}
