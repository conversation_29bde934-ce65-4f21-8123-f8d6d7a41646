package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Prospect;
//import com.intellitech.birdnotes.model.Prospect;
//import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;


@Repository
public interface ProspectsAffectationRepository extends JpaRepository<ProspectsAffectation, Long> {

	
	List<ProspectsAffectation> findByProspect(Prospect prospect);
	
	@Query("SELECT p.prospect from ProspectsAffectation p WHERE p.delegate.id in (:ids) AND p.prospect.status = 'VALID' order by p.prospect.firstName ASC")
	List<Prospect> findByUser(@Param("ids") List<Long> ids);
	
	@Query("SELECT p.prospect.id from ProspectsAffectation p WHERE p.delegate.id=:id")
	List<Long> findProspectIdByUserId(@Param("id") Long id);
	
	@Query("SELECT p.prospect from ProspectsAffectation p WHERE p.delegate.id=:id AND p.prospect.sector.id In (:sectorIds)")
	List<Prospect> findByUserAndSectors(@Param("id") Long id,@Param("sectorIds") List<Long> sectorIds);

	@Modifying
	@Query("DELETE ProspectsAffectation p WHERE p.id = ?1")
	void deleteById(Long id);
	
	@Query("SELECT p.id from ProspectsAffectation p WHERE p.delegate.id = ?1")
	List<Long> findAffectationByUser( Long id);
	
	@Query("SELECT p.prospect.id from ProspectsAffectation p WHERE p.prospect.id in (:propsectIds) AND p.delegate.id=:id")
	List<Long> findAffectedProspect( @Param("id") Long id,@Param("propsectIds") List<Long> propsectIds);
	
	@Query("SELECT p from Prospect p WHERE p.id not in (SELECT pa.prospect.id from ProspectsAffectation pa WHERE pa.delegate.id=:id ) AND p.status = 'VALID' order by p.firstName ASC")
	List<Prospect> findNotAffectedProspectsToUser(@Param("id") Long id)  ;
	
	@Query("SELECT p.prospect.id from ProspectsAffectation p WHERE p.delegate.id=:id AND p.prospect.speciality.name like 'PH %'")
	List<Long> findPharmacistsByUser(@Param("id") Long id);
	
	@Query("SELECT p.prospect.id from ProspectsAffectation p WHERE p.delegate.id=:id AND p.prospect.speciality.name not like 'PH %'")
	List<Long> findDoctorsByUser(@Param("id") Long id);

	@Query("SELECT DISTINCT p.prospect.sector from ProspectsAffectation p WHERE p.delegate.id in (:ids) ORDER BY p.prospect.sector.name")
	List<Sector> findDelegatesSectors(@Param("ids")  List<Long> ids);
	
	@Query("SELECT DISTINCT p.prospect.sector from ProspectsAffectation p WHERE p.delegate.id =:id ORDER BY p.prospect.sector.name")
	List<Sector> findDelegateSectors(@Param("id")  Long id);
	
	@Query("SELECT DISTINCT p.prospect.locality from ProspectsAffectation p WHERE p.delegate.id in (:ids) ORDER BY p.prospect.locality.name")
	List<Locality> findDelegateslocalities(@Param("ids")  List<Long> ids);
	
	@Query("SELECT DISTINCT p.prospect.speciality from ProspectsAffectation p WHERE p.delegate.id in (:ids) ORDER BY p.prospect.speciality.name")
	List<Speciality> findDelegatesSpecialities(@Param("ids") List<Long> ids);
	
	
	@Query("SELECT DISTINCT p.prospect.sector.id from ProspectsAffectation p WHERE p.delegate.id=:id")
	List<Long> findUserSectors(@Param("id") Long id);
	
	@Query("SELECT p from Prospect p WHERE p.id not in (SELECT pa.prospect.id from ProspectsAffectation pa WHERE pa.delegate.id=:id) AND p.sector.id IN (:sectorIds) order by p.firstName ASC")
	List<Prospect> findNotAffectedProspectsToUserWithSectors(@Param("id") Long id,@Param("sectorIds") List<Long> sectorIds);
	
	@Query("Select p from ProspectsAffectation p WHERE p.delegate.id = ?1 AND p.prospect.id = ?2")
	ProspectsAffectation findByDelegateAndProspect(Long userId, Long prospectId);

	
	@Modifying
	@Query("DELETE ProspectsAffectation p WHERE p.delegate.id = ?1 AND p.prospect.id = ?2")
	void deleteByDelegateAndProspect(Long userId, Long prospectId);
	
	@Modifying
	@Query("DELETE ProspectsAffectation p WHERE p.prospect.id = ?1")
	void deleteByProspect(Long prospectId);

	
	@Modifying
	@Query("DELETE from ProspectsAffectation p where p.preAffectation.id=:id")
	void deleteByPreAffectation(@Param("id") Long id);
	

	
	@Modifying
	@Query("DELETE from ProspectsAffectation p WHERE p.delegate.id = ?1")
	void deleteByUserId(Long userId);

	
	@Query("select p from ProspectsAffectation p where p.prospect.id=:prospectId")
	ProspectsAffectation findByProspect(@Param("prospectId") Long prospectId);
	
	@Query("select p from ProspectsAffectation p where p.prospect.id=:prospectId and p.delegate.id=:userId")
	ProspectsAffectation findByProspectAndUser(@Param("prospectId") Long prospectId, @Param("userId") Long userId);
}

