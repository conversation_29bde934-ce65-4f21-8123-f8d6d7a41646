{"java.configuration.updateBuildConfiguration": "automatic", "java.compile.nullAnalysis.mode": "automatic", "java.debug.settings.onBuildFailureProceed": true, "java.import.maven.enabled": true, "java.maven.downloadSources": true, "java.maven.downloadJavadoc": true, "spring-boot.ls.checkJVM": false, "spring.initializr.defaultLanguage": "Java", "spring.initializr.defaultJavaVersion": "8", "spring.initializr.defaultPackaging": "War", "files.exclude": {"**/target": true, "**/.classpath": true, "**/.project": true, "**/.settings": true, "**/.factorypath": true}, "java.saveActions.organizeImports": true, "java.format.settings.url": "https://raw.githubusercontent.com/google/styleguide/gh-pages/eclipse-java-google-style.xml", "java.format.settings.profile": "GoogleStyle", "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": true}}