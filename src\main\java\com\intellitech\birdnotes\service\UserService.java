
package com.intellitech.birdnotes.service;

import java.util.List;
import java.util.Map;
import java.util.Set;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.UserDtoRequest;
import com.intellitech.birdnotes.model.dto.VisitHistoryParamDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import org.springframework.dao.DataIntegrityViolationException;



public interface UserService {

	//List<UserDto> getAllDelegates();
	List<UserDto> findAllDelegatesWithoutGoal(GoalRequest goalsRequestDto);
	//List<UserDto> getAllDelegatesByUserRole(BirdnotesUser user);

	List<User> saveAll(List<UserDto> userDtos) throws BirdnotesException;

	void deleteUser(Long userId) throws BirdnotesException;

	UserDto findByUsername(String username);
	
	List<UserDto> getAllUsers();

	User saveUser(UserDtoRequest userDto) throws BirdnotesException;

	void updateUser(UserDtoRequest userDto) throws BirdnotesException;
	
	public List<UserDto> getAllSupervisors(List<Integer> roleId) throws BirdnotesException;
	
	User findUserById(Long id);
	
	public String handleDataIntegrityViolationException(DataIntegrityViolationException e);
	
	public String getTranslatedLabel(String key) ;

	
	void sendCredentials(UserDto userDto);
	
	//List<Long> getAllDelegatesIds();

	UserDto findUserDto(String userName, List<UserDto> userDtos);

	Set<Role> getRolesOfCurrentUser ();
	Set<String> getAllPermissionsOfCurrentUser();
	boolean checkHasPermission(String permissionName);
	void sendMailReport(String reportLink,List<UserDto> list);
	
	public List<UserDto>  getSubUsers();
	
	public List<UserDto>  findAllUsers();
	
	public List<Long>  getSubUsersIds();
	
	public List<Long>  getSubDelegatesIds();


	List<UserDto> findUsers();

	VisitHistoryParamDto findHistoryParams(List<Long> userIds, String type);

	Map<String, List<UserDto>> importUser(String path) throws BirdnotesException;

	List<UserDto> getOnlyUsers();
	


	
  
}
