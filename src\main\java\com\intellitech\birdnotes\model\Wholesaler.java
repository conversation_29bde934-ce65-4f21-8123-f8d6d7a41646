package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.enumeration.WholesalerStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.WHOLESALER, schema = Common.PUBLIC_SCHEMA)
public class Wholesaler implements Serializable {

	private static final long serialVersionUID = 1L;
	
	@Id
	@SequenceGenerator(name = Sequences.WHOLESALER_SEQUENCE, sequenceName = Sequences.WHOLESALER_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.WHOLESALER_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.NAME, length = Numbers.N_85)
	private String name;
	
	@Column(name = Columns.RESPONSIBLE)
	private String responsible;
	
	@Column(name = Columns.PHONE, length = Numbers.N_14)
	private String phone;
	
	@Column(name = Columns.ADDRESS)
	private String address;
	
	@Column(name = Columns.DISCOUNT)
	private Float discount;

	
	@Column(name = Columns.DESCRIPTION)
	private String description;
	
	@Column(name = Columns.EMAIL, length = Numbers.N_120)
	private String email;
	
	@ManyToOne(optional = false)
	@JoinColumn(name = Columns.SECTOR_ID)
	private Sector sector;

	@JsonIgnore
	@OneToOne(optional = true)
	@JoinColumn(name = Columns.PROSPECT_ID)
	private Prospect prospect;

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	private WholesalerStatus status;
	
	public Wholesaler() {
		super();
	}
	
	public Float getDiscount() {
		return discount;
	}


	public void setDiscount(Float discount) {
		this.discount = discount;
	}

	
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	
	public String getResponsible() {
		return responsible;
	}

	public void setResponsible(String responsible) {
		this.responsible = responsible;
	}

	
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}
	
	
	public String getAddress() {
		return address;
	}
	public void setAddress(String address) {
		this.address = address;
	}
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}
	
	
	public Sector getSector() {
		return sector;
	}

	public void setSector(Sector sector) {
		this.sector = sector;
	}
	public Prospect getProspect() {
		return prospect;
	}


	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

	public WholesalerStatus getStatus() {
		return status;
	}

	public void setStatus(WholesalerStatus status) {
		this.status = status;
	}
	


	/*@Column(name = BirdnotesConstants.Columns.SALEEMAIL, length = BirdnotesConstants.Numbers.N_120)
	public String getSaleEmail() {
		return saleEmail;
	}

	public void setSaleEmail(String saleEmail) {
		this.saleEmail = saleEmail;
	}*/
	/*
	 * @ManyToOne(fetch = FetchType.EAGER, optional = true)
	 * 
	 * @JoinColumn(name = BirdnotesConstants.Columns.VISIT_PRODUCT_ID)
	 * 
	 * public VisitsProducts getVisitsProducts() { return visitsProducts; }
	 * 
	 * public void setVisitsProducts(VisitsProducts visitsProducts) {
	 * this.visitsProducts = visitsProducts; }
	 */
	
}


