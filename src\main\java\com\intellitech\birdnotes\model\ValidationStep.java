package com.intellitech.birdnotes.model;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.VALIDATION_STEP, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ValidationStep {
	@Id
	@SequenceGenerator(name = Sequences.VALIDATION_STEP_SEQUENCE, sequenceName = Sequences.VALIDATION_STEP_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.VALIDATION_STEP_SEQUENCE)
	@Column(name = Columns.ID)
	private Integer id;
	
	@Column(name = Columns.MIN_VALIDATORS_NUMBER)
	private Integer minValidatorsNumber;
	
	@Column(name = Columns.VALIDATION_TYPE)
	private String validationType;
	
	@Column(name = Columns.RANK)
	private Integer rank ;
	
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;
    
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.ROLE_ID)
	private Role role;  

    @OneToMany(mappedBy = "validationStep")
    private List<ValidationStatus> validationStatus;
    
	public ValidationStep() {
		super();
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getMinValidatorsNumber() {
		return minValidatorsNumber;
	}

	public void setMinValidatorsNumber(Integer minValidatorsNumber) {
		this.minValidatorsNumber = minValidatorsNumber;
	}

	public String getValidationType() {
		return validationType;
	}

	public void setValidationType(String validationType) {
		this.validationType = validationType;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public List<ValidationStatus> getValidationStatus() {
		return validationStatus;
	}

	public void setValidationStatus(List<ValidationStatus> validationStatus) {
		this.validationStatus = validationStatus;
	}
    
	
}
