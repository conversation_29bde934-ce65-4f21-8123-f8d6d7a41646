package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class FavoriteDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String label;
	private String icon;
	private String routerLink;
	private Integer rank;

	public FavoriteDto() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public String getRouterLink() {
		return routerLink;
	}

	public void setRouterLink(String routerLink) {
		this.routerLink = routerLink;
	}

}
