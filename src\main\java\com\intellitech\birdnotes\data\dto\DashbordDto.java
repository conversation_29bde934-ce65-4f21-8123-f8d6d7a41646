package com.intellitech.birdnotes.data.dto;

import java.util.List;

import com.intellitech.birdnotes.model.dto.ActivityByPeriod;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.ProspectMessage;

public class DashbordDto {
	
	private List<LabelValueDto> marketingExpenses;
	private List<LabelValueDto> expensesReportsByDelegate;
	private List<LabelValueDto> expensesReportsByType;
    private List <ProspectMessage> urgentMessages;
    private List<ActivityByPeriod> activitiesByPeriod;
    private List<ActivityByPeriod> activitiesByPotential;
    private List<ActivityByPeriod> activitiesByDelegate;
    private List<ActivityByPeriod> activitiesBySpeciality;
    private List<LabelValueDto> prospectsSatisfaction;
    private Float salesRevenue;
	
	
	public List<LabelValueDto> getMarketingExpenses() {
		return marketingExpenses;
	}
	public void setMarketingExpenses(List<LabelValueDto> marketingExpenses) {
		this.marketingExpenses = marketingExpenses;
	}
	
	public List<LabelValueDto> getExpensesReportsByDelegate() {
		return expensesReportsByDelegate;
	}
	public void setExpensesReportsByDelegate(List<LabelValueDto> expensesReportsByDelegate) {
		this.expensesReportsByDelegate = expensesReportsByDelegate;
	}
	public List<LabelValueDto> getExpensesReportsByType() {
		return expensesReportsByType;
	}
	public void setExpensesReportsByType(List<LabelValueDto> expensesReportsByType) {
		this.expensesReportsByType = expensesReportsByType;
	}
	public List<ProspectMessage> getUrgentMessages() {
		return urgentMessages;
	}
	public void setUrgentMessages(List<ProspectMessage> urgentMessages) {
		this.urgentMessages = urgentMessages;
	}
	public List<ActivityByPeriod> getActivitiesByPeriod() {
		return activitiesByPeriod;
	}
	public void setActivitiesByPeriod(List<ActivityByPeriod> activitiesByPeriod) {
		this.activitiesByPeriod = activitiesByPeriod;
	}
	public List<ActivityByPeriod> getActivitiesByPotential() {
		return activitiesByPotential;
	}
	public void setActivitiesByPotential(List<ActivityByPeriod> activitiesByPotential) {
		this.activitiesByPotential = activitiesByPotential;
	}
	public List<ActivityByPeriod> getActivitiesByDelegate() {
		return activitiesByDelegate;
	}
	public void setActivitiesByDelegate(List<ActivityByPeriod> activitiesByDelegate) {
		this.activitiesByDelegate = activitiesByDelegate;
	}
	public List<ActivityByPeriod> getActivitiesBySpeciality() {
		return activitiesBySpeciality;
	}
	public void setActivitiesBySpeciality(List<ActivityByPeriod> activitiesBySpeciality) {
		this.activitiesBySpeciality = activitiesBySpeciality;
	}
	public List<LabelValueDto> getProspectsSatisfaction() {
		return prospectsSatisfaction;
	}
	public void setProspectsSatisfaction(List<LabelValueDto> prospectsSatisfaction) {
		this.prospectsSatisfaction = prospectsSatisfaction;
	}
	public Float getSalesRevenue() {
		return salesRevenue;
	}
	public void setSalesRevenue(Float salesRevenue) {
		this.salesRevenue = salesRevenue;
	}
	
	
}
