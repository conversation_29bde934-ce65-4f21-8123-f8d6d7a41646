package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ContactType;
import com.intellitech.birdnotes.model.dto.ContactTypeDto;

import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ContactTypeService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/contactTypes")
public class ContactTypeController {

	private static final Logger LOG = LoggerFactory.getLogger(ContactTypeController.class);

	@Autowired
	private ContactTypeService contactTypeService;

	@Autowired
	UserService userService;
	
	@RequestMapping(value = "/findAllContactTypes", method = RequestMethod.GET)
	public ResponseEntity<List<ContactTypeDto>> getAllContactType() {
		try {
			if (userService.checkHasPermission("AUTOMATION_RULE_VIEW")) {
				List<ContactTypeDto> result = contactTypeService.findAll();
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all contact types ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}}
	
	@RequestMapping(value = "/saveContactType", method = RequestMethod.POST)
	public ResponseEntity<String> saveContactType(@RequestBody ContactTypeDto contactTypeDto) {
	    try {
	        if (userService.checkHasPermission("CONTACT_TYPE_EDIT")) {
	        	ContactType savedContactType = contactTypeService.saveContactType(contactTypeDto);
	            if (savedContactType!= null) {
	                return new ResponseEntity<>(savedContactType.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving contact type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving contact type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}
	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteContactType(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("CONTACT_TYPE_DELETE")) {
	            contactTypeService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting contact type", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the contact type with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}
	
	

	}