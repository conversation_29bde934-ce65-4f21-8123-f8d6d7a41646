package com.intellitech.birdnotes.data.dto;

import java.util.List;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
public  class GoalFormData{

	
	private List<Sector> sectors;
	private List<ProspectType> prospectTypes;
    private List<Speciality> specialities;
    private List<Product> products;
    private List<Potential> potentials;
    private List<Range> ranges;
    
	public List<Sector> getSectors() {
		return sectors;
	}
	public void setSectors(List<Sector> sectors) {
		this.sectors = sectors;
	}
	public List<ProspectType> getProspectTypes() {
		return prospectTypes;
	}
	public void setProspectTypes(List<ProspectType> prospectTypes) {
		this.prospectTypes = prospectTypes;
	}
	public List<Speciality> getSpecialities() {
		return specialities;
	}
	public void setSpecialities(List<Speciality> specialities) {
		this.specialities = specialities;
	}
	public List<Product> getProducts() {
		return products;
	}
	public void setProducts(List<Product> products) {
		this.products = products;
	}
	public List<Potential> getPotentials() {
		return potentials;
	}
	public void setPotentials(List<Potential> potentials) {
		this.potentials = potentials;
	}
	public List<Range> getRanges() {
		return ranges;
	}
	public void setRanges(List<Range> ranges) {
		this.ranges = ranges;
	}
    
	
}