package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class SectorRequestDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private String name;
	private Double latitude;
	private Double longitude;
	public SectorRequestDto() {
		super();
	}

	public SectorRequestDto(Long user, String name, String userFirstName, String userLastName) {
		super();
		this.name = name;
	}


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}
	
}