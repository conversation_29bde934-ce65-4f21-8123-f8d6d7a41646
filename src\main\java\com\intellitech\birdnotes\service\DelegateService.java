
package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.data.dto.GoalsRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.DelegateDtoRequest;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.UserDtoRequest;
import com.intellitech.birdnotes.model.dto.VisitHistoryParamDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.security.BirdnotesUser;


public interface DelegateService {

	//List<DelegateDto> getAllDelegates();
	List<DelegateDto> findAllDelegatesWithoutGoal(GoalRequest goalsRequestDto);
	//List<DelegateDto> getAllDelegatesByDelegateRole(BirdnotesDelegate delegate);

	Delegate saveDelegate(DelegateDtoRequest delegateDto, MultipartFile file) throws BirdnotesException;
	
	List<Delegate> saveAll(List<DelegateDto> delegateDtos) throws BirdnotesException;

	
	void deleteDelegate(Long delegateId) throws BirdnotesException;

	
	List<DelegateDto> findAllDelegate();

	
	Delegate findDelegateById(Long id);
	
	//List<Long> getAllDelegatesIds();

	List<String> sendMailForSynchronization();


	VisitHistoryParamDto findHistoryParams(List<Long> delegateIds, String type);

	Map<String, List<DelegateDto>> importDelegate(String path) throws BirdnotesException;

	List<DelegateDto> getSubDelegates();

	List<DelegateDto> findAllDelegates();
	

	Map<String, Object> calculateDailyDistance(Date date) throws Exception;
	
    void updateUserStatus(Long userId, Boolean active) throws BirdnotesException;



  
}
