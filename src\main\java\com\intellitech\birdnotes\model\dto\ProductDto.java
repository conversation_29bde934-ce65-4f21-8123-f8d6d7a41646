package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.model.Range;

public class ProductDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String name;
	private Float price;
	private Float buyingPrice;
	private Integer numberOfCapsules;
	private Integer stock;
	private String description;
	private Set<Range> ranges;
	private List<Integer> rangeIds;
	private String rangesString;
	private Long version;
	private String quantityUnit;
	private String code;
	private List<String> documents;
	private List<FileDto> documentDtoList;
	private List<String> deletedFiles;
	private String documentRootPath;
	private String message;
	private Integer line;
	private Float vat;

	// for ProductRequest

	private Integer gammeId;

	private Set<String> gammesAsString;

	public ProductDto() {
		super();
	}

	public ProductDto(Long id, String name, Float price, Float buyingPrice, Integer numberOfCapsules, Integer stock,
			String description, Set<Range> ranges, List<Integer> rangeIds, String gammesString, Long version,
			String quantityUnit, String code, List<String> documents, List<FileDto> documentDtoList,
			List<String> deletedFiles, String documentRootPath, String message, Integer line, Integer gammeId,
			Set<String> gammesAsString) {
		super();
		this.id = id;
		this.name = name;
		this.price = price;
		this.buyingPrice = buyingPrice;
		this.numberOfCapsules = numberOfCapsules;
		this.stock = stock;
		this.description = description;
		this.ranges = ranges;
		this.rangeIds = rangeIds;
		this.rangesString = gammesString;
		this.version = version;
		this.quantityUnit = quantityUnit;
		this.code = code;
		this.documents = documents;
		this.documentDtoList = documentDtoList;
		this.deletedFiles = deletedFiles;
		this.documentRootPath = documentRootPath;
		this.message = message;
		this.line = line;
		this.gammeId = gammeId;
		this.gammesAsString = gammesAsString;
	}

	public Float getVat() {
		return vat;
	}

	public void setVat(Float vat) {
		this.vat = vat;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Integer getLine() {
		return line;
	}

	public void setLine(Integer line) {
		this.line = line;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Float getPrice() {
		return price;
	}

	public void setPrice(Float price) {
		this.price = price;
	}

	public Float getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(Float buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public Integer getNumberOfCapsules() {
		return numberOfCapsules;
	}

	public void setNumberOfCapsules(Integer numberOfCapsules) {
		this.numberOfCapsules = numberOfCapsules;
	}

	public Integer getStock() {
		return stock;
	}

	public void setStock(Integer stock) {
		this.stock = stock;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Set<Range> getRanges() {
		return ranges;
	}

	public void setRanges(Set<Range> ranges) {
		this.ranges = ranges;
	}

	public String getRangesString() {
		return rangesString;
	}

	public void setRangesString(String rangesString) {
		this.rangesString = rangesString;
	}

	public List<Integer> getRangeIds() {
		return rangeIds;
	}

	public void setRangeIds(List<Integer> rangeIds) {
		this.rangeIds = rangeIds;
	}

	public Integer getGammeId() {
		return gammeId;
	}

	public void setGammeId(Integer gammeId) {
		this.gammeId = gammeId;
	}

	public Set<String> getGammesAsString() {
		return gammesAsString;
	}

	public void setGammesAsString(Set<String> gammesAsString) {
		this.gammesAsString = gammesAsString;
	}

	public List<String> getDocuments() {
		return documents;
	}

	public void setDocuments(List<String> documents) {
		this.documents = documents;
	}

	public String getDocumentRootPath() {
		return documentRootPath;
	}

	public void setDocumentRootPath(String documentRootPath) {
		this.documentRootPath = documentRootPath;
	}

	public List<String> getDeletedFiles() {
		return deletedFiles;
	}

	public void setDeletedFiles(List<String> deletedFiles) {
		this.deletedFiles = deletedFiles;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getQuantityUnit() {
		return quantityUnit;
	}

	public void setQuantityUnit(String quantityUnit) {
		this.quantityUnit = quantityUnit;
	}

	public List<FileDto> getDocumentDtoList() {
		return documentDtoList;
	}

	public void setDocumentDtoList(List<FileDto> documentDtoList) {
		this.documentDtoList = documentDtoList;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

}
