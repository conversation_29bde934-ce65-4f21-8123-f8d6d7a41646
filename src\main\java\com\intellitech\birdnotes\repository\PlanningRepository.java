package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;


@Repository
public interface PlanningRepository extends JpaRepository<Planning, Long> {
	
	@Query("SELECT p from Planning p WHERE p.delegate.id =:id and (date(p.date) BETWEEN date(:firstDay) AND date(:lastDay)) order by p.prospect.locality.id")
	List<Planning> findPlanificationByUserAndDate(@Param("id") Long id, @Param("firstDay")Date firstDay, @Param("lastDay") Date lastDay);
	
	@Query("SELECT p.prospect.id from Planning p WHERE p.delegate.id =:userId and (date(p.date) BETWEEN date(:startDate) AND date(:endDate))  ")
	List<Long> findPlanedProspectIds(@Param("userId") Long userId, @Param("startDate")Date startDate, @Param("endDate") Date endDate);
	
	@Query("SELECT p.prospect.id from Planning p WHERE (date(p.date) BETWEEN date(:startDate) AND date(:endDate))  ")
	List<Long> findPlanedProspectIdsByUser( @Param("startDate")Date startDate, @Param("endDate") Date endDate);
	
	
	@Query("SELECT p from Planning p WHERE p.delegate.id =:id ")
	List<Planning> findPlanningByUser(@Param("id") Long id);
	
	
	@Modifying
	@Query("DELETE Planning p WHERE p.id in (:planningReportIds)")
	void deleteById(@Param("planningReportIds") List<Long> planningReportIds);
	
	void deleteByProspect(Prospect prospect);

	@Modifying
	@Query("UPDATE Planning p  set p.prospect =:oldProspect WHERE p.prospect =:newProspect ")
	void updateProspect(@Param("oldProspect") Prospect oldProspect,@Param("newProspect")  Prospect newProspect);
	
	
	@Query("SELECT p FROM Planning p WHERE p.identifier = ?1 And p.delegate.id = ?2")
	Planning findByIdentifier(Long identifier, Long userId);
	
	Planning findByIdentifier(Long identifier);
	
	@Modifying
	@Query("Delete FROM Planning p WHERE p.identifier = ?1 And p.delegate.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);
	
	
	@Modifying
	@Query(value ="Update planning  set planning_date =planning_date + interval '1 week' WHERE delegate_id =? and planning_date >= ? ", nativeQuery = true)
	void postponePlanning(Long userId, Date currentWeekDate);
	
	
	

	List<Planning> findByDelegateAndDateGreaterThanEqual(Delegate delegate, Date currentWeekDate);
    
	
	@Modifying
	@Query("DELETE from Planning p WHERE p.prospect.id = ?1")
	void deleteByProspectId(Long prospectId);

	@Query("SELECT new com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse(pop) FROM ProspectOrderPrediction pop, ProspectsAffectation pa WHERE pa.prospect.id = pop.prospect.id and pa.delegate.id =  ?1 AND pop.predictionDate >= ?2")
	List<ProspectOrderPredictionResponse> getProspectsOrderPrediction( Long userId, Date predictionDate);

	@Query("select p from Planning p where p.prospect.id=:prospectId and p.date=:date and p.delegate.id=:userId")
	Planning findByProspectDateAndUser(@Param("prospectId") Long prospectId ,@Param("date")  Date date,@Param("userId") Long userId);
}
