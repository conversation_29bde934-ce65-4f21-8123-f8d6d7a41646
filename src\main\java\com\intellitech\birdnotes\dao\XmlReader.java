package com.intellitech.birdnotes.dao;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.model.Action;
import com.intellitech.birdnotes.model.ActionList;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.EventList;

@Service("xmlReader")
public class XmlReader {
	@Autowired
	ResourceLoader resourceLoader;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	public List<Event> getEventXml() throws IOException {
		try {

			Resource resource = resourceLoader.getResource("classpath:events.xml");
			InputStream input = resource.getInputStream();

			File file = resource.getFile();
			JAXBContext jaxbContext = JAXBContext.newInstance(EventList.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			EventList eventsList = (EventList) jaxbUnmarshaller.unmarshal(file);

			return eventsList.getEvent();

		} catch (Exception e) {
			log.error("error in getXml ", e);
			return new ArrayList<>();
		}

	}

	public List<Action> getActionXml() throws IOException {
		try {
			Resource resource = resourceLoader.getResource("classpath:actions.xml");

			InputStream input = resource.getInputStream();

			File file = resource.getFile();
			JAXBContext jaxbContext = JAXBContext.newInstance(ActionList.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			ActionList actionsList = (ActionList) jaxbUnmarshaller.unmarshal(file);

			return actionsList.getAction();

		} catch (Exception e) {
			log.error("error in getXml ", e);
			return new ArrayList<>();
		}

	}

}
