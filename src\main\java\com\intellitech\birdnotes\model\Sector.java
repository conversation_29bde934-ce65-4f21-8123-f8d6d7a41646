package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.SECTOR, schema = Common.PUBLIC_SCHEMA)
public class Sector implements Serializable{

	private static final long serialVersionUID = 1L;
	
	@Id
	@SequenceGenerator(name = Sequences.SECTOR_SEQUENCE, sequenceName = Sequences.SECTOR_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.SECTOR_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.NAME, length = BirdnotesConstants.Numbers.N_255, unique=true)
	private String name;
	@Column(name = BirdnotesConstants.Columns.LATITUDE)
	private Double latitude;
	@Column(name = BirdnotesConstants.Columns.LONGITUDE)
	private Double longitude;
	@JsonIgnore
	@OneToMany(fetch = FetchType.LAZY, cascade=CascadeType.ALL, mappedBy = "sector", targetEntity = Prospect.class)
	private Set<Prospect> prospect;
	
	
	@OneToMany(fetch = FetchType.EAGER, cascade=CascadeType.ALL, mappedBy = "sector", targetEntity = Locality.class)
	@OrderBy("name")
	private Set<Locality> localities = new HashSet<>();
	
	@OneToMany (mappedBy = "sector")
	private List<GoalItem> goalItems;



	
	public Sector() {
		super();
	}
	
	public Sector(Long id, String name, Set<Prospect> prospect, Set<Locality> localities) {
		super();
		this.id = id;
		this.name = name;
		this.prospect = prospect;
		this.localities = localities;
	}
	



	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Set<Prospect> getProspect() {
		return prospect;
	}

	public void setProspect(Set<Prospect> prospect) {
		this.prospect = prospect;
	}

	
	public Set<Locality> getLocalities() {
		return localities;
	}

	public void setLocalities(Set<Locality> localities) {
		this.localities = localities;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	@Override
	public String toString() {
		return "Sector [id=" + id + ", name=" + name + ", latitude=" + latitude + ", longitude=" + longitude
				+ ", prospect=" + prospect + ", localities=" + localities + "]";
	}

	public List<GoalItem> getGoalItems() {
		return goalItems;
	}

	public void setGoalItems(List<GoalItem> goalItems) {
		this.goalItems = goalItems;
	}
	
}