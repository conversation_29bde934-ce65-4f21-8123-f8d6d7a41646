package com.intellitech.birdnotes.util;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;

import com.intellitech.birdnotes.controller.UploadController;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.LocalityRequestDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SectorRequestDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;

public class ReadExcelFileUtil<T> {

	

	// PROSPECT ATTRIBUTES
	private static final String PROSPECT_NAME_LABEL = "Nom";
	private static final String PROSPECT_ACTIVITY_LABEL = "Activité";
	private static final String PROSPECT_POTENTIAL_LABEL = "Potentiel";
	private static final String PROSPECT_ADDRESS_LABEL = "Adresse";
	private static final String PROSPECT_GSM_LABEL = "GSM";
	private static final String PROSPECT_PHONE_LABEL = "Téléphone";
	private static final String PROSPECT_EMAIL_LABEL = "Mail";
	private static final String PROSPECT_SPECIALITY_LABEL = "Spécialité";
	private static final String PROSPECT_SECTOR_LABEL = "Secteur";
	private static final String PROSPECT_LOCALITY_LABEL = "Localité";
	private static final String PROSPECT_NOTE_LABEL = "Remarques";

	// DELEGATE ATTRIBUTES
	private static final String DELEGATE_FIRST_NAME_LABEL = "Prénom";
	private static final String DELEGATE_LAST_NAME_LABEL = "Nom";
	private static final String DELEGATE_PHONE_LABEL = "Téléphone";
	private static final String DELEGATE_HIRING_DATE_LABEL = "Date d'embauche";
	private static final String DELEGATE_BIRTHDAY_DATE_LABEL = "Date de naissance";
	private static final String DELEGATE_WORK_TYPE_LABEL = "Type";
	private static final String DELEGATE_PHARAMA_LABEL = "Pharmaceutique";

	// SECTOR ATTRIBUTES
	private static final String SECTOR_NAME_LABEL = "secteur";
	private static final String SECTOR_DELEGATE_FIRST_NAME_LABEL = "délégué prénom";
	private static final String SECTOR_DELEGATE_LAST_NAME_LABEL = "délégué nom";

	private T entity;

	private Set<ProductDto> emptyProductData = new HashSet<>();

	private Set<ProductDto> invalidProduct = new HashSet<>();

	private Set<ProductDto> duplicateProduct = new HashSet<>();

	private Set<PlanningDto> emptyPlanningData = new HashSet<>();

	private Set<PlanningDto> invalidPlanningProspects = new HashSet<>();

	private Set<PlanningDto> duplicatePlanningProspects = new HashSet<>();

	private DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");

	@Autowired
	ProspectRepository prospectRepository;

	@Autowired
	PlanningRepository planningRepository;



	private static final Logger LOG = LoggerFactory.getLogger(UploadController.class);

	@SuppressWarnings("unchecked")
	public List<T> readDataFromExcelFile(String filePath) throws IOException, ParseException {

		Workbook workbook = null;
		try (FileInputStream fis = new FileInputStream(filePath)) {
			if (filePath.toLowerCase().endsWith("xlsx")) {
				workbook = new XSSFWorkbook(fis);
			} else if (filePath.toLowerCase().endsWith("xls")) {
				workbook = new HSSFWorkbook(fis);
			} else if (filePath.toLowerCase().endsWith("csv")) {
				workbook = new HSSFWorkbook(fis);
			}
		}
		if (workbook == null) {
			throw new NullPointerException("Workbook is null");
		}
		Sheet sheet = workbook.getSheetAt(0);

		return returnData(sheet);

	}

	private Map<String, Integer> getCellNames(Sheet sheet) {
		int totalRows = sheet.getPhysicalNumberOfRows();
		Map<String, Integer> cellNames = new HashMap<>();
		Row row = sheet.getRow(0); // Get first row

		short minColIndex = row.getFirstCellNum();
		short maxColIndex = row.getLastCellNum();

		for (short colIx = minColIndex; colIx < maxColIndex; colIx++) {
			Cell cell = row.getCell(colIx);
			if (cell != null) {
				cellNames.put(cell.getStringCellValue(), cell.getColumnIndex());
			}
		}
		return cellNames;
	}

	@SuppressWarnings("rawtypes")
	private List returnData(Sheet sheet) throws ParseException {

		if (entity instanceof PlanningDto) {
			try {
				return getPlanningData(sheet);
			} catch (ParseException e) {
				LOG.error("Error in importing planning", e);
			}
		} else if (entity instanceof UserDto) {
			return getDelegateData(sheet);
		} else {
			return getAllDataOfProspect(sheet);
		}
		return null;
	}

	@SuppressWarnings("rawtypes")
	private List getAllDataOfProspect(Sheet sheet) {
		if (entity instanceof LocalityRequestDto) {
			return getLocalitiesData(sheet);
		} else if (entity instanceof ProspectDto) {
			return getProspectData(sheet);
		} else if (entity instanceof SpecialityDto) {
			return getSpecialitiesData(sheet);
		} else if (entity instanceof SectorRequestDto) {
			return getSectorsData(sheet);
		}
		return Collections.emptyList();
	}

	
	private List<PlanningDto> getPlanningData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		// Verify file header is what we expect

		if (cellNames.get("id prospect") == null || cellNames.get("Date") == null) {
			throw new IllegalArgumentException(String.format("Unexpected header"));
		}
		checkEmptyPlanningData(sheet);
		if (emptyPlanningData.size() != 0) {
			return new ArrayList<>(emptyPlanningData);
		} else {
			checkExistingPlanningData(sheet);
		}
		if (invalidPlanningProspects.size() != 0) {
			return new ArrayList<>(invalidPlanningProspects);
		} /*
			 * else { checkDuplicatePlanning(totalRows, cellNames, sheet, userId); }
			 * if(duplicatePlanningProspects.size() !=0) { return new
			 * ArrayList<>(duplicatePlanningProspects); }
			 */
		else {
			Set<PlanningDto> listPlanning = new HashSet<>();

			int prospetIdIndex = cellNames.get("id prospect");
			int planningDateIndex = cellNames.get("Date");
			for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
				PlanningDto planning = new PlanningDto();
				Row dataRow = sheet.getRow(i);
				Long prospectId = (long) dataRow.getCell(prospetIdIndex).getNumericCellValue();
				planning.setProspect(prospectId);
				Date date = (Date) formatter.parse(dataRow.getCell(planningDateIndex).getStringCellValue());
				planning.setDate(date);
				listPlanning.add(planning);
			}

			return new ArrayList<>(listPlanning);
		}
	}

	private Set<PlanningDto> checkEmptyPlanningData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			int prospetIdIndex = cellNames.get("id prospect");
			int planningDateIndex = cellNames.get("Date");
			if (dataRow.getCell(prospetIdIndex).getStringCellValue() == null
					|| dataRow.getCell(planningDateIndex).getStringCellValue() == null) {

				/*emptyPlanningData.add(new PlanningDto(null, null, null,
						(Date) formatter.parse(dataRow.getCell(planningDateIndex).getStringCellValue()),
						(long) dataRow.getCell(prospetIdIndex).getNumericCellValue()));*/

			}

		}
		return emptyPlanningData;
	}

	

	private Set<PlanningDto> checkExistingPlanningData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			int prospectIdIndex = cellNames.get("id prospect");
			int planningDateIndex = cellNames.get("Date");
			checkIfProspectExit((long) dataRow.getCell(prospectIdIndex).getNumericCellValue(),
					(Date) formatter.parse(dataRow.getCell(planningDateIndex).getStringCellValue()));
		}
		return invalidPlanningProspects;

	}

	
	private Set<PlanningDto> checkDuplicatePlanning(Sheet sheet, Long userId) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);

		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			int prospectIdIndex = cellNames.get("id prospect");
			int planningDateIndex = cellNames.get("Date");
			checkIfPlanningIsDupplicate((long) dataRow.getCell(prospectIdIndex).getNumericCellValue(),
					(Date) formatter.parse(dataRow.getCell(planningDateIndex).getStringCellValue()), userId);
		}
		return invalidPlanningProspects;

	}

	
	private void checkIfProspectExit(Long prospectId, Date date) {
		Prospect prospect = prospectRepository.findById(prospectId);
		if (prospect == null) {
			//invalidPlanningProspects.add(new PlanningDto(null, null, null, date, prospectId));
		}
	}

	
	

	private void checkIfPlanningIsDupplicate(Long prospectId, Date date, Long userId) {
		Planning planning = planningRepository.findByProspectDateAndUser(prospectId, date, userId);
		if (planning == null) {
			//duplicatePlanningProspects.add(new PlanningDto(null, null, null, date, prospectId));
		}
	}

	

	private List<UserDto> getUserData(Sheet sheet) {
		List<UserDto> listDelegates = new ArrayList<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			UserDto user = new UserDto();
			Row dataRow = sheet.getRow(i);

			int phoneIndex = cellNames.get(DELEGATE_PHONE_LABEL);

			Double phoneAsDouble = dataRow.getCell(phoneIndex).getNumericCellValue();
			user.setPhone(formatString(Integer.toString(phoneAsDouble.intValue())));
			
			user.setPassword("");
			listDelegates.add(user);

		}
		return listDelegates;
	}

	
	private List<UserDto> getDelegateData(Sheet sheet) {
		List<UserDto> listDelegates = new ArrayList<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			UserDto delegate = new UserDto();
			Row dataRow = sheet.getRow(i);

			int firstNameIndex = cellNames.get(DELEGATE_FIRST_NAME_LABEL);
			int lastNameIndex = cellNames.get(DELEGATE_LAST_NAME_LABEL);
			int phoneIndex = cellNames.get(DELEGATE_PHONE_LABEL);
			int hiringDateIndex = cellNames.get(DELEGATE_HIRING_DATE_LABEL);
			int birthdayDateIndex = cellNames.get(DELEGATE_BIRTHDAY_DATE_LABEL);
			int workTypeIndex = cellNames.get(DELEGATE_WORK_TYPE_LABEL);

			delegate.setFirstName(dataRow.getCell(firstNameIndex).getStringCellValue());
			delegate.setLastName(dataRow.getCell(lastNameIndex).getStringCellValue());
			Double phoneAsDouble = dataRow.getCell(phoneIndex).getNumericCellValue();
			delegate.setPhone(formatString(Integer.toString(phoneAsDouble.intValue())));
			

			

			
			delegate.setPassword("");
			delegate.setUsername(dataRow.getCell(firstNameIndex).getStringCellValue() + "."
					+ dataRow.getCell(lastNameIndex).getStringCellValue());
			delegate.setEmail(dataRow.getCell(firstNameIndex).getStringCellValue() + "."
					+ dataRow.getCell(lastNameIndex).getStringCellValue());
			
			listDelegates.add(delegate);

		}
		return listDelegates;
	}
	
	private List<ProspectDto> getProspectData(Sheet sheet) {
		List<ProspectDto> prospectDtos = new ArrayList<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			ProspectDto prospectDto = new ProspectDto();
			Row dataRow = sheet.getRow(i);
			if (dataRow != null) {
				int nameIndex = cellNames.get(PROSPECT_NAME_LABEL);
				int activityIndex = cellNames.get(PROSPECT_ACTIVITY_LABEL);
				int potentialIndex = cellNames.get(PROSPECT_POTENTIAL_LABEL);
				int addressIndex = cellNames.get(PROSPECT_ADDRESS_LABEL);
				int gsmIndex = cellNames.get(PROSPECT_GSM_LABEL);
				int phoneIndex = cellNames.get(PROSPECT_PHONE_LABEL);
				int emailIndex = cellNames.get(PROSPECT_EMAIL_LABEL);
				int specialityIndex = cellNames.get(PROSPECT_SPECIALITY_LABEL);
				int sectorIndex = cellNames.get(PROSPECT_SECTOR_LABEL);
				int localityIndex = cellNames.get(PROSPECT_LOCALITY_LABEL);
				int noteIndex = cellNames.get(PROSPECT_NOTE_LABEL);

				if (dataRow.getCell(nameIndex) != null) {
					// prospectDto.setName(dataRow.getCell(nameIndex).getStringCellValue());
				}
				putCellOfSpeciality(dataRow, specialityIndex, prospectDto);

				if (dataRow.getCell(activityIndex) != null) {
					prospectDto.setActivity(dataRow.getCell(activityIndex).getStringCellValue().toUpperCase());
				}

				putCellOfPtential(dataRow, potentialIndex, prospectDto);

				if (dataRow.getCell(addressIndex) != null) {
					prospectDto.setAddress(dataRow.getCell(addressIndex).getStringCellValue());
				}

				putCellOfLocality(dataRow, localityIndex, prospectDto);

				putCellOfSector(dataRow, sectorIndex, prospectDto);

				putCellOfGsm(dataRow, gsmIndex, prospectDto);

				putCellOfPhone(dataRow, phoneIndex, prospectDto);
				if (dataRow.getCell(emailIndex) != null) {
					prospectDto.setEmail(dataRow.getCell(emailIndex).getStringCellValue());
				}
				if (dataRow.getCell(noteIndex) != null) {
					prospectDto.setNote(dataRow.getCell(noteIndex).getStringCellValue());
				}
				prospectDtos.add(prospectDto);
			}

		}
		return prospectDtos;
	}

	private void putCellOfPhone(Row dataRow, int phoneIndex, ProspectDto prospectDto) {
		if (dataRow.getCell(phoneIndex) != null) {
			if (dataRow.getCell(phoneIndex).getCellType() == Cell.CELL_TYPE_NUMERIC) {
				Double phoneAsDouble = dataRow.getCell(phoneIndex).getNumericCellValue();
				prospectDto.setPhone(formatString(Integer.toString(phoneAsDouble.intValue())));
			} else {
				prospectDto.setPhone(dataRow.getCell(phoneIndex).getStringCellValue());
			}
		}
	}

	private void putCellOfGsm(Row dataRow, int gsmIndex, ProspectDto prospectDto) {
		if (dataRow.getCell(gsmIndex) != null) {
			if (dataRow.getCell(gsmIndex).getCellType() == Cell.CELL_TYPE_NUMERIC) {
				Double gsmAsDouble = dataRow.getCell(gsmIndex).getNumericCellValue();
				prospectDto.setGsm(formatString(Integer.toString(gsmAsDouble.intValue())));
			} else {
				prospectDto.setGsm(dataRow.getCell(gsmIndex).getStringCellValue());
			}

		}
	}

	private void putCellOfSector(Row dataRow, int sectorIndex, ProspectDto prospectDto) {
		if (dataRow.getCell(sectorIndex) != null) {
			SectorDto sectorDto = new SectorDto();
			sectorDto.setName(dataRow.getCell(sectorIndex).getStringCellValue());
			prospectDto.setSectorDto(sectorDto);

		}
	}

	private void putCellOfLocality(Row dataRow, int localityIndex, ProspectDto prospectDto) {
		if (dataRow.getCell(localityIndex) != null) {
			LocalityDto localityDto = new LocalityDto();
			localityDto.setName(dataRow.getCell(localityIndex).getStringCellValue());
			prospectDto.setLocalityDto(localityDto);
		}
	}

	private void putCellOfSpeciality(Row dataRow, int specialityIndex, ProspectDto prospectDto) {
		if (dataRow.getCell(specialityIndex) != null) {
			SpecialityDto specialityDto = new SpecialityDto();
			String specialityName = dataRow.getCell(specialityIndex).getStringCellValue();
			specialityName = specialityName.replaceAll("é", "e");
			specialityName = specialityName.replaceAll("è", "e");
			specialityDto.setName(specialityName);
			prospectDto.setSpecialityDto(specialityDto);

		}
	}

	private void putCellOfPtential(Row dataRow, int potentialIndex, ProspectDto prospectDto) {
		if (dataRow.getCell(potentialIndex) != null) {
			PotentialDto potentialDto = new PotentialDto();
			potentialDto.setName(dataRow.getCell(potentialIndex).getStringCellValue());
			prospectDto.setPotentialDto(potentialDto);

		}
	}

	private List<SpecialityDto> getSpecialitiesData(Sheet sheet) {
		Set<SpecialityDto> specialityDtos = new HashSet<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			int specialityIndex = cellNames.get(PROSPECT_SPECIALITY_LABEL);
			if (dataRow.getCell(specialityIndex) != null) {
				SpecialityDto specialityDto = new SpecialityDto();
				String specialityName = dataRow.getCell(specialityIndex).getStringCellValue().trim();
				specialityName = specialityName.replaceAll("é", "e");
				specialityName = specialityName.replaceAll("è", "e");
				specialityDto.setName(specialityName.toUpperCase());
				specialityDtos.add(specialityDto);
			}
		}
		SpecialityDto specialityDto = new SpecialityDto();
		specialityDto.setName("Pharmacie".toUpperCase());
		specialityDtos.add(specialityDto);
		return specialityDtos.stream().distinct().collect(Collectors.toList());
	}

	private List<SectorRequestDto> getSectorsData(Sheet sheet) {
		Set<SectorRequestDto> sectorRequestDtos = new HashSet<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			int sectorIndex = cellNames.get(SECTOR_NAME_LABEL);
			int delegateFirstNameIndex = cellNames.get(SECTOR_DELEGATE_FIRST_NAME_LABEL);
			int delegateLastNameIndex = cellNames.get(SECTOR_DELEGATE_LAST_NAME_LABEL);
			if (dataRow.getCell(sectorIndex) != null && dataRow.getCell(delegateFirstNameIndex) != null
					&& dataRow.getCell(delegateLastNameIndex) != null) {
				SectorRequestDto sectorRequestDto = new SectorRequestDto();
				sectorRequestDto.setName(dataRow.getCell(sectorIndex).getStringCellValue());
				sectorRequestDtos.add(sectorRequestDto);
			}
		}
		return sectorRequestDtos.stream().distinct().collect(Collectors.toList());
	}

	private List<LocalityRequestDto> getLocalitiesData(Sheet sheet) {
		Set<LocalityRequestDto> listLocalityDtos = new HashSet<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			int sectorIndex = cellNames.get(PROSPECT_SECTOR_LABEL);
			int localityIndex = cellNames.get(PROSPECT_LOCALITY_LABEL);

			String sectorName;
			String localityName;

			if (dataRow.getCell(sectorIndex) != null && dataRow.getCell(localityIndex) != null) {
				sectorName = dataRow.getCell(sectorIndex).getStringCellValue();
				localityName = dataRow.getCell(localityIndex).getStringCellValue();
				sectorName = sectorName.replaceAll("é", "e");
				sectorName = sectorName.replaceAll("è", "e");
				LocalityRequestDto localityDto = new LocalityRequestDto();
				localityDto.setName(localityName);
				localityDto.setSectorName(sectorName);
				listLocalityDtos.add(localityDto);
			}

		}

		return listLocalityDtos.stream().distinct().collect(Collectors.toList());
	}

	private String formatString(String str) {

		if (str.isEmpty()) {
			return "";
		}

		StringBuilder string = new StringBuilder();
		string.append(str.charAt(0));
		string.append(str.charAt(1));
		string.append(" ");
		string.append(str.charAt(2));
		string.append(str.charAt(3));
		string.append(str.charAt(4));
		string.append(" ");
		string.append(str.charAt(5));
		string.append(str.charAt(6));
		string.append(str.charAt(7));
		return string.toString();
	}

	public T getEntity() {
		return entity;
	}

	public void setEntity(T entity) {
		this.entity = entity;
	}

}
