package com.intellitech.birdnotes.controller;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.DelegateType;
import com.intellitech.birdnotes.model.dto.DelegateTypeDto;
import com.intellitech.birdnotes.service.DelegateTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/delegateType")
public class DelegateTypeController {
	private static final Logger LOG = LoggerFactory.getLogger(RangeController.class);

	@Autowired
	private DelegateTypeService delegateTypeService;
	@Autowired
	UserService userService;
	
	@RequestMapping(value = "/getAllDelegateType", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateType>> findAll() {
		try {
			if (userService.checkHasPermission("DELEGATE_TYPE_VIEW")) {
				List<DelegateType> toReturn = delegateTypeService.findAll();
				return new ResponseEntity<>(toReturn, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting delegate types", e);
			return new ResponseEntity<>(new ArrayList<DelegateType>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/saveDelegateType", method = RequestMethod.POST)
	public ResponseEntity<String> saveDelegateType(@RequestBody DelegateTypeDto delegatetypeDto) {
	    try {
	        if (userService.checkHasPermission("DELEGATE_TYPE_EDIT")) {
	        	DelegateType saveddelegateType = delegateTypeService.savedelegateType(delegatetypeDto);
	            if (saveddelegateType != null) {
	                return new ResponseEntity<>(saveddelegateType.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving delegate type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving delegate type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePreference(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("DELEGATE_TYPE_DELETE")) {
	        	delegateTypeService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); 
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting delegate type", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the delegate type with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}
	}
	