package com.intellitech.birdnotes.data.dto;

public class CommentClassification {

	

	private String comment;
	
	private Long visitProductId;
	
	private String score;
	
	public CommentClassification() {
        // Default constructor
    }
	
	public CommentClassification(String comment, Long visitProductId) {
		super();
		this.comment = comment;
		this.visitProductId = visitProductId;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Long getVisitProductId() {
		return visitProductId;
	}

	public void setVisitProductId(Long visitProductId) {
		this.visitProductId = visitProductId;
	}

	public String getScore() {
		return score;
	}

	public void setScore(String score) {
		this.score = score;
	}
	
}
