package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ValidationType;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationStepDto;
import com.intellitech.birdnotes.model.request.ValidationStepRequest;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.NotificationRuleService;
import com.intellitech.birdnotes.service.RoleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/validation-step")
public class ValidationStepController {

	private static final Logger LOG = LoggerFactory.getLogger(ValidationStepController.class);

	@Autowired
	private ValidationStepService validationStepService;

	@Autowired
	private CurrentUser currentUser;
	@Autowired
	private RoleService roleService;

	@Autowired
	private UserRepository userRepository;
	@Autowired
	private UserService userService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addValidationStep(@RequestBody ValidationStepRequest validationStepRequest) {
		try {
			if (userService.checkHasPermission("VALIDATION_ADD")) {
				validationStepService.add(validationStepRequest);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add validationStep", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (DataIntegrityViolationException e) {
			return new ResponseEntity<>(Exceptions.VALIDATION_INPROGRESS, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new validationStep", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/findAllValidationType", method = RequestMethod.GET)
	public ResponseEntity<List<ValidationType>> getValidationTypeXml() {
		try {

			List<ValidationType> ValidationTypeList = validationStepService.getValidationTypeXml();
			return new ResponseEntity<>(ValidationTypeList, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all validation type ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findValidationStepByType/{validationType}", method = RequestMethod.GET)
	public ResponseEntity<List<ValidationStepDto>> findValidationStepByType(
			@PathVariable("validationType") String validationType) {
		try {
			if (userService.checkHasPermission("VALIDATION_VIEW")) {
				List<ValidationStepDto> validationStepDtos = validationStepService
						.findValidationStepByType(validationType);
				return new ResponseEntity<>(validationStepDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findValidationStepByType", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "/findAllRoles", method = RequestMethod.GET)
	public ResponseEntity<List<RoleDto>> findAllRoles() {
		try {
			if (userService.checkHasPermission("VALIDATION_VIEW")) {
				List<RoleDto> rolesDtos = roleService.findAll();
				return new ResponseEntity<>(rolesDtos, HttpStatus.OK);
			}
			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all roles", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@RequestMapping(value = "getAllUsers", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllUsersWithRoles() {
		try {

			if (userService.checkHasPermission("VALIDATION_VIEW")) {
				List<UserDto> userDtos = userService.getSubUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
