package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.NextActionRule;
import com.intellitech.birdnotes.model.dto.NextActionRuleDto;
import com.intellitech.birdnotes.repository.NextActionRuleRepository;
import com.intellitech.birdnotes.service.NextActionRuleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("nextActionRuleService")
@Transactional
public class NextActionRuleServiceImpl implements NextActionRuleService {

	private NextActionRuleRepository nextActionRuleRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;

	
	

	@Autowired
	NextActionRuleServiceImpl(NextActionRuleRepository nextActionRuleRepository) {
		super();
		this.nextActionRuleRepository = nextActionRuleRepository;
	}



	@Override
	public List<NextActionRuleDto> findAll() throws BirdnotesException {
		List<NextActionRuleDto> back = new ArrayList<>();
		List<NextActionRule> allTypes= nextActionRuleRepository.findAll();
		for (NextActionRule nextActionRule : allTypes) {
			NextActionRuleDto nextActionRuleDto = new NextActionRuleDto();
			nextActionRuleDto.setId(nextActionRule.getId());
			nextActionRuleDto.setTotalRevenue(nextActionRule.getTotalRevenue());
			nextActionRuleDto.setPeriod(nextActionRule.getPeriod());
			nextActionRuleDto.setAction(nextActionRule.getAction());
			back.add(nextActionRuleDto);
		}
		return back;
	}

    @Override
    public NextActionRuleDto findNextActionRuleDto(Long id) throws BirdnotesException {
    	NextActionRule nextActionRule = nextActionRuleRepository.findOne(id);
        if (nextActionRule == null) {
            throw new BirdnotesException("Next action rule not found");
        }
        NextActionRuleDto dto = new NextActionRuleDto();
        dto.setId(nextActionRule.getId());
        dto.setTotalRevenue(nextActionRule.getTotalRevenue());
        dto.setPeriod(nextActionRule.getPeriod());
        dto.setAction(nextActionRule.getAction());
        return dto;
    }
	
	@Override
	public NextActionRule saveNextActionRule(NextActionRuleDto nextActionRuleDto) throws BirdnotesException {

	    NextActionRule nextActionRule = null;
	    if (nextActionRuleDto.getId() != null) {
	    	nextActionRule = nextActionRuleRepository.findOne(nextActionRuleDto.getId());
	    }
	    if (nextActionRule == null) {
	    	nextActionRule = new NextActionRule();
	    }

	    nextActionRule.setId(nextActionRuleDto.getId());
	    nextActionRule.setTotalRevenue(nextActionRuleDto.getTotalRevenue());
	    nextActionRule.setPeriod(nextActionRuleDto.getPeriod());
	    nextActionRule.setAction(nextActionRuleDto.getAction());
	    return nextActionRuleRepository.save(nextActionRule);
	}
	public void delete (long id)throws BirdnotesException {
		nextActionRuleRepository.delete(id);
}
}
