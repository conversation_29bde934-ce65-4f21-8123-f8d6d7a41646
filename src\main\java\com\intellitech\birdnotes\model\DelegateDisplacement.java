package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.DELEGATE_DISPLACEMENT, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class DelegateDisplacement implements Serializable {
    
    private static final long serialVersionUID = 1L;
    private Long id;
    private Delegate delegate;
    private Date date;
    private Double distance;
    
    @Id
    @SequenceGenerator(name = Sequences.DELEGATE_DISPLACEMENT_SEQUENCE, sequenceName = Sequences.DELEGATE_DISPLACEMENT_SEQUENCE, allocationSize = 1)
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.DELEGATE_DISPLACEMENT_SEQUENCE)
    @Column(name = Columns.ID)
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
    
    @ManyToOne(fetch = FetchType.EAGER, optional = false)
    @JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
    public Delegate getDelegate() {
        return delegate;
    }

    public void setDelegate(Delegate delegate) {
        this.delegate = delegate;
    }
    
    @Temporal(TemporalType.DATE)
    @Column(name = BirdnotesConstants.Columns.DATE)
    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }
    
    @Column(name = Columns.DISTANCE)
    public Double getDistance() {
        return distance;
    }

    public void setDistance(Double distance) {
        this.distance = distance;
    }
    
    public DelegateDisplacement() {
        super();
    }
}