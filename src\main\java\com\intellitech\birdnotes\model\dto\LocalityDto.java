package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class LocalityDto implements Serializable{

	private static final long serialVersionUID = 1L;
	private String name;
	private Long id;
	private Long sectorId;
	private String sectorName;
	
	public LocalityDto() {
		super();
	}

	public LocalityDto(String name, Long id, Long sectorId, String sectorName) {
		super();
		this.name = name;
		this.id = id;
		this.sectorId = sectorId;
		this.sectorName = sectorName;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getId() {
		return id;
	}

	public Long getSectorId() {
		return sectorId;
	}

	public void setSectorId(Long sectorId) {
		this.sectorId = sectorId;
	}

	public String getSectorName() {
		return sectorName;
	}

	public void setSectorName(String sectorName) {
		this.sectorName = sectorName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		LocalityDto other = (LocalityDto) obj;
		if (name == null) {
			if (other.name != null)
				return false;
		} else if (!name.equalsIgnoreCase(other.name))
			return false;
		return true;
	}

}
