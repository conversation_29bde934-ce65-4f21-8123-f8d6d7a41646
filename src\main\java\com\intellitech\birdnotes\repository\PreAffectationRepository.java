package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.PreAffectation;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.PreAffectationDto;

@Repository
public interface PreAffectationRepository extends JpaRepository<PreAffectation, Long> {

	@Query("SELECT p.preAffectation from ProspectsAffectation p join p.preAffectation WHERE p.delegate.id=:id AND p.prospect.status = 'VALID'")
	PreAffectation findByUser(@Param("id") Long id);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.PreAffectationDto(p.preAffectation.id,"
			+ "CONCAT(p.delegate.firstName,' ',p.delegate.lastName),"
			+ "p.delegate.id, "
			+ "p.prospect.sector.name,"
			+ "p.prospect.speciality.name,"
			+ "p.preAffectation.delegateNumber) "
			+ "from ProspectsAffectation p join p.preAffectation WHERE p.delegate.id=:id AND p.prospect.status = 'VALID' ")
	List<PreAffectationDto> findPreAffectationByUser(@Param("id") Long id);
	
	@Modifying
	void deleteById(Long id);
}
