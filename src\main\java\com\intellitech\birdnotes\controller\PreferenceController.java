package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.dao.DataIntegrityViolationException;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Preference;
import com.intellitech.birdnotes.model.dto.PreferenceDto;

import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.PreferenceService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/preferences")
public class PreferenceController {

	private static final Logger LOG = LoggerFactory.getLogger(PreferenceController.class);

	@Autowired
	private PreferenceService preferenceService;

	@Autowired
	UserService userService;
	
	
	@RequestMapping(value = "/findAllPreferences", method = RequestMethod.GET)
	public ResponseEntity<List<PreferenceDto>> getAllPreference() {
		try {
			if (userService.checkHasPermission("PREFERENCE_VIEW")) {
				List<PreferenceDto> result = preferenceService.findAll();
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all preferences ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	@RequestMapping(value = "/savePreference", method = RequestMethod.POST)
	public ResponseEntity<String> savePreference(@RequestBody PreferenceDto preferenceDto) {
	    try {
	        if (userService.checkHasPermission("PREFERENCE_EDIT")) {
	        	Preference savedPreference = preferenceService.savePreference(preferenceDto);
	            if (savedPreference != null) {
	                return new ResponseEntity<>(savedPreference.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving preference", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving preference", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePreference(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("PREFERENCE_DELETE")) {
	        	preferenceService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting preference", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the preference with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	}
