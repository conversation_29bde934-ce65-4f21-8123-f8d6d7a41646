package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.PurchaseOrderToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.RecoveryToDtoConvertor;
import com.intellitech.birdnotes.repository.ConfigurationRepository;

public class VisitHistoryDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Long delegateId;
	private String comment;
	private Integer orderQuantity;
	private Integer sampleQuantity;
	private ProspectDto prospectDto;
	private Long prospectId;
	private String productName;
	private Long productId;
	private Long doubleVisitId;
	private String CompanionName;
	private Long visitProductId;
	private String visitDate;
	private Float discount;
	private String synchronisationDate;
	private Long synchronisationDelay;
	private String generalComment;
	private Integer patientNumber;
	private String gadgetName;
	private Integer gadgetQuantity;
	private Integer rank;
	private String delegateName;
	private Integer saleQuantity;
	private Integer smily;
	private boolean urgent;
	private float price;
	private float buyingPrice;
	private Integer prescriptionQuantity;
	private ProspectDto wholesaler;
	private Integer freeOrder;
	private Integer labGratuity;
	private String attachmentName;
	private String attachmentBase64;
	private Long purchaseOrderId;
	private Long identifier;
	private Long vpCount;
	private boolean prospectPlanned;
	private PurchaseOrderDto purchaseOrder;
	private String orderPlacementMethod;

	private LocationDto location;
	private Double locationDistance;
	private String locationValidation;
	private Long visitId;
	private String commentRating;
	private FileNamePath nameFile;
	private FileNamePath generatedPurchaseOrder;
	private String prospectLocality ;
	private String prospectSector ;
	private String prospectPotential ;
	private String prospectName ;
	private String prospectActivity ;
	private String wholesalerName;
    private String delegatePhoto;





	
	
	private SimpleDateFormat df = new SimpleDateFormat("dd/MM/yyyy");
	private ConvertProspectToDto convertProspectToDto;

	
	public Long getVpCount() {
		return vpCount;
	}

	public void setVpCount(Long vpCount) {
		this.vpCount = vpCount;
	}

	public Long getPurchaseOrderId() {
		return purchaseOrderId;
	}

	public void setPurchaseOrderId(Long purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}
	public FileNamePath getNameFile() {
		return nameFile;
	}



	public void setNameFile(FileNamePath nameFile) {
		this.nameFile = nameFile;
	}



	public FileNamePath getGeneratedPurchaseOrder() {
		return generatedPurchaseOrder;
	}



	public Long getProspectId() {
		return prospectId;
	}

	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}

	public void setGeneratedPurchaseOrder(FileNamePath generatedPurchaseOrder) {
		this.generatedPurchaseOrder = generatedPurchaseOrder;
	}



	public LocationDto getLocation() {
		return location;
	}



	public void setLocation(LocationDto location) {
		this.location = location;
	}



	public Integer getGadgetQuantity() {
		return gadgetQuantity;
	}



	public void setGadgetQuantity(Integer gadgetQuantity) {
		this.gadgetQuantity = gadgetQuantity;
	}



	public String getProspectLocality() {
		return prospectLocality;
	}



	public void setProspectLocality(String prospectLocality) {
		this.prospectLocality = prospectLocality;
	}



	public String getProspectSector() {
		return prospectSector;
	}



	public void setProspectSector(String prospectSector) {
		this.prospectSector = prospectSector;
	}



	public String getProspectPotential() {
		return prospectPotential;
	}



	public void setProspectPotential(String prospectPotential) {
		this.prospectPotential = prospectPotential;
	}



	public String getProspectName() {
		return prospectName;
	}



	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}



	public String getProspectActivity() {
		return prospectActivity;
	}



	public void setProspectActivity(String prospectActivity) {
		this.prospectActivity = prospectActivity;
	}



	public String getWholesalerName() {
		return wholesalerName;
	}



	public void setWholesalerName(String wholesalerName) {
		this.wholesalerName = wholesalerName;
	}



	public Float getDiscount() {
		return discount;
	}

	public void setDiscount(Float discount) {
		this.discount = discount;
	}

	public VisitHistoryDto(VisitsProducts visitsProducts, String delegateName, 
			Long delegateId, Date visitDate, Date synchronisationDate, Long purchaseOrderId, Long visitId)
			throws BirdnotesException {
		
		this.visitDate = df.format(visitDate);
		this.identifier = visitsProducts.getVisit().getIdentifier();
		
		this.synchronisationDate = df.format(synchronisationDate);
		long diffInMillies = Math.abs(synchronisationDate.getTime() - visitDate.getTime());
		synchronisationDelay = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);	 
		this.comment = visitsProducts.getComment();
		this.orderQuantity = visitsProducts.getOrderQuantity();
		this.sampleQuantity = visitsProducts.getSampleQuantity();
		this.prospectId = visitsProducts.getVisit().getProspect().getId();
		this.productName = visitsProducts.getProduct().getName();
		this.productId = visitsProducts.getProduct().getId();
		if(visitsProducts.getVisit().getDoubleVisit() != null) {
			this.doubleVisitId = visitsProducts.getVisit().getDoubleVisit().getId();
			this.CompanionName = visitsProducts.getVisit().getDoubleVisit().getDelegate().getFirstName() +' '+ visitsProducts.getVisit().getDoubleVisit().getDelegate().getLastName();
		}
		this.visitProductId = visitsProducts.getId();
		this.generalComment = visitsProducts.getVisit().getGeneralNote();
		if(visitsProducts.getVisit().getGadget()!= null) {
			this.gadgetName = visitsProducts.getVisit().getGadget().getName();
	        this.gadgetQuantity = visitsProducts.getVisit().getGadgetQuantity();
        }
        this.patientNumber = visitsProducts.getVisit().getPatientNumber();
		this.rank = visitsProducts.getRank();
		this.delegateName = delegateName;
		this.smily = visitsProducts.getSmily();
		this.saleQuantity = visitsProducts.getSaleQuantity();
		this.delegateId = delegateId;
		this.purchaseOrderId = purchaseOrderId;
		
		this.freeOrder = visitsProducts.getFreeOrder();
		this.labGratuity = visitsProducts.getLabGratuity();
		this.price = visitsProducts.getProduct().getPrice();
		this.buyingPrice = visitsProducts.getProduct().getBuyingPrice();
		this.urgent = visitsProducts.isUrgent();
		this.prescriptionQuantity = visitsProducts.getPrescriptionQuantity();
		this.visitId = visitId; 
		this.commentRating = visitsProducts.getCommentRating();
		
		
		

	}


	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public VisitHistoryDto(VisitsProducts visitProduct, ConfigurationDto config, String uploadUrl, String userImagePath) {
	    this(visitProduct);

	    if (visitProduct.getVisit().getDelegate().getPhoto() != null) {
	        this.delegatePhoto = config.getBackendUrl()
	                            + uploadUrl
	                            + userImagePath
	                            + "/" + visitProduct.getVisit().getDelegate().getId()
	                            + "/" + visitProduct.getVisit().getDelegate().getPhoto();
	    } else {
	        this.delegatePhoto = null;
	    }
	}

	public VisitHistoryDto(VisitsProducts visitProduct) {
		this.visitDate = df.format(visitProduct.getVisit().getVisitDate());
		
		this.synchronisationDate = df.format(visitProduct.getVisit().getSynchronisationDate());
		long diffInMillies = Math.abs(visitProduct.getVisit().getSynchronisationDate().getTime() - visitProduct.getVisit().getVisitDate().getTime());
		synchronisationDelay = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);	 
		this.comment = visitProduct.getComment();
		this.orderQuantity = visitProduct.getOrderQuantity();
		this.sampleQuantity = visitProduct.getSampleQuantity();
		this.prospectId = visitProduct.getVisit().getProspect().getId();
		this.productName = visitProduct.getProduct().getName();
		this.productId = visitProduct.getProduct().getId();
		if(visitProduct.getVisit().getDoubleVisit() != null) {
			this.doubleVisitId = visitProduct.getVisit().getDoubleVisit().getId();
			this.CompanionName = visitProduct.getVisit().getDoubleVisit().getDelegate().getFirstName() +' '+ visitProduct.getVisit().getDoubleVisit().getDelegate().getLastName();
		}
		
		this.visitProductId = visitProduct.getId();
		this.generalComment = visitProduct.getVisit().getGeneralNote();
		if(visitProduct.getVisit().getGadget()!= null) {
			this.gadgetName = visitProduct.getVisit().getGadget().getName();
	        this.gadgetQuantity = visitProduct.getVisit().getGadgetQuantity();
        }
        this.patientNumber = visitProduct.getVisit().getPatientNumber();
		this.rank = visitProduct.getRank();
		this.delegateName = visitProduct.getVisit().getDelegate().getFirstName() + " " + visitProduct.getVisit().getDelegate().getLastName();
		
		
		this.smily = visitProduct.getSmily();
		this.saleQuantity = visitProduct.getSaleQuantity();
		this.delegateId = visitProduct.getVisit().getDelegate().getId();
		if(visitProduct.getPurchaseOrder() != null) {
			this.purchaseOrderId = visitProduct.getPurchaseOrder().getId();
		}
		this.freeOrder = visitProduct.getFreeOrder();
		this.labGratuity = visitProduct.getLabGratuity();
		this.price = visitProduct.getProduct().getPrice();
		if(visitProduct.getProduct().getBuyingPrice() != null) {
			this.buyingPrice = visitProduct.getProduct().getBuyingPrice();		
		}

		this.urgent = visitProduct.isUrgent();
		this.prescriptionQuantity = visitProduct.getPrescriptionQuantity();
		this.visitId = visitProduct.getVisit().getId();
		this.commentRating = visitProduct.getCommentRating();
		this.identifier = visitProduct.getVisit().getIdentifier();
			
	}


	public VisitHistoryDto(String comment, Integer orderQuantity, Integer sampleQuantity, Integer rank, Integer smily,
			Integer saleQuantity, boolean urgent,Integer prescriptionQuantity, Wholesaler wholesaler, Integer freeOrder, Integer labGratuity) {
		super();
		this.comment = comment;
		this.orderQuantity = orderQuantity;
		this.sampleQuantity = sampleQuantity;
		this.rank = rank;
		this.smily = smily;
		this.saleQuantity = saleQuantity;
		this.urgent = urgent;
		this.prescriptionQuantity=prescriptionQuantity;
		this.freeOrder = freeOrder;
		this.labGratuity = labGratuity;
	}

	public VisitHistoryDto() {
		super();

	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Integer getOrderQuantity() {
		return orderQuantity;
	}

	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}

	public Integer getSampleQuantity() {
		return sampleQuantity;
	}

	public void setSampleQuantity(Integer sampleQuantity) {
		this.sampleQuantity = sampleQuantity;
	}

	public ProspectDto getProspectDto() {
		return prospectDto;
	}

	public void setProspectDto(ProspectDto prospectDto) {
		this.prospectDto = prospectDto;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}
	
	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}



	public Long getDoubleVisitId() {
		return doubleVisitId;
	}

	public void setDoubleVisitId(Long doubleVisitId) {
		this.doubleVisitId = doubleVisitId;
	}
	
	

	public String getCompanionName() {
		return CompanionName;
	}

	public void setCompanionName(String companionName) {
		CompanionName = companionName;
	}

	public String getVisitDate() {
		return visitDate;
	}

	public void setVisitDate(String visitDate) {
		this.visitDate = visitDate;
	}

	public String getGeneralComment() {
		return generalComment;
	}

	public void setGeneralComment(String generalComment) {
		this.generalComment = generalComment;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

	public Integer getSmily() {
		return smily;
	}

	public void setSmily(Integer smily) {
		this.smily = smily;
	}

	public Integer getSaleQuantity() {
		return saleQuantity;
	}

	public void setSaleQuantity(Integer saleQuantity) {
		this.saleQuantity = saleQuantity;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}
	
	

	public float getBuyingPrice() {
		return buyingPrice;
	}

	public void setBuyingPrice(float buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	public Long getDelegateId() {
		return delegateId;
	}

	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}
	
	public ProspectDto getWholesaler() {
		return wholesaler;
	}

	public void setWholesaler(ProspectDto wholesaler) {
		this.wholesaler = wholesaler;
	}

	public boolean isUrgent() {
		return urgent;
	}

	public void setUrgent(boolean urgent) {
		this.urgent = urgent;
	}

	@Override
	public String toString() {
		return "VisitHistoryDto [comment=" + comment + ", orderQuantity=" + orderQuantity + ", sampleQuantity="
				+ sampleQuantity + ", prospectDto=" + prospectDto + ", productName=" + productName + ", visitDate="
				+ visitDate + ", generalComment=" + generalComment +", patientNumber ="+patientNumber+", gadgetName ="+ gadgetName +", gadgetQuantity ="+gadgetQuantity+ ", rank=" + rank + ", delegateName=" + delegateName
				+ ", saleQuantity=" + saleQuantity + ", smily=" + smily + ", df=" + df + ", convertProspectToDto="
				+ convertProspectToDto + "]";
	}

	public Integer getPrescriptionQuantity() {
		return prescriptionQuantity;
	}

	public void setPrescriptionQuantity(Integer prescriptionQuantity) {
		this.prescriptionQuantity = prescriptionQuantity;
	}
	
	public Integer getFreeOrder() {
		return freeOrder;
	}

	public void setFreeOrder(Integer freeOrder) {
		this.freeOrder = freeOrder;
	}


	public String getSynchronisationDate() {
		return synchronisationDate;
	}

	public void setSynchronisationDate(String synchronisationDate) {
		this.synchronisationDate = synchronisationDate;
	}

	public Long getSynchronisationDelay() {
		return synchronisationDelay;
	}

	public void setSynchronisationDelay(Long synchronisationDelay) {
		this.synchronisationDelay = synchronisationDelay;
	}

	public Integer getPatientNumber() {
		return patientNumber;
	}

	public void setPatientNumber(Integer patientNumber) {
		this.patientNumber = patientNumber;
	}

	public String getGadgetName() {
		return gadgetName;
	}

	public void setGadgetName(String gadgetName) {
		this.gadgetName = gadgetName;
	}



	public Integer getLabGratuity() {
		return labGratuity;
	}



	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}



	public Long getIdentifier() {
		return identifier;
	}



	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}



	public boolean isProspectPlanned() {
		return prospectPlanned;
	}



	public void setProspectPlanned(boolean prospectPlanned) {
		this.prospectPlanned = prospectPlanned;
	}



	public PurchaseOrderDto getPurchaseOrder() {
		return purchaseOrder;
	}



	public void setPurchaseOrder(PurchaseOrderDto purchaseOrder) {
		this.purchaseOrder = purchaseOrder;
	}



	public String getOrderPlacementMethod() {
		return orderPlacementMethod;
	}



	public void setOrderPlacementMethod(String orderPlacementMethod) {
		this.orderPlacementMethod = orderPlacementMethod;
	}



	public Double getLocationDistance() {
		return locationDistance;
	}



	public void setLocationDistance(Double locationDistance) {
		this.locationDistance = locationDistance;
	}




	public String getLocationValidation() {
		return locationValidation;
	}



	public void setLocationValidation(String locationValidation) {
		this.locationValidation = locationValidation;
	}



	public Long getVisitId() {
		return visitId;
	}



	public void setVisitId(Long visitId) {
		this.visitId = visitId;
	}



	public String getCommentRating() {
		return commentRating;
	}



	public void setCommentRating(String commentRating) {
		this.commentRating = commentRating;
	}

	public Long getVisitProductId() {
		return visitProductId;
	}

	public void setVisitProductId(Long visitProductId) {
		this.visitProductId = visitProductId;
	}

	public String getDelegatePhoto() {
		return delegatePhoto;
	}

	public void setDelegatePhoto(String delegatePhoto) {
		this.delegatePhoto = delegatePhoto;
	}



}