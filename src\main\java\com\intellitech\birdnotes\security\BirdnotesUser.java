package com.intellitech.birdnotes.security;

import java.util.Collection;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.User;

import com.intellitech.birdnotes.model.dto.UserDto;

public class BirdnotesUser extends User {

	private static final long serialVersionUID = 1L;
	private UserDto userDto;

	
	public BirdnotesUser(String username, String password, Collection<? extends GrantedAuthority> authorities) {
		super(username, password, authorities);
	}

	public UserDto getUserDto() {
		return userDto;
	}

	public void setUserDto(UserDto userDto) {
		this.userDto = userDto;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = super.hashCode();
		result = prime * result + ((userDto == null) ? 0 : userDto.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (!super.equals(obj))
			return false;
		if (getClass() != obj.getClass())
			return false;
		BirdnotesUser other = (BirdnotesUser) obj;
		if (userDto == null) {
			if (other.userDto != null)
				return false;
		} else if (!userDto.equals(other.userDto))
			return false;
		return true;
	}
	
	
	

}
