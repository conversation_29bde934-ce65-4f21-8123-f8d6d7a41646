package com.intellitech.birdnotes.model.request;

import java.util.List;

public class NotificationRuleRequest {
	private String selectedEventType;

	private List<NotificationRuleItem> notificationsRule;

	public NotificationRuleRequest() {
		super();
	}

	public String getSelectedEventType() {
		return selectedEventType;
	}

	public void setSelectedEventType(String selectedEventType) {
		this.selectedEventType = selectedEventType;
	}

	public List<NotificationRuleItem> getNotificationsRule() {
		return notificationsRule;
	}

	public void setNotificationsRule(List<NotificationRuleItem> notificationsRule) {
		this.notificationsRule = notificationsRule;
	}

}
