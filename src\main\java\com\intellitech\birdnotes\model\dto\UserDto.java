package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Value;

import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;

public class UserDto extends UserDtoRequest  implements Serializable {

	private static final long serialVersionUID = 1L;
 	
 	public String SupervisorsString;
 	private Set<Role> roles;
 	private String rolesString;
 	private String oneSignalUserId;
	private List<GoalDto> goals;
	private String lastLogin;
	private Integer line;
	private String message;
	private String FirstName;
	private String LastName;
	private Long delegateId;
	private Long prospectId;
	private Boolean active;
	
	@Value("${uploadUrl}")
	private String uploadUrl;
	
	@Value("${userImagePath}")
	private String userImagePath;
	
	public UserDto() {
		super();
		
	}
	
	public UserDto(User user) {
		
		this.id= user.getId();
		this.password=user.getPassword();
		this.phone=user.getPhone();
		this.email=user.getEmail();
		this.roles=user.getRoles();	
		this.username = user.getUsername();
		this.active = user.getActive();
		
	}
	


	public List<Integer> getRangeIds() {
		return rangeIds;
	}

	public void setRangeIds(List<Integer> rangeIds) {
		this.rangeIds = rangeIds;
	}

	public Long getDelegateId() {
		return delegateId;
	}

	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}

	public Long getProspectId() {
		return prospectId;
	}

	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}

	public UserDto(Long id, String email, String phone,	String username, String password, Set<Role> roles, 
			 String message, Integer line, String rolesString, String SupervisorsString) {
		super();
		this.id = id;
		this.SupervisorsString = SupervisorsString;
		this.rolesString = rolesString;
		this.phone = phone;
		this.email = email;
		this.username = username;
		this.password = password;
		this.roles = roles;
		this.line = line;
		this.message = message;
		
		
	}
	
	public String getSupervisorsString() {
		return SupervisorsString;
	}

	public void setSupervisorsString(String SupervisorsString) {
		this.SupervisorsString = SupervisorsString;
	}



	public String getRolesString() {
		return rolesString;
	}

	public void setRolesString(String rolesString) {
		this.rolesString = rolesString;
	}
	
	public Integer getLine() {
		return line;
	}

	public void setLine(Integer line) {
		this.line = line;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}


	public List<Integer> getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(List<Integer> roleIds) {
		this.roleIds = roleIds;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Set<Role> getRoles() {
		return roles;
	}

	public void setRoles(Set<Role> roles) {
		this.roles = roles;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getOneSignalUserId() {
		return oneSignalUserId;
	}

	public void setOneSignalUserId(String oneSignalUserId) {
		this.oneSignalUserId = oneSignalUserId;
	}
	

	public List<GoalDto> getGoals() {
		return goals;
	}

	public void setGoals(List<GoalDto> goals) {
		this.goals = goals;
	}

	public String getLastLogin() {
		return lastLogin;
	}

	public void setLastLogin(String lastLogin) {
		this.lastLogin = lastLogin;
	}

	public String getFirstName() {
		return FirstName;
	}

	public void setFirstName(String firstName) {
		FirstName = firstName;
	}

	public String getLastName() {
		return LastName;
	}

	public void setLastName(String lastName) {
		LastName = lastName;
	}
	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}
	

}