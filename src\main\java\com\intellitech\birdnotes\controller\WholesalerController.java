package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.WholesalerService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/wholesalers")
public class WholesalerController {

	private static final Logger LOG = LoggerFactory.getLogger(WholesalerController.class);

	@Autowired
	private WholesalerService wholesalerService;

	@Autowired
	private ProspectService prospectService;
	
	@Autowired
	UserService userService;
	
	@Autowired
	private SectorService sectorService;

	@RequestMapping(value = "getAllWholesalers", method = RequestMethod.GET)
	public ResponseEntity<List<WholesalerDto>> getAllWholesalers() {
		try {
			if (userService.checkHasPermission("GROSSISTE_VIEW")) {
				List<WholesalerDto> wholesalerDtos = prospectService.getWholesalersByStatus(false);
				return new ResponseEntity<>(wholesalerDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllWholsalers", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "saveWholesaler", method = RequestMethod.POST)
	public ResponseEntity<Long> saveWholesaler(@RequestBody WholesalerDto wholesalerRequest) {
		try {
			if (userService.checkHasPermission("GROSSISTE_ADD")) {
				Wholesaler wholesalerSaved = wholesalerService.saveWholesaler(wholesalerRequest);
				if (wholesalerSaved != null) {
					return new ResponseEntity<>(wholesalerSaved.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException fe) {
			LOG.error("An exception occurred when saving wholesaler", fe);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in saveWholesaler", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deleteWholesaler/{wholesalerId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteWholesaler(@PathVariable("wholesalerId") Long wholesalerId) {
		try {
			if (userService.checkHasPermission("GROSSISTE_DELETE")) {
				wholesalerService.deleteWholesaler(wholesalerId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>("", HttpStatus.CONFLICT);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in deleteWholesaler", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "updateWholesaler", method = RequestMethod.PUT)
	public ResponseEntity<String> updateWholesaler(@RequestBody WholesalerDto wholesalerDto) {
		try {
			if (userService.checkHasPermission("GROSSISTE_EDIT")) {
				wholesalerService.updateWholesaler(wholesalerDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException fe) {
			LOG.error("An exception occurred :Non-Authoritative Information when update wholesaler", fe);
			return new ResponseEntity<>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in updateWholesaler", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	
	@RequestMapping(value = "findallsectors", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> findallSectors() {
		try {
			if (userService.checkHasPermission("GROSSISTE_VIEW") || userService.checkHasPermission("GROSSISTE_EDIT")) {
				List<SectorDto> sectorDtos = sectorService.findAll();
				return new ResponseEntity<>(sectorDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
