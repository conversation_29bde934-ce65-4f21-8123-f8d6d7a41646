package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.LocalityRequestDto;

public interface LocalityService {
	List<LocalityDto> findAll() throws BirdnotesException;

	Locality add(LocalityRequestDto localityRequestDto) throws BirdnotesException;
	
	List<Locality> saveAll(List<LocalityRequestDto> localityRequestDtos) throws BirdnotesException;

	void delete(Long id) throws BirdnotesException;
	

	List<LocalityDto> findBySector(Long sectorId) throws BirdnotesException;

	LocalityDto findByName(String name) throws BirdnotesException;
	void saveAllLocalities(List<LocalityRequestDto> localityRequestDto) throws BirdnotesException;

	LocalityDto findLocalityDto(String localityName,String sectorName,List<LocalityDto> localityDtos);
	Locality saveLocality(LocalityDto localityDto) throws BirdnotesException;
}