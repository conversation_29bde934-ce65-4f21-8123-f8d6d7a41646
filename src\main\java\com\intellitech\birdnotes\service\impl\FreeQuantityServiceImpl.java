package com.intellitech.birdnotes.service.impl;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.FreeQuantityRule;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.CommissionItem;
import com.intellitech.birdnotes.model.FreeQuantityRule;

import com.intellitech.birdnotes.model.FreeQuantityRuleItem;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.ConvertPotentialToDto;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.dto.CommissionDto;
import com.intellitech.birdnotes.model.dto.CommissionItemDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleDto;

import com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.FreeQuantityRuleItemRepository;
import com.intellitech.birdnotes.repository.FreeQuantityRuleRepository;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.service.FreeQuantityRuleService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("freeQuantityRuleService")
@Transactional
public class FreeQuantityServiceImpl implements FreeQuantityRuleService {
	@Autowired
	private FreeQuantityRuleRepository freeQuantityRuleRepository;
	@Autowired
	private FreeQuantityRuleItemRepository freeQuantityRuleItemRepository;
	@Autowired
	private ProductRepository productRepository;
	@Autowired
	private PotentialRepository potentialRepository;
	@Autowired
	private ProspectTypeRepository prospectTypeRepository;
	@Autowired
	private ConvertPotentialToDto convertPotentialToDto;
	@Autowired
	private ProductToDtoConvertor productToDtoConvertor;
	
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	

	@Override
	public FreeQuantityRule saveFreeQuantityRule(FreeQuantityRuleDto freeQuantityRuleDto)throws BirdnotesException {
		FreeQuantityRule freeQuantityRule = null;
		if(freeQuantityRuleDto.getId() != null) {
			freeQuantityRule = freeQuantityRuleRepository.findOne(freeQuantityRuleDto.getId());
			
		}
		if(freeQuantityRule == null) {
			freeQuantityRule = new FreeQuantityRule();
		}
		freeQuantityRule.setName(freeQuantityRuleDto.getName());
		List<Product> products = productRepository.findAll(freeQuantityRuleDto.getProductIds());
		Set<Product> productSet = products.stream().collect(Collectors.toSet());
		if(products!=null) {
			freeQuantityRule.setProducts(productSet);
		}
		List<Potential> potentials = potentialRepository.findAll(freeQuantityRuleDto.getPotentialIds());
		if(potentials!=null) {
			freeQuantityRule.setPotentials(potentials);
		}
	
		List<ProspectType> prospectTypes = prospectTypeRepository.findAll(freeQuantityRuleDto.getProspectTypeIds());
		if(prospectTypes!=null) {
			freeQuantityRule.setProspectTypes(prospectTypes);
		}
	
		
		FreeQuantityRule savedFreeQuantityRule = freeQuantityRuleRepository.save(freeQuantityRule);
		
		for(FreeQuantityRuleItemDto freeQuantityRuleItemDto :  freeQuantityRuleDto.getFreeQuantityRuleItem()) {
			FreeQuantityRuleItem freeQuantityRuleItem = null;
			if(freeQuantityRuleItemDto.getId() != null) {
				freeQuantityRuleItem = freeQuantityRuleItemRepository.findOne(freeQuantityRuleItemDto.getId());
			}else if(freeQuantityRuleItemDto.getId() == null) {
				freeQuantityRuleItem = new FreeQuantityRuleItem();
			}
			
			freeQuantityRuleItem.setFreeQuantityRule(savedFreeQuantityRule);
			freeQuantityRuleItem.setFreeQuantity(freeQuantityRuleItemDto.getFreeQuantity());
			freeQuantityRuleItem.setLabGratuity(freeQuantityRuleItemDto.getLabGratuity());
			freeQuantityRuleItem.setOrderQuantity(freeQuantityRuleItemDto.getOrderQuantity());
			freeQuantityRuleItemRepository.save(freeQuantityRuleItem);
		}
		
		return savedFreeQuantityRule;
	}
	
	@Override
	public List<ProductDto> getProductWithoutRule(List<Long>prospectTypeList) throws BirdnotesException{
		//List<Product> productListwithrule = productRepository.findProductWithRule(prospectTypeList);
		List<Product> productList = new ArrayList<>();
		if(prospectTypeList.size() > 0) {
			productList = productRepository.findProductWithoutRuleWithType(prospectTypeList);
		}else {
			productList = productRepository.findProductWithoutRule();
		}
		
		List<ProductDto> productDtos = new ArrayList<>();
		for(Product product : productList) {
			productDtos.add(productToDtoConvertor.convert(product));
			
		}
		return productDtos;
	}
	
	@Override
	public List<FreeQuantityRuleItemDto> findFreeQuantityRules() throws BirdnotesException{
		
		 List<FreeQuantityRuleItemDto> freeQuantityRuleDtoList = freeQuantityRuleItemRepository.findFreeQuantityRulesAndItems();

		return freeQuantityRuleDtoList;
		
	}
	
	@Override
	public List<FreeQuantityRuleDto> getFreeQuantityRule() throws BirdnotesException, ParseException{
		List<FreeQuantityRule> freeQuantityRuleList;
		List<FreeQuantityRuleDto> freeQuantityRuleDtoList = new ArrayList<>();
		freeQuantityRuleList = freeQuantityRuleRepository.findAll();

		for(FreeQuantityRule freeQuantityRule : freeQuantityRuleList) {
			
			
			List<ProductDto> products = new ArrayList<>();
			List<Long> productIds = new ArrayList<>();
List<String> productNames = new ArrayList<>();
			
			List<Long> potentialIds = new ArrayList<>();
			List<PotentialDto> potentials = new ArrayList<>();
			List<String> potentialNames = new ArrayList<>();
			
			List<Long> prospectTypeIds = new ArrayList<>();
			List<ProspectTypeDto> prospectTypes = new ArrayList<>();
			List<String> typeNames = new ArrayList<>();
			
			FreeQuantityRuleDto freeQuantityRuleDto = new FreeQuantityRuleDto();
			freeQuantityRuleDto.setId(freeQuantityRule.getId());
			freeQuantityRuleDto.setName(freeQuantityRule.getName());
			for(Product product :freeQuantityRule.getProducts()) {
				products.add(productToDtoConvertor.convert(product));
				productIds.add(product.getId());
				productNames.add(product.getName());
				
			}
			for(Potential potential :freeQuantityRule.getPotentials()) {
				potentials.add(convertPotentialToDto.convert(potential));
				potentialIds.add(potential.getId());
				potentialNames.add(potential.getName());
			}
			
			for(ProspectType prospectType :freeQuantityRule.getProspectTypes()) {
				prospectTypes.add(new ProspectTypeDto(prospectType.getId(), prospectType.getName()));
				prospectTypeIds.add(prospectType.getId());
				typeNames.add(prospectType.getName());
			}
			freeQuantityRuleDto.setId(freeQuantityRule.getId());
			freeQuantityRuleDto.setProduct(products);
			freeQuantityRuleDto.setProductIds(productIds);
			freeQuantityRuleDto.setPotential(potentials);
			freeQuantityRuleDto.setPotentialIds(potentialIds);
			freeQuantityRuleDto.setProspectTypes(prospectTypes);
			freeQuantityRuleDto.setProspectTypeIds(prospectTypeIds);
			freeQuantityRuleDto.setPotentialName(potentialNames);
			freeQuantityRuleDto.setProductName(productNames);
			freeQuantityRuleDto.setProspectTypeName(typeNames);
			freeQuantityRuleDtoList.add(freeQuantityRuleDto);

		}
		

		return freeQuantityRuleDtoList;
		
	}
	
	@Override
	public List<FreeQuantityRuleItemDto> getFreeQuantityRuleItem(
			FreeQuantityRuleDto freeQuantityRuleRequestDto) {
		
		List<FreeQuantityRuleItemDto> freeQuantityRuleItemDtoList = new ArrayList<>();
		
		FreeQuantityRule freeQuantityRule = freeQuantityRuleRepository.findOne(freeQuantityRuleRequestDto.getId());
		
		Set<FreeQuantityRuleItem> freeQuantityRuleItems = freeQuantityRuleItemRepository.findByFreeQuantityRule(freeQuantityRule);
		
		for(FreeQuantityRuleItem freeQuantityRuleItem : freeQuantityRuleItems) {
			FreeQuantityRuleItemDto freeQuantityRuleItemDto = new FreeQuantityRuleItemDto();
			freeQuantityRuleItemDto.setId(freeQuantityRuleItem.getId());
			freeQuantityRuleItemDto.setFreeQuantity(freeQuantityRuleItem.getFreeQuantity());
			freeQuantityRuleItemDto.setFreeQuantityRuleId(freeQuantityRule.getId());
			freeQuantityRuleItemDto.setOrderQuantity(freeQuantityRuleItem.getOrderQuantity());
			freeQuantityRuleItemDto.setLabGratuity(freeQuantityRuleItem.getLabGratuity());
			freeQuantityRuleItemDtoList.add(freeQuantityRuleItemDto);
		}
		return freeQuantityRuleItemDtoList;
	}
	

	
	
	@Override
	public void delete (long id)throws BirdnotesException {

			FreeQuantityRule freeQuantityRule = freeQuantityRuleRepository.findOne(id);
			
			for (FreeQuantityRuleItem freeQuantityRuleItem : freeQuantityRuleItemRepository.findByFreeQuantityRule(freeQuantityRule) )
			{
				freeQuantityRuleItemRepository.delete(freeQuantityRuleItem);
			}
			
			freeQuantityRuleRepository.delete(id);

		}

	

	@Override
	public void deleteItem (long id) throws BirdnotesException {

			FreeQuantityRuleItem freeQuantityRuleItem = freeQuantityRuleItemRepository.findOne(id);
			
				freeQuantityRuleItemRepository.delete(freeQuantityRuleItem);
			


	

}}
