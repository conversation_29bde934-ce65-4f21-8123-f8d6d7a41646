package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;


public class PurchaseOrderDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long identifier;
	
	private Long visitId;
	
	private Long wholesalerId;	
	
	private String attachmentBase64;
	
	private String attachmentName;

	private String oldAttachmentBase64;
	
	private String oldAttachmentName;
	
	private Long purchaseOrderTemplateId;
	
	private String placementMethod;
	
	private  Boolean generatePo ;
	
	private  Boolean generateDo ;
	
	private List<RecoveryDto> recoveries;
	
	private String emailType;
	
	private Boolean mailSent;
	
	private ProspectDto prospect;
	
	private ProspectDto wholesaler;
	
	private ProspectDto oldWholesaler;
	
	private DelegateDto delegate;
	
	private String status;


	public PurchaseOrderDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}


	
	public Long getWholesalerId() {
		return wholesalerId;
	}

	public void setWholesalerId(Long wholesalerId) {
		this.wholesalerId = wholesalerId;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public Long getVisitId() {
		return visitId;
	}

	public void setVisitId(Long visitId) {
		this.visitId = visitId;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public String getPlacementMethod() {
		return placementMethod;
	}

	public void setPlacementMethod(String placementMethod) {
		this.placementMethod = placementMethod;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public List<RecoveryDto> getRecoveries() {
		return recoveries;
	}

	public void setRecoveries(List<RecoveryDto> recoveries) {
		this.recoveries = recoveries;
	}

	public Long getPurchaseOrderTemplateId() {
		return purchaseOrderTemplateId;
	}

	public void setPurchaseOrderTemplateId(Long purchaseOrderTemplateId) {
		this.purchaseOrderTemplateId = purchaseOrderTemplateId;
	}


	public String getEmailType() {
		return emailType;
	}

	public void setEmailType(String emailType) {
		this.emailType = emailType;
	}

	public Boolean getMailSent() {
		return mailSent;
	}

	public void setMailSent(Boolean mailSent) {
		this.mailSent = mailSent;
	}

	public ProspectDto getProspect() {
		return prospect;
	}

	public void setProspect(ProspectDto prospect) {
		this.prospect = prospect;
	}




	public ProspectDto getWholesaler() {
		return wholesaler;
	}

	public void setWholesaler(ProspectDto wholesaler) {
		this.wholesaler = wholesaler;
	}

	public ProspectDto getOldWholesaler() {
		return oldWholesaler;
	}

	public void setOldWholesaler(ProspectDto oldWholesaler) {
		this.oldWholesaler = oldWholesaler;
	}

	public DelegateDto getDelegate() {
		return delegate;
	}


	public void setDelegate(DelegateDto delegate) {
		this.delegate = delegate;
	}




	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getOldAttachmentBase64() {
		return oldAttachmentBase64;
	}

	public void setOldAttachmentBase64(String oldAttachmentBase64) {
		this.oldAttachmentBase64 = oldAttachmentBase64;
	}

	public String getOldAttachmentName() {
		return oldAttachmentName;
	}

	public void setOldAttachmentName(String oldAttachmentName) {
		this.oldAttachmentName = oldAttachmentName;
	}
	
	

	public Boolean getGeneratePo() {
		return generatePo;
	}

	public void setGeneratePo(Boolean generatePo) {
		this.generatePo = generatePo;
	}

	public Boolean getGenerateDo() {
		return generateDo;
	}

	public void setGenerateDo(Boolean generateDo) {
		this.generateDo = generateDo;
	}

	public PurchaseOrderDto(PurchaseOrder purchaseOrder) {
		super();
		this.id = purchaseOrder.getId();
		this.identifier = purchaseOrder.getIdentifier();
		this.visitId = purchaseOrder.getVisit().getId();
		this.wholesalerId = purchaseOrder.getWholesaler().getId();
		this.attachmentBase64 = purchaseOrder.getAttachmentBase64();
		this.attachmentName = purchaseOrder.getAttachmentName();
		this.oldAttachmentBase64 = purchaseOrder.getAttachmentBase64();;
		this.oldAttachmentName = purchaseOrder.getAttachmentName();
		if(purchaseOrder.getPurchaseOrderTemplate()!=null) {
			this.purchaseOrderTemplateId = purchaseOrder.getPurchaseOrderTemplate().getId();
		}
		this.placementMethod = purchaseOrder.getPlacementMethod();
		
		
		this.status = purchaseOrder.getStatus().toString();
		
	}


	

}
