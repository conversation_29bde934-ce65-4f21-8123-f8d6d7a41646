package com.intellitech.birdnotes.data.dto;


public class PurchaseOrderItemDto {

	private String productName;
	private Integer freeOrder;
	private Integer orderQuantity;
	private Float price;
	private Float buyingPrice;
	private Float totalExludingVat;
	private Float totalIncludingVat;
	private Integer labGratuity;
	private Float vat;
	
	
	
	
	public PurchaseOrderItemDto(String productName, Integer freeOrder,Integer labGratuity, Integer orderQuantity, Float price, Float vat, Float buyingPrice) {
		this.productName = productName;
		if(freeOrder == null) {
			this.freeOrder = 0;
		}else {
			this.freeOrder = freeOrder;
		}
		if(labGratuity == null) {
			this.labGratuity = 0;
		}else {
			this.labGratuity = labGratuity;
		}
		
		this.orderQuantity = orderQuantity;
		
		//this.price = buyingPrice;
		//price <- buyingPrice to have HT prices
		this.price = buyingPrice;
		this.buyingPrice = buyingPrice;
		if(buyingPrice != 0) {
			this.totalExludingVat = buyingPrice * orderQuantity;
		}else {
			this.totalExludingVat = price * orderQuantity;
		}
		
		if(vat == null) {
			this.vat = (float) 0;
			this.totalIncludingVat = this.totalExludingVat;
		}else {
			this.vat = vat;
			this.totalIncludingVat = (totalExludingVat * vat) + totalExludingVat;
		}
		
	}
	public String getProductName() {
		return productName;
	}
	public void setProductName(String productName) {
		this.productName = productName;
	}
	public Integer getFreeOrder() {
		return freeOrder;
	}
	public void setFreeOrder(Integer freeOrder) {
		this.freeOrder = freeOrder;
	}
	public Integer getOrderQuantity() {
		return orderQuantity;
	}
	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}
	public Float getPrice() {
		return price;
	}
	public void setPrice(Float price) {
		this.price = price;
	}
	
	public Float getTotalExludingVat() {
		return totalExludingVat;
	}
	public void setTotalExludingVat(Float totalExludingVat) {
		this.totalExludingVat = totalExludingVat;
	}
	public Float getTotalIncludingVat() {
		return totalIncludingVat;
	}
	public void setTotalIncludingVat(Float totalIncludingVat) {
		this.totalIncludingVat = totalIncludingVat;
	}
	public Integer getLabGratuity() {
		return labGratuity;
	}
	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}
	public Float getVat() {
		return vat;
	}
	public void setVat(Float vat) {
		this.vat = vat;
	}
	
	public Float getBuyingPrice() {
		return buyingPrice;
	}
	public void setBuyingPrice(Float buyingPrice) {
		this.buyingPrice = buyingPrice;
	}

	
}
