package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class PatientDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	private String firstName;
	private String lastName;
	private PotentialDto potentialDto;
	private String address;
	private String gsm;
	private String phone;
	private String email;
	private String note;
	private String socialMedia;
	private SectorDto sectorDto;
	private LocalityDto localityDto;
	private Long idprospect;
	private Long doctorId;

	public PatientDto() {
		super();

	}

	public PatientDto(Long id, String firstName, String lastName, PotentialDto potentialDto, String address, String gsm,
			String phone, String email, String note, SectorDto sectorDto, LocalityDto localityDto, Long idprospect) {
		super();
		this.id = id;
		this.firstName = firstName;
		this.lastName = lastName;
		this.potentialDto = potentialDto;
		this.address = address;
		this.gsm = gsm;
		this.phone = phone;
		this.email = email;
		this.note = note;
		this.sectorDto = sectorDto;
		this.localityDto = localityDto;
		this.idprospect = idprospect;
	}

	public Long getIdprospect() {
		return idprospect;
	}

	public void setIdprospect(Long idprospect) {
		this.idprospect = idprospect;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public PotentialDto getPotentialDto() {
		return potentialDto;
	}

	public void setPotentialDto(PotentialDto potentialDto) {
		this.potentialDto = potentialDto;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getGsm() {
		return gsm;
	}

	public void setGsm(String gsm) {
		this.gsm = gsm;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getNote() {
		return note;
	}
	
	public String getSocialMedia() {
		return socialMedia;
	}



	public void setNote(String note) {
		this.note = note;
	}

	public SectorDto getSectorDto() {
		return sectorDto;
	}

	public void setSectorDto(SectorDto sectorDto) {
		this.sectorDto = sectorDto;
	}

	public LocalityDto getLocalityDto() {
		return localityDto;
	}

	public void setLocalityDto(LocalityDto localityDto) {
		this.localityDto = localityDto;
	}

	public Long getDoctorId() {
		return doctorId;
	}

	public void setDoctorId(Long doctorId) {
		this.doctorId = doctorId;
	}

}
