package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class ExportProspects {
	private	List<String> selectedActivities;
	private	List<Long> selectedUsers;
	private	List<Long> selectedSpecialities;
	private	List<Long> selectedPotentials;
	private	List<Long> selectedLocalities;
	private	List<Long> selectedSectors;
	private	List<String> dataToExport;
	public ExportProspects() {
		super();
		// TODO Auto-generated constructor stub
	}
	public List<String> getSelectedActivities() {
		return selectedActivities;
	}
	public void setSelectedActivities(List<String> selectedActivities) {
		this.selectedActivities = selectedActivities;
	}
	public List<Long> getSelectedUsers() {
		return selectedUsers;
	}
	public void setSelectedUsers(List<Long> selectedUsers) {
		this.selectedUsers = selectedUsers;
	}
	public List<Long> getSelectedSpecialities() {
		return selectedSpecialities;
	}
	public void setSelectedSpecialities(List<Long> selectedSpecialities) {
		this.selectedSpecialities = selectedSpecialities;
	}
	public List<Long> getSelectedPotentials() {
		return selectedPotentials;
	}
	public void setSelectedPotentials(List<Long> selectedPotentials) {
		this.selectedPotentials = selectedPotentials;
	}
	public List<Long> getSelectedLocalities() {
		return selectedLocalities;
	}
	public void setSelectedLocalities(List<Long> selectedLocalities) {
		this.selectedLocalities = selectedLocalities;
	}
	public List<Long> getSelectedSectors() {
		return selectedSectors;
	}
	public void setSelectedSectors(List<Long> selectedSectors) {
		this.selectedSectors = selectedSectors;
	}
	public List<String> getDataToExport() {
		return dataToExport;
	}
	public void setDataToExport(List<String> dataToExport) {
		this.dataToExport = dataToExport;
	}
	
	
	
}
