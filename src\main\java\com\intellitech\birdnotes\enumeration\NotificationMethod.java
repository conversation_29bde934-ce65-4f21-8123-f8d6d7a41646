package com.intellitech.birdnotes.enumeration;

import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum NotificationMethod {
	
	PUSH(BirdnotesConstants.NotificationMethod.PUSH), EMAIL(BirdnotesConstants.NotificationMethod.EMAIL),
	APP_NOTIF(BirdnotesConstants.NotificationMethod.APP_NOTIF),SMS(BirdnotesConstants.NotificationMethod.SMS);

	private String name;

	

	private NotificationMethod(String name) {
		this.name = name;
	}



	public String getName() {
		return name;
	}



	public void setName(String name) {
		this.name = name;
	}



	@Override
	public String toString() {
		if (BirdnotesConstants.NotificationMethod.PUSH.equals(name)) {
			return BirdnotesConstants.NotificationMethod.PUSH;
		}
		if (BirdnotesConstants.NotificationMethod.EMAIL.equals(name)) {
			return BirdnotesConstants.NotificationMethod.EMAIL;
		}
		if (BirdnotesConstants.NotificationMethod.APP_NOTIF.equals(name)) {
			return BirdnotesConstants.NotificationMethod.APP_NOTIF;
		}

		return BirdnotesConstants.NotificationMethod.SMS;

	}
}
