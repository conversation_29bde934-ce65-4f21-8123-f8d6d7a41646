package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.dto.StatisticActivity;

public interface RecoveryRepository extends JpaRepository<Recovery, Long> {

	
	@Query("SELECT r FROM Recovery r WHERE r.identifier = ?1 And r.purchaseOrder.visit.delegate.id = ?2)")
	Recovery findByIdentifier(Long identifier , Long userId);
	
	@Modifying
	@Query("Delete FROM Recovery r WHERE r.identifier = ?1 And r.purchaseOrder.id IN (Select p.id from PurchaseOrder p where p.visit.delegate.id = ?2)")
	void deleteByIdentifier(Long identifier, Long userId);
	
	@Query("SELECT DISTINCT r FROM Recovery r WHERE r.purchaseOrder.id = ?1)")
	List<Recovery> findByPurchaseOrder(Long purchaseOrderId);
	
	@Query("SELECT r from Recovery r WHERE r.purchaseOrder.visit.delegate.id  = :userId ")
	List<Recovery> findByUser(@Param("userId") Long userId);
}
