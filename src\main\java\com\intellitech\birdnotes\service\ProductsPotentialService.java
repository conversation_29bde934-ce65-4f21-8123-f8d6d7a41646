package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.MinimizedPotentialDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDtoRequest;


public interface ProductsPotentialService { 
	
	  public void saveProductsPotential(ProductPotentielDto productsPotentialDto)
	    		throws BirdnotesException;  
	  public  MinimizedPotentialDto findpotentialbyIds(Long productId,Long prospectId) throws BirdnotesException;
	
      public void updateProductPotential(ProductPotentielDtoRequest productsPotentialDto) throws BirdnotesException;
      
      List<ProductPotentielDto> getAllPotentialProducts() throws BirdnotesException;
}
