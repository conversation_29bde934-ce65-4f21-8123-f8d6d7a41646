package com.intellitech.birdnotes.controller;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.MinimizedPotentialDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDtoRequest;
import com.intellitech.birdnotes.service.ProductsPotentialService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/productsPotential")
public class ProductsPotentialController {
	private static final Logger LOG = LoggerFactory.getLogger(ProductsPotentialController.class);
	
	@Autowired
	private ProductsPotentialService productsPotentialService ;
	
	@RequestMapping(value = "saveProductsPotential", method = RequestMethod.POST)
	public ResponseEntity<String> saveProduct(@RequestBody ProductPotentielDto productPotentielDto) {
		try {
		    productsPotentialService.saveProductsPotential(productPotentielDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add product potential", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new prospect", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}}
	
	@RequestMapping(value = "updateProductPotential", method = RequestMethod.POST)
	public ResponseEntity<String> updateProductPotential(@RequestBody ProductPotentielDtoRequest productsPotentialDto) {
		try {
			productsPotentialService.updateProductPotential(productsPotentialDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("Error Non-Authoritative Information in update ProductPotential", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("Error in updateProductPotential", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);}
		}
	
	@RequestMapping(value = "/findPotentialByIds/{productId}/{prospectId}", method = RequestMethod.GET)
	public ResponseEntity<MinimizedPotentialDto> findpotentialbyIds(@PathVariable("productId") Long productId,@PathVariable("prospectId") Long prospectId) {
		try {
			MinimizedPotentialDto potentialDto=productsPotentialService.findpotentialbyIds(productId,prospectId);
			return new ResponseEntity<>(potentialDto, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting potential by Ids", e);
			return new ResponseEntity<>(new MinimizedPotentialDto(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	

	


	
}
