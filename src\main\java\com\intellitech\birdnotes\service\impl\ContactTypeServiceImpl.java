package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ContactType;
import com.intellitech.birdnotes.model.dto.ContactTypeDto;
import com.intellitech.birdnotes.repository.ContactTypeRepository;
import com.intellitech.birdnotes.service.ContactTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("contactTypeService")
@Transactional
public class ContactTypeServiceImpl implements ContactTypeService {

	private ContactTypeRepository contactTypeRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;

	
	

	@Autowired
	ContactTypeServiceImpl(ContactTypeRepository contactTypeRepository) {
		super();
		this.contactTypeRepository = contactTypeRepository;
	}



	@Override
	public List<ContactTypeDto> findAll() throws BirdnotesException {
		List<ContactTypeDto> back = new ArrayList<>();
		List<ContactType> allTypes= contactTypeRepository.findAll();
		for (ContactType contactType : allTypes) {
			ContactTypeDto contactTypeDto = new ContactTypeDto();
			contactTypeDto.setId(contactType.getId());
			contactTypeDto.setName(contactType.getName());
			back.add(contactTypeDto);
		}
		return back;
	}

	@Override
	public ContactTypeDto findContactTypeDto(String contactTypeName, List<ContactTypeDto>contactTypeDtos)   {
		for (ContactTypeDto contactTypeDto : contactTypeDtos) {
			if (contactTypeDto.getName().equalsIgnoreCase(contactTypeName)) {
				return contactTypeDto;
			}
		}
		return null;
	}
	
	@Override
	public void saveAllContactTypes(List<ContactTypeDto> contactTypeDtos) throws BirdnotesException {
		for(ContactTypeDto contactTypeDto:contactTypeDtos) {
			ContactType contactType = new ContactType();
		if (contactTypeDto.getName() == null || contactTypeDto.getName().equals("")) {
			throw new BirdnotesException("contact type is empty");
		}
		
		contactType.setName(contactTypeDto.getName());
		contactTypeRepository.save(contactType);
		}
	}
	@Override
	public ContactType saveContactType(ContactTypeDto contactTypeDto) throws BirdnotesException {
	    if (contactTypeDto == null || contactTypeDto.getId() == null) {
	        throw new BirdnotesException(Exceptions.CONTACT_TYPE_DTO_IS_NULL);
	    }

	    if (contactTypeDto.getName() == null || contactTypeDto.getName().isEmpty()) {
	        throw new BirdnotesException(Exceptions.CONTACT_TYPE_NAME_IS_EMPTY);
	    }

	    ContactType existingContactType = contactTypeRepository.findByNameAndAnotherId(contactTypeDto.getName(), contactTypeDto.getId());
	    if (existingContactType != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    ContactType contactType = null;
	    if (contactTypeDto.getId() != null) {
	    	contactType = contactTypeRepository.findOne(contactTypeDto.getId());
	    }
	    if (contactType == null) {
	    	contactType = new ContactType();
	    }

	    contactType.setId(contactTypeDto.getId());
	    contactType.setName(contactTypeDto.getName());
	    return contactTypeRepository.save(contactType);
	}

	
	public void delete (long id)throws BirdnotesException {
		contactTypeRepository.deleteById(id);
	}
	
	@Override
	public Set<ContactType> findContactTypesByIds(List<Long> contactTypeIds) {
	    Set<ContactType> contactTypes = new HashSet<>(contactTypeRepository.findAll(contactTypeIds));
	    return contactTypes;
	}

}
