package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;


@Component("convertActivityTypeToDto")
public class ConvertActivityTypeToDto {
	private static final Logger LOG = LoggerFactory.getLogger(ConvertActivityTypeToDto.class);

	public ActivityTypeDto convert(ActivityType activityTypes) throws BirdnotesException {

		if (activityTypes == null) {
			LOG.error("activityTypes is null");
			throw new BirdnotesException("activityTypes is null");
		}
		ActivityTypeDto activityTypeDto = new ActivityTypeDto();
		activityTypeDto.setName(activityTypes.getName());
		activityTypeDto.setId(activityTypes.getId());
		return activityTypeDto;
	}

}
