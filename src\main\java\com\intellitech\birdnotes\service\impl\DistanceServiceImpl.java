package com.intellitech.birdnotes.service.impl;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;


import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.convertor.ConvertActivityTypeToDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.repository.ActivityTypeRepository;
import com.intellitech.birdnotes.service.ActivityTypeService;
import com.intellitech.birdnotes.service.DistanceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service("distanceService")
@Transactional
public class DistanceServiceImpl implements DistanceService {
	@Autowired
	private RestTemplate restTemplate;
	
	@Value("${mapKey}")
	private String mapKey;

	private static final Logger LOG = LoggerFactory.getLogger(DistanceServiceImpl.class);
	
	@Override
	public ResponseEntity<DistanceResponse>  distance(Double latOrigin, Double lngOrigin, Double latDest, Double lngDest) throws BirdnotesException {
		URI uri = null;
		final String baseUrl;
		try {
			baseUrl = "https://maps.googleapis.com/maps/api/distancematrix/json?origins="+latOrigin+"%2C"+lngOrigin+"&destinations="+latDest+"%2C"+ lngDest+"&key="+mapKey;
			uri = new URI(baseUrl);
		} catch (URISyntaxException e) {
			LOG.error("Error api google call ", e);
		}
		 
		ResponseEntity<DistanceResponse> result = restTemplate.getForEntity(uri, DistanceResponse.class);
		
		return result;
	}
	
}
