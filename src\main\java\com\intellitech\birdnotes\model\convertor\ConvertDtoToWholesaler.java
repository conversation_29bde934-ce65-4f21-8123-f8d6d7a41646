package com.intellitech.birdnotes.model.convertor;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

@Component("convertDtoToWholesaler")
public class ConvertDtoToWholesaler {

		private static final Logger LOG = LoggerFactory.getLogger(ConvertDtoToWholesaler.class);

		public Wholesaler convert(WholesalerDto wholesalerDto) throws BirdnotesException {

			if (wholesalerDto == null) {
				LOG.error("wholesaler is null");
				throw new BirdnotesException("wholesaler is null");
			}

			Wholesaler wholesaler = new Wholesaler();
			wholesaler.setId(wholesalerDto.getId());
			wholesaler.setName(wholesalerDto.getName());
			wholesaler.setResponsible(wholesalerDto.getResponsible());
			wholesaler.setAddress(wholesalerDto.getAddress());
			wholesaler.setPhone(wholesalerDto.getPhone());
			wholesaler.setDescription(wholesalerDto.getDescription());
			wholesaler.setEmail(wholesalerDto.getEmail());
			wholesaler.setDiscount(wholesalerDto.getDiscount());
			//wholesaler.setSector(wholesalerDto.getSector().getId());
			//wholesalerDto.setShopEmail(wholesaler.getShopEmail());
			return wholesaler;

		}
}
