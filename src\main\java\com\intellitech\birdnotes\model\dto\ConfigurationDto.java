package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class ConfigurationDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer id;
	private String name;
	private String logo;
	private Boolean syncro;
	private Boolean autoSync;
	private Boolean autoExpense;
	private Boolean lockAfterSync;
	private Integer openReportPeriod;
	private Integer openExpensePeriod;
	private Boolean multiWholesaler;
	private Integer cycle;
	private Integer syncCycle;
	private Integer acceptedPointingDistance;
	private AvatarDto avatar;
	private String pathFile;
	private Boolean sendSyncReminder;
	private String syncReminderPeriod;
	private String holidays;
	private String commentsDictionary;
	private String commentsRatingNotification;
	private String orderPredictionCron;
	private String biPanels;
	private String erpParams;
	private String workType;
	private Integer workingDays;
	private String serverPath;
	private String mlServerUrl;
	private String biServerUrl;
	private String packageName;
	private String orderValidation;
	private Date reportingStartingTime;
	private Date reportingEndingTime;
	private Integer delayedReportingTolerence;
	private Integer noGeolocationTolerence;
	private String host;
	private Integer port;
	private String email;
	private String emailPassword;
	private String language;
	private String displacementCron;
	private String erpType;
	private String erpUrl;
	private String erpSyncCron;
	private String reportValidationCron;
	private String reportingReminderCron;
	private String expenseDistanceCron;
	private String backendUrl;
	private Double defaultLatitude;
	private Double defaultLongitude;
	private String fieldParameters;
	

	public ConfigurationDto() {
		super();

	}

	public ConfigurationDto(Integer id, String name, String logo, Boolean syncro, Integer cycle, Boolean autoSync,
			Boolean lockAfterSync, Integer openReportPeriod, Integer openExpensePeriod, Boolean multiWholesaler,
			AvatarDto avatar, String pathFile, Integer syncCycle, Date reportingStartingTime,
			Date reportingEndingTime) {
		super();
		this.id = id;
		this.name = name;
		this.logo = logo;
		this.syncro = syncro;
		this.autoSync = autoSync;
		this.lockAfterSync = lockAfterSync;
		this.openReportPeriod = openReportPeriod;
		this.openExpensePeriod = openExpensePeriod;
		this.multiWholesaler = multiWholesaler;
		this.cycle = cycle;
		this.syncCycle = syncCycle;
		this.avatar = avatar;
		this.pathFile = pathFile;
		this.reportingStartingTime = reportingStartingTime;
		this.reportingEndingTime = reportingEndingTime;
	}

	public String getOrderValidation() {
		return orderValidation;
	}

	public void setOrderValidation(String orderValidation) {
		this.orderValidation = orderValidation;
	}

	public Boolean getMultiWholesaler() {
		return multiWholesaler;
	}

	public void setMultiWholesaler(Boolean multiWholesaler) {
		this.multiWholesaler = multiWholesaler;
	}

	public Boolean getAutoSync() {
		return autoSync;
	}

	public void setAutoSync(Boolean autoSync) {
		this.autoSync = autoSync;
	}

	public Integer getAcceptedPointingDistance() {
		return acceptedPointingDistance;
	}

	public void setAcceptedPointingDistance(Integer acceptedPointingDistance) {
		this.acceptedPointingDistance = acceptedPointingDistance;
	}

	public Boolean getAutoExpense() {
		return autoExpense;
	}

	public void setAutoExpense(Boolean autoExpense) {
		this.autoExpense = autoExpense;
	}

	public Boolean getLockAfterSync() {
		return lockAfterSync;
	}

	public void setLockAfterSync(Boolean lockAfterSync) {
		this.lockAfterSync = lockAfterSync;
	}

	public Integer getOpenReportPeriod() {
		return openReportPeriod;
	}

	public void setOpenReportPeriod(Integer openReportPeriod) {
		this.openReportPeriod = openReportPeriod;
	}

	public Integer getOpenExpensePeriod() {
		return openExpensePeriod;
	}

	public void setOpenExpensePeriod(Integer openExpensePeriod) {
		this.openExpensePeriod = openExpensePeriod;
	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public Boolean getSyncro() {
		return syncro;
	}

	public void setSyncro(Boolean syncro) {
		this.syncro = syncro;
	}

	public Integer getCycle() {
		return cycle;
	}

	public void setCycle(Integer cycle) {
		this.cycle = cycle;
	}

	public Integer getSyncCycle() {
		return syncCycle;
	}

	public void setSyncCycle(Integer syncCycle) {
		this.syncCycle = syncCycle;
	}

	public AvatarDto getAvatar() {
		return avatar;
	}

	public void setAvatar(AvatarDto avatar) {
		this.avatar = avatar;
	}

	public String getPathFile() {
		return pathFile;
	}

	public void setPathFile(String pathFile) {
		this.pathFile = pathFile;
	}

	public Boolean getSendSyncReminder() {
		return sendSyncReminder;
	}

	public void setSendSyncReminder(Boolean sendSyncReminder) {
		this.sendSyncReminder = sendSyncReminder;
	}

	public String getSyncReminderPeriod() {
		return syncReminderPeriod;
	}

	public void setSyncReminderPeriod(String syncReminderPeriod) {
		this.syncReminderPeriod = syncReminderPeriod;
	}

	public String getHolidays() {
		return holidays;
	}

	public void setHolidays(String holidays) {
		this.holidays = holidays;
	}

	public String getCommentsDictionary() {
		return commentsDictionary;
	}

	public void setCommentsDictionary(String commentsDictionary) {
		this.commentsDictionary = commentsDictionary;
	}

	public String getBiPanels() {
		return biPanels;
	}

	public void setBiPanels(String biPanels) {
		this.biPanels = biPanels;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	public String getServerPath() {
		return serverPath;
	}

	public void setServerPath(String serverPath) {
		this.serverPath = serverPath;
	}

	public String getMlServerUrl() {
		return mlServerUrl;
	}

	public void setMlServerUrl(String mlServerUrl) {
		this.mlServerUrl = mlServerUrl;
	}

	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	public String getBiServerUrl() {
		return biServerUrl;
	}

	public void setBiServerUrl(String biServerUrl) {
		this.biServerUrl = biServerUrl;
	}

	public String getCommentsRatingNotification() {
		return commentsRatingNotification;
	}

	public void setCommentsRatingNotification(String commentsRatingNotification) {
		this.commentsRatingNotification = commentsRatingNotification;
	}

	public String getOrderPredictionCron() {
		return orderPredictionCron;
	}

	public void setOrderPredictionCron(String orderPredictionCron) {
		this.orderPredictionCron = orderPredictionCron;
	}

	
	public Date getReportingStartingTime() {
		return reportingStartingTime;
	}

	public void setReportingStartingTime(Date reportingStartingTime) {
		this.reportingStartingTime = reportingStartingTime;
	}

	public Date getReportingEndingTime() {
		return reportingEndingTime;
	}

	public void setReportingEndingTime(Date reportingEndingTime) {
		this.reportingEndingTime = reportingEndingTime;
	}

	public Integer getDelayedReportingTolerence() {
		return delayedReportingTolerence;
	}

	public void setDelayedReportingTolerence(Integer delayedReportingTolerence) {
		this.delayedReportingTolerence = delayedReportingTolerence;
	}

	public Integer getNoGeolocationTolerence() {
		return noGeolocationTolerence;
	}

	public void setNoGeolocationTolerence(Integer noGeolocationTotolerence) {
		this.noGeolocationTolerence = noGeolocationTotolerence;
	}

	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	public Integer getPort() {
		return port;
	}

	public void setPort(Integer port) {
		this.port = port;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getEmailPassword() {
		return emailPassword;
	}

	public void setEmailPassword(String emailPassword) {
		this.emailPassword = emailPassword;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public Integer getWorkingDays() {
		return workingDays;
	}

	public void setWorkingDays(Integer workingDays) {
		this.workingDays = workingDays;
	}

	public String getDisplacementCron() {
		return displacementCron;
	}

	public void setDisplacementCron(String displacementCron) {
		this.displacementCron = displacementCron;
	}

	public String getErpType() {
		return erpType;
	}

	public void setErpType(String erpType) {
		this.erpType = erpType;
	}

	public String getErpUrl() {
		return erpUrl;
	}

	public void setErpUrl(String erpUrl) {
		this.erpUrl = erpUrl;
	}

	public String getErpSyncCron() {
		return erpSyncCron;
	}

	public void setErpSyncCron(String erpSyncCron) {
		this.erpSyncCron = erpSyncCron;
	}

	public String getReportValidationCron() {
		return reportValidationCron;
	}

	public void setReportValidationCron(String reportValidationCron) {
		this.reportValidationCron = reportValidationCron;
	}

	public String getReportingReminderCron() {
		return reportingReminderCron;
	}

	public void setReportingReminderCron(String reportingReminderCron) {
		this.reportingReminderCron = reportingReminderCron;
	}

	public String getExpenseDistanceCron() {
		return expenseDistanceCron;
	}

	public void setExpenseDistanceCron(String expenseDistanceCron) {
		this.expenseDistanceCron = expenseDistanceCron;
	}

	public String getBackendUrl() {
		return backendUrl;
	}

	public void setBackendUrl(String backendUrl) {
		this.backendUrl = backendUrl;
	}

	public Double getDefaultLatitude() {
		return defaultLatitude;
	}

	public void setDefaultLatitude(Double defaultLatitude) {
		this.defaultLatitude = defaultLatitude;
	}

	public Double getDefaultLongitude() {
		return defaultLongitude;
	}

	public void setDefaultLongitude(Double defaultLongitude) {
		this.defaultLongitude = defaultLongitude;
	}

	public String getFieldParameters() {
		return fieldParameters;
	}

	public void setFieldParameters(String fieldParameters) {
		this.fieldParameters = fieldParameters;
	}

	public String getErpParams() {
		return erpParams;
	}

	public void setErpParams(String erpParams) {
		this.erpParams = erpParams;
	}

}
