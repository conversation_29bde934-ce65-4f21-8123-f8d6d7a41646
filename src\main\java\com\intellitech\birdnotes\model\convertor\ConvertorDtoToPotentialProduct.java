package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.PotentielProduit;
import com.intellitech.birdnotes.model.dto.ProductPotentielDto;

@Component("convertorDtoToPotentialProduct")
public class ConvertorDtoToPotentialProduct {
	

		private static final Logger LOG = LoggerFactory.getLogger(ConvertorDtoToPotentialProduct.class);

		public ProductPotentielDto convert(PotentielProduit potentialProduct) throws BirdnotesException {

			if (potentialProduct == null) {
				LOG.error("potential product is null");
				throw new BirdnotesException("potential product is null");
			}

			ProductPotentielDto potentialProductDto = new ProductPotentielDto();
			potentialProductDto.setId(potentialProduct.getId());
			potentialProductDto.setPotentialId(potentialProduct.getPotential().getId());
			potentialProductDto.setProductId(potentialProduct.getProduct().getId());
			potentialProductDto.setProspectId(potentialProduct.getProspect().getId());
			

			return potentialProductDto;

		}

	}


