package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.RangeRequestDto;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.RangeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("gammeService")
@Transactional
public class RangeServiceImpl implements RangeService {
	
	@Autowired
	private RangeRepository gammeRepository;
	
	@Autowired
	private CurrentUser currentUser;
	
	@Autowired
	UserRepository userRepository;
	
	@Autowired
	UserService userService;

	
	@Override
	public Range add( RangeRequestDto gammeRequestDto ) throws BirdnotesException{
		Range gamme = new Range();
		Range result = gammeRepository.findByName(gammeRequestDto.getName());
		if(result!=null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}else {
		gamme.setName(gammeRequestDto.getName());
		return gammeRepository.save(gamme);}
	}

	
	@Override
	public List<RangeDto> findAll() throws BirdnotesException{
		Long userId = currentUser.getBirdnotesUser().getUserDto().getId();
		List<RangeDto> allGammes = findByUserRange(userId);		
		return allGammes;
	}
	
	
	@Override
	public List<RangeDto> findSubRange() throws BirdnotesException{
		Long userId = currentUser.getBirdnotesUser().getUserDto().getId();
		List<RangeDto> result = new ArrayList<>();
		List<Range> subRanges = gammeRepository.findSubRange(userId);
		for(Range gamme: subRanges) {
			RangeDto g = new RangeDto();
			g.setId(gamme.getId());
			g.setName(gamme.getName());
			result.add(g);
		}
		return result;
	}
	
	@Override
	public List<RangeDto> findByUserRange(Long userId) throws BirdnotesException{
		List<RangeDto> result = new ArrayList<>();
		List<Range> allGammes = gammeRepository.findByUserRange(userId);
		for(Range gamme: allGammes) {
			RangeDto g = new RangeDto();
			g.setId(gamme.getId());
			g.setName(gamme.getName());
			if(gamme.getParent() != null) {
				g.setParentId(gamme.getParent().getId());
			}
			result.add(g);
		}
		return result;
	}
	
	@Override
	public void delete (Integer id) throws BirdnotesException {	
		gammeRepository.deleteById(id);
	}
	
	@Override
	public Range saveRange(RangeDto gammeDto) throws BirdnotesException {
		if(gammeDto.getId() == null || gammeDto == null) {
			throw new RuntimeException("gammeDto est null");
		}
	    Range existingRange = gammeRepository.findByNameAndAnotherId(gammeDto.getName(), gammeDto.getId());
	    if (existingRange != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }
		
		if(gammeDto.getName() == null || gammeDto.getName().isEmpty()) {
			throw new BirdnotesException("nom du gamme est vide");
		}
		Range parent = null;
		if(gammeDto.getParentId() != null) {
			parent = gammeRepository.findOne(gammeDto.getParentId());
		}
		Range gamme = null;
		if(gammeDto.getId() != null) {
			gamme = gammeRepository.findOne(gammeDto.getId());
			
		}
		if(gamme == null) {
			gamme = new Range();
		}
		
		if(parent != null) {
			gamme.setParent(parent);
		}
		gamme.setName(gammeDto.getName());
		Range savedRange = gammeRepository.save(gamme);
		User user = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		boolean exist = false;
		for(Range userGamme : user.getRanges()) {
			if(userGamme.getId() == savedRange.getId()) {
				exist = true;
				break;
			}
		}
		if(exist == false) {
			user.getRanges().add(gamme);
			userRepository.save(user);
		}
		return savedRange;
	}
}
