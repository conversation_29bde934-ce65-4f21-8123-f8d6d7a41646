package com.intellitech.birdnotes.controller;

import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/configuration")
public class ConfigurationController {
	private static final Logger LOG = LoggerFactory.getLogger(ConfigurationController.class);

	@Autowired
	private ConfigurationService configurationService;
	
	@RequestMapping(value = "/getBiPanels", method = RequestMethod.GET)
	public ResponseEntity<String> getBiPanels() {
		try {
			String biPanels = configurationService.findConfiguration().getBiPanels();
			return new ResponseEntity<>(biPanels, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting configuration lists", e);
			return new ResponseEntity<>("", HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	
	@Autowired
	UserService userService;

	@RequestMapping(value = "/getAll", method = RequestMethod.GET)
	public ResponseEntity<ConfigurationDto> findConfiguration() {
		try {
			if(userService.checkHasPermission("GENERAL_SETUP_VIEW")) {
				ConfigurationDto toReturn = configurationService.findConfiguration();
				return new ResponseEntity<>(toReturn, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		}catch (Exception e) {
			LOG.error("An exception occurred while getting configuration lists", e);
			return new ResponseEntity<>(new ConfigurationDto(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addConfiguration(@RequestBody ConfigurationDto configurationDto) {
		try {
			configurationService.add(configurationDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while adding a new configuration", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred : internal server error", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/updateConfiguration", method = RequestMethod.PUT)
	public ResponseEntity<String> updateConfiguration(@RequestBody ConfigurationDto configurationDto) {

		try {
			if (userService.checkHasPermission("GENERAL_SETUP_EDIT")) {
				configurationService.updateConfiguration(configurationDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :non authoritative information", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while updating configuration", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}
	
	
	
	@RequestMapping(value = "/executeDeletionScript/{scriptType}", method = RequestMethod.GET)
	public ResponseEntity<String> executeDeletionScript(@PathVariable("scriptType") String scriptType) {

		try {
			if (userService.checkHasPermission("GENERAL_SETUP_EDIT")) {
				configurationService.executeDeletionScript(scriptType);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		
		} catch (Exception e) {
			LOG.error("An exception occurred while updating configuration", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}
}
