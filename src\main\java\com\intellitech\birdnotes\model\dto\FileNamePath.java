package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class FileNamePath implements Serializable {
	
	private static final long serialVersionUID = 1L;

	private String name;

	private String path;

	public FileNamePath(String name, String path) {
		super();
		this.name = name;
		this.path = path;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPath() {
		return path;
	}

	public void setPath(String path) {
		this.path = path;
	}
}
