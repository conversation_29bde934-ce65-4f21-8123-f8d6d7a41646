package com.intellitech.birdnotes.model;

import java.util.List;

import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;

public class ProspectsToImportDto {

	List<SectorDto> newSectors;
	List<LocalityDto> newLocalities;
	List<SpecialityDto> newSpecialities;
	List<ProspectDto> prospectDtos;
	
	public ProspectsToImportDto() {
		
	}
	public ProspectsToImportDto(List<SectorDto> newSectors, List<LocalityDto> newLocalities, List<SpecialityDto> newSpecialities,
			List<ProspectDto> prospectDtos) {
		super();
		this.newSectors = newSectors;
		this.newLocalities = newLocalities;
		this.newSpecialities = newSpecialities;
		this.prospectDtos = prospectDtos;
	}
	public List<SectorDto> getNewSectors() {
		return newSectors;
	}
	public void setNewSectors(List<SectorDto> newSectors) {
		this.newSectors = newSectors;
	}
	public List<LocalityDto> getNewLocalities() {
		return newLocalities;
	}
	public void setNewLocalities(List<LocalityDto> newLocalities) {
		this.newLocalities = newLocalities;
	}
	public List<SpecialityDto> getNewSpecialities() {
		return newSpecialities;
	}
	public void setNewSpecialities(List<SpecialityDto> newSpecialities) {
		this.newSpecialities = newSpecialities;
	}
	public List<ProspectDto> getProspectDtos() {
		return prospectDtos;
	}
	public void setProspectDtos(List<ProspectDto> prospectDtos) {
		this.prospectDtos = prospectDtos;
	}
	
	
	
}
