
package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.OpportunityNoteDto;
import com.intellitech.birdnotes.model.dto.OpportunityNoteResponseDto;
import com.intellitech.birdnotes.model.request.ActionMarquetingRequest;

public interface OpportunityNoteService {
	   
	void delete(Long id) throws BirdnotesException;

	void update(OpportunityNoteDto opportunityNoteDto) throws BirdnotesException;

	void changeStatus(ActionMarquetingRequest opportunitynoteRequest);

	List<OpportunityNoteResponseDto> findOpportunityNoteByUser(Long userId);

	
	List<OpportunityNoteDto> findAll() throws BirdnotesException;


}
