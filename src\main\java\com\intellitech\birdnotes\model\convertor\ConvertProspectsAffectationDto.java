package com.intellitech.birdnotes.model.convertor;



import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.dto.ProspectsAffectationDto;


@Component("convertProspectsAffectationDto")
public class ConvertProspectsAffectationDto {

		private static final Logger LOG = LoggerFactory.getLogger(ConvertProspectsAffectationDto.class);

		public ProspectsAffectationDto convert(ProspectsAffectation prospectsAffectation) throws BirdnotesException {

			if (prospectsAffectation == null) {
				LOG.error("prospectsAffectation is null");
				throw new BirdnotesException("prospectsAffectation is null");
			}
			ProspectsAffectationDto prospectsAffectationDto = new ProspectsAffectationDto();
	
			prospectsAffectationDto.setUserName(prospectsAffectation.getDelegate().getFirstName() +' ' + prospectsAffectation.getDelegate().getLastName());
			prospectsAffectationDto.setId(prospectsAffectation.getId());
			prospectsAffectationDto.setUserId(prospectsAffectation.getDelegate().getId());
			prospectsAffectationDto.setProspectName(prospectsAffectation.getProspect().getFirstName()+' '+prospectsAffectation.getProspect().getLastName());
			prospectsAffectationDto.setProspectId(prospectsAffectation.getProspect().getId());
			return prospectsAffectationDto;


		}}