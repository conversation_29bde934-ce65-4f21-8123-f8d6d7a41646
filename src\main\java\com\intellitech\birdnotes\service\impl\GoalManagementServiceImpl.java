package com.intellitech.birdnotes.service.impl;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.transaction.Transactional;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.AutomationRuleFormData;
import com.intellitech.birdnotes.data.dto.GoalFormData;
import com.intellitech.birdnotes.enumeration.GroupType;
import com.intellitech.birdnotes.enumeration.Period;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.model.Action;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.ConvertGoalToDto;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;
import com.intellitech.birdnotes.model.dto.GoalSum;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.VisitRequestDto;
import com.intellitech.birdnotes.model.request.GoalItemRequest;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.repository.GoalItemRepository;
import com.intellitech.birdnotes.repository.GoalRepository;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.GoalManagementService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("goalManagementService")
@Transactional
public class GoalManagementServiceImpl implements GoalManagementService {

	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
	private GoalRepository goalRepository;
	private GoalItemRepository goalItemRepository;
	private ConvertGoalToDto convertGoalManagementToDto;
	
	private UserRepository userRepository;
	private RangeRepository rangeRepository;
	private PotentialRepository potentialRepository;
	private ProductRepository productRepository;
	private SectorRepository sectorRepository;
	private SpecialityRepository specialityRepository;
	private ProspectTypeRepository prospectTypeRepository;
	private DynamicQueries dynamicQueries;
	
	private UserToDtoConvertor userToDtoConvertor;
	
	private UserService userService;

	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;
		
	}
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	public GoalManagementServiceImpl(
			SpecialityRepository specialityRepository ,
			ProspectTypeRepository prospectTypeRepository,
			UserRepository userRepository,
			SectorRepository sectorRepository,
			ProductRepository productRepository ,
			PotentialRepository potentialRepository, 
			GoalRepository goalRepository,
			ConvertGoalToDto convertGoalManagementToDto,
			GoalItemRepository goalItemRepository,
			DynamicQueries dynamicQueries, RangeRepository rangeRepository,
			UserToDtoConvertor userToDtoConvertor) {
		super();
		this.goalRepository = goalRepository;
		this.convertGoalManagementToDto=convertGoalManagementToDto;
		this.potentialRepository = potentialRepository;
		this.productRepository= productRepository;
		this.sectorRepository=sectorRepository;
		this.rangeRepository=rangeRepository;
		this.specialityRepository=specialityRepository;
		this.prospectTypeRepository = prospectTypeRepository;
		this.userRepository=userRepository;
		this.goalItemRepository=goalItemRepository;
		this.dynamicQueries = dynamicQueries;
		this.userToDtoConvertor = userToDtoConvertor;
	}
	
	//Ajouté pour le test
	public GoalManagementServiceImpl() {
		super();

	}
	
	//Ajouté pour le test
	public GoalManagementServiceImpl(GoalRepository goalRepository, ConvertGoalToDto convertGoalManagementToDto) {
		super();
	}

    //Ajouté pour le test
	public void setConvertGoalToDto(ConvertGoalToDto convertGoalManagementToDto) {
		this.convertGoalManagementToDto = convertGoalManagementToDto;
		
	}
	//Ajouté pour le test
	public void setGoalRepository(GoalRepository goalRepository) {
		this.goalRepository = goalRepository;
		
	}
	
	
	@Override	
	public List<GoalSum> getGoalsSum (VisitRequestDto coverageRequestDto) throws BirdnotesException{
		
		StringBuilder query = new StringBuilder();
		StringBuilder whereClause = new StringBuilder();
		
		Date startDate = null;
		Date endDate = null;
		try {
			startDate = format.parse(coverageRequestDto.getFirstDate());
			endDate = format.parse(coverageRequestDto.getLastDate());

		} catch (ParseException e) {
			log.error("error when parsing dates", e);
		}
		if (coverageRequestDto.getSelectedUser() != null && coverageRequestDto.getSelectedUser() != 0) {

			User user = userRepository.findUserByDelegateId(coverageRequestDto.getSelectedUser());
			Long count = goalRepository.checkGoalPeriodUnicity(startDate, endDate, GoalType.VISIT, user.getId());
			if (count != 1) {
				throw new BirdnotesException("La période selectionnée ne correspond pas avec les périodes des objectifs de l'utilisateur séléctionné");
			}

		} else {
			List<UserDto> subUsers = userService.getSubUsers();
			for (UserDto user : subUsers) {
				Long count = goalRepository.checkGoalPeriodUnicity(startDate, endDate, GoalType.VISIT, user.getId());
				if (count != 1) {
					throw new BirdnotesException( "La période selectionnée ne correspond pas avec les périodes des objectifs de  " + user.getFirstName() + " " + user.getLastName() );
				}
			}

		}

		
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.DELEGUE.getId().longValue())) {
			query.append(
					"SELECT new com.intellitech.birdnotes.model.dto.GoalSum(sum (gi.value), "
					+ "CONCAT(d.firstName,' ',d.lastName)) "
					+ "From GoalItem gi join gi.goal.users u inner join u.delegate d");
		}
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.SECTOR.getId().longValue())) {
			query.append(
					"SELECT new com.intellitech.birdnotes.model.dto."
					+"GoalSum ( sum(gi.value) , gi.sector.name) "
					+" from GoalItem gi join gi.goal g join g.users users");
			}
		whereClause.append(" gi.goal.firstDate <= (:startDate) AND  gi.goal.lastDate >= (:endDate) ");


		if(coverageRequestDto.getSelectedGroup().equals(GroupType.ACTIVITY.getId().longValue())) {
			query.append(
					"SELECT new com.intellitech.birdnotes.model.dto."
					+ "GoalSum ( sum(gi.value) , gi.activity) "
					+" from GoalItem gi join gi.goal g join g.users users");
			}

		if(coverageRequestDto.getSelectedGroup().equals(GroupType.POTENTIAL.getId().longValue())) {
			query.append(
					"SELECT new com.intellitech.birdnotes.model.dto."
					+ "GoalSum ( sum(gi.value) , gi.potential.name) "
					+" from GoalItem gi join gi.goal g join g.users users");
			}
		
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.SPECIALITY.getId().longValue())) {
			query.append(
					"SELECT new com.intellitech.birdnotes.model.dto."
					+ "GoalSum(sum (gi.value), gi.speciality.name) "
					+" from GoalItem gi join gi.goal g join g.users users");
			}

		if(coverageRequestDto.getSelectedGroup().equals(GroupType.PRODUCT.getId().longValue())) {
			query.append(
					"SELECT new com.intellitech.birdnotes.model.dto."
					+ "GoalSum(sum (gi.value), gi.product.name) "
					+" from GoalItem gi join gi.goal g join g.users users");
		}
		if(coverageRequestDto.getSelectedPotential() != null && coverageRequestDto.getSelectedPotential() !=0 ) {
			if(whereClause.length() > 0) {
				whereClause.append(" AND ");
			}
			whereClause.append(" gi.potential.id =" + coverageRequestDto.getSelectedPotential());
		}
		
		if(coverageRequestDto.getSelectedProduct() != null && coverageRequestDto.getSelectedProduct() !=0) {
			if(whereClause.length() > 0) {
				whereClause.append(" AND ");
			}
			whereClause.append(" gi.product.id =" + coverageRequestDto.getSelectedProduct());
		}
		
		if(coverageRequestDto.getSelectedSector() != null && coverageRequestDto.getSelectedSector() !=0) {
			if(whereClause.length() > 0) {
				whereClause.append(" AND ");
			}
			whereClause.append(" gi.sector.id =" + coverageRequestDto.getSelectedSector());
		}
		
		if(coverageRequestDto.getSelectedSpeciality() != null && coverageRequestDto.getSelectedSpeciality() !=0) {
			if(whereClause.length() > 0) {
				whereClause.append(" AND ");
			}
			whereClause.append(" gi.speciality.id =" + coverageRequestDto.getSelectedSpeciality());
		}
		
		if(coverageRequestDto.getSelectedActivity() != null ) {
			if(whereClause.length() > 0) {
				whereClause.append(" AND ");
			}
			whereClause.append(" gi.activity = " + "'" + coverageRequestDto.getSelectedActivity()) ;
			whereClause.append("'");
		}
		
		if(whereClause.length() != 0) {
			query.append(" where ").append(whereClause);
		}
		
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.DELEGUE.getId().longValue())) {
		query.append(" Group by CONCAT(d.firstName,' ',d.lastName)");
		}

		if(coverageRequestDto.getSelectedGroup().equals(GroupType.SECTOR.getId().longValue())) {
			query.append(" Group by gi.sector.name");
		}
		
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.SPECIALITY.getId().longValue())) {
			query.append(" Group by gi.speciality.name");
		}
		
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.ACTIVITY.getId().longValue())) {
			query.append(" Group by gi.activity");
			}

		if(coverageRequestDto.getSelectedGroup().equals(GroupType.POTENTIAL.getId().longValue())) {
			query.append(" Group by gi.potential.name");
			}
		if(coverageRequestDto.getSelectedGroup().equals(GroupType.PRODUCT.getId().longValue())) {
			query.append(" Group by gi.product.name");
			}

		List<GoalSum> goalSum = null;
		 goalSum = dynamicQueries.findGoalsSum(query.toString(),  startDate , endDate);

		return goalSum;
	}
	
	@Override
	public Goal saveGoal( GoalRequest goalManagementRequest ) throws BirdnotesException{
		
		
		if (goalManagementRequest.getName() == null || "".equals(goalManagementRequest.getName())) {
			throw new BirdnotesException(Exceptions.GOAL_NAME_IS_EMPTY);
		}
		if (StringUtils.isNumeric(goalManagementRequest.getName()))
		{
			throw new BirdnotesException(Exceptions.GOAL_NAME_IS_NUMBER);
		}
		
		Goal result = goalRepository.findByNameWithDiffId(goalManagementRequest.getName(), 
				goalManagementRequest.getId());

		if(result!=null) {
			throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
		}
		
		if(goalManagementRequest.getUserIds().size() == 0) {
			throw new BirdnotesException(Exceptions.GOAL_DELEGATE_IS_EMPTY);
		}
		if(goalManagementRequest.getGoalItems().size() == 0) {
			throw new BirdnotesException(Exceptions.GOAL_ITEM_IS_EMPTY);
		}
		Goal goal = null;
		if(goalManagementRequest.getId() != null) {
			goal = goalRepository.findOne(goalManagementRequest.getId());			
		}
		if(goal == null) {
			goal = new Goal();
		}

		goal.setName(goalManagementRequest.getName());
		goal.setFirstDate(goalManagementRequest.getFirstDate());
		goal.setLastDate(goalManagementRequest.getLastDate());
		goal.setGoalType(GoalType.valueOf((goalManagementRequest.getGoalType())));
		goal.setPeriod(Period.valueOf((goalManagementRequest.getPeriod())));
		goal.setItemsOrder(goalManagementRequest.getItemsOrder());
		goalRepository.save(goal);
		List <GoalItemRequest>goalItemManagementRequest = goalManagementRequest.getGoalItems();
		Set<GoalItem> goalItems = new HashSet<GoalItem> ();
		Set<User> users = new HashSet<User>();

		for(int x=0;x< goalItemManagementRequest.size();x++) {
			GoalItem goalItem = new GoalItem();
			if(goalItemManagementRequest.get(x).getValue()==null) {
				goalItemManagementRequest.get(x).setValue(0) ;
			}
			goalItem.setValue(goalItemManagementRequest.get(x).getValue());
			if (goalItemManagementRequest.get(x).getActivity() != null) {
			goalItem.setActivity(goalItemManagementRequest.get(x).getActivity());
			}
			if(goalItemManagementRequest.get(x).getPotentialId()!= null) {
			Potential potential = potentialRepository.findOne(goalItemManagementRequest.get(x).getPotentialId());
			goalItem.setPotential(potential);
			}
			if(goalItemManagementRequest.get(x).getProductId()!= null) {
			Product product = productRepository.findOne(goalItemManagementRequest.get(x).getProductId());
			goalItem.setProduct(product);
			}
			if(goalItemManagementRequest.get(x).getSectorId()!= null) {
			Sector sector = sectorRepository.findOne(goalItemManagementRequest.get(x).getSectorId());
			goalItem.setSector(sector);
			}
			if(goalItemManagementRequest.get(x).getSpecialityId()!= null) {
			Speciality speciality = specialityRepository.findOne(goalItemManagementRequest.get(x).getSpecialityId());
			goalItem.setSpeciality(speciality);
			}
			if(goalItemManagementRequest.get(x).getProspectTypeId()!= null) {
				ProspectType prospectType = prospectTypeRepository.findOne(goalItemManagementRequest.get(x).getProspectTypeId());
				goalItem.setProspectType(prospectType);
			}
			
			goalItems.add(goalItem);
			goalItem.setGoal(goal);
		}
		
	
		
		for (Long userId : goalManagementRequest.getUserIds()) {
			User user = userRepository.findOne(userId);
			users.add(user);
			user.getGoals().add(goal);
			userRepository.save(user);
			}
		
		goalItemRepository.deleteById(goal.getId());
		goalItemRepository.save(goalItems);	
		goal.setGoalItems(goalItems);
		goalRepository.save(goal);
		return goal;
		
	}

	
	@Override
	public List<GoalDto> findAll() throws BirdnotesException{
		List<GoalDto> result = new ArrayList<>();
		List<Long> subUsers = userService.getSubUsersIds();
		
		List<Goal> allGoalManagement = goalRepository.findAll();
		for(Goal goal: allGoalManagement) {
			GoalDto g = new GoalDto();	
			Set<UserDto> users = new HashSet <>();
			g.setId(goal.getId());
			g.setName(goal.getName());
			g.setGoalType(goal.getGoalType().name());
			g.setFirstDate(goal.getFirstDate());
			g.setLastDate(goal.getLastDate());
			g.setPeriod(goal.getPeriod().name());
			Set<User> userList= userRepository.findUsersOfGoal(goal.getId());
			for(User user : userList) {
				UserDto userDto = userToDtoConvertor.convert(user);
				users.add(userDto);
			}
			g.setUsers(users);
			result.add(g);
		}
		return result;
	}
	
	@Override
	public void delete (Long id)throws BirdnotesException {
			Goal goal = goalRepository.findOne(id);
			
			for (GoalItem goalItem : goalItemRepository.findByGoal(goal) )
			{
				goalItemRepository.delete(goalItem);
			}
			
			for ( User user : userRepository.findUsersOfGoal(goal.getId()) ) 
			{
				if(user.getGoals()!=null) {
					user.getGoals().remove(goal);
				}	
				
				userRepository.save(user);
			}
			goalRepository.delete(id);


	}
	
	
	@Override
	public GoalFormData getAllDataGoalForm() {
		List<Product> products = productRepository.findAll();
		List<Speciality> specialities = specialityRepository.findAll();
		List<Sector> sectors = sectorRepository.findAll();
		List<ProspectType> prospectTypes = prospectTypeRepository.findAll();
		List<Potential> potentials = potentialRepository.findAll();
		List<Range> ranges = rangeRepository.findAll();
		
		GoalFormData goalFormData = new GoalFormData();
		goalFormData.setPotentials(potentials);
		goalFormData.setProducts(products);
		goalFormData.setProspectTypes(prospectTypes);
		goalFormData.setSectors(sectors);
		goalFormData.setSpecialities(specialities);
		goalFormData.setRanges(ranges);
		return goalFormData;
	}
	
	@Override
	public void update(GoalDto goalDto) throws BirdnotesException {
		if(goalDto == null || goalDto.getId()== null) {
			throw new BirdnotesException(Exceptions.GOAL_DTO_IS_NULL);
		}
		if(goalDto.getName() == null || goalDto.getName().isEmpty()) {
			throw new BirdnotesException(Exceptions.GOAL_NAME_IS_EMPTY);
		}
		
		Goal result = goalRepository.findFirstByNameIgnoreCase(goalDto.getName());
		if(result!=null  && !result.getId().equals(goalDto.getId())) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}
		
		Goal goalToUpdate = goalRepository.findOne(goalDto.getId());
		List <GoalItemDto> goalItemDto = goalDto.getGoalItems();
		Set<Long> listUserDto = goalDto.getUserIds();
		Set <User> users = new HashSet <User>();
		Set <GoalItem> goalItems = new HashSet <GoalItem> ();
		if(goalToUpdate == null ) {
			throw new BirdnotesException(Exceptions.GOAL_TO_UPDATE_ALREADY_DELETED);
		}

		for ( User user : userRepository.findUsersOfGoal(goalToUpdate.getId()) ) 
			{
			if(user.getGoals()!=null) {
				user.getGoals().remove(goalToUpdate);
			}	
			userRepository.save(user);
			}
		
		for (GoalItem goalItem : goalItemRepository.findByGoal(goalToUpdate) )
		{
			goalItemRepository.delete(goalItem);
		}
		
		goalToUpdate.setId(goalDto.getId());
		goalToUpdate.setName(goalDto.getName());
		goalToUpdate.setFirstDate(goalDto.getFirstDate());
		goalToUpdate.setLastDate(goalDto.getLastDate());
		goalToUpdate.setGoalType(GoalType.valueOf(goalDto.getGoalType()));
		goalToUpdate.setPeriod(Period.valueOf(goalDto.getPeriod()));
		goalToUpdate.setItemsOrder(goalDto.getItemsOrder());
		for( int x=0; x< goalItemDto.size(); x++) {
			GoalItem goalItemToUpdate = new GoalItem();
			if(goalItemDto.get(x).getValue()==null) {
				goalItemDto.get(x).setValue(0) ;
			}
			goalItemToUpdate.setValue(goalItemDto.get(x).getValue());
			if (goalItemDto.get(x).getActivity() != null) {
			goalItemToUpdate.setActivity(goalItemDto.get(x).getActivity());
			}
			if (goalItemDto.get(x).getPotentialId() != null) {
			Potential potential =potentialRepository.findOne(goalItemDto.get(x).getPotentialId());
			goalItemToUpdate.setPotential(potential);
			}
			if (goalItemDto.get(x).getProductId() != null) {
			Product product =productRepository.findOne(goalItemDto.get(x).getProductId());
			goalItemToUpdate.setProduct(product);
			}
			if (goalItemDto.get(x).getSectorId() != null) {
			Sector sector =sectorRepository.findOne(goalItemDto.get(x).getSectorId());
			goalItemToUpdate.setSector(sector);
			}
			if (goalItemDto.get(x).getSpecialityId() != null) {
			Speciality speciality =specialityRepository.findOne(goalItemDto.get(x).getSpecialityId());
			goalItemToUpdate.setSpeciality(speciality);
			}
			goalItemToUpdate.setGoal(goalToUpdate);
			goalItemRepository.save(goalItemToUpdate);
			goalItems.add(goalItemToUpdate);
		}			

		for( Long userDto : listUserDto)
		{
			User user = userRepository.findOne(userDto);
			user.getGoals().add(goalToUpdate);
			userRepository.save(user);
			users.add(user);
		}
		
		goalToUpdate.setGoalItems(goalItems);
		goalRepository.save(goalToUpdate);

	}
	
	@Override
	public GoalDto findGoalByName(String name) throws BirdnotesException {
		Goal goal=goalRepository.findByName(name);
		GoalDto goalManagementDto=convertGoalManagementToDto.convert(goal);
		return goalManagementDto;
		 
	}

	@Override
	public GoalDto getGoal(Long id) throws BirdnotesException {
		Goal goal=goalRepository.findOne(id);
		Set <GoalItem> goalItems = goalItemRepository.findByGoal(goal);
		goal.setGoalItems(goalItems);
		GoalDto goalManagementDto=convertGoalManagementToDto.convert(goal);
		return goalManagementDto;
	}


	
}
