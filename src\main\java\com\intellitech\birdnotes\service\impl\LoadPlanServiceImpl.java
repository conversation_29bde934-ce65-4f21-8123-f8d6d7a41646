package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.LoadPlan;
import com.intellitech.birdnotes.model.dto.LoadPlanDto;
import com.intellitech.birdnotes.repository.LoadPlanRepository;
import com.intellitech.birdnotes.service.LoadPlanService;

@Service("loadPlanService")
@Transactional
public class LoadPlanServiceImpl implements LoadPlanService {

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	private LoadPlanRepository loadPlanRepository;

	@Override
	public List<LoadPlanDto> getAllLoadPlans() throws BirdnotesException {
		List<LoadPlanDto> result = new ArrayList<>();
		List<LoadPlan> loadPlans = loadPlanRepository.findAll();
		if (loadPlans != null && !loadPlans.isEmpty()) {
			for (LoadPlan loadPlan : loadPlans) {
				// if (Boolean.TRUE.equals(loadPlan.getActive())) {
				LoadPlanDto loadPlanDto = new LoadPlanDto();
				loadPlanDto.setId(loadPlan.getId());
				loadPlanDto.setName(loadPlan.getName());
				loadPlanDto.setActive(loadPlan.getActive());
				if (loadPlan.getStartDate() != null) {
					loadPlanDto.setStartDate(loadPlan.getStartDate());
				}
				if (loadPlan.getEndDate() != null) {
					loadPlanDto.setEndDate(loadPlan.getEndDate());
				}

				result.add(loadPlanDto);
			}

		}
		return result;
	}

	@Override
	public LoadPlan saveLoadPlan(LoadPlanDto loadPlanDto) throws BirdnotesException {
		LoadPlan loadPlan = null;
		if (loadPlanDto.getId() != null) {
			loadPlan = loadPlanRepository.findOne(loadPlanDto.getId());
		}
		if (loadPlan == null) {
			loadPlan = new LoadPlan();
		}
		loadPlan.setId(loadPlanDto.getId());
		loadPlan.setName(loadPlanDto.getName());
		loadPlan.setStartDate(loadPlanDto.getStartDate());
		loadPlan.setEndDate(loadPlanDto.getEndDate());
		loadPlan.setActive(loadPlanDto.getActive());
		LoadPlan savedLoadPlan = loadPlanRepository.save(loadPlan);
		
		if(loadPlanDto.getActive() == true) {
			loadPlanRepository.setAllPlanInactive(savedLoadPlan.getId());
		}
		
		return savedLoadPlan;
	}

	@Override
	public void deleteLoadPlan(Long loadPlanId) {
		loadPlanRepository.delete(loadPlanId);
		;
	}

}
