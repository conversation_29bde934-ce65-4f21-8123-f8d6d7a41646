package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.ValidationStep;

public interface ValidationStepRepository extends JpaRepository<ValidationStep, Integer>{

	@Query("SELECT v from ValidationStep v where v.validationType=:validationType")
	List<ValidationStep> findValidationStepByType(@Param("validationType") String validationType);
	
	void deleteByValidationType(String validationType);
	
	@Query("SELECT v.id from ValidationStep v where v.validationType=:validationType")
	List<Long> findValidationStepIdsByType(@Param("validationType") String validationType);
	
	@Modifying
	@Query("DELETE from ValidationStep v where v.id in :validationStepToDelete")
	void deleteByValidationStep(@Param("validationStepToDelete")List<Long> validationStepToDelete);
	
}
