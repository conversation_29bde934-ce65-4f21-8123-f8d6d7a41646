package com.intellitech.birdnotes.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;

public interface EstablishmentService {
	
	List<EstablishmentDto> findAll() throws BirdnotesException;

	
	List<EstablishmentDto> findEstablishmentByAffectedSectors(Long userId) throws BirdnotesException;


	Establishment saveEstablishment(EstablishmentDto establishmentRequest) throws BirdnotesException;


	void deleteEstablishment(Long establishmentId) throws BirdnotesException;


	Establishment updateEstablishment(EstablishmentDto establishmentDto) throws BirdnotesException;
	
	EstablishmentDto findEstablishmentDto(String establishmentName, List<EstablishmentDto> establishmentDtos);


	void saveAllEstablishments(List<EstablishmentDto> establishmentDtos) throws BirdnotesException;

}
