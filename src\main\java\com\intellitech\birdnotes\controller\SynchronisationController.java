package com.intellitech.birdnotes.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.ReceiveRequestInput;
import com.intellitech.birdnotes.model.dto.ReceiveResponsedDto;
import com.intellitech.birdnotes.model.dto.SendRequestDto;
import com.intellitech.birdnotes.model.dto.SendResponseDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.SynchronisationService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/synchronise")
public class SynchronisationController {

	private static final Logger LOG = LoggerFactory.getLogger(SynchronisationController.class);

	@Autowired
	private CurrentUser currentUser;

	@Autowired
	private SynchronisationService synchronisationService;

	@Autowired
	UserService userService;

	@RequestMapping(value = "/send", method = RequestMethod.POST)
	public ResponseEntity<SendResponseDto> send(@RequestBody SendRequestDto sendRequestDto) {
		try {
			BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
			if (birdnotesUser == null || birdnotesUser.getUserDto() == null
					|| birdnotesUser.getUserDto().getId() == null) {
				return new ResponseEntity<>(new SendResponseDto(), HttpStatus.UNAUTHORIZED);
			}
			if (userService.checkHasPermission("SYNCHRONIZATION")) {
				return new ResponseEntity<>(synchronisationService.send(sendRequestDto, birdnotesUser.getUserDto().getId()),HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while send a new data", e);
			return new ResponseEntity<>(new SendResponseDto(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@RequestMapping(value = "/receive", method = RequestMethod.POST)
	public ResponseEntity<ReceiveResponsedDto> receive(@RequestBody ReceiveRequestInput receiveRequestInput) {
		try {
			BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
			if (birdnotesUser == null || birdnotesUser.getUserDto() == null
					|| birdnotesUser.getUserDto().getId() == null) {
				return new ResponseEntity<>(new ReceiveResponsedDto(), HttpStatus.UNAUTHORIZED);
			}
			if (userService.checkHasPermission("SYNCHRONIZATION")) {			
				return new ResponseEntity<>(synchronisationService.receive(
						birdnotesUser.getUserDto().getId(), receiveRequestInput), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while receive a new data", e);
			return new ResponseEntity<>(new ReceiveResponsedDto(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
