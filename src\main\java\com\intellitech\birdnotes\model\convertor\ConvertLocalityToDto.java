package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.dto.LocalityDto;

@Component("convertLocalityToDto")
public class ConvertLocalityToDto {

	private static final Logger LOG = LoggerFactory.getLogger(ConvertLocalityToDto.class);

	public LocalityDto convert(Locality locality) throws BirdnotesException {

		if (locality == null) {
			LOG.error("locality is null");
			throw new BirdnotesException("locality is null");
		}

		if (locality.getSector() == null) {
			LOG.error("locality with id = " + locality.getId() + " have a nullable sector");
			throw new BirdnotesException("sector is null");
		}
		LocalityDto localityDto = new LocalityDto();
		localityDto.setName(locality.getName());
		localityDto.setId(locality.getId());
		localityDto.setSectorId(locality.getSector().getId());
		localityDto.setSectorName(locality.getSector().getName());

		return localityDto;
	}

}
