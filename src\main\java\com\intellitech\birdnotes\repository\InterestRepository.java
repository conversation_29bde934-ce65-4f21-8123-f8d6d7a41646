package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


import com.intellitech.birdnotes.model.Interest;

@Repository
public interface InterestRepository extends JpaRepository<Interest, Long> {
	
	
	
	@Override
	@Query("SELECT i from Interest i order by i.name ASC")
	List<Interest> findAll();

	Interest findByName(String name);

	@Modifying
	@Query("DELETE FROM Interest where id=:id")
	void deleteById(@Param ("id") Long id);
    @Query("SELECT i.id from Interest i")
	List<Long> getAllInterestIds();
    
	@Query("SELECT i from Interest i where  LOWER(name) = LOWER(?1) AND id != ?2")
	Interest findByNameAndAnotherId(String name, Long id);

	
	
}