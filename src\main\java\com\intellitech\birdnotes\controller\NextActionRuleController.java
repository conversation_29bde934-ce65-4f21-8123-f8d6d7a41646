package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.dao.DataIntegrityViolationException;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.NextActionRule;
import com.intellitech.birdnotes.model.dto.NextActionRuleDto;


import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.NextActionRuleService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/nextActionRules")
public class NextActionRuleController {

	private static final Logger LOG = LoggerFactory.getLogger(NextActionRuleController.class);

	@Autowired
	private NextActionRuleService nextActionRuleService;

	@Autowired
	UserService userService;
	
	
	@RequestMapping(value = "/findAllNextActionRules", method = RequestMethod.GET)
	public ResponseEntity<List<NextActionRuleDto>> getAllNextActionRule() {
		try {
			if (userService.checkHasPermission("NEXT_ACTION_RULE_VIEW")) {
				List<NextActionRuleDto> result = nextActionRuleService.findAll();
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all nextActionRule ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	@RequestMapping(value = "/saveNextActionRule", method = RequestMethod.POST)
	public ResponseEntity<String> saveNextActionRule(@RequestBody NextActionRuleDto nextActionRuleDto) {
	    try {
	        if (userService.checkHasPermission("NEXT_ACTION_RULE_EDIT")) {
	        	NextActionRule savedNextActionRule = nextActionRuleService.saveNextActionRule(nextActionRuleDto);
	            if (savedNextActionRule != null) {
	                return new ResponseEntity<>(savedNextActionRule.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving next action rule", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving prospect type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteNextActionRule(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("NEXT_ACTION_RULE_DELETE")) {
	        	nextActionRuleService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting next action rule", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the next action rule with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	}
