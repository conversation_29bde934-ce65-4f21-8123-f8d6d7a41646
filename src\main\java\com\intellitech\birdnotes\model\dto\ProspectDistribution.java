package com.intellitech.birdnotes.model.dto;


public class ProspectDistribution {
private String groupOfSearch;
private Long count;
private String groupOfSearchValue;


public ProspectDistribution() {
	super();
	
}


public ProspectDistribution(Long count, String groupOfSearchValue) {
	super();
	this.count = count;
	this.groupOfSearchValue = groupOfSearchValue;
	
}


public ProspectDistribution(String groupOfSearch, Long count, String groupOfSearchValue) {
	super();
	this.groupOfSearch = groupOfSearch;
	this.count = count;
	this.groupOfSearchValue=groupOfSearchValue;

}

public String getGroupOfSearch() {
	return groupOfSearch;
}
public void setGroupOfSearch(String groupOfSearch) {
	this.groupOfSearch = groupOfSearch;
}
public Long getCount() {
	return count;
}
public void setCount(Long count) {
	this.count = count;
}
public String getGroupOfSearchValue() {
	return groupOfSearchValue;
}
public void setGroupOfSearchValue(String groupOfSearchValue) {
	this.groupOfSearchValue = groupOfSearchValue;
}

@Override
public int hashCode() {
	final int prime = 31;
	int result = 1;
	result = prime * result + ((count == null) ? 0 : count.hashCode());
	result = prime * result + ((groupOfSearch == null) ? 0 : groupOfSearch.hashCode());
	result = prime * result + ((groupOfSearchValue == null) ? 0 : groupOfSearchValue.hashCode());
	return result;
}

@Override
public boolean equals(Object obj) {
	if (this == obj)
		return true;
	if (obj == null)
		return false;
	if (getClass() != obj.getClass())
		return false;
	ProspectDistribution other = (ProspectDistribution) obj;
	if (count == null) {
		if (other.count != null)
			return false;
	} else if (!count.equals(other.count))
		return false;
	if (groupOfSearch == null) {
		if (other.groupOfSearch != null)
			return false;
	} else if (!groupOfSearch.equals(other.groupOfSearch))
		return false;
	if (groupOfSearchValue == null) {
		if (other.groupOfSearchValue != null)
			return false;
	} else if (!groupOfSearchValue.equals(other.groupOfSearchValue))
		return false;
	return true;
}

}
