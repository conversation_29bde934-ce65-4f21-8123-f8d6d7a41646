package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.PotentielProduit;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.convertor.ConvertorDtoToPotentialProduct;
import com.intellitech.birdnotes.model.dto.MinimizedPotentialDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDto;
import com.intellitech.birdnotes.model.dto.ProductPotentielDtoRequest;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProductsPotentialRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.service.ProductsPotentialService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("productsPotentialService")
@Transactional
public class ProductsPotentialServiceImpl implements ProductsPotentialService {
	
	
	private ProductsPotentialRepository productsPotentialRepository;
	
	private PotentialRepository potentialRepository;
	
	private ProductRepository productRepository;
	
	private ProspectRepository prospectRepository;
	
	private ConvertorDtoToPotentialProduct convertorDtoToPotentialProduct;
	


	@Autowired
	public void setProductsPotentialRepository(ProductsPotentialRepository productsPotentialRepository) {
		this.productsPotentialRepository = productsPotentialRepository;
	}

	@Autowired
	public void setPotentialRepository(PotentialRepository potentialRepository) {
		this.potentialRepository = potentialRepository;
	}

	@Autowired
	public void setProductRepository(ProductRepository productRepository) {
		this.productRepository = productRepository;
	}
	
	@Autowired
	public void setProspectRepository(ProspectRepository prospectRepository) {
		this.prospectRepository = prospectRepository;
	}
	@Autowired
	public void setconvertorDtoToPotentialProduct(ConvertorDtoToPotentialProduct convertorDtoToPotentialProduct) {
		this.convertorDtoToPotentialProduct = convertorDtoToPotentialProduct;
	}
	

	@Override
	@Transactional(readOnly = true)
	public List<ProductPotentielDto> getAllPotentialProducts() throws BirdnotesException {

		List<PotentielProduit> potentialProducts = productsPotentialRepository.findAll();
		List<ProductPotentielDto> potentialProductDtos = new ArrayList<>();

		if (potentialProducts != null && !potentialProducts.isEmpty()) {
			for (PotentielProduit potentialProduct : potentialProducts) {
				ProductPotentielDto potentialProductDto = convertorDtoToPotentialProduct.convert(potentialProduct);
				potentialProductDtos.add(potentialProductDto);
			}
		}

		return potentialProductDtos;
	}

	@Override
    public void saveProductsPotential(ProductPotentielDto productsPotentialDto)
    		throws BirdnotesException {      
    	
    	
        Prospect prospect = prospectRepository.findByFirstNameAndLastName(productsPotentialDto.getProspectFirstName(),productsPotentialDto.getProspectLastName());
    	
		if (productsPotentialDto.getPotentialIds() == null) {
			throw new BirdnotesException(Exceptions.EMPTY_PRODUCT_POTENTIAL);
		}else {
			  int i =-1;
		     	for (Long potentialId : productsPotentialDto.getPotentialIds())
			    { 	i++;
		     		if (potentialId != null) {	Potential potential = potentialRepository.findOne(potentialId);
			      PotentielProduit potentielProduit = new PotentielProduit();
			    
			Product product = productRepository.findOne((productsPotentialDto.getProductIds()).get(i));
			potentielProduit.setProduct(product);
			potentielProduit.setPotential(potential);
		    potentielProduit.setProspect(prospect);
			productsPotentialRepository.save(potentielProduit);
		}}
	}
	}
	
	@Override
	public void updateProductPotential(ProductPotentielDtoRequest productsPotentialDto) throws BirdnotesException {

		if (productsPotentialDto == null ) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_DTO_PRODUCT_POTENTIAL);
		} else {
			
			if(productsPotentialDto.getPotentialNames() != null) {
				
				for (PotentielProduit productPotential : prospectRepository
						.findPotentialProductById(productsPotentialDto.getProspectId())) {
					productsPotentialRepository.deleteById(productPotential.getId());
				}
				Prospect prospect = prospectRepository.findOne(productsPotentialDto.getProspectId());
				int i = -1;
				for (String potentialName : productsPotentialDto.getPotentialNames()) {
					i++;
					if (potentialName != null) {
						Potential potential = potentialRepository.findByName(potentialName);
						PotentielProduit potentielProduit = new PotentielProduit();

						Product product = productRepository.findOne((productsPotentialDto.getProductIds()).get(i));
						potentielProduit.setProduct(product);
						potentielProduit.setPotential(potential);
						potentielProduit.setProspect(prospect);
						productsPotentialRepository.save(potentielProduit);
					}
				}
				
			}
			
		}
	}

	
	
	@Override
	public  MinimizedPotentialDto findpotentialbyIds(Long productId, Long prospectId) throws BirdnotesException {

	    MinimizedPotentialDto potentialDto = new MinimizedPotentialDto();
		Potential potential = productsPotentialRepository.findpotentialbyIds(productId,prospectId);
		if (potential != null) {
		potentialDto.setId(potential.getId());
		potentialDto.setName(potential.getName());	
			}
		return potentialDto;
  }
	
	
 }







