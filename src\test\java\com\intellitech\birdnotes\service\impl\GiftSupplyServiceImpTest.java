package com.intellitech.birdnotes.service.impl;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.GiftSupply;
import com.intellitech.birdnotes.model.dto.GiftSupplyDto;
import com.intellitech.birdnotes.model.dto.GiftSupplyRequestDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.repository.GiftSupplyRepository;
import com.intellitech.birdnotes.service.UserService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class GiftSupplyServiceImpTest {

    @Mock
    private GiftRepository mockGadgetRepository;
    @Mock
    private DelegateRepository mockDelegateRepository;
    @Mock
    private GiftSupplyRepository mockGadgetSupplyRepository;
    @Mock
    private DynamicQueries mockDynamicQueries;
    @Mock
    private UserService mockUserService;

    @InjectMocks
    private GiftSupplyServiceImp giftSupplyServiceImpUnderTest;

    @Before
    public void setUp() throws Exception {
        giftSupplyServiceImpUnderTest.setDelegateRepository(mockDelegateRepository);
        giftSupplyServiceImpUnderTest.setDynamicQueries(mockDynamicQueries);
        giftSupplyServiceImpUnderTest.setUserRepository(mockUserService);
    }

    @Test
    public void testSaveGadgetSupply() throws Exception {
        // Setup
        final GiftSupply giftSupply = new GiftSupply();
        final Gift gift = new Gift();
        gift.setName("name");
        giftSupply.setGift(gift);
        final Delegate delegate = new Delegate();
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        giftSupply.setDelegate(delegate);
        giftSupply.setQuantity(0L);
        giftSupply.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final GiftSupplyDto gadgetSupplyDto = new GiftSupplyDto(giftSupply);

        // Configure GiftSupplyRepository.findOne(...).
        final GiftSupply giftSupply1 = new GiftSupply();
        final Gift gift1 = new Gift();
        gift1.setName("name");
        giftSupply1.setGift(gift1);
        final Delegate delegate1 = new Delegate();
        delegate1.setFirstName("firstName");
        delegate1.setLastName("lastName");
        giftSupply1.setDelegate(delegate1);
        giftSupply1.setQuantity(0L);
        giftSupply1.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGadgetSupplyRepository.findOne(0)).thenReturn(giftSupply1);

        when(mockGadgetRepository.findOne(0L)).thenReturn(new Gift(0L, "name", 0.0f));

        // Configure DelegateRepository.findOne(...).
        final Delegate delegate2 = new Delegate();
        delegate2.setId(0L);
        delegate2.setFirstName("firstName");
        delegate2.setHiringDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        delegate2.setLastName("lastName");
        delegate2.setImage("image");
        when(mockDelegateRepository.findOne(0L)).thenReturn(delegate2);

        // Configure GiftSupplyRepository.save(...).
        final GiftSupply giftSupply2 = new GiftSupply();
        final Gift gift2 = new Gift();
        gift2.setName("name");
        giftSupply2.setGift(gift2);
        final Delegate delegate3 = new Delegate();
        delegate3.setFirstName("firstName");
        delegate3.setLastName("lastName");
        giftSupply2.setDelegate(delegate3);
        giftSupply2.setQuantity(0L);
        giftSupply2.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        when(mockGadgetSupplyRepository.save(any(GiftSupply.class))).thenReturn(giftSupply2);

        // Run the test
        final GiftSupply result = giftSupplyServiceImpUnderTest.saveGadgetSupply(gadgetSupplyDto);

        // Verify the results
    }

    @Test
    public void testGetAllGadgetsSupply() throws Exception {
        // Setup
        // Configure GiftSupplyRepository.findAll(...).
        final GiftSupply giftSupply = new GiftSupply();
        final Gift gift = new Gift();
        gift.setName("name");
        giftSupply.setGift(gift);
        final Delegate delegate = new Delegate();
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        giftSupply.setDelegate(delegate);
        giftSupply.setQuantity(0L);
        giftSupply.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GiftSupply> giftSupplies = Arrays.asList(giftSupply);
        when(mockGadgetSupplyRepository.findAll()).thenReturn(giftSupplies);

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.getAllGadgetsSupply();

        // Verify the results
    }

    @Test
    public void testGetAllGadgetsSupply_GiftSupplyRepositoryReturnsNull() throws Exception {
        // Setup
        when(mockGadgetSupplyRepository.findAll()).thenReturn(null);

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.getAllGadgetsSupply();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAllGadgetsSupply_GiftSupplyRepositoryReturnsNoItems() throws Exception {
        // Setup
        when(mockGadgetSupplyRepository.findAll()).thenReturn(Collections.emptyList());

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.getAllGadgetsSupply();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindGadgetSupplyByUserAndDateAndGadget() throws Exception {
        // Setup
        when(mockUserService.getSubUsersIds()).thenReturn(Arrays.asList(0L));

        // Configure GiftSupplyRepository.findGadgetsSupplyByUserDateAndGadget(...).
        final GiftSupply giftSupply = new GiftSupply();
        final Gift gift = new Gift();
        gift.setName("name");
        giftSupply.setGift(gift);
        final Delegate delegate = new Delegate();
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        giftSupply.setDelegate(delegate);
        giftSupply.setQuantity(0L);
        giftSupply.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GiftSupply> giftSupplies = Arrays.asList(giftSupply);
        when(mockGadgetSupplyRepository.findGadgetsSupplyByUserDateAndGadget(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0, Arrays.asList(0L)))
                .thenReturn(giftSupplies);

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.findGadgetSupplyByUserAndDateAndGadget(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0);

        // Verify the results
    }

    @Test
    public void testFindGadgetSupplyByUserAndDateAndGadget_UserServiceReturnsNoItems() throws Exception {
        // Setup
        when(mockUserService.getSubUsersIds()).thenReturn(Collections.emptyList());

        // Configure GiftSupplyRepository.findGadgetsSupplyByUserDateAndGadget(...).
        final GiftSupply giftSupply = new GiftSupply();
        final Gift gift = new Gift();
        gift.setName("name");
        giftSupply.setGift(gift);
        final Delegate delegate = new Delegate();
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        giftSupply.setDelegate(delegate);
        giftSupply.setQuantity(0L);
        giftSupply.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GiftSupply> giftSupplies = Arrays.asList(giftSupply);
        when(mockGadgetSupplyRepository.findGadgetsSupplyByUserDateAndGadget(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0, Arrays.asList(0L)))
                .thenReturn(giftSupplies);

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.findGadgetSupplyByUserAndDateAndGadget(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0);

        // Verify the results
    }

    @Test
    public void testFindGadgetSupplyByUserAndDateAndGadget_GiftSupplyRepositoryReturnsNoItems() throws Exception {
        // Setup
        when(mockUserService.getSubUsersIds()).thenReturn(Arrays.asList(0L));
        when(mockGadgetSupplyRepository.findGadgetsSupplyByUserDateAndGadget(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0, Arrays.asList(0L)))
                .thenReturn(Collections.emptyList());

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.findGadgetSupplyByUserAndDateAndGadget(
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(), 0L, 0);

        // Verify the results
    }

    @Test
    public void testGetGadgetSupplyByUserGadgetAndDate() throws Exception {
        // Setup
        final GiftSupplyRequestDto gadgetSupplyRequestDto = new GiftSupplyRequestDto();
        gadgetSupplyRequestDto.setSelectedUser(0L);
        gadgetSupplyRequestDto.setFirstDate("firstDate");
        gadgetSupplyRequestDto.setLastDate("lastDate");
        gadgetSupplyRequestDto.setSelectedGift(0L);

        when(mockUserService.getSubUsersIds()).thenReturn(Arrays.asList(0L));

        // Configure DynamicQueries.findGadget(...).
        final GiftSupply giftSupply = new GiftSupply();
        final Gift gift = new Gift();
        gift.setName("name");
        giftSupply.setGift(gift);
        final Delegate delegate = new Delegate();
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        giftSupply.setDelegate(delegate);
        giftSupply.setQuantity(0L);
        giftSupply.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GiftSupplyDto> giftSupplyDtos = Arrays.asList(new GiftSupplyDto(giftSupply));
        when(mockDynamicQueries.findGadget("query", new HashMap<>())).thenReturn(giftSupplyDtos);

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.getGadgetSupplyByUserGadgetAndDate(
                gadgetSupplyRequestDto);

        // Verify the results
    }

    @Test
    public void testGetGadgetSupplyByUserGadgetAndDate_UserServiceReturnsNoItems() throws Exception {
        // Setup
        final GiftSupplyRequestDto gadgetSupplyRequestDto = new GiftSupplyRequestDto();
        gadgetSupplyRequestDto.setSelectedUser(0L);
        gadgetSupplyRequestDto.setFirstDate("firstDate");
        gadgetSupplyRequestDto.setLastDate("lastDate");
        gadgetSupplyRequestDto.setSelectedGift(0L);

        when(mockUserService.getSubUsersIds()).thenReturn(Collections.emptyList());

        // Configure DynamicQueries.findGadget(...).
        final GiftSupply giftSupply = new GiftSupply();
        final Gift gift = new Gift();
        gift.setName("name");
        giftSupply.setGift(gift);
        final Delegate delegate = new Delegate();
        delegate.setFirstName("firstName");
        delegate.setLastName("lastName");
        giftSupply.setDelegate(delegate);
        giftSupply.setQuantity(0L);
        giftSupply.setDeliveryDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        final List<GiftSupplyDto> giftSupplyDtos = Arrays.asList(new GiftSupplyDto(giftSupply));
        when(mockDynamicQueries.findGadget("query", new HashMap<>())).thenReturn(giftSupplyDtos);

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.getGadgetSupplyByUserGadgetAndDate(
                gadgetSupplyRequestDto);

        // Verify the results
    }

    @Test
    public void testGetGadgetSupplyByUserGadgetAndDate_DynamicQueriesReturnsNoItems() throws Exception {
        // Setup
        final GiftSupplyRequestDto gadgetSupplyRequestDto = new GiftSupplyRequestDto();
        gadgetSupplyRequestDto.setSelectedUser(0L);
        gadgetSupplyRequestDto.setFirstDate("firstDate");
        gadgetSupplyRequestDto.setLastDate("lastDate");
        gadgetSupplyRequestDto.setSelectedGift(0L);

        when(mockUserService.getSubUsersIds()).thenReturn(Arrays.asList(0L));
        when(mockDynamicQueries.findGadget("query", new HashMap<>())).thenReturn(Collections.emptyList());

        // Run the test
        final List<GiftSupplyDto> result = giftSupplyServiceImpUnderTest.getGadgetSupplyByUserGadgetAndDate(
                gadgetSupplyRequestDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testDeleteGadgetSupply() {
        // Setup
        // Run the test
        giftSupplyServiceImpUnderTest.deleteGadgetSupply(0);

        // Verify the results
        verify(mockGadgetSupplyRepository).delete(0);
    }
}
