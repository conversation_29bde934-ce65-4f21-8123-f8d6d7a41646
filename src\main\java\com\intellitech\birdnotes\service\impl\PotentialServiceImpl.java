package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.convertor.ConvertPotentialToDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("potentialService")
@Transactional
public class PotentialServiceImpl implements PotentialService {
	
	private PotentialRepository potentialRepository;
	private ConvertPotentialToDto convertPotentialToDto;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;
	
	@Autowired
	public PotentialServiceImpl(PotentialRepository potentialRepository,ConvertPotentialToDto convertPotentialToDto) {
		super();
		this.potentialRepository = potentialRepository;
		this.convertPotentialToDto=convertPotentialToDto;
	}

	@Override
	public void addAll( List<PotentialDto> potentialRequestDtos ) throws BirdnotesException{
		for(PotentialDto potentialRequestDto:potentialRequestDtos) {
		Potential potential = new Potential();
		potential.setName(potentialRequestDto.getName());
		 potentialRepository.save(potential);
		}
		}
	
	@Override
	public Potential add( PotentialDto potentialRequestDto ) throws BirdnotesException{
		
		Potential potential = new Potential();
		if (potentialRequestDto.getName() == null || "".equals(potentialRequestDto.getName())) {
			throw new BirdnotesException(Exceptions.POTENTIAL_NAME_IS_EMPTY);
		}
		Potential result = potentialRepository.findFirstByNameIgnoreCase(potentialRequestDto.getName());
		if(result!=null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}
		
		potential.setName(potentialRequestDto.getName());
		potential.setWeight(potentialRequestDto.getWeight());
		return potentialRepository.save(potential);
	}
	
	@Override
	public List<PotentialDto> findAll() throws BirdnotesException{
		List<PotentialDto> result = new ArrayList<>();
		List<Potential> allPotentials = potentialRepository.findAll();
		for(Potential potential: allPotentials) {
			PotentialDto p = new PotentialDto();
			p.setId(potential.getId());
			p.setName(potential.getName());
			p.setWeight(potential.getWeight());
			result.add(p);
		}
		return result;
	}
	
	@Override
	public void delete (long id)throws BirdnotesException {
			potentialRepository.deleteById(id);

	}
	
	@Override
	public Potential savePotential(PotentialDto potentialDto) throws BirdnotesException {
	    if (potentialDto == null || potentialDto.getId() == null) {
	        throw new BirdnotesException(Exceptions.POTENTIAL_DTO_IS_NULL);
	    }

	    Potential result = potentialRepository.findByNameAndAnotherId(potentialDto.getName(), potentialDto.getId());
	    if (result != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    Potential potential = null;
	    if (potentialDto.getId() != null) {
	        potential = potentialRepository.findOne(potentialDto.getId());
	    }
	    if (potential == null) {
	        potential = new Potential();
	    }

	    potential.setName(potentialDto.getName());
	    potential.setWeight(potentialDto.getWeight());
	    return potentialRepository.save(potential);
	}


	@Override
	public PotentialDto findPotentialByName(String name) throws BirdnotesException {
		Potential potential=potentialRepository.findByName(name);
		PotentialDto potentialDto=convertPotentialToDto.convert(potential);
		return potentialDto;
	}
	@Override
	public PotentialDto findPotentialDto(String potentialName,List<PotentialDto>potentialDtos)   {
		for (PotentialDto potentialDto : potentialDtos) {
			if (potentialDto.getName().equalsIgnoreCase(potentialName)) {
				return potentialDto;
			}
		}
		return null;
	}


}
