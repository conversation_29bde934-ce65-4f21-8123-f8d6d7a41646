package com.intellitech.birdnotes.controller;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.VisitAveragePerDayDto;
import com.intellitech.birdnotes.model.dto.VisitProspectDto;
import com.intellitech.birdnotes.service.VisitService;

@RestController
@RequestMapping("/charts")
public class VisitAverageController {

	private static final  Logger LOG = LoggerFactory.getLogger(VisitAverageController.class);

	@Autowired
	private VisitService visitService;

	@RequestMapping(value = "visitAveragePerDay/{mondayDate}", method = RequestMethod.GET)
	public ResponseEntity<List<VisitAveragePerDayDto>> visitAveragePerDay(@PathVariable("mondayDate") Date mondayDate) {
		try {
			List<VisitAveragePerDayDto> result = visitService.visitAveragePerDay(mondayDate);
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in visitAveragePerDay", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}

	}
	
	@RequestMapping(value = "visitAveragePerMonth/{month}/{year}", method = RequestMethod.GET)
	public ResponseEntity<List<VisitAveragePerDayDto>> visitAveragePerMonth(@PathVariable("month") Integer month, @PathVariable("year") Integer year) {
		try {
			List<VisitAveragePerDayDto> result = visitService.visitAveragePerMonth(month, year);
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in visitAveragePerDay", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}

	}

	@RequestMapping(value = "visitProspect/{mondayDate}/{specialityId}", method = RequestMethod.GET)
	public ResponseEntity<List<VisitProspectDto>> visitProspect(@PathVariable("mondayDate") Date mondayDate, @PathVariable("specialityId") Long specialityId) {
		try {
			List<VisitProspectDto> result = visitService.visitProspect(mondayDate, specialityId);
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in visitProspect", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
}
