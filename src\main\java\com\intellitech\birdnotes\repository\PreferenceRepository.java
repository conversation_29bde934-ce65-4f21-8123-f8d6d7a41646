package com.intellitech.birdnotes.repository;


import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.intellitech.birdnotes.model.Preference;


@Repository
public interface PreferenceRepository extends JpaRepository<Preference, Long> {
	
	@Query("SELECT p from Preference p where  LOWER(name) = LOWER(?1)")
	Preference findByName(String name);
	
	
	
}