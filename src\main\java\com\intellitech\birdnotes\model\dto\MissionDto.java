package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class MissionDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long sampleSupplyId;
	private Long sectorId;

	public Long getSectorId() {
		return sectorId;
	}

	public void setSectorId(Long sectorId) {
		this.sectorId = sectorId;
	}

	private Date endDate;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getSampleSupplyId() {
		return sampleSupplyId;
	}

	public void setSampleSupplyId(Long sampleSupplyId) {
		this.sampleSupplyId = sampleSupplyId;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

}
