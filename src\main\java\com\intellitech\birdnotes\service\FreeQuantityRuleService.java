package com.intellitech.birdnotes.service;

import java.text.ParseException;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.FreeQuantityRule;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto;
import com.intellitech.birdnotes.model.dto.ProductDto;

public interface FreeQuantityRuleService {

	void delete(long id) throws BirdnotesException;

	FreeQuantityRule saveFreeQuantityRule(FreeQuantityRuleDto freeQuantityRuleDto) throws BirdnotesException;

	List<FreeQuantityRuleItemDto> getFreeQuantityRuleItem(FreeQuantityRuleDto freeQuantityRuleRequestDto);

	List<FreeQuantityRuleDto> getFreeQuantityRule() throws BirdnotesException, ParseException;

	List<FreeQuantityRuleItemDto> findFreeQuantityRules() throws BirdnotesException;

	void deleteItem(long id) throws BirdnotesException;

	List<ProductDto> getProductWithoutRule(List<Long> prospectTypeList) throws BirdnotesException;

	

}
