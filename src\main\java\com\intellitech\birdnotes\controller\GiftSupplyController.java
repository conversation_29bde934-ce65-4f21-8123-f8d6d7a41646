package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.GiftSupply;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.GiftSupplyDto;
import com.intellitech.birdnotes.model.dto.GiftSupplyRequestDto;

import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.GadgetSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/giftsSupply")
public class GiftSupplyController {
	private static final Logger LOG = LoggerFactory.getLogger(GiftSupplyController.class);
	@Autowired
	private GadgetSupplyService gadgetSupplyService;
	@Autowired
	UserService userService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	private GadgetService gadgetService;
	@Autowired
	DelegateService delegateService;
	
	@RequestMapping(value = "/saveGiftSupply", method = RequestMethod.POST)
	public ResponseEntity<String> saveGadgetSupply(@RequestBody GiftSupplyDto gadgetSupplyDto) {
		try {
			if (userService.checkHasPermission("AFFECTATION_GADGET_ADD")) {
				GiftSupply gadgetSupplySaved = gadgetSupplyService.saveGadgetSupply(gadgetSupplyDto);
				if (gadgetSupplySaved != null) {
					return new ResponseEntity<String>(Exceptions.OK, HttpStatus.OK);
				}
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add gadget supply", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new gadget supply", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllGiftsSupply", method = RequestMethod.GET)
	public ResponseEntity<List<GiftSupplyDto>> getAllGadgetsSupply() {
		try {
			if (userService.checkHasPermission("AFFECTATION_GADGET_VIEW")) {
				List<GiftSupplyDto> gadgetsSupplyDto = gadgetSupplyService.getAllGadgetsSupply();
				return new ResponseEntity<>(gadgetsSupplyDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in findAllGadgetsSupply", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/getGiftSupplyByUserGiftAndDate", method = RequestMethod.POST)

	public ResponseEntity<List<GiftSupplyDto>> getGiftSupplyByUserGadgetAndDate(@RequestBody GiftSupplyRequestDto GadgetSupplyRequestDto) {

		try {
			if (userService.checkHasPermission("AFFECTATION_GADGET_VIEW")) {
				List<GiftSupplyDto> gadgetsSupplyDto = gadgetSupplyService.getGadgetSupplyByUserGadgetAndDate(GadgetSupplyRequestDto);
				return new ResponseEntity<>(gadgetsSupplyDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {

			LOG.error("An exception occurred while getting all gadgetsSupply", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("AFFECTATION_GADGET_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "findAllGift", method = RequestMethod.GET)
	public ResponseEntity<List<GiftDto>> findAllGadget() {

		try {
			if (userService.checkHasPermission("AFFECTATION_GADGET_VIEW")) {
				List<GiftDto> gadgetDto = gadgetService.findAll();
				return new ResponseEntity<>(gadgetDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all gadget", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	
	@RequestMapping(value = "deleteGiftSupply/{gadgetSupplyId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteGadgetSupply(@PathVariable("gadgetSupplyId") Integer gadgetSupplyId) {
		try {
			if (userService.checkHasPermission("SAMPLE_DELETE")) {
				gadgetSupplyService.deleteGadgetSupply(gadgetSupplyId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		}
	    catch (DataIntegrityViolationException e) {	
	    	
		LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
		return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	
	    }
		 catch (Exception e) {
			LOG.error("Error in deleteGiftSupply", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "updateGiftSupply", method = RequestMethod.PUT)
	public ResponseEntity<String> updateGadgetSupply(@RequestBody GiftSupplyDto gadgetSupplyDto) {
		try {
			if (userService.checkHasPermission("SAMPLE_EDIT")) {
				gadgetSupplyService.saveGadgetSupply(gadgetSupplyDto);
				
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException fe) {
			LOG.error("An exception occurred :Non-Authoritative Information when update gadget supply", fe);
			return new ResponseEntity<>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in updateGadgetSupply", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
