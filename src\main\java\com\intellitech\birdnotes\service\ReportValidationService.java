package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.ReportValidationDto;
import com.intellitech.birdnotes.model.dto.StatusReportValidation;
import com.intellitech.birdnotes.model.request.ReportValidationRequest;

public interface ReportValidationService {
	List<ReportValidationDto> getReportsValidationByDateAndUser(ReportValidationRequest reportValidationRequest)
			throws BirdnotesException;;

	void updateStatusReportValidation(StatusReportValidation statusReportValidation);

	void validateVisitReport(Date visitDate, Delegate delegate);

}
