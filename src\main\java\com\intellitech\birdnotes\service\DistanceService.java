package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import org.springframework.http.ResponseEntity;

import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.dto.ActionMarketingDto;
import com.intellitech.birdnotes.model.dto.ActionMarketingResponseDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.request.ActionMarketingRequest;

public interface DistanceService {

	ResponseEntity<DistanceResponse> distance(Double latOrigin, Double lngOrigin, Double latDest, Double lngDest)
			throws BirdnotesException;
	
}
