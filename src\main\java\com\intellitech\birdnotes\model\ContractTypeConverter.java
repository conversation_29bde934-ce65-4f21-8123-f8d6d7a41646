package com.intellitech.birdnotes.model;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

import com.intellitech.birdnotes.enumeration.ContractType;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Converter(autoApply = true)
public class ContractTypeConverter implements AttributeConverter<ContractType, String>{

	@Override
	public String convertToDatabaseColumn(ContractType contractType) {
		if(ContractType.CDI.equals(contractType)) {
			return BirdnotesConstants.ContractType.CDI;
		}
		if(ContractType.CDD.equals(contractType)) {
			return BirdnotesConstants.ContractType.CDD;
		}
		return BirdnotesConstants.ContractType.SIVP;
	}

	@Override
	public ContractType convertToEntityAttribute(String dbData) {
		if(dbData.equals(BirdnotesConstants.ContractType.CDI)) {
			return ContractType.CDI;
		}
		if(dbData.equals(BirdnotesConstants.ContractType.CDD))  {
			return ContractType.CDD;
		}
		return ContractType.SIVP;
	}

}
