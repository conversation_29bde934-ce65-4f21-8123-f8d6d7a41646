package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.dto.AutomationRuleDto;
import com.intellitech.birdnotes.model.dto.InterestDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;

import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.InterestService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/interests")
public class InterestController {

	private static final Logger LOG = LoggerFactory.getLogger(InterestController.class);

	@Autowired
	private InterestService interestService;

	@Autowired
	UserService userService;
	
	@RequestMapping(value = "/findAllInerests", method = RequestMethod.GET)
	public ResponseEntity<List<InterestDto>> getAllInterest() {
		try {
			if (userService.checkHasPermission("AUTOMATION_RULE_VIEW")) {
				List<InterestDto> result = interestService.findAll();
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all interests ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}}
	
	@RequestMapping(value = "/saveInterest", method = RequestMethod.POST)
	public ResponseEntity<String> saveInterest(@RequestBody InterestDto interestDto) {
	    try {
	        if (userService.checkHasPermission("INTEREST_EDIT")) {
	        	Interest savedInterest = interestService.saveInterest(interestDto);
	            if (savedInterest != null) {
	                return new ResponseEntity<>(savedInterest.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving interest", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving interest", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}
	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteInterest(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("INTEREST_DELETE")) {
	            interestService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting interest", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the interest with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}
	
	

	}