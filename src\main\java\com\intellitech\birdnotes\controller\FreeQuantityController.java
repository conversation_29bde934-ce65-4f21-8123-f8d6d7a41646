package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.FreeQuantityRule;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.CommissionDto;
import com.intellitech.birdnotes.model.dto.CommissionItemDto;
import com.intellitech.birdnotes.model.dto.DelegateCommissionDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.CommissionService;
import com.intellitech.birdnotes.service.FreeQuantityRuleService;
import com.intellitech.birdnotes.service.SampleSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/freeQuantityRule")
public class FreeQuantityController {
	private static final Logger LOG = LoggerFactory.getLogger(FreeQuantityController.class);
	@Autowired
	private FreeQuantityRuleService freeQuantityRuleService;
	@Autowired
	UserService userService;
	@Autowired
	private PotentialService potentialService;
	@Autowired
	private ProductService productService;
	@Autowired
	private ProspectTypeService prospectTypeService;



	@RequestMapping(value = "/saveFreeQuantityRule", method = RequestMethod.POST)
	public ResponseEntity<Long> saveFreeQuantityRule(@RequestBody FreeQuantityRuleDto freeQuantityDto) {
		try {
			if (userService.checkHasPermission("FREE_QUANTITY_ADD")|| userService.checkHasPermission("FREE_QUANTITY_EDIT")) {
				FreeQuantityRule savedFreeQuantityRule = freeQuantityRuleService.saveFreeQuantityRule(freeQuantityDto);
				if (savedFreeQuantityRule != null) {
					return new ResponseEntity<>(savedFreeQuantityRule.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when saving free quantity", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving free quantity", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteFreeQuantityRule(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("FREE_QUANTITY_DELETE")) {
	            freeQuantityRuleService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting free quantity rule", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the free quantity rule with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "/deleteItem/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteFreeQuantityRuleItem(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("FREE_QUANTITY_DELETE")) {
	            freeQuantityRuleService.deleteItem(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting free quantity rule item", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the free quantity rule item with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "getFreeQuantityRule", method = RequestMethod.POST)
	public ResponseEntity<List<FreeQuantityRuleDto>> getFreeQuantityRule() {

		try {
			if (userService.checkHasPermission("FREE_QUANTITY_VIEW")) {
				List<FreeQuantityRuleDto> freeQuantityDto = freeQuantityRuleService.getFreeQuantityRule();
				return new ResponseEntity<>(freeQuantityDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all freeQuantityDto", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	
	@RequestMapping(value = "/getFreeQuantityRuleItem", method = RequestMethod.POST)

	public ResponseEntity<List<FreeQuantityRuleItemDto>> getFreeQuantityRuleItem(
			@RequestBody FreeQuantityRuleDto freeQuantityRuleRequestDto) {

		try {
			if (userService.checkHasPermission("FREE_QUANTITY_VIEW")) {
				List<FreeQuantityRuleItemDto> freeQuantityRuleItemDto = freeQuantityRuleService
						.getFreeQuantityRuleItem(freeQuantityRuleRequestDto);
				return new ResponseEntity<>(freeQuantityRuleItemDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting FreeQuantityRuleItemDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "findAllPotentials", method = RequestMethod.GET)
	public ResponseEntity<List<PotentialDto>> findAllPotentials() {

		try {
			if (userService.checkHasPermission("FREE_QUANTITY_VIEW")) {
				List<PotentialDto> potentialsDto = potentialService.findAll();
				return new ResponseEntity<>(potentialsDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all potentials", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findAllProspectTypes", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectTypeDto>> findAllProspectTypes() {
		try {
			if (userService.checkHasPermission("FREE_QUANTITY_VIEW") || userService.checkHasPermission("FREE_QUANTITY_ADD") || userService.checkHasPermission("FREE_QUANTITY_EDIT")) {
				List<ProspectTypeDto> prospectTypeDto = prospectTypeService.findAll();
				return new ResponseEntity<>(prospectTypeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all prospect types", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	
	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("FREE_QUANTITY_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "getProductWithoutRule", method = RequestMethod.POST)
	public ResponseEntity<List<ProductDto>> getProductWithoutRule(@RequestBody List<Long> prospectTypeList ) {
		try {
			if (userService.checkHasPermission("FREE_QUANTITY_VIEW")) {
				List<ProductDto> productDtos = freeQuantityRuleService.getProductWithoutRule(prospectTypeList);
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getProductWithoutRule", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	
}
