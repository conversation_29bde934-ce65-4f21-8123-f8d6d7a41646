package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.util.List;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.DelegateCommission;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.ValidationType;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.model.dto.ValidationStepDto;
import com.intellitech.birdnotes.model.request.ValidationStepRequest;

public interface ValidationStepService {
	
	
    public boolean accept(List<ValidationStatus> allValidationStatus, Long planningValidationId, Long entityId) ;
	public void refuse (Long planningValidationId);
	public void review (Long planningValidationId);
	boolean add (ValidationStepRequest validationStepRequest) throws BirdnotesException;
	List<ValidationType> getValidationTypeXml() throws IOException;
	List<ValidationStepDto> findValidationStepByType(String validationType)throws BirdnotesException;
	void deleteByValidationType(String validationType);
	void addValidationStatus (String validationType , long id,long idDelegate) throws BirdnotesException;
	
	List<ValidationStatusDto> findValidationStatusNoteFrais() throws BirdnotesException;
	List <ValidationStatusDto> findByNoteFrais (ExpenseReport noteFrais) throws BirdnotesException;
	List <ValidationStatusDto> findByPlanningValidation (PlanningValidation planningValidation) throws BirdnotesException;
	List<ValidationStatusDto> findByActionMarketing(ActionMarketing actionMarketing) throws BirdnotesException;
	List<ValidationStatusDto> findByProspect(long prospectId) throws BirdnotesException;
	List<ValidationStatusDto> findByDelegateCommission(DelegateCommission delegateCommission) throws BirdnotesException;
	void cancelAccept(List<ValidationStatus> allValidationStatus, Long validationStatusId, Long entityId);

}
