package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

import com.intellitech.birdnotes.enumeration.HolidayType;

public class HolidayDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Integer day;
	private Integer month;
	private HolidayType holidayType;
	private Date date;

	public HolidayDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	public HolidayType getHolidayType() {
		return holidayType;
	}

	public void setHolidayType(HolidayType holidayType) {
		this.holidayType = holidayType;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

}
