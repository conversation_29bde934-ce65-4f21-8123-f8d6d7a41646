package com.intellitech.birdnotes.model;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.ACTIONMARKETING, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ActionMarketing {
	@Id
	@SequenceGenerator(name = Sequences.ACTIONMARKETING_SEQUENCE, sequenceName = Sequences.ACTIONMARKETING_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.ACTIONMARKETING_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.IDENTIFIER)
	private Long identifier;
	
	@Column(name = Columns.NAME)
	private String name;

	@Column(name = Columns.BUDGET)
	private Float budget;

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.ACTION_MARKETING_DATE)
	private Date date;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.ACTIONMARKETING_PROSPECTS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.IDPROSPECT, nullable = false, updatable = false) })
	private Set<Prospect> prospects;

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.ACTIONMARKETING_PRODUCTS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, nullable = false, updatable = false) })
	private Set<Product> products;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.MARKETING_ACTION_TYPE_ID)
	private MarketingActionType marketingActionType;

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	private UserValidationStatus status;
	
	@Column(name = BirdnotesConstants.Columns.DESCRIPTION_EXPENSE_REPORT, length = BirdnotesConstants.Numbers.N_255)
	private String description;
	
	 @OneToMany(mappedBy = "actionMarketing")
	 private List<ValidationStatus> validationStatus;

	public ActionMarketing(Long id, Long identifier, String name, Float budget, Date date, User user, Set<Prospect> prospects,
			Set<Product> products, UserValidationStatus status) {
		super();
		this.id = id;
		this.identifier = identifier;
		this.name = name;
		this.budget = budget;
		this.date = date;
		this.user = user;
		this.prospects = prospects;
		this.products = products;
		this.status = status;
	}

	public ActionMarketing() {
		super();
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}
	
	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public Set<Product> getProducts() {
		return products;
	}

	public void setProducts(Set<Product> products) {
		this.products = products;
	}

	public Set<Prospect> getProspects() {
		return prospects;
	}

	public void setProspects(Set<Prospect> prospects) {
		this.prospects = prospects;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Float getBudget() {
		return budget;
	}

	public void setBudget(Float budget) {
		this.budget = budget;
	}
	
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public List<ValidationStatus> getValidationStatus() {
		return validationStatus;
	}

	public void setValidationStatus(List<ValidationStatus> validationStatus) {
		this.validationStatus = validationStatus;
	}

	public MarketingActionType getMarketingActionType() {
		return marketingActionType;
	}

	public void setMarketingActionType(MarketingActionType marketingActionType) {
		this.marketingActionType = marketingActionType;
	}

}