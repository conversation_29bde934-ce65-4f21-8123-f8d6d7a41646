package com.intellitech.birdnotes.model.dto;

import java.util.Set;

import com.intellitech.birdnotes.model.Role;

public class UserTransferDto {

	private String firstLastName;
	private String token;
	private String workType;
	private Integer workingDays;
	private Integer syncCycle;
	private Boolean autoSync;
	private Boolean autoExpense;
	private String orderValidation;
	private Boolean lockAfterSync;
	private Integer openReportPeriod;
	private Integer openExpensePeriod;
	private Boolean multiWholesaler;
	private Set<Role> roles;
	private String commentsDictionary;
	private String biPanels;
	private  Set<String>  permissions;
	//ajouter la liste des permissions
	
	private Long userId;

	public UserTransferDto(String firstLastName, String token, Boolean isDelegate, Boolean autoSync, Boolean autoExpense, String orderValidation,
			Boolean lockAfterSync, Integer openReportPeriod,  Integer openExpensePeriod, Boolean multiWholesaler, Integer syncCycle, String workType, Integer workingDays, Set<Role> roles, Set<String> permissions) {
		super();
		this.firstLastName = firstLastName;
		this.token = token;
		this.autoSync = autoSync;
		this.autoExpense = autoExpense;
		this.orderValidation = orderValidation;
		this.lockAfterSync = lockAfterSync;
		this.openReportPeriod = openReportPeriod;
		this.openExpensePeriod = openExpensePeriod;
		this.multiWholesaler = multiWholesaler;
		this.workType = workType;
		this.workingDays = workingDays;
		this.roles=roles;
		this.syncCycle=syncCycle;
		this.permissions=permissions;
	}

	public Set<Role> getRoles() {
		return roles;
	}

	
	public Boolean getAutoSync() {
		return autoSync;
	}

	public void setAutoSync(Boolean autoSync) {
		this.autoSync = autoSync;
	}
	
	public Boolean getAutoExpense() {
		return autoExpense;
	}

	public void setAutoExpense(Boolean autoExpense) {
		this.autoExpense = autoExpense;
	}
	
	

	public String getOrderValidation() {
		return orderValidation;
	}

	public void setOrderValidation(String orderValidation) {
		this.orderValidation = orderValidation;
	}

	public  Set<String> getPermissions() {
		return permissions;
	}

	public void setPermissions( Set<String>  permissions) {
		this.permissions = permissions;
	}

	
	public Boolean getLockAfterSync() {
		return lockAfterSync;
	}

	public void setLockAfterSync(Boolean lockAfterSync) {
		this.lockAfterSync = lockAfterSync;
	}

	
	
	public Integer getOpenReportPeriod() {
		return openReportPeriod;
	}

	public void setOpenReportPeriod(Integer openReportPeriod) {
		this.openReportPeriod = openReportPeriod;
	}

	
	
	public Integer getOpenExpensePeriod() {
		return openExpensePeriod;
	}

	public void setOpenExpensePeriod(Integer openExpensePeriod) {
		this.openExpensePeriod = openExpensePeriod;
	}

	public Boolean getMultiWholesaler() {
		return multiWholesaler;
	}

	public void setMultiWholesaler(Boolean multiWholesaler) {
		this.multiWholesaler = multiWholesaler;
	}

	public void setRoles(Set<Role> roles) {
		this.roles = roles;
	}


	public UserTransferDto() {
		super();
	}

	public String getFirstLastName() {
		return firstLastName;
	}

	public void setFirstLastName(String firstLastName) {
		this.firstLastName = firstLastName;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}


	public Integer getSyncCycle() {
		return syncCycle;
	}

	public void setSyncCycle(Integer syncCycle) {
		this.syncCycle = syncCycle;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	public Long getUserId() {
		return userId;
	}

	public String getBiPanels() {
		return biPanels;
	}

	public void setBiPanels(String biPanels) {
		this.biPanels = biPanels;
	}

	
	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getCommentsDictionary() {
		return commentsDictionary;
	}

	public void setCommentsDictionary(String commentsDictionary) {
		this.commentsDictionary = commentsDictionary;
	}

	public Integer getWorkingDays() {
		return workingDays;
	}

	public void setWorkingDays(Integer workingDays) {
		this.workingDays = workingDays;
	}
	
	
}