package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class VisitProductDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Long identifier;
	private Long productId;
	private Long visitId;
	private Long purchaseOrderId;
	private String comment;
	private Integer orderQuantity;
	private Integer sampleQuantity;
	private Integer rank;
	private Integer smily;
	private Integer saleQuantity;
	private boolean urgent;
	private Integer prescriptionQuantity;
    private Integer freeOrder;
    private Integer labGratuity;
  
    
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}

	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}
	public Integer getOrderQuantity() {
		return orderQuantity;
	}
	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}
	public Integer getSampleQuantity() {
		return sampleQuantity;
	}
	public void setSampleQuantity(Integer sampleQuantity) {
		this.sampleQuantity = sampleQuantity;
	}
	public Integer getRank() {
		return rank;
	}
	public void setRank(Integer rank) {
		this.rank = rank;
	}
	public Integer getSmily() {
		return smily;
	}
	public void setSmily(Integer smily) {
		this.smily = smily;
	}
	public Integer getSaleQuantity() {
		return saleQuantity;
	}
	public void setSaleQuantity(Integer saleQuantity) {
		this.saleQuantity = saleQuantity;
	}
	public boolean isUrgent() {
		return urgent;
	}
	public void setUrgent(boolean urgent) {
		this.urgent = urgent;
	}
	public Integer getPrescriptionQuantity() {
		return prescriptionQuantity;
	}
	public void setPrescriptionQuantity(Integer prescriptionQuantity) {
		this.prescriptionQuantity = prescriptionQuantity;
	}
	public Integer getFreeOrder() {
		return freeOrder;
	}
	public void setFreeOrder(Integer freeOrder) {
		this.freeOrder = freeOrder;
	}
	public Integer getLabGratuity() {
		return labGratuity;
	}
	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}
	public Long getProductId() {
		return productId;
	}
	public void setProductId(Long productId) {
		this.productId = productId;
	}
	public Long getVisitId() {
		return visitId;
	}
	public void setVisitId(Long visitId) {
		this.visitId = visitId;
	}
	public Long getIdentifier() {
		return identifier;
	}
	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}
	public Long getPurchaseOrderId() {
		return purchaseOrderId;
	}
	public void setPurchaseOrderId(Long purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}

	
	
	
}