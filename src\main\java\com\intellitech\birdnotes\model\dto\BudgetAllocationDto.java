package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

import com.intellitech.birdnotes.model.User;

public class BudgetAllocationDto implements Serializable {
    
    private static final long serialVersionUID = 1L;
    private Long id;
    private Integer year;
    private Float monthlyBudget;
    private String type;
    private Long userId;
    private String username;
    
    public BudgetAllocationDto() {
        super();
    }
    
    public BudgetAllocationDto(Long id, Integer year, Float monthlyBudget, String type, Long userId) {
        super();
        this.id = id;
        this.year = year;
        this.monthlyBudget = monthlyBudget;
        this.type = type;
        this.userId = userId;
    }

    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }

    public Integer getYear() {
        return year;
    }

    public void setYear(Integer year) {
        this.year = year;
    }

    public Float getMonthlyBudget() {
        return monthlyBudget;
    }

    public void setMonthlyBudget(Float monthlyBudget) {
        this.monthlyBudget = monthlyBudget;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}




}