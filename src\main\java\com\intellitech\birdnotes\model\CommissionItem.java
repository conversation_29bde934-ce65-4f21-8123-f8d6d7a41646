package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.COMMISSION_ITEM, schema = Common.PUBLIC_SCHEMA)
public class CommissionItem implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	private Float threshold;
	private Float value;
	private Commission commission;
    
	public CommissionItem() {
		super();
	}

	@Id
	@SequenceGenerator(name = Sequences.COMMISSION_ITEM_SEQUENCE, sequenceName = Sequences.COMMISSION_ITEM_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.COMMISSION_ITEM_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.COMMISSION_ID, unique = false)
	public Commission getCommission() {
		return commission;
	}

	public void setCommission(Commission commission) {
		this.commission = commission;
	}

	@Column(name = Columns.THRESHOLD)
	public Float getThreshold() {
		return threshold;
	}

	public void setThreshold(Float threshold) {
		this.threshold = threshold;
	}

	@Column(name = Columns.VALUE)
	public Float getValue() {
		return value;
	}

	public void setValue(Float value) {
		this.value = value;
	}



}
