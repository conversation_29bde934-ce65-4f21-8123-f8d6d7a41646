package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Entity
@Table(name = BirdnotesConstants.Tables.ROLE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Role implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.ROLE_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.ROLE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.ROLE_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Integer id;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = BirdnotesConstants.Columns.NAME, length = BirdnotesConstants.Numbers.N_35)
	private String name;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@Column(name = BirdnotesConstants.Columns.RANK)
	private Integer rank;
	

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	@OneToMany(mappedBy = "role", fetch = FetchType.EAGER)
	@JsonIgnore
	private Set<RolePermission> rolePermissions;

	public Set<RolePermission> getRolePermissions() {
		return rolePermissions;
	}

	public void setRolePermissions(Set<RolePermission> rolePermissions) {
		this.rolePermissions = rolePermissions;
	}
	
	@JsonIgnore
	@OneToMany(mappedBy = "role")
	private List<ValidationStep> validationSteps;

	public List<ValidationStep> getValidationSteps() {
		return validationSteps;
	}

	public void setValidationSteps(List<ValidationStep> validationSteps) {
		this.validationSteps = validationSteps;
	}

	@OneToMany(mappedBy = "role", fetch = FetchType.EAGER)
	@JsonIgnore
	private List<NotificationRule> notificationsManager;

	public List<NotificationRule> getNotificationsManager() {
		return notificationsManager;
	}

	public void setNotificationsManager(List<NotificationRule> notificationsManager) {
		this.notificationsManager = notificationsManager;
	}

	public Role() {
		super();
	}

	public Role(Integer id, String name) {
		super();
		this.id = id;
		this.name = name;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		Role other = (Role) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (name == null) {
			if (other.name != null)
				return false;
		} else if (!name.equals(other.name))
			return false;
		return true;
	}

}
