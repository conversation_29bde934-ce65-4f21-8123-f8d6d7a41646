package com.intellitech.birdnotes.model.convertor;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.RecoveryDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

@Component("recoveryToDtoConvertor")
public class RecoveryToDtoConvertor {

		private static final Logger LOG = LoggerFactory.getLogger(RecoveryToDtoConvertor.class);

		public RecoveryDto convert(Recovery recovery) throws BirdnotesException {

			if (recovery == null) {
				LOG.error("recovery is null");
				throw new BirdnotesException("recovery is null");
			}

			RecoveryDto recoveryDto = new RecoveryDto();
			recoveryDto.setId(recovery.getId());
			recoveryDto.setIdentifier(recovery.getIdentifier());
			recoveryDto.setAmount(recovery.getAmount());
			recoveryDto.setPayment(recovery.getPayment());
			recoveryDto.setDate(recovery.getDate());
			if(recovery.getAttachment() != null) {
				recoveryDto.setAttachmentId(recovery.getAttachment().getIdentifier());
				recoveryDto.setAttachmentBase64(recovery.getAttachment().getAttachmentBase64());
				recoveryDto.setAttachmentName(recovery.getAttachment().getAttachmentName());
			}
			if(recovery.getDescription() != null) {
				recoveryDto.setDescription(recovery.getDescription());
			}
			recoveryDto.setPurchaseOrderId(recovery.getPurchaseOrder().getIdentifier());
			//recoveryDto.setStringDate(recovery.getPayment());
			
			//wholesalerDto.setShopEmail(wholesaler.getShopEmail());
			return recoveryDto;

		}
}
