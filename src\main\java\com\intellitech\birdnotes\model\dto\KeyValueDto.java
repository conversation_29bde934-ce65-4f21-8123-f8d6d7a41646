package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class KeyValueDto implements Serializable {

	private static final long serialVersionUID = 1L;
	protected Long id;
	protected String name;
	protected Float value;
	protected Integer count;

	public KeyValueDto() {
		super();
	}
	
	
	public KeyValueDto(Long id, Long value) {
		this.value = new Float(value);
		this.id = id;
	}

	public KeyValueDto(String name, Long id) {
		this.name = name;
		this.id = id;
	}
	

	public KeyValueDto(Long id, String name, Float value) {
		super();
		this.id = id;
		this.name = name;
		this.value = value;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	

	public Integer getCount() {
		return count;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public Float getValue() {
		return value;
	}

	public void setValue(Float value) {
		this.value = value;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((name == null) ? 0 : name.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		KeyValueDto other = (KeyValueDto) obj;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (name == null) {
			if (other.name != null)
				return false;
		} else if (!name.equals(other.name))
			return false;
		return true;
	}
	
	

}
