{"version": "2.0.0", "tasks": [{"label": "maven: clean compile", "type": "shell", "command": "mvn", "args": ["clean", "compile"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$maven-compiler-java"}, {"label": "maven: clean package", "type": "shell", "command": "mvn", "args": ["clean", "package"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$maven-compiler-java"}, {"label": "maven: test", "type": "shell", "command": "mvn", "args": ["test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": "$maven-compiler-java"}, {"label": "spring-boot: run", "type": "shell", "command": "mvn", "args": ["spring-boot:run"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}