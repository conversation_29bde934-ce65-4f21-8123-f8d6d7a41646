package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class ActionMarketingFormDto {

	 List<LabelValueDto>  prospects;
	 List<LabelValueDto>  products;
	 List<MarketingActionTypeDto> marketingActionTypes;
	 List<UserDto> users;
	 
	 
	 
	public List<LabelValueDto> getProspects() {
		return prospects;
	}
	public void setProspects(List<LabelValueDto> prospects) {
		this.prospects = prospects;
	}
	public List<LabelValueDto> getProducts() {
		return products;
	}
	public void setProducts(List<LabelValueDto> products) {
		this.products = products;
	}
	public List<MarketingActionTypeDto> getMarketingActionTypes() {
		return marketingActionTypes;
	}
	public void setMarketingActionTypes(List<MarketingActionTypeDto> marketingActionTypes) {
		this.marketingActionTypes = marketingActionTypes;
	}
	public List<UserDto> getUsers() {
		return users;
	}
	public void setUsers(List<UserDto> users) {
		this.users = users;
	}
	 
	 
	 
}
