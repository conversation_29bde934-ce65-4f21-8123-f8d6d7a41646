package com.intellitech.birdnotes.model.convertor;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

@Component("purchaseOrderToDtoConvertor")
public class PurchaseOrderToDtoConvertor {

		private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderToDtoConvertor.class);

		public PurchaseOrderDto convert(PurchaseOrder purchaseOrder) throws BirdnotesException {

			if (purchaseOrder == null) {
				LOG.error("purchaseOrder is null");
				throw new BirdnotesException("purchaseOrder is null");
			}

			PurchaseOrderDto purchaseOrderDto = new PurchaseOrderDto();
			purchaseOrderDto.setId(purchaseOrder.getId());
			purchaseOrderDto.setIdentifier(purchaseOrder.getIdentifier());
			purchaseOrderDto.setAttachmentBase64(purchaseOrder.getAttachmentBase64());
			purchaseOrderDto.setAttachmentName(purchaseOrder.getAttachmentName());
			purchaseOrderDto.setPlacementMethod(purchaseOrder.getPlacementMethod());
			purchaseOrderDto.setVisitId(purchaseOrder.getVisit().getId());
			purchaseOrderDto.setWholesalerId(purchaseOrder.getWholesaler().getId());
			purchaseOrderDto.setStatus(purchaseOrder.getStatus().toString());
			
			
			//wholesalerDto.setShopEmail(wholesaler.getShopEmail());
			return purchaseOrderDto;

		}
}
