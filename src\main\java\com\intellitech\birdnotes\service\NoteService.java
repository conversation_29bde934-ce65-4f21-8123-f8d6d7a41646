package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.util.List;

import com.intellitech.birdnotes.data.dto.NoteFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Note;
import com.intellitech.birdnotes.model.dto.NoteDto;

public interface NoteService {

	Note saveNote(NoteDto noteDto) throws BirdnotesException;

	List<NoteDto> findAll() throws BirdnotesException;

	void delete(Long id) throws BirdnotesException;

	NoteFormData getAllDataNote() throws IOException, BirdnotesException;

}
