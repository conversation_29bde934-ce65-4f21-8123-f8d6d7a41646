package com.intellitech.birdnotes.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.dto.GiftSupplyDto;
import com.intellitech.birdnotes.model.dto.GoalSum;
import com.intellitech.birdnotes.model.dto.ProspectDistribution;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryGroupDto;
import com.intellitech.birdnotes.model.request.ProspectListRequest;;

public interface DynamicQueries {
	List<VisitHistoryGroupDto> findGroupSearch(String query, Map<String, Object> parameters);

	List<Prospect> findProspects(String query, Map<String, Object> parameters);

	List<ProspectDistribution> findProspectsDistribution(String query, Map<String, Object> parameters);

	List<PlanningObjectiveCountDto> findPlanifiedProspects(String query, Map<String, Object> parameters);

	// List<Prospect> findProspectsList(String query, Map<String, Object>
	// parameters, ProspectListRequest prospectListRequest);
	Long findProspectsCount(String query, Map<String, Object> parameters);

	List<ProspectDto> findProspectsListCartography(String query, Map<String, Object> parameters);

	List<GoalSum> findGoalsSum(String queryString, Date startDate, Date endDate);

	List<SampleSupply> findSample(String query, Map<String, Object> parameters);

	List<GiftSupplyDto> findGadget(String query, Map<String, Object> parameters);

	List<PurchaseOrderTemplate> findPurchaseOrderTemplate(String query, Map<String, Object> parameters);

	List<Commission> findCommission(String query, Map<String, Object> parameters);

	List<Prospect> findProspectsList(String queryString, Map<String, Object> parameters,
			ProspectListRequest prospectListRequest, Integer limit);

	List<Long> findPlanningOfWeeksToShow(String query, Map<String, Object> parameters);

	List<Prospect> findPatients(String query);

	List<VisitsProducts> findVisits(String query, Map<String, Object> parameters,Integer first,Integer rows);

	Long findVisitProductsCount(String queryString, Map<String, Object> parameters);

}