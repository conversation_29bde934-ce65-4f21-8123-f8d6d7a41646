package com.intellitech.birdnotes.model.convertor;

import java.io.File;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.FileNamePath;

@Component("convertNoteFraisToDto")
public class ConvertExpenseReportToDto {

	private static final Logger LOG = LoggerFactory.getLogger(ConvertExpenseReportToDto.class);

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${uploadPath}")
	private String uploadPath;
	@Value("${expenseReportPath}")
	private String expenseReportPath;

	public ConvertExpenseReportToDto() {
		super();
	}

	public ConvertExpenseReportToDto(String uploadUrl, String uploadPath) {
		this.uploadUrl = uploadUrl;
		this.uploadPath = uploadPath;
	}

	public String getUploadUrl() {
		return uploadUrl;
	}

	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}

	public String getUploadPath() {
		return uploadPath;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	public String getExpenseReportPath() {
		return expenseReportPath;
	}

	public void setExpenseReportPath(String expenseReportPath) {
		this.expenseReportPath = expenseReportPath;
	}

	public ExpenseReportDto convert(ExpenseReport noteFrais) throws BirdnotesException {

		if (noteFrais == null) {
			LOG.error("noteFrais is null");
			throw new BirdnotesException("noteFrais is null");
		}
		ExpenseReportDto noteFraisDto = new ExpenseReportDto();
		noteFraisDto.setId(noteFrais.getId());
		noteFraisDto.setDate(noteFrais.getDate());
		if (noteFrais.getStatus() != null) {
			noteFraisDto.setStatus(noteFrais.getStatus().toString());
		}
		noteFraisDto.setIdentifier(noteFrais.getIdentifier());
		Calendar cal = Calendar.getInstance();
		cal.setTime(noteFrais.getDate());
		int year = cal.get(Calendar.YEAR);
		int month = cal.get(Calendar.MONTH) + 1;
		int day = cal.get(Calendar.DAY_OF_MONTH);
		noteFraisDto.setExpenseDate(day + "/" + month + "/" + year);
		noteFraisDto.setDay(day);
		noteFraisDto.setMonth(month);
		noteFraisDto.setYear(year);
		noteFraisDto.setDescription(noteFrais.getDescription());
		noteFraisDto.setMontant(noteFrais.getMontant());
		noteFraisDto.setAttachmentBase64(noteFrais.getAttachmentBase64());
		noteFraisDto.setAttachmentName(noteFrais.getPieceJointe());
		noteFraisDto.setDelegateId(noteFrais.getDelegate().getId());
		noteFraisDto.setExpenseTypeId(noteFrais.getExpenseType().getId());
		noteFraisDto.setExpenseTypeName(noteFrais.getExpenseType().getName());
		String path = uploadPath + expenseReportPath + File.separator + noteFrais.getId();
		File filePath = new File(path);
		noteFraisDto.setPieceJointe(path);
		String[] file = filePath.list();
		if (file != null) {
			List<FileNamePath> listFichiers = new ArrayList<>();
			for (int i = 0; i < file.length; i++) {
				File fileNoteFrais = new File(file[i]);
				FileNamePath fileNamePath = new FileNamePath(fileNoteFrais.getName(), uploadUrl + expenseReportPath
						+ File.separator + noteFrais.getId() + File.separator + fileNoteFrais.getName());
				listFichiers.add(fileNamePath);
				noteFraisDto.setNameFile(listFichiers);
			}
		}
		if (noteFrais.getActivity() != null) {
			noteFraisDto.setActivityTypeName(noteFrais.getActivity().getActivityType().getName());
			noteFraisDto.setActivityComment(noteFrais.getActivity().getComment());
		}

		ExpenseTypeDto typeNoteFraisDto = new ExpenseTypeDto();
		ExpenseType typeNoteFrais = noteFrais.getExpenseType();
		if (typeNoteFrais != null) {
			typeNoteFraisDto.setName(typeNoteFrais.getName());
			typeNoteFraisDto.setId(typeNoteFrais.getId());
			typeNoteFraisDto.setPrice(typeNoteFrais.getPrice());

		} else {
			LOG.error("typeNoteFrais is null");
			throw new BirdnotesException("typeNoteFrais is null");
		}
		noteFraisDto.setExpenseTypeDto(typeNoteFraisDto);

		DelegateDto delegateDto = new DelegateDto();
		Delegate delegate = noteFrais.getDelegate();
		if (delegate != null) {
			delegateDto.setFirstName(delegate.getFirstName());
			delegateDto.setId(delegate.getId());

		} else {
			LOG.error("delegate is null");
			throw new BirdnotesException("delegate is null");
		}
		noteFraisDto.setDelegateDto(delegateDto);

		if (noteFrais.getDelegate() == null) {
			throw new BirdnotesException("delegate is null");
		}
		noteFraisDto
				.setDelegateName(noteFrais.getDelegate().getFirstName() + " " + noteFrais.getDelegate().getLastName());

		return noteFraisDto;

	}

}
