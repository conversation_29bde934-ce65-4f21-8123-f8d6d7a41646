package com.intellitech.birdnotes.model.dto;

public class PurchaseOrderTemplateItemDto {
	private Long id;
	private Long purchaseOrderTemplateId;
	private Long productId;
	private String productName;
	private Integer quantity;
	private Integer freeOrder;
	private Integer labGratuity;
	
	public PurchaseOrderTemplateItemDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getPurchaseOrderTemplateId() {
		return purchaseOrderTemplateId;
	}

	public void setPurchaseOrderTemplateId(Long purchaseOrderTemplateId) {
		this.purchaseOrderTemplateId = purchaseOrderTemplateId;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public Integer getQuantity() {
		return quantity;
	}

	public void setQuantity(Integer quantity) {
		this.quantity = quantity;
	}

	public Integer getFreeOrder() {
		return freeOrder;
	}

	public void setFreeOrder(Integer freeOrder) {
		this.freeOrder = freeOrder;
	}

	public Integer getLabGratuity() {
		return labGratuity;
	}

	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}

	
}
