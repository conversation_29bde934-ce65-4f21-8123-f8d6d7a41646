package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.PRODUCT, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Product implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String name;
	private Float price;
	private Float buyingPrice;
	private Integer numberOfCapsules;
	private Integer stock;
	private String description;
	private Set<Range> ranges = new HashSet<>();
	private Long version;
	private String quantityUnit;
	private String code;
    private List<GoalItem> goalItems;
	private SampleSupplyItem samplesSupplyItem;
	private Set<Commission> commissions;
	private Float vat;
	public Product() {
		super();

	}

	@Id
	@SequenceGenerator(name = Sequences.PRODUCT_SEQUENCE, sequenceName = Sequences.PRODUCT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PRODUCT_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = BirdnotesConstants.Columns.NAME, length = BirdnotesConstants.Numbers.N_85)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = BirdnotesConstants.Columns.PRICE)
	public Float getPrice() {
		return price;
	}

	public void setPrice(Float price) {
		this.price = price;
	}
	
	@Column(name = BirdnotesConstants.Columns.BUYING_PRICE)
		public Float getBuyingPrice() {
			return buyingPrice;
		}
	
	public void setBuyingPrice(Float buyingPrice) {
				this.buyingPrice = buyingPrice;
			}

	@Column(name = BirdnotesConstants.Columns.NUMBER_OF_CAPSULES)
	public Integer getNumberOfCapsules() {
		return numberOfCapsules;
	}

	public void setNumberOfCapsules(Integer numberOfCapsules) {
		this.numberOfCapsules = numberOfCapsules;
	}
	
	@Column(name = BirdnotesConstants.Columns.STOCK)
	public Integer getStock() {
		return stock;
	}

	public void setStock(Integer stock) {
		this.stock = stock;
	}

	@Column(name = BirdnotesConstants.Columns.DESCRIPTION, length = BirdnotesConstants.Numbers.N_1024)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.RANGES_PRODUCTS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.RANGE_ID, nullable = false, updatable = false) })
	public Set<Range> getRanges() {
		return ranges;
	}

	public void setRanges(Set<Range> ranges) {
		this.ranges = ranges;
	}

	@Column(name = BirdnotesConstants.Columns.VERSION)
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	@Column(name = BirdnotesConstants.Columns.QUANTITY_UNIT, length = BirdnotesConstants.Numbers.N_85)
	public String getQuantityUnit() {
		return quantityUnit;
	}

	public void setQuantityUnit(String quantityUnit) {
		this.quantityUnit = quantityUnit;
	}

	@OneToMany (mappedBy = "product")
	public List<GoalItem> getGoalItems() {
		return goalItems;
	}

	public void setGoalItems(List<GoalItem> goalItems) {
		this.goalItems = goalItems;
	}
	

	@JsonIgnore
	@OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "product", targetEntity = SampleSupplyItem.class)
	public SampleSupplyItem getSamplesSupplyItem() {
		return samplesSupplyItem;
	}

	public void setSamplesSupplyItem(SampleSupplyItem samplesSupplyItem) {
		this.samplesSupplyItem = samplesSupplyItem;
	}

	@Column(name = BirdnotesConstants.Columns.CODE, length = BirdnotesConstants.Numbers.N_85)
	public String getCode() {
		return code;
	}

	

	public void setCode(String code) {
		this.code = code;
	}

	@JsonIgnore
	@ManyToMany(mappedBy = "products")
	public Set<Commission> getCommissions() {
		return commissions;
	}

	public void setCommissions(Set<Commission> commissions) {
		this.commissions = commissions;
	}
	
	@Column(name = BirdnotesConstants.Columns.VAT)
	public Float getVat() {
		return vat;
	}

	public void setVat(Float vat) {
		this.vat = vat;
	}

	
}
