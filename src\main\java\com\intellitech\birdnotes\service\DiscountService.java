package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.text.ParseException;
import java.util.List;

import com.intellitech.birdnotes.data.dto.DiscountFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.DiscountDto;
import com.intellitech.birdnotes.model.dto.ProductDto;

public interface DiscountService {

	void delete(Long id) throws BirdnotesException;

	List<Long> saveDiscount(DiscountDto DiscountDto) throws BirdnotesException;

	List<DiscountDto> getDiscount() throws BirdnotesException, ParseException;

	List<ProductDto> getProductWithoutDiscount() throws BirdnotesException;

	void updateDiscount(DiscountDto discountDto) throws BirdnotesException;

	DiscountFormData loadDiscountFormData() throws IOException, BirdnotesException;

}
