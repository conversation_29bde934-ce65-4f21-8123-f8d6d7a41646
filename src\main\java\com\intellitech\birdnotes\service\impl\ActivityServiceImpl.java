package com.intellitech.birdnotes.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.AutomationRule;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.convertor.ConvertActivityToDto;
import com.intellitech.birdnotes.model.dto.ActivityCalanderRequestDto;
import com.intellitech.birdnotes.model.dto.ActivityDataDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.StatisticActivity;
import com.intellitech.birdnotes.model.dto.VisitHistoryGroupDto;
import com.intellitech.birdnotes.repository.ActivityRepository;
import com.intellitech.birdnotes.repository.ActivityTypeRepository;
import com.intellitech.birdnotes.repository.AutomationRuleRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ActivityService;
import com.intellitech.birdnotes.service.AutomationRuleService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.model.User;


@Service("activityService")
@Transactional
public class ActivityServiceImpl implements ActivityService {
	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	private ActivityRepository activityRepository;

	private UserRepository userRepository;

	private UserService userService;

	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");

	private ConvertActivityToDto convertActivityToDto;

	private CurrentUser currentUser;

	private ActivityTypeRepository activityTypeRepository;

	private DelegateRepository delegateRepository;

	private NotificationService notificationService;

	private AutomationRuleRepository automationRuleRepository;

	private AutomationRuleService automationRuleService;
	private ProspectRepository prospectRepository;

	@Autowired
	public void setUserRepository(UserRepository userRepository, UserService userService,
			ConvertActivityToDto convertActivityToDto, ActivityTypeRepository activityTypeRepository,
			DelegateRepository delegateRepository, NotificationService notificationService,
			AutomationRuleRepository automationRuleRepository, AutomationRuleService automationRuleService,
			ProspectRepository prospectRepository) {
		this.userRepository = userRepository;
		this.userService = userService;
		this.convertActivityToDto = convertActivityToDto;
		this.activityTypeRepository = activityTypeRepository;
		this.delegateRepository = delegateRepository;
		this.notificationService = notificationService;
		this.automationRuleRepository = automationRuleRepository;
		this.automationRuleService = automationRuleService;
		this.prospectRepository = prospectRepository;
	}

	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	@Autowired
	public void ActivityRepository(ActivityRepository activityRepository) {
		this.activityRepository = activityRepository;
	}

	public ActivityDataDto getStatisticActivity(ActivityCalanderRequestDto activityCalanderRequestDto)
			throws BirdnotesException {
		List<VisitHistoryGroupDto> visitHistoryGroupDtoList = new ArrayList<>();
		ActivityDataDto activityDataDto = new ActivityDataDto();
		List<StatisticActivity> list = new ArrayList<>();
		List<Activity> activitiesList = new ArrayList<>();
		List<ActivityDto> activityDtoList = new ArrayList<>();
		try {

			List<Long> subUsers;

			if (activityCalanderRequestDto.getSelectedUser() != 0) {

				User user = userRepository.findUserByDelegateId(activityCalanderRequestDto.getSelectedUser());
				subUsers = Arrays.asList(user.getId());
			} else {

				subUsers = userService.getSubUsersIds();

			}
			list = activityRepository.getStatisticActivity(format.parse(activityCalanderRequestDto.getFirstDate()),
					format.parse(activityCalanderRequestDto.getLastDate()), subUsers);

			activitiesList = activityRepository.getActivities(format.parse(activityCalanderRequestDto.getFirstDate()),
					format.parse(activityCalanderRequestDto.getLastDate()), subUsers);

			for (StatisticActivity item : list) {
				visitHistoryGroupDtoList
						.add(new VisitHistoryGroupDto(item.getName(), new Float(item.getHourNumber() / 8f)));
			}

		} catch (ParseException e) {
			log.error("Exception in pars last date ", e);

		}

		for (Activity activity : activitiesList) {

			activityDtoList.add(convertActivityToDto.convert(activity));
		}

		activityDataDto.setActivitiesList(activityDtoList);
		activityDataDto.setVisitHistoryGroupDtoList(visitHistoryGroupDtoList);
		return activityDataDto;

	}

	@Override
	public void updateActivity(ActivityDto activityDto) throws BirdnotesException {

		if (activityDto == null || activityDto.getId() == null) {
			throw new BirdnotesException(Exceptions.ACTIVITY_DTO_ID_NULL);
		}

		Activity activityToUpdate = activityRepository.findOne(activityDto.getId());
		if (activityToUpdate == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.ACTIVIYUTO_UPDATE_ALREADY_DELETED);
		}

		if (activityToUpdate.getActivityType() == null) {
			throw new BirdnotesException(Exceptions.TYPE_IS_NULL);
		}
		activityToUpdate.setComment(activityDto.getComment());
		activityToUpdate.setHourNumber(activityDto.getHourNumber());
		activityRepository.save(activityToUpdate);
	}

	@Override
	public Activity saveActivity(ActivityDto activityDto) throws BirdnotesException {

		Activity activity = activityRepository.findById(activityDto.getId());
		if (activity == null) {
			activity = new Activity();
			activity.setIdentifier(activityDto.getIdentifier());
		}
		activity.setActivityDate(activityDto.getDate());
		activity.setComment(activityDto.getComment());
		activity.setHourNumber(activityDto.getHourNumber());
		activity.setIdentifier(new Date().getTime());
		ActivityType activityType = activityTypeRepository.findOne(activityDto.getActivityTypeId());
		if (activityType == null) {
			throw new BirdnotesException("type activity est obligatoire");
		}
		activity.setActivityType(activityType);
		Delegate delegate = null;
		if (activityDto.getDelegateId() != null) {
			delegate = delegateRepository.findById(activityDto.getDelegateId());
			activity.setDelegate(delegate);
		} else {
			throw new BirdnotesException("Session est expiré");
		}

		if (activityDto.getProspectId() != null) {
			Prospect prospect = prospectRepository.findOne(activityDto.getProspectId());
			if (prospect != null) {
				activity.setProspect(prospect);
			}
		}

		Activity mergedActivity = activityRepository.save(activity);

		if (mergedActivity != null) {
			notificationService.sendNotificationUsingWorkflow(delegate.getUser(), mergedActivity.getId(),
					"ADD_ACTIVITY");
		}

		List<AutomationRule> automationRuleList = automationRuleRepository.findByActivityTypeEvent(activityType.getId());
		if (automationRuleList != null) {
			automationRuleService.generatePlanningsFromRules(automationRuleList, mergedActivity);
		}

		return mergedActivity;

	}

	@Override
	public List<ActivityDto> findActivityByUser(Long userId) throws BirdnotesException {
		List<ActivityDto> activitiesDto = new ArrayList<>();
		List<Activity> activities = activityRepository.findActivityByUser(userId);
		if (activities != null && !activities.isEmpty()) {
			for (Activity activity : activities) {
				ActivityDto activityDto = new ActivityDto();
				activityDto.setId(activity.getId());
				activityDto.setComment(activity.getComment());
				activityDto.setDate(activity.getActivityDate());
				activityDto.setHourNumber(activity.getHourNumber());
				activityDto.setIdentifier(activity.getIdentifier());

				ActivityTypeDto activityTypeDto = new ActivityTypeDto();
				ActivityType activityType = activity.getActivityType();
				if (activityType != null) {
					activityTypeDto.setName(activityType.getName());
					activityTypeDto.setId(activityType.getId());
				}
				activityDto.setActivityTypeDto(activityTypeDto);
				activitiesDto.add(activityDto);
			}
		}
		return activitiesDto;
	}

	@Override
	public ActivityDto findActivityById(long id) throws BirdnotesException {
		Activity activity = activityRepository.findById(id);
		ActivityDto activityDto = convertActivityToDto.convert(activity);
		return activityDto;
	}

	@Override
	public void deleteActivity(Long activityId) {
		activityRepository.delete(activityId);

	}

}
