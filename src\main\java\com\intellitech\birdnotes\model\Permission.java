package com.intellitech.birdnotes.model;

import java.util.List;

import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

import com.intellitech.birdnotes.model.dto.RoleDto;

@XmlRootElement
public class Permission {
	private String name;
	private String label;
	private List<RoleDto> rolesDto;

	public Permission() {
	}

	public Permission(String name, String label, List<RoleDto> rolesDto) {
		super();
		this.name = name;
		this.label = label;
		this.rolesDto = rolesDto;
	}

	public List<RoleDto> getRolesDto() {
		return rolesDto;
	}

	public void setRolesDto(List<RoleDto> rolesDto) {
		this.rolesDto = rolesDto;
	}

	@XmlAttribute
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@XmlAttribute
	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

}
