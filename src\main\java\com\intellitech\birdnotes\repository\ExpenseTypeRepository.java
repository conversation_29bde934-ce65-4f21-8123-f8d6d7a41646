package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.ExpenseType;

public interface ExpenseTypeRepository extends JpaRepository<ExpenseType, Long> {
	
	ExpenseType findByName(String name);
	
	@Override
	@Query("SELECT t from ExpenseType t order by t.name ASC")
	List<ExpenseType> findAll();
	
	@Modifying
	@Query("DELETE from ExpenseType WHERE id=:id")
	void deleteByID(@Param("id") Long id);
	
	@Query("SELECT t.id from ExpenseType t WHERE t.id in (:typeNoteFrais) order by t.name")
	List<Long> findWhereIdIn(@Param("typeNoteFrais") List<Long> typeNoteFrais);

	@Query("SELECT t from ExpenseType t where  LOWER(name) = LOWER(?1) AND id != ?2")
	ExpenseType findByNameAndAnotherId(String name, Long id);
	
	@Query("SELECT t from ExpenseType t join t.delegates u where t.mileage <= ?1 and u.id = ?2")
	List<ExpenseType> getTypesByMileage(float maxDistance, Long userId);
	
	@Query("SELECT t from ExpenseType t join t.delegates u where  u.id = ?1")
	List<ExpenseType> findByUser(Long userId);
	


}
