package com.intellitech.birdnotes.service.impl;


import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.ArrayList;

import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.enumeration.CarType;
import com.intellitech.birdnotes.enumeration.ContractType;
import com.intellitech.birdnotes.enumeration.WorkType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.DelegateDisplacement;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Gender;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.DelegateToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.DelegateDtoRequest;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.UserDtoRequest;
import com.intellitech.birdnotes.model.dto.VisitHistoryParamDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.repository.DelegateDisplacementRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.NetworkRepository;

import com.intellitech.birdnotes.repository.ProspectsAffectationRepository;
import com.intellitech.birdnotes.repository.RoleRepository;

import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.DistanceService;
import com.intellitech.birdnotes.service.ImportService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.thread.ThreadSendEmail;
import com.intellitech.birdnotes.util.BirdnotesConstants;


@Service("delegateService")
@Transactional
public class DelegateServiceImpl implements DelegateService {

	public CurrentUser getCurrentUser() {
		return currentUser;
	}

	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	private static final Logger LOG = LoggerFactory.getLogger(DelegateServiceImpl.class);

	private static final String IMGBALISE = " <br/> <img src=";
	private RangeRepository gammeRepository;
	private UserRepository userRepository;
	private UserService userService;
	private VisitRepository visitRepository;
	private RoleRepository roleRepository;
	private DelegateToDtoConvertor delegateToDtoConvertor;
	private JavaMailSender javaMailSender;
	
	private ConvertProspectToDto convertProspectToDto;
	private NetworkRepository networkRepository;

	private DelegateRepository delegateRepository;
	@Autowired
	private ProspectsAffectationRepository prospectSAffectationRepository;
	private ConfigurationService configurationService;

	// problem dans cet autowired

	@Autowired
	private ImportService importService;

	@Autowired
	private CurrentUser currentUser;

    @Autowired
    private DistanceService distanceService;
	
	

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${logoPath}")
	private String logoPath;

	private String pathLogo;

	
	@Autowired
	private StorageService storageService;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${userPath}")
	private String userPath;
	
	/*@Value("${editUsernamePasswordEmailSubject}")
	private String editUsernamePasswordEmailSubject;

	@Value("${sendRememberSynchronizationSubject}")
	private String sendRememberSynchronizationSubject;

	@Value("${sendRememberSynchronizationHtmlBody}")
	private String sendRememberSynchronizationHtmlBody;

	@Value("${editPasswordEmailSubject}")
	private String editPasswordEmailSubject;

	@Value("${editPasswordEmailHtmlBody}")
	private String editPasswordEmailHtmlBody;
	
	@Value("${editUsernameEmailHtmlBody}")
	private String editUsernameEmailHtmlBody;

	@Value("${editUsernameEmailSubject}")
	private String editUsernameEmailSubject;*/



	public DelegateServiceImpl() {
		super();

	}

	@PostConstruct
	private void init() {
		ConfigurationDto config = configurationService.findConfiguration();
		if (config != null) {
			pathLogo = config.getBackendUrl() + uploadUrl + logoPath + "/" + config.getLogo();
		}

	}
	
	@Autowired
	private MessageSource messageSource;

	@Autowired
	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	@Autowired
	public void setGammeRepository(RangeRepository gammeRepository) {
		this.gammeRepository = gammeRepository;
	}

	@Autowired
	public void setNetworkRepository(NetworkRepository networkRepository) {
		this.networkRepository = networkRepository;
	}

	@Autowired
	public void setProspectSAffectationRepository(ProspectsAffectationRepository prospectSAffectationRepository) {
		this.prospectSAffectationRepository = prospectSAffectationRepository;
	}

	
	@Autowired
	public void setConfigurationService(ConfigurationService configurationService) {
		this.configurationService = configurationService;
	}
	
	
	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	@Autowired
	public void setRoleRepository(RoleRepository roleRepository) {
		this.roleRepository = roleRepository;
	}

	@Autowired
	public void setDelegateToDtoConvertor(DelegateToDtoConvertor delegateToDtoConvertor) {
		this.delegateToDtoConvertor = delegateToDtoConvertor;
	}

	@Autowired
	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}

	@Autowired
	public void setConvertProspectToDto(ConvertProspectToDto convertProspectToDto) {
		this.convertProspectToDto = convertProspectToDto;
	}
	
    @Autowired
    private DelegateDisplacementRepository delegateDisplacementRepository;


	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}

	private void setDelegateData(DelegateDtoRequest delegateDto, Delegate delegate) throws BirdnotesException {
		delegate.setFirstName(delegateDto.getFirstName());
		delegate.setLastName(delegateDto.getLastName());
		delegate.setHiringDate(delegateDto.getHiringDate());
		delegate.setBirthdayDate(delegateDto.getBirthdayDate());
		delegate.setCin(delegateDto.getCin());
		if (delegateDto.getCarType() != null && CarType.valueOf(delegateDto.getCarType()) != null) {
			delegate.setCarType(CarType.valueOf(delegateDto.getCarType()));
		}
		delegate.setWorkingDaysPerWeek(delegateDto.getWorkingDaysPerWeek());
		delegate.setLatitude(delegateDto.getLat());
		delegate.setLongitude(delegateDto.getLng());
		delegate.setCin(delegateDto.getCin());
		setGender(delegateDto, delegate);
		setContrat(delegateDto, delegate);
		setWork(delegateDto, delegate);
		Network network = networkRepository.findOne(delegateDto.getNetworkId());
		delegate.setNetwork(network);
		UserDto userDto = new UserDto();
		User user = userRepository.findUserByDelegateId(delegateDto.getId());
		userDto.setId(0L);
		if(user != null) {
			userDto.setId(user.getId());
		}
		userDto.setPhone(delegateDto.getPhone());
		userDto.setEmail(delegateDto.getEmail());
		userDto.setUsername(delegateDto.getUsername());
		userDto.setPassword(delegateDto.getPassword());
		userDto.setRoleIds(delegateDto.getRoleIds());
		userDto.setRangeIds(delegateDto.getRangeIds());
		userDto.setSuperiors(delegateDto.getSuperiors());
		userDto.setActive(delegateDto.getActive());
		if (delegateDto.getCarRegistration() != null ) {
			delegate.setCarRegistration(delegateDto.getCarRegistration());
		}
		delegate.setCarRegistration(delegateDto.getCarRegistration());
		user = userService.saveUser(userDto);
		delegate.setUser(user);
	}

	@Override
	public Delegate findDelegateById(Long id) {
		Delegate delegate = delegateRepository.findOne(id);
		return delegate;
	}

	/*
	 * @Override
	 * 
	 * @Transactional(readOnly = true) public List<UserDto> getAllDelegates() {
	 * 
	 * List<UserDto> userDtos = getSubUsers(); return userDtos; }
	 */

	@Override
	@Transactional(readOnly = true)
	public List<DelegateDto> findAllDelegatesWithoutGoal(GoalRequest goalsRequestDto) {

		List<DelegateDto> delegateDtos = findAllDelegate();

		
		/*
		 * if (userDtos != null && !userDtos.isEmpty()) {
		 * 
		 * //userWithoutGoal =
		 * userRepository.findUsersWithoutGoal(goalsRequestDto.getFirstDate(),
		 * goalsRequestDto.getLastDate(), subUserIds);
		 * 
		 * 
		 * for (UserDto user : userDtos) {
		 * 
		 * if(user.getGoals() == null) {
		 * 
		 * userWithoutGoal.add(user);
		 * 
		 * }else {
		 * 
		 * for(GoalDto goal : user.getGoals()) { if( //selected period do not include
		 * goal of user with the same type
		 * (((goalsRequestDto.getFirstDate().compareTo(goal.getFirstDate())<=0 &&
		 * goalsRequestDto.getLastDate().compareTo(goal.getFirstDate())<=0) ||
		 * (goalsRequestDto.getFirstDate().compareTo(goal.getLastDate())>=0 &&
		 * goalsRequestDto.getLastDate().compareTo(goal.getLastDate())>=0)) &&
		 * goalsRequestDto.getType().equals(goal.getGoalType())) || //the same selected
		 * period include goal with different type
		 * ((goalsRequestDto.getFirstDate().compareTo(goal.getFirstDate())>=0 &&
		 * goalsRequestDto.getLastDate().compareTo(goal.getFirstDate())<=0) &&
		 * !goalsRequestDto.getType().equals(goal.getGoalType()))) {
		 * if(!userWithoutGoal.contains(user)) { userWithoutGoal.add(user); }
		 * 
		 * }
		 * 
		 * }
		 * 
		 * 
		 * }
		 * 
		 * 
		 * 
		 * }
		 * 
		 * 
		 * }
		 */

		return delegateDtos;

	}




	
	@Override
	public List<DelegateDto> findAllDelegate() {
		List<Delegate> delegates = delegateRepository.findAll();
		List<DelegateDto> delegateDtos = new ArrayList<DelegateDto>();
		if (delegates.size() > 0) {
			for (Delegate delegate : delegates) {
				delegateDtos.add(delegateToDtoConvertor.convert(delegate));
			}
		}
		return delegateDtos;
	}


	/*
	 * @Override
	 * 
	 * @Transactional(readOnly = true) public List<UserDto>
	 * getAllDelegatesByUserRole(BirdnotesUser birdnotesUser) {
	 * 
	 * List<UserDto> listUsersDto = new ArrayList<>(); Set<Role> userRoles =
	 * birdnotesUser.getUserDto().getRoles(); List<String> userRolesNames = new
	 * ArrayList<>(); for (Role role : userRoles) {
	 * userRolesNames.add(role.getName()); } listUsersDto = getSubUsers();
	 * 
	 * return listUsersDto; }
	 */
	
	public List<DelegateDto> getSubDelegates(Long superior, List<User> subUsers) {

		List<User> users = userRepository.getUsersBySuperior(superior);
		List<DelegateDto> delegateDtos = new ArrayList<>();
		if (users.size() > 0) {
			subUsers.addAll(users);
			for (User user : users) {
				getSubDelegates(user.getId(), subUsers);
			}
		}
		for (User user : users) {
			Delegate delegate = delegateRepository.findDelegateByUserId(user.getId());
			if(delegate != null) {
				delegateDtos.add(delegateToDtoConvertor.convert(delegate));
			}
		}
		return delegateDtos;
	}
	
	@Override
	public List<DelegateDto> getSubDelegates() {
		List<User> subDelegates = new ArrayList<>();
		return this.getSubDelegates(currentUser.getBirdnotesUser().getUserDto().getId(), subDelegates);
	}
	

	@Override
	public List<DelegateDto> findAllDelegates() {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		Integer currentRoleRank = roleRepository.findRoleByUserId(birdnotesUser.getUserDto().getId());
		if (currentRoleRank == 1) {
			List<Delegate> delegates = delegateRepository.findAll();
			List<DelegateDto> delegateDtos = new ArrayList<DelegateDto>();
			if (delegates.size() > 0) {
				for (Delegate delegate : delegates) {
					delegateDtos.add(delegateToDtoConvertor.convert(delegate));
				}
			}
			return delegateDtos;
		} else {
			return getSubDelegates();
		}

	}
	

	@Override
	public Delegate saveDelegate(DelegateDtoRequest delegateDto, MultipartFile file) throws BirdnotesException {

		Delegate delegate = null; 
		if (delegateDto == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.USER_DTO_IS_NULL);
		}
		if(delegateDto.getId() != null && delegateDto.getId() != 0) {
			delegate = delegateRepository.findById(delegateDto.getId());		
		}
		if(delegate == null) {
			delegate = new Delegate();
		}
		setDelegateData(delegateDto, delegate);

		List<Range> gammes = gammeRepository.findAll();
		if (gammes != null && !gammes.isEmpty()) {
			fillGammes(gammes, delegateDto, delegate);
		}
		
		if (file != null) {
			delegate.setPhoto(file.getOriginalFilename());
		}
		Delegate delegateSaved = delegateRepository.save(delegate);

		if (delegateSaved != null) {

			if (file != null) {
				MultipartFile[] files = { file };
				storageService.storeFiles(files, uploadPath + userPath + "/" + delegateSaved.getId());
			}
			// sendMailOfAddUser(userSaved, userDto);
		}
		
		return delegateSaved;
	}


	private void fillGammes(List<Range> gammes, DelegateDtoRequest delegateDto, Delegate delegate) {
		Set<Range> gammeDelegate = new HashSet<>();
		for (Integer gammeId : delegateDto.getRangeIds()) {
			for (Range gamme : gammes) {
				if (gamme.getId() == gammeId) {
					gammeDelegate.add(gamme);
				}
			}
		}
		delegate.getUser().setRanges(gammeDelegate);
	}

	private void fillRoles(List<Role> roles, DelegateDtoRequest userDto, User user) {

		Set<Role> roleDelegate = new HashSet<>();
		for (Integer roleId : userDto.getRoleIds()) {
			for (Role role : roles) {
				if (role.getId().equals(roleId)) {
					roleDelegate.add(role);
				}
			}
		}
		user.setRoles(roleDelegate);

	}



	public List<String> sendMailForSynchronization() {

		List<String> NotifiedDelegates = new ArrayList<String>();
		List<Delegate> delegates = delegateRepository.findAllDelegates();
		LocalDate now = LocalDate.now();
		User user = null;
		for (Delegate delegate : delegates) {
			user = userRepository.findUserByDelegateId(delegate.getId());
			if (user.getEmail() != null && !user.getEmail().equals("")) {

				if (delegate.getLastSyncro() != null) {
					LocalDate lastSyn = (delegate.getLastSyncro()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

					Period period = Period.between(lastSyn, now);
					int nbDays = period.getDays();

					if (nbDays > 1) {
						ConfigurationDto config = configurationService.findConfiguration();
						Locale locale = new Locale(config.getLanguage());
						String sendRememberSynchronizationSubject = messageSource.getMessage("sendRememberSynchronizationSubject", null, locale);
						String sendRememberSynchronizationHtmlBody = messageSource.getMessage("sendRememberSynchronizationHtmlBody", null, locale);
						MessageFormat messageFormat = new MessageFormat(sendRememberSynchronizationHtmlBody);
						String[] args = { user.getUsername(), delegate.getLastSyncro().toString() };
						String htmlBody = messageFormat.format(args) + IMGBALISE + pathLogo + "> ";
						String[] to = { user.getEmail() };
						ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to,
								sendRememberSynchronizationSubject, htmlBody);
						Thread thread = new Thread(threadSendEmail);
						thread.start();
					}
				}

			}
		}
		return NotifiedDelegates;
	}



	private void setGender(DelegateDtoRequest delegateDto, Delegate delegate) {
		if (delegateDto.getGender() != null) {
			if (BirdnotesConstants.Gender.MEN.equals(delegateDto.getGender())) {
				delegate.setGender(Gender.MEN);
			} else {
				delegate.setGender(Gender.WOMEN);
			}
		}

	}

	private void setWork(DelegateDtoRequest delegateDto, Delegate delegate) {
		if (delegateDto.getWorkType() != null) {
			if (BirdnotesConstants.WorkType.MEDICAL.equals(delegateDto.getWorkType())) {
				delegate.setWorkType(WorkType.MEDICAL);
			} else {
				delegate.setWorkType(WorkType.PHARMACEUTICAL);
			}
		}
	}

	private void setContrat(DelegateDtoRequest delegateDto, Delegate delegate) {
		if (delegateDto.getContractType() != null) {
			if (BirdnotesConstants.ContractType.CDI.equals(delegateDto.getContractType())) {
				delegate.setContractType(ContractType.CDI);
			} else if (BirdnotesConstants.ContractType.CDD.equals(delegateDto.getContractType())) {
				delegate.setContractType(ContractType.CDD);
			} else {
				delegate.setContractType(ContractType.SIVP);
			}
		}
	}

	@Override
	public List<Delegate> saveAll(List<DelegateDto> delegateDtos) throws BirdnotesException {
		List<Delegate> savedDelegates = new ArrayList<>();
		for (DelegateDto delegateDto : delegateDtos) {
			savedDelegates.add(saveDelegate(delegateDto, null));
		}
		return savedDelegates;
	}

	@Override
	public void deleteDelegate(Long deleteId) throws BirdnotesException {


			delegateRepository.delete(deleteId);
		}

	

		private void sentMailofUpdateUser(Integer userIsUpdated, boolean usernameIsUpdated, boolean passwordIsUpdated,
			UserDtoRequest userDto) {

		String htmlBody = "";
		String emailSubject = "";
		ConfigurationDto config = configurationService.findConfiguration();
		Locale locale = new Locale(config.getLanguage());
		if (userIsUpdated == 1 && passwordIsUpdated) {
			String editPasswordEmailSubject = messageSource.getMessage("editPasswordEmailSubject", null, locale);
			MessageFormat messageFormat = new MessageFormat(editPasswordEmailSubject);
			String[] args = { userDto.getPassword() };
			htmlBody = messageFormat.format(args) + IMGBALISE + pathLogo + "> ";
			emailSubject = editPasswordEmailSubject;
		}

		if (userIsUpdated == 1 && usernameIsUpdated) {
			
			String editUsernameEmailSubject = messageSource.getMessage("editUsernameEmailSubject", null, locale);
			MessageFormat messageFormat = new MessageFormat(editUsernameEmailSubject);
			String[] args = { userDto.getUsername() };
			htmlBody = "<img src=" + pathLogo + " /> <br/> " + messageFormat.format(args);
			emailSubject = editUsernameEmailSubject;
		}

		if (userIsUpdated == 1 && usernameIsUpdated && passwordIsUpdated) {
			String editUsernamePasswordEmailSubject = messageSource.getMessage("editUsernamePasswordEmailSubject", null, locale);
			MessageFormat messageFormat = new MessageFormat(editUsernamePasswordEmailSubject);
			String[] args = { userDto.getUsername(), userDto.getPassword() };
			htmlBody = messageFormat.format(args) + IMGBALISE + pathLogo + "> ";
			emailSubject = editUsernamePasswordEmailSubject;
		}

		if (!"".equals(htmlBody)) {
			String[] to = { userDto.getEmail() };
			ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, emailSubject, htmlBody);
			Thread thread = new Thread(threadSendEmail);
			thread.start();
		}
	}




	private boolean checkEmailIsUnique(String email) {
		User user = userRepository.findByEmail(email);

		return user != null;
	}

	private boolean checkEmailIsUniqueAndIdNotIn(String email, Long id) {
		User user = userRepository.findByEmailAndId(email, id);

		return user != null;
	}

	private boolean checkPhoneIsUniqueAndIdNotIn(String phone, Long id) {
		User user = userRepository.findByPhoneAndId(phone, id);

		return user != null;
	}

	private boolean checkPhoneIsUnique(String phone) {
		User user = userRepository.findByPhone(phone);

		return user != null;
	}

	private boolean checkUsernameIsUnique(String username) {
		User user = userRepository.findByUsername(username);

		return user != null;
	}

	private boolean checkUsernameIsUniqueAndIdNotIn(String username, Long id) {
		User user = userRepository.findByUsernameAndId(username, id);

		return user != null;
	}

	@Override
	public VisitHistoryParamDto findHistoryParams(List<Long> userIds, String type) {

		List<SectorDto> sectors = new ArrayList<>();
		List<LocalityDto> localities = new ArrayList<>();
		List<ProspectDto> prospects = new ArrayList<>();
		List<SpecialityDto> specialities = new ArrayList<>();

		findSectorAndLocality(userIds, sectors, localities);

		if (type == null || !type.equals("PLANNING")) {
			List<Prospect> prospectsFromDB = prospectSAffectationRepository.findByUser(userIds);
			// List<Prospect> prospectsFromDB = prospectRepository.findByUser(userId);

			if (prospectsFromDB != null && !prospectsFromDB.isEmpty()) {
				for (Prospect prospect : prospectsFromDB) {
					try {
						prospects.add(convertProspectToDto.convert(prospect));
					} catch (BirdnotesException e) {
						LOG.error("Cannot convert from prospect to dto" + e);
					}
				}
			}
		}

		List<Speciality> specialityFromDB = prospectSAffectationRepository.findDelegatesSpecialities(userIds);
		if (specialityFromDB != null && !specialityFromDB.isEmpty()) {
			for (Speciality speciality : specialityFromDB) {
				SpecialityDto specialityDto = new SpecialityDto();
				specialityDto.setId(speciality.getId());
				specialityDto.setName(speciality.getName());
				specialities.add(specialityDto);
			}
		}
		return getVisitHistoryParamDto(localities, prospects, sectors, specialities);
	}

	private void findSectorAndLocality(List<Long> userIds, List<SectorDto> sectors, List<LocalityDto> localities) {
		List<Sector> userSectors = prospectSAffectationRepository.findDelegatesSectors(userIds);
		if (userSectors != null && !userSectors.isEmpty()) {
			for (Sector sector : userSectors) {
				SectorDto sectorDto = new SectorDto();
				sectorDto.setId(sector.getId());
				sectorDto.setName(sector.getName());
				sectors.add(sectorDto);

			}
			findLocality(userIds, localities);
			localities = localities.stream().sorted(Comparator.comparing(LocalityDto::getName))
					.collect(Collectors.toList());
		}

	}

	private void findLocality(List<Long> userIds, List<LocalityDto> localities) {
		List<Locality> userLocalities = prospectSAffectationRepository.findDelegateslocalities(userIds);
		if (userLocalities != null && !userLocalities.isEmpty()) {
			for (Locality locality : userLocalities) {
				LocalityDto localityDto = new LocalityDto();
				localityDto.setId(locality.getId());
				localityDto.setName(locality.getName());
				localityDto.setSectorId(locality.getSector().getId());
				localities.add(localityDto);
			}

		}
	}

	private VisitHistoryParamDto getVisitHistoryParamDto(List<LocalityDto> localities, List<ProspectDto> prospects,
			List<SectorDto> sectors, List<SpecialityDto> specialities) {
		VisitHistoryParamDto back = new VisitHistoryParamDto();
		back.setLocalities(localities);
		back.setProspects(prospects);
		back.setSectors(sectors);
		back.setSpecialities(specialities);
		return back;
	}

	@Autowired
	public void setVisitRepository(VisitRepository visitRepository) {
		this.visitRepository = visitRepository;
	}



	@Override
	public Map<String, List<DelegateDto>> importDelegate(String filePath) throws BirdnotesException {
		try {
			Map<String, List<DelegateDto>> delegateDtos = importService.getDelegateData(filePath);
			if (delegateDtos.get("validData") != null) {
				for (DelegateDto delegateDto : delegateDtos.get("validData")) {
					Delegate delegate = new Delegate();
					delegate.setBirthdayDate(delegateDto.getBirthdayDate());
					delegate.setHiringDate(delegateDto.getHiringDate());
					delegate.setFirstName(delegateDto.getFirstName());
					delegate.setLastName(delegateDto.getLastName());
					delegate.setCin(delegateDto.getCin());
					delegate.setCycle(delegateDto.getCycle());
					delegate.setWorkingDaysPerWeek(delegateDto.getWorkingDaysPerWeek());
					String gender = delegateDto.getGender();
					if (gender != null) {
						if (gender.equalsIgnoreCase("femme")) {
							delegate.setGender(Gender.WOMEN);
						} else if (gender.equalsIgnoreCase("homme")) {
							delegate.setGender(Gender.MEN);
						}
					}
					String contract = delegateDto.getContractType();
					if (contract != null) {
						if (contract.equalsIgnoreCase("cdi")) {
							delegate.setContractType(ContractType.CDI);
						} else if (contract.equalsIgnoreCase("cdd")) {
							delegate.setContractType(ContractType.CDD);
						} else if (contract.equalsIgnoreCase("sivp")) {
							delegate.setContractType(ContractType.SIVP);
						}
					}
					String workType = delegateDto.getWorkType();
					if (workType != null) {
						if (workType.equalsIgnoreCase("medical")) {
							delegate.setWorkType(WorkType.MEDICAL);
						} else if (workType.equalsIgnoreCase("admin")) {
							delegate.setWorkType(WorkType.ADMIN);
						} else if (workType.equalsIgnoreCase("pharamaceutical")) {
							delegate.setWorkType(WorkType.PHARMACEUTICAL);
						}
					}
										
					String car = delegateDto.getCarType();
					if (car != null) {
						if (car.equalsIgnoreCase("personal")) {
							delegate.setCarType(CarType.PERSONAL);
						} else {
							delegate.setCarType(CarType.NOT_PERSONAL);
						}
					}
				
					delegate.setCarRegistration(delegateDto.getCarRegistration());
					String networkString = delegateDto.getNetworkString();
					String[] networks = networkString.split(",");
					for (String networkName : networks) {
						delegate.setNetwork(networkRepository.findByName(networkName.trim()));
					}
					
					String rangesString = delegateDto.getGammesString();
					String[] ranges = rangesString.split(",");
					Set<Range> rangeList = new HashSet<>();
					
					for (String rangeName : ranges) {
							rangeList.add(gammeRepository.findByName(rangeName.trim()));
							
					}					
					delegate.getUser().setRanges(rangeList);					
			
					UserDtoRequest userDto = new UserDtoRequest();
					userDto.setPhone(delegateDto.getPhone());
					userDto.setEmail(delegateDto.getEmail());
					userDto.setPassword(delegateDto.getPassword());
					userDto.setUsername(delegateDto.getUsername());
					String roleString = delegateDto.getRolesString();
					String[] roles = roleString.split(",");
					Set<Integer> roleIds = new HashSet<>();
					for (String role : roles) {
						roleIds.add((roleRepository.findByName(role.trim())).getId());
					}
					userDto.setRoleIds(new ArrayList<>(roleIds));
					String supervisorsString = delegateDto.getSupervisorsString();
					String[] supervisors = supervisorsString.split(",");
					Set<Long> supervisorIds = new HashSet<>();
					for (String supervisor : supervisors) {						
						supervisorIds.add((userRepository.findByUsername(supervisor.trim())).getId());						
					}
					userDto.setSuperiors(new ArrayList<>(supervisorIds));
					User user = userService.saveUser(userDto);
					delegate.setUser(user);
					
					delegateRepository.save(delegate);

				}

			}
			return delegateDtos;
		} catch (Exception e) {
			LOG.error("Error in importing user ", e);
		}
		return null;

	}
	
	@Override
	public Map<String, Object> calculateDailyDistance(Date date) throws Exception {
	    Map<String, Object> response = new HashMap<>();


	    List<Delegate> delegates = delegateRepository.findAllDelegates();

	    for (Delegate delegate : delegates) {
	        try {
	            double totalDistance = calculateDelegateDistance(delegate, date);
	            saveDelegateDisplacement(delegate, date, totalDistance);
	        } catch (Exception e) {
	            LOG.error("Error processing delegate {}: {}", delegate.getId(), e.getMessage());
	        }
	    }

	    response.put("date", new SimpleDateFormat("yyyy-MM-dd").format(date));
	    response.put("status", "Distance calculation and saving completed.");
	    return response;
	}
	private void saveDelegateDisplacement(Delegate delegate, Date date, double distance) {
	    try {
	        DelegateDisplacement displacement = delegateDisplacementRepository.findByDelegateAndDate(delegate, date);
	        if (displacement == null) {
	            displacement = new DelegateDisplacement();
	            displacement.setDelegate(delegate);
	            displacement.setDate(date);
	        }
	        displacement.setDistance(distance);
	        delegateDisplacementRepository.save(displacement);
	        LOG.info("Saved displacement for delegate {} on date {} with distance {} km", delegate.getId(), date, distance);
	    } catch (Exception e) {
	        LOG.error("Error saving displacement for delegate {}: {}", delegate.getId(), e.getMessage());
	        throw new RuntimeException("Error saving displacement: " + e.getMessage());
	    }
	}

	private double calculateDelegateDistance(Delegate delegate, Date date) throws BirdnotesException {
	    List<Visit> visits = visitRepository.findByDateAndUser(date, delegate).stream()
	        .filter(visit -> visit.getLocation() != null && visit.getLocation().getLatitude() != null && visit.getLocation().getLongitude() != null)
	        .collect(Collectors.toList());

	    if (visits.size() == 0) {
	    	return 0.0;
	    }

	    double totalDistance = 0.0;
	    Location firstVisitLocation = visits.get(0).getLocation();
	    Location lastVisitLocation = visits.get(visits.size() - 1).getLocation();
	    
	    totalDistance += calculateDistance(firstVisitLocation.getLatitude(), firstVisitLocation.getLongitude(), delegate.getLatitude(), delegate.getLongitude());
	    totalDistance += calculateDistance(delegate.getLatitude(), delegate.getLongitude(), lastVisitLocation.getLatitude(), lastVisitLocation.getLongitude());
	    
	    
	    for (int i = 0; i < visits.size() - 1; i++) {
	        totalDistance += calculateDistance(visits.get(i).getLocation().getLatitude(), visits.get(i).getLocation().getLongitude(), visits.get(i + 1).getLocation().getLatitude(), visits.get(i + 1).getLocation().getLongitude());
	    }
	    return totalDistance;
	}
	
	
	private double calculateDistance( Double latOrigin, Double lngOrigin, Double latDest, Double lngDest) throws BirdnotesException {

	    ResponseEntity<DistanceResponse> response = distanceService.distance(latOrigin, lngOrigin, latDest, lngDest);
	    DistanceResponse distanceResp = response.getBody();

	    if (distanceResp != null && !distanceResp.getRows().isEmpty()) {
	        return extractDistanceFromRow(distanceResp.getRows().get(0)) / 1000.0;
	    }
	    return 0.0;
	}

	private double calculateDistanceBetweenVisits(Visit currentVisit, Visit nextVisit) throws BirdnotesException {
	    Double latOrigin = currentVisit.getLocation().getLatitude();
	    Double lngOrigin = currentVisit.getLocation().getLongitude();
	    Double latDest = nextVisit.getLocation().getLatitude();
	    Double lngDest = nextVisit.getLocation().getLongitude();

	    ResponseEntity<DistanceResponse> response = distanceService.distance(latOrigin, lngOrigin, latDest, lngDest);
	    DistanceResponse distanceResp = response.getBody();

	    if (distanceResp != null && !distanceResp.getRows().isEmpty()) {
	        return extractDistanceFromRow(distanceResp.getRows().get(0)) / 1000.0;
	    }
	    return 0.0;
	}

	private double extractDistanceFromRow(Object row) {
	    if (row instanceof Map) {
	        @SuppressWarnings("unchecked")
	        Map<String, Object> rowMap = (Map<String, Object>) row;
	        @SuppressWarnings("unchecked")
	        List<Map<String, Object>> elements = (List<Map<String, Object>>) rowMap.get("elements");
	        if (!elements.isEmpty()) {
	            @SuppressWarnings("unchecked")
	            Map<String, Object> distance = (Map<String, Object>) elements.get(0).get("distance");
	            return ((Number) distance.get("value")).doubleValue();
	        }
	    }
	    return 0.0;
	}
	
	@Override
	public void updateUserStatus(Long delegateId, Boolean active) throws BirdnotesException {
	    Delegate delegate = delegateRepository.findOne(delegateId);
	    
	    if (delegate == null || delegate.getUser() == null) {
	        throw new BirdnotesException("Delegate not found for User ID: " + delegateId);
	    }

	    User user = delegate.getUser();
	    user.setActive(active);

	    userRepository.save(user);
	}


}


