package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

import com.intellitech.birdnotes.model.FreeQuantityRuleItem;

public class FreeQuantityRuleDto implements Serializable {


	private static final long serialVersionUID = 1L;
	private Long id;
	private List<Long> productIds;
	private List<Long> potentialIds;
	private List<Long> prospectTypeIds;
	private Long productId;
	private Long potentialId;
	private Long prospectTypeId;
	private List<ProductDto> product;
	private List<PotentialDto> potential;
	private List<ProspectTypeDto> prospectType;
	private List<String> productName;
	private List<String> potentialName;
	private List<String> prospectTypeName;
	private Integer orderQuantity;
	private Integer freeQuantity;
	private Integer labGratuity;
	private String name;
	private List<FreeQuantityRuleItemDto> freeQuantityRuleItem;
	
	
	

	public FreeQuantityRuleDto(Long id, Long productId, Long potentialId, Integer orderQuantity, Integer freeQuantity,
			Integer labGratuity, String name) {
		super();
		this.id = id;
		this.productId = productId;
		this.potentialId = potentialId;
		this.orderQuantity = orderQuantity;
		this.freeQuantity = freeQuantity;
		this.labGratuity = labGratuity;
		this.name = name;
	}


	public List<FreeQuantityRuleItemDto> getFreeQuantityRuleItem() {
		return freeQuantityRuleItem;
	}


	public void setFreeQuantityRuleItem(List<FreeQuantityRuleItemDto> freeQuantityRuleItem) {
		this.freeQuantityRuleItem = freeQuantityRuleItem;
	}


	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	public List<Long> getProductIds() {
		return productIds;
	}


	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}


	public List<Long> getPotentialIds() {
		return potentialIds;
	}


	public void setPotentialIds(List<Long> potentialIds) {
		this.potentialIds = potentialIds;
	}


	public List<ProductDto> getProduct() {
		return product;
	}


	public void setProduct(List<ProductDto> product) {
		this.product = product;
	}


	public List<PotentialDto> getPotential() {
		return potential;
	}


	public void setPotential(List<PotentialDto> potential) {
		this.potential = potential;
	}


	public Integer getOrderQuantity() {
		return orderQuantity;
	}


	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}


	public Integer getFreeQuantity() {
		return freeQuantity;
	}


	public void setFreeQuantity(Integer freeQuantity) {
		this.freeQuantity = freeQuantity;
	}


	public Integer getLabGratuity() {
		return labGratuity;
	}


	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}


	public FreeQuantityRuleDto() {
		super();
		
	}


	public Long getProductId() {
		return productId;
	}


	public void setProductId(Long productId) {
		this.productId = productId;
	}


	public Long getPotentialId() {
		return potentialId;
	}


	public void setPotentialId(Long potentialId) {
		this.potentialId = potentialId;
	}


	public String getName() {
		return name;
	}


	public void setName(String name) {
		this.name = name;
	}


	public List<Long> getProspectTypeIds() {
		return prospectTypeIds;
	}


	public void setProspectTypeIds(List<Long> prospectTypeIds) {
		this.prospectTypeIds = prospectTypeIds;
	}


	public Long getProspectTypeId() {
		return prospectTypeId;
	}


	public void setProspectTypeId(Long prospectTypeId) {
		this.prospectTypeId = prospectTypeId;
	}


	public List<ProspectTypeDto> getProspectType() {
		return prospectType;
	}


	public void setProspectTypes(List<ProspectTypeDto> prospectType) {
		this.prospectType = prospectType;
	}


	public List<String> getProductName() {
		return productName;
	}


	public void setProductName(List<String> productName) {
		this.productName = productName;
	}


	public List<String> getPotentialName() {
		return potentialName;
	}


	public void setPotentialName(List<String> potentialName) {
		this.potentialName = potentialName;
	}


	public List<String> getProspectTypeName() {
		return prospectTypeName;
	}


	public void setProspectTypeName(List<String> prospectTypeName) {
		this.prospectTypeName = prospectTypeName;
	}


	public void setProspectType(List<ProspectTypeDto> prospectType) {
		this.prospectType = prospectType;
	}
	

	
}
