package com.intellitech.birdnotes.controller;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;

import com.intellitech.birdnotes.data.dto.ProductCompareDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.ProductCoverageResult;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.service.RangeService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.impl.ImportServiceImpl;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/products")
public class ProductController {

	private static final Logger LOG = LoggerFactory.getLogger(ProductController.class);

	@Autowired
	private  ProductService productService;
	@Autowired
	private StorageService storageService;
	@Autowired
	UserService userService;
	@Autowired
	private RangeService gammeService;
	
	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${productPath}")
	private String productPath;
	
	
	@RequestMapping(value = "updateQuantityFromErp", method = RequestMethod.GET)
	public ResponseEntity<String> startPrediction() {

		try {
			productService.updateQuantityFromErp();
			return new ResponseEntity<>("ok", HttpStatus.OK);

		} catch (Exception e) {

			LOG.error("Error in startPrediction", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}
	

	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("PRODUCT_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getProductCoverage/{productId}/{date}", method = RequestMethod.GET)
	public ResponseEntity<ProductCoverageResult> getProductCoverage(@PathVariable("productId") Long productId,
			@PathVariable("date") String date) {
		try {
			if (userService.checkHasPermission("PRODUCT_VIEW")) {
				ProductCoverageResult back = productService.getProductCoverage(productId, date);
				return new ResponseEntity<>(back, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getProductCoverage", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/saveProduct", method = RequestMethod.POST)
	public ResponseEntity<String> saveProduct(@RequestPart("productRequest") ProductDto productRequest,
	        @RequestPart(name = "files", required = false) MultipartFile[] files) {

	    try {
	        if (userService.checkHasPermission("PRODUCT_ADD") || userService.checkHasPermission("PRODUCT_EDIT")) {
	            Product productSaved = productService.saveProduct(productRequest);
	            if (productRequest.getDeletedFiles().size() > 0) {
	                storageService.deleteFiles(uploadPath + productPath + "/" + productSaved.getId(), productRequest.getDeletedFiles());
	            }
	            if (files != null) {
	                storageService.storeFiles(files, uploadPath + productPath + "/" + productSaved.getId());
	            }
	            if (productSaved != null) {
	                return new ResponseEntity<>(productSaved.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving product", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving product", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/findProductsByRange/{rangeIds}", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> findProductsByGamme(@PathVariable("rangeIds") List<Integer> rangeIds) {
		try {
			if (userService.checkHasPermission("PRODUCT_VIEW")) {
				List<ProductDto> productDtos = productService.findProductsByGamme(rangeIds);
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting products by gamme", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deleteProduct/{productId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteProduct(@PathVariable("productId") Long productId) {
		try {
			if (userService.checkHasPermission("PRODUCT_DELETE")) {
				productService.deleteProduct(productId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in deleteProduct", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}}



	@RequestMapping(value = "updateProduct", method = RequestMethod.PUT)
	public ResponseEntity<String> updateProduct(@RequestPart("productDto") ProductDto productDto,
			@RequestPart(name = "files", required = false) MultipartFile[] files) {
		try {
			if (userService.checkHasPermission("PRODUCT_EDIT")) {
				productService.updateProduct(productDto);
				storageService.deleteFiles(uploadPath + productPath + "/" + productDto.getId(),
						productDto.getDeletedFiles());
				if (files != null) {
					storageService.storeFiles(files, uploadPath + productPath + "/" + productDto.getId());
				}

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException fe) {
			LOG.error("An exception occurred :Non-Authoritative Information when update product", fe);
			return new ResponseEntity<>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in updateProduct", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "compareProductsPerMonth/{month}/{year}", method = RequestMethod.GET)
	public ResponseEntity<List<ProductCompareDto>> compareProductsPerMonth(@PathVariable("month") Integer month,
			@PathVariable("year") Integer year) {
		try {
			if (userService.checkHasPermission("PRODUCT_VIEW")) {
				List<ProductCompareDto> result = productService.compareProductsPerMonth(month, year);
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in compareProductsPerMonth", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}

	}

	@RequestMapping(value = "compareProductsPerWeek/{mondayDate}", method = RequestMethod.GET)
	public ResponseEntity<List<ProductCompareDto>> compareProductsPerWeek(@PathVariable("mondayDate") Date mondayDate) {
		try {
			if (userService.checkHasPermission("PRODUCT_VIEW")) {
				List<ProductCompareDto> result = productService.compareProductsPerWeek(mondayDate);
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in compareProductsPerWeek", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "findAllGamme", method = RequestMethod.GET)
	public ResponseEntity<List<RangeDto>> findAllGamme() {

		try {
			if (userService.checkHasPermission("PRODUCT_EDIT")) {
				List<RangeDto> gammeDto = gammeService.findAll();
				return new ResponseEntity<>(gammeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all gamme", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
@RequestMapping(value ="import", method = RequestMethod.POST)
	
	public ResponseEntity<Map<String,List<ProductDto>>> importProduct(@RequestParam("file") MultipartFile file) throws BirdnotesException {
		try {
			File tmpFile = File.createTempFile("import", file.getOriginalFilename());
			file.transferTo(tmpFile);
			if (userService.checkHasPermission("PRODUCT_ADD")) {

				Map<String,List<ProductDto>> productList = productService.importProduct(tmpFile.getPath());

				return new ResponseEntity<>(productList, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}
			
			
			
		}  catch (Exception e) {

			LOG.error("An exception occurred while importing product", e);

			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	
}
