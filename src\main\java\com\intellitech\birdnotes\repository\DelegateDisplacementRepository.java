package com.intellitech.birdnotes.repository;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.DelegateDisplacement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface DelegateDisplacementRepository extends JpaRepository<DelegateDisplacement, Long> {
    List<DelegateDisplacement> findByDelegateIdAndDateBetween(Long delegateId, Date startDate, Date endDate);
    DelegateDisplacement findByDelegateAndDate(Delegate delegate, Date date);

}

