package com.intellitech.birdnotes.dao.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.hibernate.jpa.QueryHints;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.dto.GiftSupplyDto;
import com.intellitech.birdnotes.model.dto.GoalSum;
import com.intellitech.birdnotes.model.dto.ProspectDistribution;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryGroupDto;
import com.intellitech.birdnotes.model.request.ProspectListRequest;

@Repository
public class DynamicQueriesImpl implements DynamicQueries {
	@PersistenceContext
	private EntityManager entityManager;

	@SuppressWarnings("unchecked")
	@Override
	public List<GoalSum> findGoalsSum(String queryString, Date startDate, Date endDate) {

		Query query = entityManager.createQuery(queryString, GoalSum.class);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", endDate);

		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VisitsProducts> findVisits(String query, Map<String, Object> parameters, Integer first, Integer rows) {
		Query result = entityManager.createQuery(query, VisitsProducts.class);
		if(first != null && rows != null) {
			result.setFirstResult(first);
			result.setMaxResults(rows);
		}		
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<SampleSupply> findSample(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, SampleSupply.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}
		//return result.setHint(QueryHints.HINT_PASS_DISTINCT_THROUGH, false).getResultList();
		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PurchaseOrderTemplate> findPurchaseOrderTemplate(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, PurchaseOrderTemplate.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Commission> findCommission(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, Commission.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Long> findPlanningOfWeeksToShow(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, Long.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<GiftSupplyDto> findGadget(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, GiftSupplyDto.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<VisitHistoryGroupDto> findGroupSearch(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, VisitHistoryGroupDto.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Prospect> findProspects(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, Prospect.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<ProspectDistribution> findProspectsDistribution(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, ProspectDistribution.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Prospect> findProspectsList(String queryString, Map<String, Object> parameters,
			ProspectListRequest prospectListRequest, Integer limit) {

		Query query = entityManager.createQuery(queryString, Prospect.class);
		if (limit != null) {
			query.setFirstResult(0);
			query.setMaxResults(limit);
		} else {
			query.setFirstResult(prospectListRequest.getFirst());
			query.setMaxResults(prospectListRequest.getRows());
		}
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			query.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public Long findProspectsCount(String queryString, Map<String, Object> parameters) {

		Query query = entityManager.createQuery(queryString, Long.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			query.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return (Long) query.getSingleResult();
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public Long findVisitProductsCount(String queryString, Map<String, Object> parameters) {

		Query query = entityManager.createQuery(queryString, Long.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			query.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return (Long) query.getSingleResult();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<ProspectDto> findProspectsListCartography(String queryString, Map<String, Object> parameters) {

		Query query = entityManager.createQuery(queryString, ProspectDto.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			query.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return query.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<PlanningObjectiveCountDto> findPlanifiedProspects(String query, Map<String, Object> parameters) {
		Query result = entityManager.createQuery(query, PlanningObjectiveCountDto.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Prospect> findPatients(String query) {
		Query result = entityManager.createQuery(query, Prospect.class);
		return result.setMaxResults(10).getResultList();
	}

}
