package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class VisitRequestDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Long selectedUser;
	private Long selectedProspect;
  	private String firstDate;
	private String lastDate;
	private boolean displayProspectsNonVisisted;
	private boolean displayVisistedProspects;
	private Long selectedSector;
	private Long selectedLocality;
	private String selectedActivity;
	private String historicMode;
	private Long selectedPotential; 
  	private Long selectedSpeciality;
  	private Long selectedProduct;
  	private Long selectedValue;
  	private Long selectedGroup;
  	private Integer selectedProductSatisfaction;
  	private Integer selectedPresentationOrder;
  	private String coverageTargetType;
  	private String selectedCommentRating;
  	private boolean order;
  	private String comment;
  	private Integer first;
  	private Integer rows;
  	private boolean pagination;
  	
	public Long getSelectedUser() {
		return selectedUser;
	}
	public void setSelectedUser(Long selectedUser) {
		this.selectedUser = selectedUser;
	}
	public Long getSelectedProspect() {
		return selectedProspect;
	}
	public void setSelectedProspect(Long selectedProspect) {
		this.selectedProspect = selectedProspect;
	}
	public String getFirstDate() {
		return firstDate;
	}
	public void setFirstDate(String firstDate) {
		this.firstDate = firstDate;
	}
	public String getLastDate() {
		return lastDate;
	}
	public void setLastDate(String lastDate) {
		this.lastDate = lastDate;
	}
	public boolean getDisplayProspectsNonVisisted() {
		return displayProspectsNonVisisted;
	}
	public void setDisplayProspectsNonVisisted(boolean displayProspectsNonVisisted) {
		this.displayProspectsNonVisisted = displayProspectsNonVisisted;
	}
	
	public String getHistoricMode() {
		return historicMode;
	}
	public void setHistoricMode(String historicMode) {
		this.historicMode = historicMode;
	}
	public Long getSelectedSector() {
		return selectedSector;
	}
	public void setSelectedSector(Long selectedSector) {
		this.selectedSector = selectedSector;
	}
	public Long getSelectedLocality() {
		return selectedLocality;
	}
	public void setSelectedLocality(Long selectedLocality) {
		this.selectedLocality = selectedLocality;
	}
	public String getSelectedActivity() {
		return selectedActivity;
	}
	public void setSelectedActivity(String selectedActivity) {
		this.selectedActivity = selectedActivity;
	}
	public Long getSelectedPotential() {
		return selectedPotential;
	}
	public void setSelectedPotential(Long selectedPotential) {
		this.selectedPotential = selectedPotential;
	}
	public Long getSelectedSpeciality() {
		return selectedSpeciality;
	}
	public void setSelectedSpeciality(Long selectedSpeciality) {
		this.selectedSpeciality = selectedSpeciality;
	}
	public Long getSelectedValue() {
		return selectedValue;
	}
	public void setSelectedValue(Long selectedValue) {
		this.selectedValue = selectedValue;
	}
	public Long getSelectedGroup() {
		return selectedGroup;
	}
	public void setSelectedGroup(Long selectedGroup) {
		this.selectedGroup = selectedGroup;
	}
	public Long getSelectedProduct() {
		return selectedProduct;
	}
	public void setSelectedProduct(Long selectedProduct) {
		this.selectedProduct = selectedProduct;
	}
	public Integer getSelectedProductSatisfaction() {
		return selectedProductSatisfaction;
	}
	public void setSelectedProductSatisfaction(Integer selectedProductSatisfaction) {
		this.selectedProductSatisfaction = selectedProductSatisfaction;
	}
	public Integer getSelectedPresentationOrder() {
		return selectedPresentationOrder;
	}
	public void setSelectedPresentationOrder(Integer selectedPresentationOrder) {
		this.selectedPresentationOrder = selectedPresentationOrder;
	}
	public boolean isDisplayVisistedProspects() {
		return displayVisistedProspects;
	}
	public void setDisplayVisistedProspects(boolean displayVisistedProspects) {
		this.displayVisistedProspects = displayVisistedProspects;
	}
	public String getCoverageTargetType() {
		return coverageTargetType;
	}
	public void setCoverageTargetType(String coverageTargetType) {
		this.coverageTargetType = coverageTargetType;
	}
	public String getSelectedCommentRating() {
		return selectedCommentRating;
	}
	public void setSelectedCommentRating(String selectedCommentRating) {
		this.selectedCommentRating = selectedCommentRating;
	}
	public boolean isOrder() {
		return order;
	}
	public void setOrder(boolean order) {
		this.order = order;
	}
	public Integer getFirst() {
		return first;
	}
	public void setFirst(Integer first) {
		this.first = first;
	}
	public Integer getRows() {
		return rows;
	}
	public void setRows(Integer rows) {
		this.rows = rows;
	}
	public boolean isPagination() {
		return pagination;
	}
	public void setPagination(boolean pagination) {
		this.pagination = pagination;
	}
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}

	
}