package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.PRODUCT_POTENTIAL, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class PotentielProduit implements Serializable {


	private static final long serialVersionUID = 1L;
	private Long id;
	private Prospect prospect;
	private Potential potential;
	private Product product;
	
	
	@Id
	@SequenceGenerator(name = Sequences.PRODUCT_POTENTIAL_SEQUENCE, sequenceName = Sequences.PRODUCT_POTENTIAL_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PRODUCT_POTENTIAL_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID)
	public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

	@ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.POTENTIAL_ID)
	public Potential getPotential() {
		return potential;
	}
	public void setPotential(Potential potential) {
		this.potential = potential;
	}


	
	@ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

}
