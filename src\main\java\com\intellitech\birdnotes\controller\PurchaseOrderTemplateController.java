package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateItemDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.PurchaseOrderTemplateService;
import com.intellitech.birdnotes.service.SampleSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/purchaseOrderTemplate")
public class PurchaseOrderTemplateController {
	private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderTemplateController.class);
	@Autowired
	private PurchaseOrderTemplateService purchaseOrderTemplateService;
	@Autowired
	UserService userService;
	@Autowired
	private GadgetService gadgetService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	DelegateService delegateService;
	@Autowired
	private ProductService productService;

	@RequestMapping(value = "/savePurchaseOrderTemplate", method = RequestMethod.POST)
	public ResponseEntity<String> savePurchaseOrderTemplate(@RequestBody PurchaseOrderTemplateDto purchaseOrderTemplateDto) {
	    try {
	        if (userService.checkHasPermission("ORDER_TEMPLATE_ADD") || userService.checkHasPermission("ORDER_TEMPLATE_EDIT")) {
	            PurchaseOrderTemplate savedPurchaseOrderTemplate = purchaseOrderTemplateService.savePurchaseOrderTemplate(purchaseOrderTemplateDto);
	            if (savedPurchaseOrderTemplate != null) {
	                return new ResponseEntity<>(savedPurchaseOrderTemplate.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }
	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving the Purchase Order Template", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving the Purchase Order Template", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePurchaseOrderTemplate(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("ORDER_TEMPLATE_DELETE")) {
	            purchaseOrderTemplateService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.LOCALITY_TO_DELETE_ALREADY_DELETED, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting purchase order template", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the purchase order template with id =" + id, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "/deletePurchaseOrderTemplateItemId/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePurchaseOrderTemplateItem(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("ORDER_TEMPLATE_DELETE")) {
	            purchaseOrderTemplateService.deletePurchaseOrderTemplateItem(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting purchase order template item", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the purchase order template item with id =" + id, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/getPurchaseOrderTemplateByUserProductAndDate", method = RequestMethod.POST)

	public ResponseEntity<List<PurchaseOrderTemplateDto>> getPurchaseOrderTemplateByUserProductAndDate(
			@RequestBody PurchaseOrderTemplateDto purchaseOrderTemplateRequestDto) {

		try {
			if (userService.checkHasPermission("ORDER_TEMPLATE_VIEW")) {
				List<PurchaseOrderTemplateDto> purchaseOrderTemplateDto = purchaseOrderTemplateService
						.getPurchaseOrderTemplateByUserProductAndDate(purchaseOrderTemplateRequestDto);
				return new ResponseEntity<>(purchaseOrderTemplateDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting all PurchaseOrderTemplateDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "/getPurchaseOrderTemplateItem", method = RequestMethod.POST)

	public ResponseEntity<List<PurchaseOrderTemplateItemDto>> getPurchaseOrderTemplateItem(
			@RequestBody PurchaseOrderTemplateDto purchaseOrderTemplateRequestDto) {

		try {
			if (userService.checkHasPermission("ORDER_TEMPLATE_VIEW")) {
				List<PurchaseOrderTemplateItemDto> purchaseOrderTemplateItemDto = purchaseOrderTemplateService
						.getPurchaseOrderTemplateItem(purchaseOrderTemplateRequestDto);
				return new ResponseEntity<>(purchaseOrderTemplateItemDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting PurchaseOrderTemplateItemDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "getAllGift", method = RequestMethod.GET)
	public ResponseEntity<List<GiftDto>> getAllGift() {
		try {
			if (userService.checkHasPermission("ORDER_TEMPLATE_VIEW")) {
				List<GiftDto> giftDtos = gadgetService.findAllPurchaseOrderTemplateGifts();
				return new ResponseEntity<>(giftDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in find all gift", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("ORDER_TEMPLATE_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("ORDER_TEMPLATE_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
}
