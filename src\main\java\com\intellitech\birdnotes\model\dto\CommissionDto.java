package com.intellitech.birdnotes.model.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.intellitech.birdnotes.data.dto.ProductRevenueByMonthDto;
import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.convertor.ConvertGiftToDto;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;



public class CommissionDto {
	private Long id;

	private String name;
	
	private Date firstDate;
	
	private Date lastDate;
	
	private List<ProductRevenueByMonthDto> productRevenueByMonth;
	
	private Float delegateCommission;

	private List<Long> usersId;
	private List<Long> productsId;
	private List<Long> wholesalersId;
	
	private List<UserDto> users;
	private List<ProductDto> products;
	private List<ProspectDto> wholesalers;
	
	private Long selectedUserId;
	private Long selectedProductId;
	private Long selectedWholesalerId;
	
	private String type;
	
	
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	private UserToDtoConvertor userToDtoConvertor ;
	
	private ProductToDtoConvertor productToDtoConvertor ;
	
	private WholesalerToDtoConvertor wholesalerToDtoConvertor;
	
	private List<CommissionItemDto> commissionItem;

	
	public List<CommissionItemDto> getCommissionItem() {
		return commissionItem;
	}

	public void setCommissionItem(List<CommissionItemDto> commissionItem) {
		this.commissionItem = commissionItem;
	}

	public CommissionDto(Commission commission) throws BirdnotesException {
		this.products = new ArrayList<>();
		this.users = new ArrayList<>();
		userToDtoConvertor = new UserToDtoConvertor();
		productToDtoConvertor = new ProductToDtoConvertor();
		wholesalerToDtoConvertor = new WholesalerToDtoConvertor();
		this.id = commission.getId();
		this.firstDate = commission.getFirstDate();
		this.lastDate = commission.getLastDate();
		this.name = commission.getName();
		for(Product product : commission.getProducts()) {
			this.products.add(productToDtoConvertor.convert(product));
		}
		/*if(commission.getWholesalers()!=null && commission.getWholesalers().size()>0) {
			for(Wholesaler wholesaler : commission.getWholesalers()) {
				this.wholesalers.add(wholesalerToDtoConvertor.convert(wholesaler));
			}
		}*/
		
		for(User user :commission.getUsers()) {
			UserDto userDto = userToDtoConvertor.convert(user);
			this.users.add(userDto);
		}
		
	}

	public CommissionDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getFirstDate() {
		return firstDate;
	}

	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}

	public Date getLastDate() {
		return lastDate;
	}

	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}

	public List<Long> getUsersId() {
		return usersId;
	}

	public void setUsersId(List<Long> usersId) {
		this.usersId = usersId;
	}

	public List<UserDto> getUsers() {
		return users;
	}

	public void setUsers(List<UserDto> users) {
		this.users = users;
	}


	public Long getSelectedUserId() {
		return selectedUserId;
	}

	public void setSelectedUserId(Long selectedUserId) {
		this.selectedUserId = selectedUserId;
	}

	public List<Long> getProductsId() {
		return productsId;
	}

	public void setProductsId(List<Long> productsId) {
		this.productsId = productsId;
	}

	public List<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(List<ProductDto> products) {
		this.products = products;
	}

	public Long getSelectedProductId() {
		return selectedProductId;
	}

	public void setSelectedProductId(Long selectedProductId) {
		this.selectedProductId = selectedProductId;
	}

	public UserToDtoConvertor getUserToDtoConvertor() {
		return userToDtoConvertor;
	}

	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}

	public ProductToDtoConvertor getProductToDtoConvertor() {
		return productToDtoConvertor;
	}

	public void setProductToDtoConvertor(ProductToDtoConvertor productToDtoConvertor) {
		this.productToDtoConvertor = productToDtoConvertor;
	}

	

	public List<ProductRevenueByMonthDto> getProductRevenueByMonth() {
		return productRevenueByMonth;
	}

	public void setProductRevenueByMonth(List<ProductRevenueByMonthDto> productRevenueByMonth) {
		this.productRevenueByMonth = productRevenueByMonth;
	}

	public Float getDelegateCommission() {
		return delegateCommission;
	}

	public void setDelegateCommission(Float delegateCommission) {
		this.delegateCommission = delegateCommission;
	}

	public List<Long> getWholesalersId() {
		return wholesalersId;
	}

	public void setWholesalersId(List<Long> wholesalersId) {
		this.wholesalersId = wholesalersId;
	}

	

	public List<ProspectDto> getWholesalers() {
		return wholesalers;
	}

	public void setWholesalers(List<ProspectDto> wholesalers) {
		this.wholesalers = wholesalers;
	}

	public Long getSelectedWholesalerId() {
		return selectedWholesalerId;
	}

	public void setSelectedWholesalerId(Long selectedWholesalerId) {
		this.selectedWholesalerId = selectedWholesalerId;
	}

	public WholesalerToDtoConvertor getWholesalerToDtoConvertor() {
		return wholesalerToDtoConvertor;
	}

	public void setWholesalerToDtoConvertor(WholesalerToDtoConvertor wholesalerToDtoConvertor) {
		this.wholesalerToDtoConvertor = wholesalerToDtoConvertor;
	}

	


	}
