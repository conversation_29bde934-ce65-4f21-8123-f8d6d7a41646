package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;


public class MessageDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long identifier;
	
	private Date date;
	
	private Long userId;
		
	private String type;
	
	private String text;
	
	private UserDto userDto;

	public MessageDto() {
		super();
	}




	public Long getId() {
		return id;
	}




	public void setId(Long id) {
		this.id = id;
	}


	public Long getIdentifier() {
		return identifier;
	}




	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}




	public UserDto getUserDto() {
		return userDto;
	}




	public void setUserDto(UserDto userDto) {
		this.userDto = userDto;
	}




	public Date getDate() {
		return date;
	}




	public void setDate(Date date) {
		this.date = date;
	}




	public Long getUserId() {
		return userId;
	}




	public void setUserId(Long userId) {
		this.userId = userId;
	}




	public String getType() {
		return type;
	}




	public void setType(String type) {
		this.type = type;
	}




	public String getText() {
		return text;
	}




	public void setText(String text) {
		this.text = text;
	}

	
}
