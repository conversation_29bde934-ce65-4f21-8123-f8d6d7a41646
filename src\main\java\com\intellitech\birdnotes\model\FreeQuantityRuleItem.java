package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.FREE_QUANTITY_RULE_ITEM, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class FreeQuantityRuleItem implements Serializable {
	
	private static final long serialVersionUID = 1L;
		
	@Id
	@SequenceGenerator(name = Sequences.FREE_QUANTITY_RULE_ITEM_SEQUENCE, sequenceName = Sequences.FREE_QUANTITY_RULE_ITEM_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.FREE_QUANTITY_RULE_ITEM_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.ORDER_QUANTITY)
	private Integer orderQuantity;
	
	@Column(name = Columns.FREE_QUANTITY)
	private Integer freeQuantity;
	
	@Column(name = Columns.LAB_GRATUITY)
	private Integer labGratuity;
	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.FREE_QUANTITY_RULE_ID, unique = false)
	private FreeQuantityRule freeQuantityRule;
	

	
	public FreeQuantityRule getFreeQuantityRule() {
		return freeQuantityRule;
	}

	public void setFreeQuantityRule(FreeQuantityRule freeQuantityRule) {
		this.freeQuantityRule = freeQuantityRule;
	}

	public Integer getLabGratuity() {
		return labGratuity;
	}

	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}
		

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	
	public Integer getOrderQuantity() {
		return orderQuantity;
	}

	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}

	public Integer getFreeQuantity() {
		return freeQuantity;
	}

	public void setFreeQuantity(Integer freeQuantity) {
		this.freeQuantity = freeQuantity;
	}

	public FreeQuantityRuleItem() {
		super();

	}
}
