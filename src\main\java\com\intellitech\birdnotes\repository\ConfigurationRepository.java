package com.intellitech.birdnotes.repository;


import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Configuration;

@Repository
public interface ConfigurationRepository extends JpaRepository<Configuration, Integer>{

	Configuration findByName(String name);
	
	Configuration findById(Integer id);
	
	
	

	
}
