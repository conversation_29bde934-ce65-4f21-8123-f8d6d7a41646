package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.convertor.ConvertLocalityToDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.LocalityRequestDto;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.service.LocalityService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("localityService")
@Transactional
public class LocalityServiceImpl implements LocalityService {

	private LocalityRepository localityRepository;
	private SectorRepository sectorRepository;
	private ConvertLocalityToDto convertLocalityToDto;
	private ProspectRepository prospectRepository;
	private SectorService sectorService;
	
	@Autowired
	UserService userService;

	@Autowired
	LocalityServiceImpl(ProspectRepository prospectRepository, ConvertLocalityToDto convertLocalityToDto,
			SectorRepository sectorRepository, LocalityRepository localityRepository,SectorService sectorService) {
		
		this.localityRepository = localityRepository;
		this.sectorRepository = sectorRepository;
		this.convertLocalityToDto = convertLocalityToDto;
		this.prospectRepository = prospectRepository;
		this.sectorService=sectorService;
	}

	@Override
	public List<LocalityDto> findAll() throws BirdnotesException {
		List<LocalityDto> back = new ArrayList<>();
		List<Locality> allLocalities = localityRepository.findAll();
		for (Locality locality : allLocalities) {
			back.add(convertLocalityToDto.convert(locality));
		}
		return back;
	}

	@Override
	public void saveAllLocalities(List<LocalityRequestDto> localityRequestDtos) throws BirdnotesException {
		for(LocalityRequestDto localityRequestDto:localityRequestDtos) {
		Locality locality = new Locality();
		if (localityRequestDto.getName() == null || localityRequestDto.getName().equals("")) {
			throw new BirdnotesException("Locality name is empty");
		}
		if (localityRequestDto.getSectorName() == null) {
			throw new BirdnotesException("Sector name is empty");
		}
		Sector sector = sectorRepository.findFirstByNameIgnoreCase(localityRequestDto.getSectorName());
		if (sector == null) {
			sector=sectorService.addSector(localityRequestDto);
		}
		locality.setName(localityRequestDto.getName());
		locality.setSector(sector);
		localityRepository.save(locality);
		}
	}
	@Override
	public Locality add(LocalityRequestDto localityRequestDto) throws BirdnotesException {
	    
	    if (localityRequestDto == null || localityRequestDto.getName() == null || "".equals(localityRequestDto.getName())) {
	        throw new BirdnotesException("Locality name is empty");
	    }
	    
	    if (localityRequestDto.getSectorId() == null) {
	        throw new BirdnotesException("Sector id is empty");
	    }

	    Locality existingLocality = localityRepository.findFirstByNameIgnoreCaseAndSectorId(localityRequestDto.getName(), localityRequestDto.getSectorId());
	    if (existingLocality != null) {
	        throw new BirdnotesException(Exceptions.ALREADY_EXIST);
	    }

	    Sector sector = sectorRepository.findOne(localityRequestDto.getSectorId());
	    if (sector == null) {
	        throw new BirdnotesException("Sector has been deleted");
	    }

	    Locality locality = new Locality();
	    locality.setName(localityRequestDto.getName());
	    locality.setSector(sector);
	    
	    return localityRepository.save(locality);
	}


	@Override
	public List<Locality> saveAll(List<LocalityRequestDto> localityRequestDtos) throws BirdnotesException {
		List<Locality> localities = new ArrayList<>();
		for (LocalityRequestDto localityRequestDto : localityRequestDtos) {
			localities.add(add(localityRequestDto));
		}
		return localities;
	}

	@Override
	public void delete(Long id) throws BirdnotesException {
		localityRepository.deleteByID(id);
	}

	@Override
	public List<LocalityDto> findBySector(Long sectorId) throws BirdnotesException {
		List<LocalityDto> back = new ArrayList<>();
		List<Locality> allLocalities = localityRepository.findBySectorId(sectorId);
		for (Locality locality : allLocalities) {
			back.add(convertLocalityToDto.convert(locality));
		}
		return back;
	}

	@Override
	public Locality saveLocality(LocalityDto localityDto) throws BirdnotesException {
		if (localityDto == null || localityDto.getId() == null) {
			throw new BirdnotesException(Exceptions.LOCALITY_DTO_NULL);
		}

		if (localityDto.getName() == null || localityDto.getName().isEmpty()) {
			throw new BirdnotesException(Exceptions.EMPTY_NAME);
		}

		Locality result = localityRepository.findFirstByNameIgnoreCaseAndSectorId(localityDto.getName(),localityDto.getSectorId());
		if (result!=null) {
			throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
		}
		
		Locality locality = null;
		if(localityDto.getId() != null) {
			locality = localityRepository.findOne(localityDto.getId());
			
		}
		if (locality == null) {
			locality = new Locality();
		}else {
			prospectRepository.updateProspectLocality(localityDto.getSectorId(), localityDto.getId());
		}
		setSectorToLocality(locality, localityDto);
		locality.setName(localityDto.getName());
		return localityRepository.save(locality);

	}

	private void setSectorToLocality(Locality localityToUpdate, LocalityDto localityDto) throws BirdnotesException {
		Sector sector = sectorRepository.findById(localityDto.getSectorId());
		if (sector == null) {
			throw new BirdnotesException(Exceptions.SECTOR_DELETED);
		}

		localityToUpdate.setSector(sector);
	}

	@Override
	public LocalityDto findByName(String name) throws BirdnotesException {
		Locality locality=localityRepository.findByName(name);
		LocalityDto localityDto=convertLocalityToDto.convert(locality);
		return localityDto;
	}
	@Override
	public LocalityDto findLocalityDto(String localityName,String sectorName,List<LocalityDto>localityDtos)   {
		for (LocalityDto localityDto : localityDtos) {
			if (localityDto.getName().equals(localityName)&&localityDto.getSectorName().equals(sectorName)) {
				return localityDto;
			}
		}
		return null;
		 
	}
}
