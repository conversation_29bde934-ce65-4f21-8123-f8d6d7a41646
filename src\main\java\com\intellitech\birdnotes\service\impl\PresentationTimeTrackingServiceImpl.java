package com.intellitech.birdnotes.service.impl;

import java.io.File;
import static java.time.temporal.ChronoUnit.SECONDS;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.data.dto.SurveyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Criteria;
import com.intellitech.birdnotes.model.CriteriaList;
import com.intellitech.birdnotes.model.PresentationTimeTracking;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Survey;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.dto.PresentationTimeTrackingDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.SurveyDto;
import com.intellitech.birdnotes.repository.PresentationTimeTrackingRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.SurveyRepository;
import com.intellitech.birdnotes.service.PresentationTimeTrackingService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.SurveyService;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import com.intellitech.birdnotes.repository.ConfigurationRepository;

@Service("presentationTimeTrackingService")
@Transactional
public class PresentationTimeTrackingServiceImpl implements PresentationTimeTrackingService {
	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	private PresentationTimeTrackingRepository presentationTimeTrackingRepository;
	
	@Autowired
	private ConfigurationRepository configureRepository;
	
	 @Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${productPath}")
	private String productPath;
	
	@Override
	public List<PresentationTimeTrackingDto> getTimeTracking(Long visitProductId, Long productId) throws BirdnotesException {
		List<PresentationTimeTrackingDto> presentationTimeTrackingDtoList = new ArrayList<>();
		Configuration config = configureRepository.findById(1);
		List<PresentationTimeTracking> presentationTimeTrackingList = presentationTimeTrackingRepository.findByProductAndVisitProduct(visitProductId, productId);
		if (presentationTimeTrackingList != null && !presentationTimeTrackingList.isEmpty()) {
			for (PresentationTimeTracking presentationTimeTracking : presentationTimeTrackingList) {
				PresentationTimeTrackingDto presentationTimeTrackingDto = new PresentationTimeTrackingDto();
				presentationTimeTrackingDto.setId(presentationTimeTracking.getId());
				presentationTimeTrackingDto.setDocumentName(presentationTimeTracking.getDocumentName());
				presentationTimeTrackingDto.setProductId(presentationTimeTracking.getVisitProduct().getProduct().getId());
				presentationTimeTrackingDto.setStartTime(presentationTimeTracking.getStartTime());
				presentationTimeTrackingDto.setEndTime(presentationTimeTracking.getEndTime());
				LocalDateTime startTime = presentationTimeTracking.getStartTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
				LocalDateTime endTime = presentationTimeTracking.getEndTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
				Duration duration = Duration.between(startTime, endTime);
				presentationTimeTrackingDto.setDuration(duration.getSeconds());
				presentationTimeTrackingDto.setDocumentUrl(config.getBackendUrl() + uploadUrl + productPath + "/" + presentationTimeTracking.getVisitProduct().getProduct().getId() + "/" + presentationTimeTracking.getDocumentName());
				presentationTimeTrackingDtoList.add(presentationTimeTrackingDto);
			}

		}
		return presentationTimeTrackingDtoList;
	}

}
