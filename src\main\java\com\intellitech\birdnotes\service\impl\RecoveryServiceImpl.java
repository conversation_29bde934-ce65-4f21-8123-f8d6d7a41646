package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.convertor.RecoveryToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.RecoveryDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.RecoveryRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.RecoveryService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("recoveryService")
@Transactional
public class RecoveryServiceImpl implements RecoveryService {

	private RecoveryRepository recoveryRepository;
	
	

	@Autowired
	RecoveryServiceImpl(RecoveryRepository recoveryRepository) {
		super();
		this.recoveryRepository = recoveryRepository;
	}

	@Override

	public List<RecoveryDto> getAllByUser(Long userId) throws BirdnotesException {

		List<RecoveryDto> back = new ArrayList<>();

		List<Recovery> recoveryList;

		RecoveryToDtoConvertor recoveryToDtoConvertor = new RecoveryToDtoConvertor();
		recoveryList = recoveryRepository.findByUser(userId);


		if (recoveryList != null && !recoveryList.isEmpty()) {

			for (Recovery recovery : recoveryList) {
				
				back.add(recoveryToDtoConvertor.convert(recovery));
			}

		}

		return back;

	}



	
}
