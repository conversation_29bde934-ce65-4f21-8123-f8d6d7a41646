package com.intellitech.birdnotes.model;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.enumeration.Period;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.GOAL, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Goal {
	@Id
	@SequenceGenerator(name = Sequences.GOAL_SEQUENCE, sequenceName = Sequences.GOAL_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.GOAL_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.NAME)
	private String name;
	
	@Column(name = Columns.ITEMS_ORDER)
	private String itemsOrder;
	
	@Column(name = Columns.FIRST_DATE)
	private Date firstDate;
	
	@Column(name = Columns.LAST_DATE)
	private Date lastDate;
	
	@Enumerated(EnumType.STRING)
	@Column(name = Columns.GOAL_TYPE)
	private GoalType goalType;

	
	// @OneToMany(fetch = FetchType.LAZY, cascade=CascadeType.ALL, mappedBy = "goal", targetEntity = Goal.class)
	
	@OneToMany (fetch = FetchType.LAZY, mappedBy = "goal")
	@JsonIgnore
	private Set<GoalItem> goalItems;
	
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY, mappedBy = "goals")
    private Set<User> users = new HashSet<>();

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.PERIOD)
	private Period period;


	public Goal() {
		super();
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	
	public String getItemsOrder() {
		return itemsOrder;
	}
	public void setItemsOrder(String itemsOrder) {
		this.itemsOrder = itemsOrder;
	}
	
	public Set<GoalItem> getGoalItems() {
		return goalItems;
	}
	public void setGoalItems(Set<GoalItem> goalItems) {
		this.goalItems = goalItems;
	}
	
	public Date getFirstDate() {
		return firstDate;
	}
	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}
	public Date getLastDate() {
		return lastDate;
	}
	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}
	public GoalType getGoalType() {
		return goalType;
	}
	public void setGoalType(GoalType goalType) {
		this.goalType = goalType;
	}
	public Set<User> getUsers() {
		return users;
	}
	public void setUsers(Set<User> users) {
		this.users = users;
	}
	public Period getPeriod() {
		return period;
	}
	public void setPeriod(Period period) {
		this.period = period;
	}

	

}
