package com.intellitech.birdnotes.service.impl;

import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.data.dto.DiscountFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Discount;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.dto.DiscountDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.DiscountRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.service.DiscountService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.RangeService;

@Service("discountService")
@Transactional
public class DiscountServiceImpl implements DiscountService {
	@Autowired
	private DiscountRepository discountRepository;
	@Autowired
	private ProductRepository productRepository;
	@Autowired
	private ProductToDtoConvertor productToDtoConvertor;
	@Autowired
	private ProspectTypeRepository prospectTypeRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	private ProspectService prospectService;
	@Autowired
	private ProspectRepository prospectRepository;
	@Autowired
	private RangeService rangeService;
	@Autowired
	private RangeRepository rangeRepository;

	@Autowired
	private ProductService productService;

	@Override
	public List<Long> saveDiscount(DiscountDto discountDto) throws BirdnotesException {

		Discount discount = null;
		List<Long> savedDiscountIds = new ArrayList<>();
		if (discountDto.getId() != null) {
			discount = discountRepository.findOne(discountDto.getId());
		}
		if (discount != null) {
			discount.setPercentage(discountDto.getPercentage());
			Product product = productRepository.findById(discountDto.getProductId());
			discount.setProduct(product);
			Prospect wholesaler = prospectRepository.findById(discountDto.getWholesalerId());
			discount.setWholesaler(wholesaler);
			Range range = rangeRepository.findOne(discountDto.getRangeId());

			/*
			 * ProspectType prospectType =
			 * prospectTypeRepository.findOne(discountDto.getProspectTypeId());
			 * discount.setProspectType(prospectType);
			 */
			List<Long> productIds = new ArrayList<>();
			productIds.add(discount.getProduct().getId());
			discountDto.setProductIds(productIds);

			List<Long> wholesalerIds = new ArrayList<>();
			wholesalerIds.add(discount.getWholesaler().getId());
			discountDto.setWholesalerIds(wholesalerIds);

			Discount savedDiscount = discountRepository.save(discount);
			savedDiscountIds.add(savedDiscount.getId());

		}

		else {
			for (Long productId : discountDto.getProductIds()) {
				if (discountDto.getWholesalerIds() != null) {
					for (Long wholesalerId : discountDto.getWholesalerIds()) {
						Discount discountToSave = new Discount();
						Product product = productRepository.findOne(productId);
						discountToSave.setProduct(product);
						discountToSave.setPercentage(discountDto.getPercentage());
						Prospect wholesaler = prospectRepository.findOne(wholesalerId);
						discountToSave.setWholesaler(wholesaler);
						Discount savedDiscount = discountRepository.save(discountToSave);
						savedDiscountIds.add(savedDiscount.getId());
					}
				}

			}

		}
		return savedDiscountIds;
	}

	@Override
	public void updateDiscount(DiscountDto discountDto) throws BirdnotesException {

	}

	@Override
	public List<ProductDto> getProductWithoutDiscount() throws BirdnotesException {
		List<Product> productList = productRepository.findProductWithoutDiscount();
		List<ProductDto> productDtos = new ArrayList<>();
		for (Product product : productList) {
			productDtos.add(productToDtoConvertor.convert(product));
		}
		return productDtos;
	}

	@Override
	public List<DiscountDto> getDiscount() throws BirdnotesException, ParseException {
		List<Discount> discountList;
		List<DiscountDto> discountDtoList = new ArrayList<>();
		discountList = discountRepository.findAll();
		for (Discount discount : discountList) {
			DiscountDto discountDto = new DiscountDto();
			discountDto.setId(discount.getId());
			discountDto.setPercentage(discount.getPercentage());
			if (discount.getProduct() != null) {
				discountDto.setProductName(discount.getProduct().getName());
				discountDto.setProduct(productToDtoConvertor.convert(discount.getProduct()));
				discountDto.setProductId(discount.getProduct().getId());
			}
			if (discount.getWholesaler() != null) {
				discountDto.setWholesalerId(discount.getWholesaler().getId());
				discountDto.setWholesalerName(
						discount.getWholesaler().getFirstName() + discount.getWholesaler().getLastName());
			}

			/*
			 * discountDto.setProspectType(discount.getProspectType().getName());
			 * discountDto.setProspectTypeId(discount.getProspectType().getId());
			 */
			discountDtoList.add(discountDto);
		}
		return discountDtoList;
	}

	@Override
	public void delete(Long id) throws BirdnotesException{



			discountRepository.delete(id);



	}

	@Override
	public DiscountFormData loadDiscountFormData() throws IOException, BirdnotesException {
		List<ProductDto> products = productService.getAllProducts();
		List<WholesalerDto> wholesalers = prospectService.getWholesalersByStatus(false);
		List<RangeDto> ranges = rangeService.findAll();
		DiscountFormData discountFormData = new DiscountFormData();
		discountFormData.setProducts(products);
		discountFormData.setWholesalers(wholesalers);
		discountFormData.setRanges(ranges);
		return discountFormData;
	}

}
