package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.dto.NotificationRuleDto;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.request.NotificationRuleRequest;
import com.intellitech.birdnotes.service.NotificationRuleService;
import com.intellitech.birdnotes.service.RoleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/notifications")
public class NotificationRuleController {
	private static final Logger LOG = LoggerFactory.getLogger(NotificationRuleController.class);
	@Autowired
	private RoleService roleService;
	@Autowired
	private NotificationRuleService notificationRuleService;
	@Autowired
	private UserService userService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> saveNotification(@RequestBody NotificationRuleRequest notificationRuleRequest) {
		try {
			if (userService.checkHasPermission("NOTIFICATION_ADD")) {
				notificationRuleService.saveNotification(notificationRuleRequest);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add notification", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new notification", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/findAllEvents", method = RequestMethod.GET)
	public ResponseEntity<List<Event>> getEventXml() {
		try {
			List<Event> events = notificationRuleService.getEventXml();
			return new ResponseEntity<>(events, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all events ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findNotificationByEventType/{eventType}", method = RequestMethod.GET)
	public ResponseEntity<List<NotificationRuleDto>> findNotificationByEventType(
			@PathVariable("eventType") String eventType) {
		try {
			if (userService.checkHasPermission("NOTIFICATION_ADD")) {
				List<NotificationRuleDto> notificationsRuleDto = notificationRuleService
						.findNotificationByEvent(eventType);
				return new ResponseEntity<>(notificationsRuleDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findNotificationByEventType", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "/findAllRoles", method = RequestMethod.GET)
	public ResponseEntity<List<RoleDto>> findAllRoles() {
		try {
			if (userService.checkHasPermission("NOTIFICATION_ADD")) {
				List<RoleDto> rolesDtos = roleService.findAll();
				return new ResponseEntity<>(rolesDtos, HttpStatus.OK);
			}
			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all roles", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "getAllUsers", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllUsersWithRoles() {
		try {

			if (userService.checkHasPermission("NOTIFICATION_ADD")) {
				List<UserDto> userDtos = userService.getSubUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
