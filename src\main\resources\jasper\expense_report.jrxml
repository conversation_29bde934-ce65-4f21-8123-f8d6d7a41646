<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.21.0.final using JasperReports Library version 6.21.0-4f56c4f36cd19e17675219a9ac4692d5f0f13b06  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="expense_report" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="6c9a467f-d366-4975-99d7-ad20cace36c5">
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="ExpenseReportItem" uuid="59d692d4-7d5d-4083-9925-9772585d904c">
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="expenseDate" class="java.lang.String"/>
		<field name="expenseTypeName" class="java.lang.String"/>
		<field name="activityTypeName" class="java.lang.String"/>
		<field name="description" class="java.lang.String"/>
		<field name="montant" class="java.lang.Float"/>
		<variable name="total" class="java.lang.Float" calculation="Sum">
			<variableExpression><![CDATA[$F{montant}]]></variableExpression>
		</variable>
	</subDataset>
	<parameter name="ExpenseReportItem" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="date" class="java.lang.String"/>
	<field name="startDate" class="java.lang.String"/>
	<field name="endDate" class="java.lang.String"/>
	<field name="delegateName" class="java.lang.String"/>
	<field name="name" class="java.lang.String"/>
	<field name="logoUrl" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="86" splitType="Stretch">
			<staticText>
				<reportElement x="195" y="52" width="170" height="26" uuid="4bb4a632-4ab6-4b42-a21f-fa332943d545"/>
				<textElement textAlignment="Center">
					<font size="14" isBold="true"/>
				</textElement>
				<text><![CDATA[Note de frais]]></text>
			</staticText>
			<staticText>
				<reportElement x="436" y="0" width="124" height="30" uuid="0de342f3-6720-41d6-b889-51001b90edb9"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Date :]]></text>
			</staticText>
			<textField>
				<reportElement x="485" y="0" width="74" height="30" uuid="cab81761-c432-408a-8935-10a2d2167efd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{date}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="10" y="-4" width="70" height="60" uuid="c4fcda71-c5fe-42bd-9522-5bd5ac4766ef"/>
				<imageExpression><![CDATA[$F{logoUrl}]]></imageExpression>
			</image>
		</band>
	</title>
	<pageHeader>
		<band height="89" splitType="Stretch">
			<staticText>
				<reportElement x="31" y="10" width="210" height="30" uuid="3c8e9eb5-4723-4715-859c-899e58b7fe6b"/>
				<textElement>
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Période : du]]></text>
			</staticText>
			<staticText>
				<reportElement x="300" y="10" width="177" height="30" uuid="e5361148-8c44-40c6-b10c-7eaadfcb3463"/>
				<textElement textAlignment="Left">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[au:]]></text>
			</staticText>
			<staticText>
				<reportElement x="31" y="57" width="180" height="30" uuid="2e570afd-1c97-4d38-868b-636a06351253"/>
				<textElement>
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Délégué:]]></text>
			</staticText>
			<textField>
				<reportElement x="130" y="10" width="81" height="20" uuid="ca750fbd-4773-4136-8c6d-f8cd8265190b"/>
				<textFieldExpression><![CDATA[$F{startDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="340" y="11" width="90" height="20" uuid="40e5a0b0-9665-4a3e-a881-a4d2c78ac480"/>
				<textFieldExpression><![CDATA[$F{endDate}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="100" y="59" width="101" height="21" uuid="26fee2e1-52ab-4c7c-8e74-5626ea4eb4c8"/>
				<textFieldExpression><![CDATA[$F{delegateName}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<columnHeader>
		<band height="61" splitType="Stretch"/>
	</columnHeader>
	<detail>
		<band height="237" splitType="Stretch">
			<componentElement>
				<reportElement x="0" y="0" width="557" height="150" uuid="0759f617-d4af-40f1-8756-ae33fcb0f521">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="ExpenseReportItem" uuid="06ad2735-22ae-4a96-9770-08cf7ab38e32">
						<dataSourceExpression><![CDATA[$P{ExpenseReportItem}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="110" uuid="1cedca56-dba2-4272-8c39-bd747f4a228d">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="110" height="30" uuid="ed77afec-c3bd-4c6e-8301-a37a9af75a53"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Date]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1"/>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="110" height="30" uuid="0abaa04c-ccca-4b9a-a01c-dad23376b7bf"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{expenseDate}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="110" uuid="7598cd9f-c557-4b66-a53c-480ec17f223f">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="110" height="30" uuid="7b6e918c-051b-4d8c-be87-8be52887aa7a"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Type note de frais]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1"/>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="110" height="30" uuid="59fc6600-1e8c-44d2-a0d6-29d6a530bb8b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{expenseTypeName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="110" uuid="358b75ac-3fa5-4bca-b12a-fbe5feebc559">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="110" height="30" uuid="04914cd0-bcb3-4479-b4f2-6aa6a7fc529c"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Type d'activité]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1"/>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="110" height="30" uuid="d451921c-7c7f-448a-aa53-e516480d9eea">
									<printWhenExpression><![CDATA[$F{activityTypeName}.isEmpty()==false]]></printWhenExpression>
								</reportElement>
								<textElement textAlignment="Center" verticalAlignment="Middle" markup="styled"/>
								<textFieldExpression><![CDATA[$F{activityTypeName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="100" uuid="28fa0343-2bb6-488b-a288-f6c246deb86d">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="30" uuid="bbaf3b7f-d519-4137-8f1e-fee63c3dfa8e"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Commentaire]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="100" height="30" uuid="173f619a-9921-4c91-90d3-28a1c2c232f9"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Total]]></text>
							</staticText>
						</jr:tableFooter>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="100" height="30" uuid="d14963be-c499-4b25-af20-a8836017e3f2"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{description}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="120" uuid="1751d308-dde3-4074-ba75-004efb20118c">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="120" height="30" uuid="abc04c92-995d-482b-a1f3-f8809c8fada4"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font isBold="true"/>
								</textElement>
								<text><![CDATA[Montant]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1">
							<textField pattern="#,##0.000;(#,##0.000-)">
								<reportElement x="0" y="0" width="120" height="30" uuid="6aba9e41-6f84-4af7-9c0b-81d5b9c2436f"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$V{total}]]></textFieldExpression>
							</textField>
						</jr:tableFooter>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="120" height="30" uuid="0d3c7cab-3524-44d2-b962-f7af64272893"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{montant}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<columnFooter>
		<band height="45" splitType="Stretch"/>
	</columnFooter>
	<pageFooter>
		<band height="101" splitType="Stretch">
			<staticText>
				<reportElement x="25" y="0" width="210" height="18" uuid="a5c2e60a-2829-4dca-bdb4-f43ae3f247c2"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[Reçu conforme]]></text>
			</staticText>
			<staticText>
				<reportElement x="325" y="0" width="210" height="18" uuid="c15ab19b-41e2-4e38-891a-a308e00fae0e"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[Signature et cachet]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="230" y="71" width="100" height="30" uuid="e39e4b38-492c-4a26-a9c7-f222d8912aef"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
	<summary>
		<band height="42" splitType="Stretch"/>
	</summary>
</jasperReport>
