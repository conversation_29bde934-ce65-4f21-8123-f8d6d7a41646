package com.intellitech.birdnotes.service.impl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.model.DelegateDisplacement;
import com.intellitech.birdnotes.repository.DelegateDisplacementRepository;
import com.intellitech.birdnotes.service.DelegateDisplacementService;

import java.util.Date;
import java.util.List;

@Service
public class DelegateDisplacementServiceImpl implements DelegateDisplacementService {
    private final DelegateDisplacementRepository repository;

    @Autowired
    public DelegateDisplacementServiceImpl(DelegateDisplacementRepository repository) {
        this.repository = repository;
    }

    @Override
    public List<DelegateDisplacement> findByDelegateAndDateRange(Long delegateId, Date startDate, Date endDate) {
        return repository.findByDelegateIdAndDateBetween(delegateId, startDate, endDate);
    }
}

