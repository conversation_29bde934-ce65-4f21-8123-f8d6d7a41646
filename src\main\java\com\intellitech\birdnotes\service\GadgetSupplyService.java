package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.GiftSupply;
import com.intellitech.birdnotes.model.dto.GiftSupplyDto;
import com.intellitech.birdnotes.model.dto.GiftSupplyRequestDto;

public interface GadgetSupplyService {
	GiftSupply saveGadgetSupply(GiftSupplyDto gadgetSupplyDto) throws BirdnotesException;

	List<GiftSupplyDto> getAllGadgetsSupply() throws BirdnotesException;

	List<GiftSupplyDto> findGadgetSupplyByUserAndDateAndGadget(Date firstDate, Date lastDate, Long userId,
			Integer gadgetId) throws BirdnotesException;
	
	List<GiftSupplyDto> getGadgetSupplyByUserGadgetAndDate(GiftSupplyRequestDto gadgetSupplyRequestDto) throws BirdnotesException ;


	void deleteGadgetSupply(Integer gadgetSupplyId);
}
