package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

import com.intellitech.birdnotes.model.FavoriteMenuItem;

public class FavoriteRequestDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private List<FavoriteMenuItem> favorites;

	public FavoriteRequestDto() {
		super();
	}

	public List<FavoriteMenuItem> getFavorites() {
		return favorites;
	}

	public void setFavorites(List<FavoriteMenuItem> favorites) {
		this.favorites = favorites;
	}

}
