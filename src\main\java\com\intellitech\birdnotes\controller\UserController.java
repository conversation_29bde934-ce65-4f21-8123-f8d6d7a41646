package com.intellitech.birdnotes.controller;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.data.dto.AccessPermissionsDto;
import com.intellitech.birdnotes.data.dto.UserDataDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.LocalityV1Dto;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.UserDtoRequest;
import com.intellitech.birdnotes.model.dto.VisitHistoryParamDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.RoleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/users")
public class UserController {

	private static final Logger LOG = LoggerFactory.getLogger(UserController.class);

	@Autowired
	private UserService userService;

	@Autowired
	private RoleService roleService;

	@Autowired
	private CurrentUser currentUser;

	@Autowired
	private ConfigurationService configurationService;

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("DELEGUE_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<UserDto> userDtos = userService.findAllUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}
			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/findAllRoles", method = RequestMethod.GET)
	public ResponseEntity<List<RoleDto>> findAllRoles() {
		try {
			if (userService.checkHasPermission("DELEGUE_EDIT") || userService.checkHasPermission("USER_EDIT")) {
				List<RoleDto> rolesDtos = roleService.findAll();
				return new ResponseEntity<>(rolesDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all roles", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllDelegatesWithoutGoal", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> findAllDelegatesWithoutGoal(@RequestBody GoalRequest goalsRequestDto) {
		try {
			if (userService.checkHasPermission("DELEGUE_VIEW")) {
				List<UserDto> userDtos = userService.findAllDelegatesWithoutGoal(goalsRequestDto);
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegatesWithoutGoal", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllUsers", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllUsers() {
		try {

			if (userService.checkHasPermission("USER_VIEW")) {
				List<UserDto> userDtos = userService.getOnlyUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "findHistoryParams", method = RequestMethod.POST)
	public ResponseEntity<VisitHistoryParamDto> findHistoryParams(@RequestBody UserDataDto usersDataRequest) {
		try {
			VisitHistoryParamDto back = userService.findHistoryParams(usersDataRequest.getUserIds(),
					usersDataRequest.getType());
			return new ResponseEntity<>(back, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in sectors and localities of delegates", e);
			return new ResponseEntity<>(new VisitHistoryParamDto(), HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "sendCredentials", method = RequestMethod.POST)
	public ResponseEntity<String> sendCredntials(@RequestBody UserDto userDto) {
		try {
			if (userService.checkHasPermission("USER_ADD")) {
				userService.sendCredentials(userDto);
				return new ResponseEntity<>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in sendCredntials", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}


	@RequestMapping(value = "saveUser", method = RequestMethod.POST)
	public ResponseEntity<String> saveUser(@RequestBody UserDto userRequest) {
	    try {
	        if (userService.checkHasPermission("USER_ADD")) {
	            User userSaved = userService.saveUser(userRequest);
	            if (userSaved != null) {
	                return new ResponseEntity<>(userSaved.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }
	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving user", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving user", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "deleteUser/{userId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteUser(@PathVariable("userId") Long userId) {
		try {
			if (userService.checkHasPermission("USER_DELETE")) {
				userService.deleteUser(userId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting user", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	
		} catch (Exception e) {
			LOG.error("Error in deleteDelegate", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "updateUser", method = RequestMethod.PUT)
	public ResponseEntity<String> updateUser(@RequestBody UserDtoRequest userDto) {
		try {
			if (userService.checkHasPermission("USER_EDIT")) {
				userService.updateUser(userDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("Error Non-Authoritative Information in update User", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("Error in updateUser", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllSupervisors", method = RequestMethod.POST)
	public ResponseEntity<List<UserDto>> getAllSupervisors(@RequestBody List<Integer> roleIds) {
		try {
			List<UserDto> supervisorDtos = userService.getAllSupervisors(roleIds);
			return new ResponseEntity<>(supervisorDtos, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in getAllSupervisors", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllRoles", method = RequestMethod.GET)
	public ResponseEntity<Set<Role>> getAllRoles() {
		try {
			BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
			if (birdnotesUser == null || birdnotesUser.getUserDto() == null
					|| birdnotesUser.getUserDto().getId() == null) {
				return new ResponseEntity<>(null, HttpStatus.UNAUTHORIZED);
			}
			Set<Role> roles = userService.getRolesOfCurrentUser();
			return new ResponseEntity<>(roles, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in getAllRoles", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllPermissions", method = RequestMethod.GET)
	public ResponseEntity<AccessPermissionsDto> getAllPermissions() {
		try {
			Set<String> rolePermissions = userService.getAllPermissionsOfCurrentUser();
			ConfigurationDto config = configurationService.findConfiguration();
			AccessPermissionsDto accessPermissionsDto = new AccessPermissionsDto(rolePermissions,
					config.getPackageName());
			return new ResponseEntity<>(accessPermissionsDto, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in getAllPermissions", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "import", method = RequestMethod.POST)

	public ResponseEntity<Map<String, List<UserDto>>> importUser(@RequestParam("file") MultipartFile file)
			throws BirdnotesException {
		try {
			File tmpFile = File.createTempFile("import", file.getOriginalFilename());
			file.transferTo(tmpFile);
			if (userService.checkHasPermission("USER_ADD")) {

				Map<String, List<UserDto>> userList = userService.importUser(tmpFile.getPath());

				return new ResponseEntity<>(userList, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while importing user", e);

			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "getCurrentUser", method = RequestMethod.GET)
	public ResponseEntity<UserDto> getCurrentUser() {
		try {
				UserDto birdnotesUser = currentUser.getBirdnotesUser().getUserDto();
				return new ResponseEntity<>(birdnotesUser, HttpStatus.OK);
			
		} catch (Exception e) {
			LOG.error("Error in getCurrentUser", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
}
