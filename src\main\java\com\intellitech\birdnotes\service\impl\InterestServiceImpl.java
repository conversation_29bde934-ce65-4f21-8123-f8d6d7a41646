package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.dto.InterestDto;
import com.intellitech.birdnotes.repository.InterestRepository;
import com.intellitech.birdnotes.service.InterestService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("interestService")
@Transactional
public class InterestServiceImpl implements InterestService {

	private InterestRepository interestRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;

	
	

	@Autowired
	InterestServiceImpl(InterestRepository interestRepository) {
		super();
		this.interestRepository = interestRepository;
	}



	@Override
	public List<InterestDto> findAll() throws BirdnotesException {
		List<InterestDto> back = new ArrayList<>();
		List<Interest> allTypes= interestRepository.findAll();
		for (Interest interest : allTypes) {
			InterestDto interestDto = new InterestDto();
			interestDto.setId(interest.getId());
			interestDto.setName(interest.getName());
			back.add(interestDto);
		}
		return back;
	}

	@Override
	public InterestDto findInterestDto(String interestName, List<InterestDto>interestDtos)   {
		for (InterestDto interestDto : interestDtos) {
			if (interestDto.getName().equalsIgnoreCase(interestName)) {
				return interestDto;
			}
		}
		return null;
	}
	
	@Override
	public void saveAllInterests(List<InterestDto> interestDtos) throws BirdnotesException {
		for(InterestDto interestDto:interestDtos) {
			Interest interest = new Interest();
		if (interestDto.getName() == null || interestDto.getName().equals("")) {
			throw new BirdnotesException("Interest name is empty");
		}
		
		interest.setName(interestDto.getName());
		interestRepository.save(interest);
		}
	}
	@Override
	public Interest saveInterest(InterestDto interestDto) throws BirdnotesException {
	    if (interestDto == null || interestDto.getId() == null) {
	        throw new BirdnotesException(Exceptions.INTEREST_DTO_IS_NULL);
	    }

	    if (interestDto.getName() == null || interestDto.getName().isEmpty()) {
	        throw new BirdnotesException(Exceptions.INTEREST_NAME_IS_EMPTY);
	    }

	    Interest existingInterest = interestRepository.findByNameAndAnotherId(interestDto.getName(), interestDto.getId());
	    if (existingInterest != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    Interest interest = null;
	    if (interestDto.getId() != null) {
	        interest = interestRepository.findOne(interestDto.getId());
	    }
	    if (interest == null) {
	        interest = new Interest();
	    }

	    interest.setId(interestDto.getId());
	    interest.setName(interestDto.getName());
	    return interestRepository.save(interest);
	}
	

	
	public void delete (long id)throws BirdnotesException {
			interestRepository.deleteById(id);
	}
	@Override
	public Set<Interest> findInterestsByIds(List<Long> interestIds) {
	    Set<Interest> interests = new HashSet<>(interestRepository.findAll(interestIds));
	    return interests;
	}


}
