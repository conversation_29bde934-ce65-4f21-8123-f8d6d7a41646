package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.request.GadgetRequest;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/gift")
public class GiftController {

	private static final Logger LOG = LoggerFactory.getLogger(GiftController.class);

	@Autowired
	private GadgetService gadgetService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addGadget(@RequestBody GadgetRequest gadgetRequest) {
		try {
			if (userService.checkHasPermission("GADGET_ADD")) {
				Gift gadgetSaved = gadgetService.add(gadgetRequest);
				if (gadgetSaved != null) {
					return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
				}
				return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<String>("", HttpStatus.CONFLICT);
			}

		} catch (BirdnotesException fe) {
			return new ResponseEntity<String>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in saveGift", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "findAllGift", method = RequestMethod.GET)
	public ResponseEntity<List<GiftDto>> findAllGift() {

		try {
			if (userService.checkHasPermission("GADGET_VIEW")) {
				List<GiftDto> gadgetDto = gadgetService.findAll();
				return new ResponseEntity<>(gadgetDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all gadget", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteGadget(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("GADGET_DELETE")) {
	            gadgetService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>("GADGET_TO_DELETE_ALREADY_DELETED", HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting gadget", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the gadget with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	@RequestMapping(value = "/saveGift", method = RequestMethod.POST)
	public ResponseEntity<String> saveGift(@RequestBody GiftDto giftDto) {

	    try {
	        if (userService.checkHasPermission("GADGET_EDIT") || userService.checkHasPermission("GADGET_ADD")) {
	            Gift savedGift = gadgetService.saveGadget(giftDto);
	            if (savedGift != null) {
	                return new ResponseEntity<>(savedGift.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving gift", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving gift", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

}
