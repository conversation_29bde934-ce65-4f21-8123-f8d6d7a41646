package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.ACTIVITY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Activity implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.ACTIVITY_SEQUENCE, sequenceName = Sequences.ACTIVITY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.ACTIVITY_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	@Column(name = BirdnotesConstants.Columns.IDENTIFIER)
	private Long identifier;

	@Column(name = Columns.ACTIVITY_DATE)
	private Date activityDate;

	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	private Delegate delegate;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID)
	private Prospect prospect;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.ACTIVITY_TYPE_ID)
	private ActivityType activityType;

	private Long hourNumber;

	@Column(name = BirdnotesConstants.Columns.COMMENT, length = BirdnotesConstants.Numbers.N_255)
	private String comment;

	@OneToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "activity", targetEntity = ExpenseReport.class)
	private Set<ExpenseReport> expenses = new HashSet<>();

	@JsonIgnore
	@OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL, mappedBy = "activity", targetEntity = Planning.class)
	private Planning planning;

	public Activity() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Date getActivityDate() {
		return activityDate;
	}

	public void setActivityDate(Date activityDate) {
		this.activityDate = activityDate;
	}

	public Long getHourNumber() {
		return hourNumber;
	}

	public void setHourNumber(Long hourNumber) {
		this.hourNumber = hourNumber;
	}

	public ActivityType getActivityType() {
		return activityType;
	}

	public void setActivityType(ActivityType activityType) {
		this.activityType = activityType;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}

	public Set<ExpenseReport> getExpenses() {
		return expenses;
	}

	public void setExpenses(Set<ExpenseReport> expenses) {
		this.expenses = expenses;
	}

	public Planning getPlanning() {
		return planning;
	}

	public void setPlanning(Planning planning) {
		this.planning = planning;
	}

	public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

}
