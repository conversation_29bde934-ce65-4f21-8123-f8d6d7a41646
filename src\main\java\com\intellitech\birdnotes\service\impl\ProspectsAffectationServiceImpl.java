package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.SortedSet;
import java.util.TreeSet;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.dao.DynamicQueries;

import com.intellitech.birdnotes.data.dto.UserAffectationDetailsDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.PreAffectation;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToProspect;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.DelegateProspectsDto;
import com.intellitech.birdnotes.model.dto.NotificationRuleDto;
import com.intellitech.birdnotes.model.dto.PreAffectationDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectsAffectationRequestDto;
import com.intellitech.birdnotes.model.dto.SelectedDataForSourceProspectsRequestDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryDto;
import com.intellitech.birdnotes.model.dto.VisitRequestDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.PreAffectationRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.ProspectsAffectationRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.ProspectsAffectationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import org.apache.commons.collections.CollectionUtils;

@Service("prospectsAffectationService")
@Transactional
public class ProspectsAffectationServiceImpl implements ProspectsAffectationService {
	private ProspectsAffectationRepository prospectsAffectationRepository;
	private PreAffectationRepository preAffectationRepository;
	private ProspectRepository prospectRepository;
	private UserRepository userRepository;
	private CurrentUser currentUser;
	private DynamicQueries dynamicQueries;
	private	DelegateRepository delegateRepository;
	@Autowired
	private SectorRepository sectorRepository;
	@Autowired
	private SpecialityRepository specialityRepository;
	@Autowired
	private LocalityRepository localityRepository;
	@Autowired
	private PotentialRepository potentialRepository;
	@Autowired
	private ConvertDtoToProspect convertDtoToProspect;
	
	@Autowired
	private UserService userService;
	
	private ConvertProspectToDto convertProspectToDto;
	private NotificationService notificationService;

	private NotificationMessageBuilder notificationMessageBuilder;
	private ConfigurationService configurationService;

	@Autowired
	public ProspectsAffectationServiceImpl(ProspectsAffectationRepository prospectsAffectationRepository, PreAffectationRepository preAffectationRepository,
			ProspectRepository prospectRepository, UserRepository userRepository,
			ConvertProspectToDto convertProspectToDto, DelegateRepository delegateRepository) {
		super();
		this.prospectsAffectationRepository = prospectsAffectationRepository;
		this.prospectRepository = prospectRepository;
		this.userRepository = userRepository;
		this.preAffectationRepository = preAffectationRepository;
		this.convertProspectToDto = convertProspectToDto;
		this.delegateRepository = delegateRepository;
	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	@Autowired
	public void setProspectRepository(ProspectRepository prospectRepository) {
		this.prospectRepository = prospectRepository;
	}

	@Autowired
	public void setProspectsAffectationRepository(ProspectsAffectationRepository prospectsAffectationRepository) {
		this.prospectsAffectationRepository = prospectsAffectationRepository;
	}
	@Autowired
	private MessageSource messageSource;

	@Autowired
	public void setNotificationService(NotificationService notificationService) {
		this.notificationService = notificationService;
	}

	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	@Autowired
	public void setNotificationMessageBuilder(NotificationMessageBuilder notificationMessageBuilder) {
		this.notificationMessageBuilder = notificationMessageBuilder;
	}

	@Autowired
	public void setDynamicQueries(DynamicQueries dynamicQueries) {
		this.dynamicQueries = dynamicQueries;
	}
	


	@Override
	public List<ProspectDto> findSourceProspects(Long userId) throws BirdnotesException {
		List<ProspectDto> back = new ArrayList<>();
		List<Prospect> listOfProspects = prospectsAffectationRepository.findNotAffectedProspectsToUser(userId);
		if (listOfProspects != null && !listOfProspects.isEmpty()) {
			for (Prospect prospect : listOfProspects) {
				back.add(convertProspectToDto.convert(prospect));
			}
		}

		return back;
	}

	@Override
	public List<PreAffectationDto> findPreAffectationByUser(Long userId) throws BirdnotesException {
		List<PreAffectationDto> preAffectations = new ArrayList<>();
		preAffectations = preAffectationRepository.findPreAffectationByUser(userId);
		return preAffectations;
		
	}
	
	@Override
	public void deletePreAffectation(Long id) throws BirdnotesException {
		prospectsAffectationRepository.deleteByPreAffectation(id);
		preAffectationRepository.deleteById(id);
		

	}
	
	@Override
	public boolean delete(Long id) {
		prospectsAffectationRepository.deleteById(id);
		return true;
	}

	@Override
	public void saveAffectation(ProspectsAffectationRequestDto prospectsAffectationDto) throws BirdnotesException {

		List<Long> oldProspect = new ArrayList<>();

		List<ProspectDto> oldDelegateProspects = findDelegateProspectsByMultipleSelection(
				prospectsAffectationDto.getSelectedDataForSourceProspectsRequest()).getDelegateProspects();

		for (ProspectDto oldDelegateProspect : oldDelegateProspects) {
			oldProspect.add(oldDelegateProspect.getId());
		}

		if (prospectsAffectationDto.getUserId() == null) {
			throw new BirdnotesException(Exceptions.EMPTY_PROSPECTSAFFECTATION_USER);
		}

		Delegate delegate = delegateRepository.findOne(prospectsAffectationDto.getUserId());
		if (delegate == null) {
			throw new BirdnotesException(Exceptions.USER_ALREADY_DELETED);
		}

		delegate.setCycle(prospectsAffectationDto.getCycle());
		delegateRepository.save(delegate);
		if (prospectsAffectationDto.getProspect() == null) {
			throw new BirdnotesException(Exceptions.EMPTY_PROSPECTSAFFECTATION_PROSPECT);
		}

		Collection<Long> newProspects = CollectionUtils.subtract(prospectsAffectationDto.getProspect(), oldProspect);
		List<String> prospectsNames = new ArrayList<>();
		PreAffectation savedPreAffectation = new PreAffectation();
		if(prospectsAffectationDto.getSelectedDataForSourceProspectsRequest().getSelectedAffectationType().equals("PRE_AFFECTATION")) {
			
			PreAffectation preAffectation = new PreAffectation();			
			preAffectation.setDelegateNumber(prospectsAffectationDto.getDelegateNumber());
			savedPreAffectation = preAffectationRepository.save(preAffectation);
			for (Long prospectId : oldProspect) {
				ProspectsAffectation prospectsAffectation = prospectsAffectationRepository.findByProspectAndUser(prospectId, delegate.getId() );
				if (prospectsAffectation == null) {
					throw new BirdnotesException(Exceptions.AFFECTATION_ALREADY_DELETED);
				}
				prospectsAffectation.setPreAffectation(savedPreAffectation);
			}
			
		}
		for (Long prospectId : newProspects) {
			Prospect prospect = prospectRepository.findOne(prospectId);
			if (prospect == null) {
				throw new BirdnotesException(Exceptions.PROSPECT_ALREADY_DELETED);
			}
			prospectsNames.add(prospect.getFirstName() + " " + prospect.getLastName());

			ProspectsAffectation prospectsAffectation = new ProspectsAffectation();
			prospectsAffectation.setProspect(prospect);
			prospectsAffectation.setDelegate(delegate);
			if(prospectsAffectationDto.getSelectedDataForSourceProspectsRequest().getSelectedAffectationType().equals("PRE_AFFECTATION")) {
			   prospectsAffectation.setPreAffectation(savedPreAffectation);
			}
			prospectsAffectationRepository.save(prospectsAffectation);

		}
		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		if(prospectsNames.size() > 0) {
			notificationMessageBuilder.setMessageType("prospectsAffectationNotificationMessage");
			notificationMessageBuilder.setUser(sourceUser);
			notificationMessageBuilder.setTagetUser(delegate.getUser());
			notificationMessageBuilder.setProspects(prospectsNames);
			Notification notification = notificationMessageBuilder.Build();
			notificationService.sendAppNotification(notification);
			notificationService.sendNotificationUsingWorkflow(delegate.getUser(), null, "ADD_AFFECTATION", prospectsNames);
		}
		

		prospectsNames = new ArrayList<>();
		Collection<Long> deletedProspects = CollectionUtils.subtract(oldProspect, prospectsAffectationDto.getProspect());
		for (Long prospectId : deletedProspects) {
			Prospect prospect = prospectRepository.findOne(prospectId);
			prospectsNames.add(prospect.getFirstName() + " " + prospect.getLastName());

			prospectsAffectationRepository.deleteByDelegateAndProspect(prospectsAffectationDto.getUserId(), prospectId);
		}
		
		if(prospectsNames.size() > 0) {
			notificationMessageBuilder.setMessageType("prospectsAffectationDeleteNotificationMessage");
			notificationMessageBuilder.setUser(sourceUser);
			notificationMessageBuilder.setTagetUser(delegate.getUser());
			notificationMessageBuilder.setProspects(prospectsNames);
			Notification notification = notificationMessageBuilder.Build();
			notificationService.sendAppNotification(notification);
			notificationService.sendNotificationUsingWorkflow(delegate.getUser(), null, "REMOVE_AFFECTATION", prospectsNames);
		}
	}

	@Override
	public void deleteAffectation(ProspectsAffectationRequestDto prospectsAffectationDto) throws BirdnotesException {
		prospectsAffectationRepository.deleteByDelegateAndProspect(prospectsAffectationDto.getUserId(), prospectsAffectationDto.getProspectId());
		
	}
	
	@Override
	public UserAffectationDetailsDto getUserAffectationDetails(Long userId) throws BirdnotesException {
		UserAffectationDetailsDto userAffectationDetailsDto = new UserAffectationDetailsDto();

		SortedSet<String> sectors = new TreeSet<>();
		SortedSet<String> localities = new TreeSet<>();
		SortedSet<String> specialities = new TreeSet<>();
		SortedSet<String> activities = new TreeSet<>();
		SortedSet<String> establishments = new TreeSet<>();
		SortedSet<String> prospectTypes = new TreeSet<>();
		List<ProspectDto> allProspects = findprospectsByUser(userId);
		for (ProspectDto prospect : allProspects ) {
			activities.add(prospect.getActivity());
			sectors.add(prospect.getSectorDto().getName());
			localities.add(prospect.getLocalityDto().getName());
			specialities.add(prospect.getSpecialityDto().getName());
			if(prospect.getEstablishmentDto() != null && prospect.getEstablishmentDto().getName() != null ) {
				establishments.add(prospect.getEstablishmentDto().getName());
			}
			prospectTypes.add(prospect.getProspectTypeDto().getName());
		}
		userAffectationDetailsDto.setActivities(activities);
		userAffectationDetailsDto.setEstablishments(establishments);
		userAffectationDetailsDto.setLocalities(localities);
		userAffectationDetailsDto.setProspectTypes(prospectTypes);
		userAffectationDetailsDto.setSectors(sectors);
		userAffectationDetailsDto.setSpecialities(specialities);
		return userAffectationDetailsDto;
	}


	@Override
	public void saveAffectation(List<Long> usersIds, Prospect prospect) throws BirdnotesException {
		if (prospect == null) {
			throw new BirdnotesException(Exceptions.EMPTY_PROSPECTSAFFECTATION_PROSPECT);
		}
		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		
		for (Long userId : usersIds) {
			Delegate delegate = delegateRepository.findDelegateByUserId(userId);
			if (delegate == null) {
				throw new BirdnotesException(Exceptions.USER_ALREADY_DELETED);
			}
			ProspectsAffectation prospectsAffectation = new ProspectsAffectation();
			prospectsAffectation.setProspect(prospect);
			prospectsAffectation.setDelegate(delegate);
			prospectsAffectationRepository.save(prospectsAffectation);
			notificationMessageBuilder.setMessageType("prospectsAffectationNotificationMessage");
			notificationMessageBuilder.setUser(sourceUser);
			notificationMessageBuilder.setTagetUser(delegate.getUser());
			notificationMessageBuilder.setProspect(prospect);
			Notification notification = notificationMessageBuilder.Build();
			notificationService.sendAppNotification(notification);
			notificationService.sendNotificationUsingWorkflow(delegate.getUser(), null, "ADD_AFFECTATION", prospect);
		}
	}

	@Override
	public List<ProspectDto> findprospectsByUser(Long UserId) throws BirdnotesException {
		List <Long> users = new ArrayList<>();
		users.add(UserId);
		List<ProspectDto> back = new ArrayList<>();
		List<Prospect> allProspects = prospectsAffectationRepository.findByUser(users);
		for (Prospect prospect : allProspects) {
			back.add(convertProspectToDto.convert(prospect));
		}
		return back;

	}

	@Override
	public List<ProspectDto> findprospectsByUserAndSectors(Long userId, List<Long> sectorIds)
			throws BirdnotesException {
		List<ProspectDto> back = new ArrayList<>();
		List<Prospect> allProspects = prospectsAffectationRepository.findByUserAndSectors(userId, sectorIds);
		for (Prospect prospect : allProspects) {
			back.add(convertProspectToDto.convert(prospect));
		}
		return back;

	}

	@Override
	public List<ProspectDto> findSourceProspectsByMultipleSelection(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) throws BirdnotesException {
		StringBuilder prospectQuery;
		Map<String, Object> prospectParameters;
		List<ProspectDto> sourceProspectDtos = new ArrayList<>();

		prospectQuery = (StringBuilder) prospectQueryBuild(selectedDataForSourceProspectsRequest)
				.get(BirdnotesConstants.VisitHistory.QUERY);
		prospectParameters = (Map<String, Object>) prospectQueryBuild(selectedDataForSourceProspectsRequest)
				.get(BirdnotesConstants.VisitHistory.PARAMETERS);

		List<Prospect> sourceProspects = dynamicQueries.findProspects(prospectQuery.toString(), prospectParameters);
		if (sourceProspects != null && !sourceProspects.isEmpty()) {
			for (Prospect prospect : sourceProspects) {
				sourceProspectDtos.add(convertProspectToDto.convert(prospect));

			}
		}
		return sourceProspectDtos;
	}

	@Override
	public DelegateProspectsDto findDelegateProspectsByMultipleSelection(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) throws BirdnotesException {
		StringBuilder prospectQuery;
		Map<String, Object> prospectParameters;
		List<ProspectDto> sourceProspectDtos = new ArrayList<>();

		Map query = delegateProspectQueryBuild(selectedDataForSourceProspectsRequest);
		Delegate delegate = delegateRepository.findById(selectedDataForSourceProspectsRequest.getSelectedUser());
		
		prospectQuery = (StringBuilder) query.get(BirdnotesConstants.VisitHistory.QUERY);
		prospectParameters = (Map<String, Object>) query.get(BirdnotesConstants.VisitHistory.PARAMETERS);

		List<Prospect> sourceProspects = dynamicQueries.findProspects(prospectQuery.toString(), prospectParameters);
		if (sourceProspects != null && !sourceProspects.isEmpty()) {
			for (Prospect prospect : sourceProspects) {
				sourceProspectDtos.add(convertProspectToDto.convert(prospect));

			}
		}

		DelegateProspectsDto delegateProspectsDto = new DelegateProspectsDto();
		delegateProspectsDto.setCycle(delegate.getCycle());
		delegateProspectsDto.setDelegateProspects(sourceProspectDtos);
		
		return delegateProspectsDto;
	}

	private Map<String, Object> prospectQueryBuild(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) {
		StringBuilder prospectQuery = new StringBuilder();
		Map<String, Object> prospectParameters = new HashMap<>();
		Map<String, Object> prospectQueryParameters = new HashMap<>();

		prospectQuery.append("SELECT p from Prospect p WHERE p.status= 'VALID' ");
		findProspectByUser(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectBySector(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByLocality(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByActivity(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByPotential(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectBySpeciality(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByEstablishment(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByProspectType(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByName(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		prospectQuery.append(" order by p.sector.name, p.locality.name, p.firstName ASC");
		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.QUERY, prospectQuery);
		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.PARAMETERS, prospectParameters);
		return prospectQueryParameters;

	}

	private Map<String, Object> delegateProspectQueryBuild(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) {
		StringBuilder prospectQuery = new StringBuilder();
		Map<String, Object> prospectParameters = new HashMap<>();
		Map<String, Object> prospectQueryParameters = new HashMap<>();

		prospectQuery.append("SELECT p from Prospect p WHERE p.status= 'VALID' ");
		findPAffectedrospectByUser(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectBySector(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByLocality(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByActivity(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByPotential(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectBySpeciality(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByEstablishment(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByProspectType(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		findProspectByName(selectedDataForSourceProspectsRequest, prospectQuery, prospectParameters);
		prospectQuery.append(" order by p.sector.name, p.locality.name, p.firstName ASC");
		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.QUERY, prospectQuery);
		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.PARAMETERS, prospectParameters);
		return prospectQueryParameters;

	}
	
	public void findProspectByEstablishment(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getSelectedEstablishments() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedEstablishments().isEmpty()) {
			prospectQuery.append(" AND p.establishment.id IN (:establishmentIds)");
			prospectParameters.put("establishmentIds", selectedDataForSourceProspectsRequest.getSelectedEstablishments());
		}

	}

	public void findProspectByProspectType(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getSelectedProspectTypes() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedProspectTypes().isEmpty()) {
			prospectQuery.append(" AND p.prospectType.id IN (:prospectTypeIds)");
			prospectParameters.put("prospectTypeIds", selectedDataForSourceProspectsRequest.getSelectedProspectTypes());
		}

	}

	
	public void findProspectByName(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getProspectName() != null
				&& !selectedDataForSourceProspectsRequest.getProspectName().isEmpty()) {
			prospectQuery.append(" AND (UPPER(CONCAT(p.firstName,' ',p.lastName)) like :prospectName OR UPPER(CONCAT(p.lastName,' ',p.firstName)) like :prospectName)");
			prospectParameters.put("prospectName", "%" +selectedDataForSourceProspectsRequest.getProspectName().toUpperCase()+ "%");
		}
	}


	public void findPAffectedrospectByUser(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest, StringBuilder prospectQuery,
			Map<String, Object> prospectParameters) {

		if (selectedDataForSourceProspectsRequest.getSelectedUser() != null
				&& selectedDataForSourceProspectsRequest.getSelectedUser() != 0) {
			
			prospectQuery.append(
					" AND p.id in (SELECT pa.prospect.id from ProspectsAffectation pa WHERE pa.delegate.id=:userId)");
			prospectParameters.put("userId", selectedDataForSourceProspectsRequest.getSelectedUser());

		}

	}

	public void findProspectByUser(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {

		if (selectedDataForSourceProspectsRequest.getSelectedUser() != null
				&& selectedDataForSourceProspectsRequest.getSelectedUser() != 0) {
			prospectQuery.append(
					" AND p.id not in (SELECT pa.prospect.id from ProspectsAffectation pa WHERE pa.delegate.id=:userId)");
			prospectParameters.put("userId", selectedDataForSourceProspectsRequest.getSelectedUser());

		}

	}

	public void findProspectBySector(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getSelectedSectors() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedSectors().isEmpty()) {
			prospectQuery.append(" AND p.sector.id IN (:sectorIds)");
			prospectParameters.put("sectorIds", selectedDataForSourceProspectsRequest.getSelectedSectors());
		}

	}

	public void findProspectByLocality(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getSelectedLocalities() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedLocalities().isEmpty()) {
			prospectQuery.append(" AND p.locality.id IN (:localityIds)");
			prospectParameters.put("localityIds", selectedDataForSourceProspectsRequest.getSelectedLocalities());
		}

	}

	public void findProspectByActivity(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {

		if (selectedDataForSourceProspectsRequest.getSelectedActivities() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedActivities().isEmpty()) {
			prospectQuery.append(" AND p.activity IN (:activityNames)");
			prospectParameters.put("activityNames", selectedDataForSourceProspectsRequest.getSelectedActivities());
		}

	}

	public void findProspectByPotential(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getSelectedPotentials() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedPotentials().isEmpty()) {
			prospectQuery.append(" AND p.potential.id IN (:potentialIds)");
			prospectParameters.put("potentialIds", selectedDataForSourceProspectsRequest.getSelectedPotentials());
		}

	}

	public void findProspectBySpeciality(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {
		if (selectedDataForSourceProspectsRequest.getSelectedSpecialities() != null
				&& !selectedDataForSourceProspectsRequest.getSelectedSpecialities().isEmpty()) {
			prospectQuery.append(" AND p.speciality.id IN (:specialityIds)");
			prospectParameters.put("specialityIds", selectedDataForSourceProspectsRequest.getSelectedSpecialities());
		}

	}

	@Override
	public List<ProspectDto> findSourceProspectsWithSectors(Long userId, List<Long> sectorIds)
			throws BirdnotesException {
		List<ProspectDto> back = new ArrayList<>();

		List<Prospect> listOfProspects = prospectsAffectationRepository
				.findNotAffectedProspectsToUserWithSectors(userId, sectorIds);
		if (listOfProspects != null && !listOfProspects.isEmpty()) {
			for (Prospect prospect : listOfProspects) {
				back.add(convertProspectToDto.convert(prospect));
			}
		}

		return back;
	}

}
