package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;



public class PreAffectationDto implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private Long id;
	private String userName;
	private Long userId;
	private String sectorName;
	private String specialityName;
	private Integer delegateNumber;

	


	public PreAffectationDto(Long id, String userName, Long userId, String sectorName, String specialityName,
			 Integer delegateNumber) {
		super();
		this.id = id;
		this.userName = userName;
		this.userId = userId;
		this.sectorName = sectorName;
		this.specialityName = specialityName;
		this.delegateNumber = delegateNumber;
	}

	public PreAffectationDto() {super();}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getSectorName() {
		return sectorName;
	}

	public void setSectorName(String sectorName) {
		this.sectorName = sectorName;
	}

	public String getSpecialityName() {
		return specialityName;
	}

	public void setSpecialityName(String specialityName) {
		this.specialityName = specialityName;
	}

	

	public Integer getDelegateNumber() {
		return delegateNumber;
	}

	public void setDelegateNumber(Integer delegateNumber) {
		this.delegateNumber = delegateNumber;
	}
	

}

