package com.intellitech.birdnotes.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.PresentationTimeTrackingDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;

public interface PresentationTimeTrackingService {

	List<PresentationTimeTrackingDto> getTimeTracking(Long visitProductId, Long productId) throws BirdnotesException;
	
	

}
