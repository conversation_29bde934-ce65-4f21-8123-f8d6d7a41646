package com.intellitech.birdnotes.model.convertor;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.WholesalerDto;

@Component("wholesalerToDtoConvertor")
public class WholesalerToDtoConvertor {

		private static final Logger LOG = LoggerFactory.getLogger(WholesalerToDtoConvertor.class);

		public WholesalerDto convert(Wholesaler wholesaler) throws BirdnotesException {

			if (wholesaler == null) {
				LOG.error("wholesaler is null");
				throw new BirdnotesException("wholesaler is null");
			}

			WholesalerDto wholesalerDto = new WholesalerDto();
			wholesalerDto.setId(wholesaler.getId());
			wholesalerDto.setName(wholesaler.getName());
			wholesalerDto.setResponsible(wholesaler.getResponsible());
			wholesalerDto.setAddress(wholesaler.getAddress());
			wholesalerDto.setPhone(wholesaler.getPhone());
			wholesalerDto.setDescription(wholesaler.getDescription());
			wholesalerDto.setEmail(wholesaler.getEmail());
			wholesalerDto.setDiscount(wholesaler.getDiscount());
			wholesalerDto.setSectorId(wholesaler.getSector().getId());
			if(wholesaler.getStatus().toString()!=null) {
				wholesalerDto.setStatus(wholesaler.getStatus().toString());
			}
			
			//wholesalerDto.setShopEmail(wholesaler.getShopEmail());
			return wholesalerDto;

		}
}
