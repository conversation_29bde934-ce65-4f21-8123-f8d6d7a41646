package com.intellitech.birdnotes.util;

import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReadExcelFileUtilTest {

    @Mock
    private ProspectRepository mockProspectRepository;
    @Mock
    private PlanningRepository mockPlanningRepository;

    private ReadExcelFileUtil<String> readExcelFileUtilUnderTest;

    @Before
    public void setUp() throws Exception {
        readExcelFileUtilUnderTest = new ReadExcelFileUtil<>();
        readExcelFileUtilUnderTest.prospectRepository = mockProspectRepository;
        readExcelFileUtilUnderTest.planningRepository = mockPlanningRepository;
    }

    @Test
    public void testReadDataFromExcelFile() throws Exception {
        // Setup
        // Configure ProspectRepository.findById(...).
        final Prospect prospect = new Prospect();
        final Range range = new Range();
        range.setId(0);
        range.setName("name");
        prospect.setRanges(new HashSet<>(Arrays.asList(range)));
        final ProspectsAffectation prospectsAffectation = new ProspectsAffectation();
        prospectsAffectation.setId(0L);
        prospect.setProspectsAffectation(new HashSet<>(Arrays.asList(prospectsAffectation)));
        when(mockProspectRepository.findById(0L)).thenReturn(prospect);

        // Run the test
//        final List<String> result = readExcelFileUtilUnderTest.readDataFromExcelFile("filePath");

        // Verify the results
        //      assertEquals(Arrays.asList("value"), result);
    }

    @Test
    public void testReadDataFromExcelFile_ProspectRepositoryReturnsNull() throws Exception {
        // Setup
        when(mockProspectRepository.findById(0L)).thenReturn(null);

        // Run the test
//        final List<String> result = readExcelFileUtilUnderTest.readDataFromExcelFile("filePath");

        // Verify the results
        //       assertEquals(Arrays.asList("value"), result);
    }
}
