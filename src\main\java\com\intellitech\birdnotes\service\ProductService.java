package com.intellitech.birdnotes.service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.dto.IdVersionDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.ProductCoverageResult;
import com.intellitech.birdnotes.model.dto.ProductDto;

public interface ProductService {

	//List<ProductDto> getNewVersionProducts(List<IdVersionDto> idVersions) throws BirdnotesException;

	Product saveProduct(ProductDto productRequest) throws BirdnotesException;

	List<Product> saveAll(List<ProductDto> productRequests) throws BirdnotesException;

	void deleteProduct(Long productId) throws BirdnotesException;

	Product updateProduct(ProductDto productDto) throws BirdnotesException;

	boolean checkProductNameIsUnique(String productName);

	ProductCoverageResult getProductCoverage(Long productId, String date) throws ParseException;

	List<com.intellitech.birdnotes.data.dto.ProductCompareDto> compareProductsPerMonth(Integer month, Integer year);

	List<com.intellitech.birdnotes.data.dto.ProductCompareDto> compareProductsPerWeek(Date mondayDate);
	
	public List<ProductDto> findProductsByGamme(List<Integer> rangeIds) throws BirdnotesException;

	List<ProductDto> getAllProducts() throws BirdnotesException;

	void updateQuantityFromErp();
	
	List<ProductDto> getNewVersionProducts(List<IdVersionDto> idVersions, Long userId) throws BirdnotesException;

	Map<String, List<ProductDto>> importProduct(String path) throws BirdnotesException;
	
	List<Map<String, Object>> getAllProductsIdAndName();
	
}

