package com.intellitech.birdnotes.data.dto;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

public class PurchaseOrderDataDto {
	
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
	
	private Long id;
	private String wholesalerName;
	private String wholesalerAddress;
	private String wholesalerEmail;
	private String prospectName;
	private String prospectAddress;
	private String delegateName;
	private String date;
	private Float total;
	private String speciality;
	private String activity;
	private String sector;
	private String locality;
	private Long purchaseOrderTemplateId;

	
	
	
	
	public PurchaseOrderDataDto(Long id, String wholesalerName, String wholesalerAddress, String wholesalerEmail,
			String prospectName, String prospectAddress, String delegateName, Date date) {
		super();
		this.id = id;
		this.wholesalerName = wholesalerName;
		this.wholesalerAddress = wholesalerAddress;
		this.wholesalerEmail = wholesalerEmail;
		this.prospectName = prospectName;
		this.prospectAddress = prospectAddress;
		this.delegateName = delegateName;
		this.date = format.format(date);
	}
	
	
	public PurchaseOrderDataDto(Long id, String wholesalerName,
			String prospectName, String delegateName, String date,
			String speciality, String activity, String sector, String locality, Long purchaseOrderTemplateId) {
		super();
		this.id = id;
		this.wholesalerName = wholesalerName;
		this.prospectName = prospectName;
		this.delegateName = delegateName;
		this.date = format.format(date);
		this.speciality = speciality;
		this.activity = activity;
		this.sector = sector;
		this.locality = locality;
		this.purchaseOrderTemplateId = purchaseOrderTemplateId;
	}


	public String getWholesalerName() {
		return wholesalerName;
	}
	public void setWholesalerName(String wholesalerName) {
		this.wholesalerName = wholesalerName;
	}
	public String getWholesalerAddress() {
		return wholesalerAddress;
	}
	public void setWholesalerAddress(String wholesalerAddress) {
		this.wholesalerAddress = wholesalerAddress;
	}
	public String getWholesalerEmail() {
		return wholesalerEmail;
	}
	public void setWholesalerEmail(String wholesalerEmail) {
		this.wholesalerEmail = wholesalerEmail;
	}
	public String getProspectName() {
		return prospectName;
	}
	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}
	public String getProspectAddress() {
		return prospectAddress;
	}
	public void setProspectAddress(String prospectAddress) {
		this.prospectAddress = prospectAddress;
	}
	public String getDelegateName() {
		return delegateName;
	}
	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}


	public String getDate() {
		return date;
	}
	public void setDate(String date) {
		this.date = date;
	}
	public Float getTotal() {
		return total;
	}
	public void setTotal(Float total) {
		this.total = total;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getActivity() {
		return activity;
	}
	public void setActivity(String activity) {
		this.activity = activity;
	}
	public String getSector() {
		return sector;
	}
	public void setSector(String sector) {
		this.sector = sector;
	}
	public String getLocality() {
		return locality;
	}
	public void setLocality(String locality) {
		this.locality = locality;
	}
	
	public String getSpeciality() {
		return speciality;
	}
	public void setSpeciality(String speciality) {
		this.speciality = speciality;
	}
	
	
}