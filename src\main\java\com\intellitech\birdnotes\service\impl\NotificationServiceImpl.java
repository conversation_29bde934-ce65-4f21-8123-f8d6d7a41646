package com.intellitech.birdnotes.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Scanner;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.OneSignalDto;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.NotificationDto;
import com.intellitech.birdnotes.model.dto.NotificationRuleDto;
import com.intellitech.birdnotes.model.request.NotificationRequest;
import com.intellitech.birdnotes.repository.NotificationRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationRuleService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.thread.ThreadSendEmail;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

@Service("notificationService")
@Transactional
public class NotificationServiceImpl implements NotificationService {
	private static final Logger LOG = LoggerFactory.getLogger(IssueServiceImpl.class);
	private SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
	private JavaMailSender javaMailSender;
	private ConfigurationService configurationService;
	@Autowired
	private UserService userService;
	@Autowired
	private UserToDtoConvertor userToDtoConvertor;
	@Autowired
	private NotificationRepository notificationRepository;

	@Autowired
	private NotificationRuleService notifiationManagerService;
	@Autowired
	private MessageSource messageSource;

	@Autowired
	private RoleRepository roleRepository;
	
	@Autowired
	private UserRepository userRepository;

	@Autowired
	private NotificationMessageBuilder notificationMessageBuilder;
	
	@Autowired
	private CurrentUser currentUser;
	
	@Autowired
	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}
	

	@Override
	public List<NotificationDto> findAll(User user, NotificationRequest notificationRequest) {
		List<NotificationDto> notificationDtos = new ArrayList<>();
		List<Notification> notifications = null;// notificationRepository.findByTargetUser(user);
		try {
			notifications = notificationRepository.findAll(notificationRequest.getStartDate(),
					notificationRequest.getEndDate(), user);
		} catch (Exception e) {
			LOG.error("An exception occurred", e);
		}
		for (Notification notification : notifications) {
			notificationDtos.add(new NotificationDto(notification));
		}
		return notificationDtos;
	}
	
	
	public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId, String eventType ) {
		notificationMessageBuilder.setMessageType(getMessageType(eventType));
		notificationMessageBuilder.setEntityId(entityId);
		notificationMessageBuilder.setUser(sourceUser);
		Notification notification =	notificationMessageBuilder.Build();
		generateUsingWorkflow(sourceUser, notification, eventType);
	}
	
	public  void sendNotificationUsingWorkflow(User sourceUser, String eventType, Date date, String status) {
		notificationMessageBuilder.setMessageType(getMessageType(eventType));
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setDate(date);
		if(status != null && status.equals("VALID")) {
			notificationMessageBuilder.setStatus("valide");
		}else if(status != null && status.equals("iNVALID")) {
			notificationMessageBuilder.setStatus("Invalide");
		}
		Notification notification =	notificationMessageBuilder.Build();
		generateUsingWorkflow(sourceUser, notification, eventType);
	}
	
public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId, String eventType, List <String > prospectNames ) {
		notificationMessageBuilder.setMessageType(getMessageType(eventType));
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setEntityId(entityId);
		if(prospectNames != null) {
			notificationMessageBuilder.setProspects(prospectNames);
		}
		Notification notification =	notificationMessageBuilder.Build();
		generateUsingWorkflow(sourceUser, notification, eventType);
	}
	
	public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId, String eventType, Prospect prospect ) {
		notificationMessageBuilder.setMessageType(getMessageType(eventType));
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setEntityId(entityId);
		if(prospect != null) {
			notificationMessageBuilder.setProspect(prospect);
		}
	
		Notification notification =	notificationMessageBuilder.Build();
		generateUsingWorkflow(sourceUser, notification, eventType);
	}
	
	public  void sendNotificationUsingWorkflow(User sourceUser, Long entityId, String eventType, Prospect prospect, Date eventDate, String text, String productName  ) {
		notificationMessageBuilder.setMessageType(getMessageType(eventType));
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setEntityId(entityId);
		notificationMessageBuilder.setDate(eventDate);
		notificationMessageBuilder.setNote(text);
		notificationMessageBuilder.setProductName(productName);
		if(prospect != null) {
			notificationMessageBuilder.setProspect(prospect);
		}
		if(text  != null && !"".equals(text)) {
			Notification notification =	notificationMessageBuilder.Build();
			generateUsingWorkflow(sourceUser, notification, eventType);
		}
		
	}
	
	
	private void generateUsingWorkflow(User sourceUser, Notification notification, String eventType) {

		List<NotificationRuleDto> notificationsRuleDto = notifiationManagerService.findNotificationByEvent(eventType);
		for (NotificationRuleDto notificationRuleDto : notificationsRuleDto) {

			if (notificationRuleDto.getIdRole() != null) {
				
				Role role = roleRepository.findOne(notificationRuleDto.getIdRole());
				Set<User> userSuperiors = notifiationManagerService.getSuperiorsByRole(sourceUser.getId(), role.getName());

				for (User user : userSuperiors) {
					sendNotification(notification, user, notificationRuleDto.getNotificationMethod());
				}
				
				if(sourceUser.getRoles().stream().map(Role::getName).collect(Collectors.toList()).contains(role.getName())) {
					sendNotification(notification, sourceUser, notificationRuleDto.getNotificationMethod());
				}
			}
			
			if (notificationRuleDto.getIdUser() != null) {

				User user = userRepository.findOne(notificationRuleDto.getIdUser());
				notification.setTargetUser(user);
				sendNotification(notification, user, notificationRuleDto.getNotificationMethod());
			}

		}

	}
	
	private void sendNotification(Notification notification, User user, String notificationMethod) {
		
		Notification wfNotification = new Notification(notification, user);
		if (notificationMethod.equals("PUSH")) {
			this.sentPushNotification(wfNotification);
		}
		if (notificationMethod.equals("EMAIL")) {
			this.sentEmailNotification(wfNotification);
		}
		if (notificationMethod.equals("APP_NOTIF")) {

			this.sendAppNotification(wfNotification);
		}
		if (notificationMethod.equals("SMS")) {
			this.sentSMSNotification(wfNotification);

		}
	}
	
	private String getMessageType ( String eventType  ) {
		
		if(eventType.equals("ADD_EXPENSE")) {
			return "noteFraisNotificationMessage";
			
		}else if(eventType.equals("ADD_DELEGATE_COMMISSION")) {
		return "delegateCommissionNotificationMessage";
		
		}else if(eventType.equals("ADD_ACTIONMARKETING")) {
			return "actionMarketingNotificationMessage";
			
		}else if(eventType.equals("ADD_ACTIVITY")) {
			return "activityNotificationMessage";
			
		}else if(eventType.equals("ADD_PLANIFICATION")) {
			return "planningNotificationMessage";
			
		}else if(eventType.equals("ADD_AFFECTATION")) {
			return "prospectsAffectationNotificationMessage";
			
		}else if(eventType.equals("ADD_UPDATE_PROSPECT")) {
			return "addProspectNotificationMessage";
			
		}else if(eventType.equals("ADD_PLANIFICATION")) {
			return "planningNotificationMessage";
			
		}else if(eventType.equals("ADD_PLANIFICATION")) {
			return "planningNotificationMessage";
			
		}else if(eventType.equals("REMOVE_AFFECTATION")) {
			return "prospectsAffectationDeleteNotificationMessage";
		
		}else if(eventType.equals("IMPORTANT_COMMENT")) {
			return "importantMessageTag";
		
		}else if(eventType.equals("URGENT_COMMENT")) {
			return "urgentMessageTag";
		}  
		else if(eventType.equals("REPORT_VALIDATION")) {
			return "reportValidation";
		} 
		return "";
		
	}


	
	public  void sendSingleNotification(User user, Long entityId, String messageType ) {
		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		notificationMessageBuilder.setMessageType(messageType);
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setEntityId(entityId);
		notificationMessageBuilder.setTagetUser(user);
		Notification notification = notificationMessageBuilder.Build();
		generateUsingAllNotificationMethods(notification);	
	}
	
	public  void sendSingleNotification(User user, Long entityId, String messageType , List <String > prospectNames ) {

		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		notificationMessageBuilder.setMessageType(messageType);
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setEntityId(entityId);
		notificationMessageBuilder.setTagetUser(user);
		if(prospectNames != null) {
			notificationMessageBuilder.setProspects(prospectNames);
		}
		Notification notification = notificationMessageBuilder.Build();
		generateUsingAllNotificationMethods(notification);	
	}
	
	public  void sendSingleNotification(User user, Long entityId, String messageType , Prospect prospect ) {
		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		notificationMessageBuilder.setMessageType(messageType);
		notificationMessageBuilder.setUser(sourceUser);
		notificationMessageBuilder.setEntityId(entityId);
		notificationMessageBuilder.setTagetUser(user);
		if(prospect != null) {
			notificationMessageBuilder.setProspect(prospect);
		}
		Notification notification = notificationMessageBuilder.Build();
		generateUsingAllNotificationMethods(notification);	
	}

	@Override
	public void generateUsingAllNotificationMethods(Notification notification) {
		
		this.sendAppNotification(notification);
		//this.sentPushNotification(notification);
		//this.sentEmailNotification(notification);
	}

	public void sendAppNotification(Notification notification) {
		notificationRepository.save(notification);

	}

	@Override
	public List<NotificationDto> findByUser(User user, NotificationRequest notificationRequest) {

		List<NotificationDto> notificationDtos = new ArrayList<>();
		List<Notification> notifications = notificationRepository.findByTargetUser(user);

		for (Notification notification : notifications) {
			notificationDtos.add(new NotificationDto(notification));
		}
		return notificationDtos;
	}

	@Override
	public int getNotificationNumber(User user) {
		return notificationRepository.getNotificationNumber(user);
	}

	@Override
	public List<NotificationDto> findDelegateNotification(Long id, Long userId) {
		List<Notification> notifications = notificationRepository.findDelegateNotification(id, userId);
		List<NotificationDto> notificationDtos = new ArrayList<>();

		for (Notification notification : notifications) {
			notificationDtos.add(new NotificationDto(notification));
		}
		return notificationDtos;
	}

	@Override
	public void updateNotificationStatus(User user) {
		notificationRepository.updateNotificationStatus(user);
	}

	@Override
	public void sentPushNotification(Notification notification) {

		String oneSignalUserId = notification.getTargetUser().getOneSignalUserId();

		if (oneSignalUserId != null && !oneSignalUserId.equals("")) {

			Map<String, String> data = null;
			String message = notification.getText();

			OneSignalDto oneSignalDto = new OneSignalDto();
			oneSignalDto.setAppId("************************************");
			List<String> includePlayerIds = new ArrayList<>();
			includePlayerIds.add(oneSignalUserId);
			oneSignalDto.setIncludePlayerIds(includePlayerIds);
			Map<String, String> contents = new HashMap<>();
			contents.put("en", message);
			contents.put("fr", message);
			oneSignalDto.setContents(contents);
			if (data != null) {
				oneSignalDto.setData(data);
			}

			try {
				String jsonResponse;

				URL url = new URL("https://onesignal.com/api/v1/notifications");
				HttpURLConnection con = (HttpURLConnection) url.openConnection();
				con.setUseCaches(false);
				con.setDoOutput(true);
				con.setDoInput(true);

				con.setRequestProperty("Content-Type", "application/json; charset=UTF-8");
				con.setRequestMethod("POST");

				String strJsonBody = "";

				ObjectMapper mapper = new ObjectMapper();
				try {
					strJsonBody = mapper.writeValueAsString(oneSignalDto);

				} catch (JsonProcessingException e) {
					e.printStackTrace();
				}


				byte[] sendBytes = strJsonBody.getBytes("UTF-8");
				con.setFixedLengthStreamingMode(sendBytes.length);

				OutputStream outputStream = con.getOutputStream();
				outputStream.write(sendBytes);

				int httpResponse = con.getResponseCode();


				if (httpResponse >= HttpURLConnection.HTTP_OK && httpResponse < HttpURLConnection.HTTP_BAD_REQUEST) {
					Scanner scanner = new Scanner(con.getInputStream(), "UTF-8");
					jsonResponse = scanner.useDelimiter("\\A").hasNext() ? scanner.next() : "";
					scanner.close();
				} else {
					Scanner scanner = new Scanner(con.getErrorStream(), "UTF-8");
					jsonResponse = scanner.useDelimiter("\\A").hasNext() ? scanner.next() : "";
					scanner.close();
				}

			} catch (Throwable t) {
				t.printStackTrace();
			}
		}

	}

	@Override
	public void sentEmailNotification(Notification notification) {
		String htmlBody = notification.getText();
		String[] to = { notification.getTargetUser().getEmail() };
		ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, "Birdnotes notification", htmlBody);
		Thread thread = new Thread(threadSendEmail);
		thread.start();
	}
	
	@Override
	public void sentSMSNotification(Notification notification) {
		
    
        BufferedReader reader = null;
        try {
            String Url_str =
                "https://tunisiesms.tn/client/Api/Api.aspx?fct=sms&sender=BirdNotes&SMS=" + notification.getText() + "&key=TFgf8TIkpsW/-/VgzS7HbrpiSQLfISi/-/x1nzgLFj5XQMwovOMP5Lx7txs5l3mgk8VPvUM6BfDtpPzMhlgUboSHqHh0FlAG9NVe&mobile=" + notification.getTargetUser().getPhone() + "&msg_id=1";

            URL myURL = new URL(Url_str);

            URLConnection myURLConnection = myURL.openConnection();
            myURLConnection.connect();
            reader = new BufferedReader(new InputStreamReader(myURLConnection.getInputStream()));
            // reading response
            String response;
            while ((response = reader.readLine()) != null) {
            	LOG.info("Sms response " + response );
            }

            LOG.info("myURLConnection.getContent() " + myURLConnection.getContent());
 
        } catch (MalformedURLException e) {
        	LOG.error(e.getMessage(), e);
        } catch (IOException e) {
        	LOG.error(e.getMessage(), e);
        }

        
	}

}
