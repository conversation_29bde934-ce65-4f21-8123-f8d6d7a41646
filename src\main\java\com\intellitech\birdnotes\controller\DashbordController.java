package com.intellitech.birdnotes.controller;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.DashbordDto;
import com.intellitech.birdnotes.model.dto.ActivityByPeriod;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.ProspectMessage;
import com.intellitech.birdnotes.service.ActionMarketingService;
import com.intellitech.birdnotes.service.ExpenseReportService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.VisitService;

@RestController
@RequestMapping("/dashbord")
public class DashbordController {

	private static final Logger LOG = LoggerFactory.getLogger(DashbordController.class);

	@Autowired
	private VisitService visitService;

	@Autowired
	private ActionMarketingService actionMarketingService;

	@Autowired
	private ExpenseReportService noteFraisService;

	@Autowired
	UserService userService;

	@RequestMapping(value = "getData/{start_date}/{end_date}", method = RequestMethod.GET)
	public ResponseEntity<DashbordDto> getData(@PathVariable("start_date") Date startDate,
			@PathVariable("end_date") Date endDate) {
		try {
			if (userService.checkHasPermission("STATISTICS_VIEW")) {
				List<LabelValueDto> marketingExpenses = actionMarketingService.getMarketingExpensesByDelegate(startDate,
						endDate);
				List<LabelValueDto> expensesReportsByDelegate = noteFraisService.getExpensesReportByDelegate(startDate,
						endDate);
				List<LabelValueDto> expensesReportsByType = noteFraisService.getExpensesReportByType(startDate,
						endDate);
				List<ProspectMessage> urgentMessages = visitService.getUrgentMessages(startDate, endDate);
				List<ActivityByPeriod> activitiesByPeriod = visitService.activityByPeriod(startDate, endDate);
				List<ActivityByPeriod> activitiesByPotential = visitService.activityByPotential(startDate, endDate);
				List<ActivityByPeriod> activitiesByDelegate = visitService.activityByDelegate(startDate, endDate);
				List<ActivityByPeriod> activitiesBySpeciality = visitService.activityBySpeciality(startDate, endDate);
				List<LabelValueDto> prospectsSatisfaction = visitService.getProspectsSatisfaction(startDate, endDate);
				Float salesRevenue = visitService.getSalesRevenuee(startDate, endDate);

				DashbordDto dashbordDto = new DashbordDto();
				dashbordDto.setSalesRevenue(salesRevenue);
				dashbordDto.setExpensesReportsByDelegate(expensesReportsByDelegate);
				dashbordDto.setMarketingExpenses(marketingExpenses);
				dashbordDto.setExpensesReportsByType(expensesReportsByType);
				dashbordDto.setUrgentMessages(urgentMessages);
				dashbordDto.setActivitiesByPeriod(activitiesByPeriod);
				dashbordDto.setActivitiesByDelegate(activitiesByDelegate);
				dashbordDto.setActivitiesByPotential(activitiesByPotential);
				dashbordDto.setActivitiesBySpeciality(activitiesBySpeciality);
				dashbordDto.setProspectsSatisfaction(prospectsSatisfaction);

				return new ResponseEntity<>(dashbordDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in visitAveragePerDay", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
