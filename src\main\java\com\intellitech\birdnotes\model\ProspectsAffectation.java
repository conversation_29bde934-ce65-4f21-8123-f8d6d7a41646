package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.USER_PROSPECT, schema = Common.PUBLIC_SCHEMA)
public class ProspectsAffectation implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Delegate delegate;
	private Prospect prospect;
	private PreAffectation preAffectation;


	public ProspectsAffectation() {
		super();
	}
	
	public ProspectsAffectation(Long id, Delegate delegate, Prospect prospect, PreAffectation preAffectation) {
	super();
	this.id = id;
	this.delegate = delegate;
	this.prospect = prospect;
	this.preAffectation = preAffectation;
}

	@Id
	@SequenceGenerator(name = Sequences.AFFECTATION_SEQUENCE, sequenceName = Sequences.AFFECTATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.AFFECTATION_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {return id;}
	public void setId(Long id) {this.id = id;}
	
	@JsonIgnore
	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}


	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID)
	public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

	
	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PRE_AFFECTATION_ID, unique = false)
	public PreAffectation getPreAffectation() {
		return preAffectation;
	}

	public void setPreAffectation(PreAffectation preAffectation) {
		this.preAffectation = preAffectation;
	}
}	
	


	
	
	
