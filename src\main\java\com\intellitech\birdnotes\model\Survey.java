package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.Survey, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)

public class Survey implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Product product;
	private String evaluationCriteriaName;
	private Float evaluationCriteriaValue;
	private Date evaluationDate;
	private String comment;

	@Id
	@SequenceGenerator(name = Sequences.SURVEY_SEQUENCE, sequenceName = Sequences.SURVEY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.SURVEY_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}

	@Column(name = BirdnotesConstants.Columns.EVALUATION_CRITERIA_NAME)
	public String getEvaluationCriteriaName() {
		return evaluationCriteriaName;
	}

	public void setEvaluationCriteriaName(String evaluationCriteriaName) {
		this.evaluationCriteriaName = evaluationCriteriaName;
	}

	@Column(name = BirdnotesConstants.Columns.EVALUATION_CRITERIA_VALUE)
	public Float getEvaluationCriteriaValue() {
		return evaluationCriteriaValue;
	}

	public void setEvaluationCriteriaValue(Float evaluationCriteriaValue) {
		this.evaluationCriteriaValue = evaluationCriteriaValue;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.EVALUATION_DATE)
	public Date getEvaluationDate() {
		return evaluationDate;
	}

	public void setEvaluationDate(Date evaluationDate) {
		this.evaluationDate = evaluationDate;
	}

	@Column(name = BirdnotesConstants.Columns.COMMENT)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

}
