package com.intellitech.birdnotes.service;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.FileSystemUtils;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.FileDto;
import com.intellitech.birdnotes.util.BirdnotesUtils;

@Service
@Transactional
public class StorageService {
	
	private static final Logger LOG = LoggerFactory.getLogger(StorageService.class);

	@Value("${logoPath}")
	private String logoPath;
	@Value("${uploadPath}")
	private String uploadPath;
	private Path rootLocation;
	
	public StorageService() {
		super();
		
	}

	@PostConstruct
	public void inititialisation() {
		this.rootLocation = Paths.get(uploadPath,logoPath);	
	}

	public void store(MultipartFile file) throws BirdnotesException {
		try {
			Files.copy(file.getInputStream(), this.rootLocation.resolve(file.getOriginalFilename()));

		} catch (Exception e) {
			LOG.error("Failed of copy file", e);
			throw new BirdnotesException("Failed of copy file");
		}
	}
	

	public Resource loadFile(String filename) throws BirdnotesException {
		try {
			Path file = rootLocation.resolve(filename);
			Resource resource = new UrlResource(file.toUri());
			if (resource.exists() || resource.isReadable()) {
				return resource;
			} else {
				LOG.error("FAIL!,resource n existe pas");
				throw new BirdnotesException("FAIL!,resource n existe pas");
			}
		} catch (MalformedURLException e) {
			LOG.error("FAIL to load file!");
			throw new BirdnotesException("FAIL to load file!");
		}
	}

	public void deleteAll() throws BirdnotesException {
		try {
			FileSystemUtils.deleteRecursively(rootLocation.toFile());
		} catch (Exception e) {
			LOG.error("Could not deleted " + rootLocation.toFile() + " file!", e);
			throw new BirdnotesException("Could not deleted " + rootLocation.toFile() + " file!");
		}
	}
	
	public void init() throws BirdnotesException {
		try {
			Files.createDirectory(rootLocation);
		} catch (IOException e) {
			LOG.error("Could not initialize storage!", e);
			throw new BirdnotesException("Could not initialize storage!");
		}
	}


	public void setLogoPath(String logoPath) {
		this.logoPath = logoPath;
	}


	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	public void storeFiles(MultipartFile[] files, String folderPath) {
 
		try {
			
			if (!Files.exists(Paths.get(folderPath))) {
				Files.createDirectory(Paths.get(folderPath));
			}
			
			
			for (MultipartFile multipartFile : files) {
				Files.copy(multipartFile.getInputStream(), Paths.get(folderPath).resolve(multipartFile.getOriginalFilename()));
			}
			
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
		
		
	}
	
	
	public List<String> getFilesNameInDirectory(String folderPath) throws BirdnotesException {
		
		List<String> filesNames = new ArrayList<>();
		File folder = new File(folderPath);
		File[] listOfFiles = folder.listFiles();
		if(listOfFiles != null) {
			for (int i = 0; i < listOfFiles.length; i++) {
				  if (listOfFiles[i].isFile()) {
					  filesNames.add(listOfFiles[i].getName());
				  }
				}
		}
		
		return filesNames;
	}
	
	public List<FileDto> loadFiles(String folderPath) throws BirdnotesException {
		
		List<FileDto> documentsBase64 = new ArrayList<>();
		File folder = new File(folderPath);
		File[] listOfFiles = folder.listFiles();
		if(listOfFiles != null) {
			for (int i = 0; i < listOfFiles.length; i++) {
				  if (listOfFiles[i].isFile()) {
					  FileDto fileDto = new FileDto(listOfFiles[i].getName(), BirdnotesUtils.encodeFile(listOfFiles[i].getAbsolutePath()));
				    documentsBase64.add(fileDto);
				  }
				}
		}
		
		return documentsBase64;
	}

	public void deleteFiles(String folderPath, List<String> deletedFiles) {
		
		if(deletedFiles != null) {
			for (String deletedFile : deletedFiles) {
				try {
					Files.delete(Paths.get(folderPath + File.separator + deletedFile));
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
		
		
		
	}

	public void deleteFolderContent(String directoryPath) {
		
		File folder = new File(directoryPath);
		File[] listOfFiles = folder.listFiles();
		if(listOfFiles != null) {
			for (int i = 0; i < listOfFiles.length; i++) {
					listOfFiles[i].delete();
				}
		}
		
	}
}
