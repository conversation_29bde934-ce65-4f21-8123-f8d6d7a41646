package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.User;

public class LocationDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Date date;
	private Delegate delegate;
	private Double latitude;
	private Double longitude;
	private  List<ProspectDto> prospects;
	private Integer distance;
	


	public LocationDto(Long id, Date date,Delegate delegate, Double latitude,Double longitude) {
		super();
		this.id = id;
		this.date = date;
		this.delegate = delegate;
		this.longitude= longitude;
		this.latitude=latitude;
		
	}

	public LocationDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}


	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public Integer getDistance() {
		return distance;
	}

	public void setDistance(Integer distance) {
		this.distance = distance;
	}

	

}
