package com.intellitech.birdnotes.service;

import java.util.List;
import java.util.Set;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ContactType;
import com.intellitech.birdnotes.model.dto.ContactTypeDto;



public interface ContactTypeService {
	
	List<ContactTypeDto> findAll() throws BirdnotesException;
	
	void saveAllContactTypes(List<ContactTypeDto> contactTypeDtos) throws BirdnotesException;

	ContactTypeDto findContactTypeDto(String contactTypeName, List<ContactTypeDto> contactTypeDtos);
	
	ContactType saveContactType(ContactTypeDto contactTypeDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;
	
	Set<ContactType> findContactTypesByIds(List<Long> contactTypeIds);






	

}