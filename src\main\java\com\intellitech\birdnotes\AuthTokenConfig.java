package com.intellitech.birdnotes;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

@Component
public class AuthTokenConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

	@Autowired
	private UserDetailsService birdnotesUserDetailsService;

	@Override
	public void configure(HttpSecurity http) throws Exception {
						
		http.addFilterBefore(new AuthTokenFilter(birdnotesUserDetailsService), UsernamePasswordAuthenticationFilter.class);
		http.addFilterBefore(new SimpleCORSFilter(), AuthTokenFilter.class);
		//AuthTokenFilter customFilter = new AuthTokenFilter(birdnotesUserDetailsService);
		//http.addFilterBefore(customFilter, UsernamePasswordAuthenticationFilter.class);
		//http.addFilterBefore(new SimpleCORSFilter(), UsernamePasswordAuthenticationFilter.class);
		//http.addFilterAfter(new AuthTokenFilter(birdnotesUserDetailsService), SimpleCORSFilter.class);

	}
}
