package com.intellitech.birdnotes.thread;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mail.javamail.JavaMailSender;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class ThreadSendEmailTest {

    @Mock
    private JavaMailSender mockJavaMailSender;

    private ThreadSendEmail threadSendEmailUnderTest;

    @Before
    public void setUp() throws Exception {
        threadSendEmailUnderTest = new ThreadSendEmail(mockJavaMailSender, "from", new String[]{"to"},
                Arrays.asList("value"), "subject", "htmlBody", "pathAttachment", "nameAttachment");
    }

    @Test
    public void testRun() {
        // Setup
        // Run the test
        threadSendEmailUnderTest.run();

        // Verify the results
    }
}
