
package com.intellitech.birdnotes.model.dto;

import java.util.Date;
import java.util.List;



public class OpportunityNoteRequestDto {

	private Long id;
	private String name;
	private Float budget;
	private Date date;
	private String description;
	private String nameAttachment;
	private String attachmentBase64;
	private List<Long> prospectsIds;
	private List<Long> pharmaciesIds;
	private List<Long> productsIds;
	private List<FileNamePath> nameFile;

	

	public OpportunityNoteRequestDto() {
		super();
	}
	public OpportunityNoteRequestDto(Long id, String name, Float budget, Date date, List<Long> prospectsIds, List<Long> pharmaciesIds,
			List<Long> productsIds,String nameAttachment,String attachmentBase64, String description) {
		super();
		this.id = id;
		this.name = name;
		this.budget = budget;
		this.date = date;
		this.pharmaciesIds=pharmaciesIds;
		this.prospectsIds = prospectsIds;
		this.productsIds = productsIds;
		this.nameAttachment=nameAttachment;
		this.attachmentBase64=attachmentBase64;
		this.setDescription(description);
	}
	public String getNameAttachment() {
		return nameAttachment;
	}
	public void setNameAttachment(String nameAttachment) {
		this.nameAttachment = nameAttachment;
	}
	public String getAttachmentBase64() {
		return attachmentBase64;
	}
	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}
	public List<Long> getPharmaciesIds() {
		return pharmaciesIds;
	}
	public void setPharmaciesIds(List<Long> pharmaciesIds) {
		this.pharmaciesIds = pharmaciesIds;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Float getBudget() {
		return budget;
	}
	public void setBudget(Float budget) {
		this.budget = budget;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public List<Long> getProspectsIds() {
		return prospectsIds;
	}
	public List<Long> getProductsIds() {
		return productsIds;
	}
	public void setProductsIds(List<Long> productsIds) {
		this.productsIds = productsIds;
	}
	public void setProspectsIds(List<Long> prospectsIds) {
		this.prospectsIds = prospectsIds;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	public List<FileNamePath> getNameFile() {
		return nameFile;
	}
	public void setNameFile(List<FileNamePath> nameFile) {
		this.nameFile = nameFile;
	}
	
	
	
	
	
	
	
	
	
	
}
