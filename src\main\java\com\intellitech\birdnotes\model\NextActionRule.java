package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.NEXTACTIONRULE, schema = Common.PUBLIC_SCHEMA)
public class NextActionRule implements Serializable {
	
	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.NEXT_ACTION_RULE_SEQUENCE, sequenceName = Sequences.NEXT_ACTION_RULE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.NEXT_ACTION_RULE_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.ACTION)
	private String action;
	
	@Column(name = Columns.TOTALREVENUE)
	private Integer totalRevenue;
	
	@Column(name = Columns.PERIOD)
	private Integer period;
	
	
	
	
	public NextActionRule(Long id, String action , Integer totalRevenue, Integer period) {
		super();
		this.id = id;
		this.action = action;
		this.totalRevenue = totalRevenue;
		this.period = period;
		
	}
	

	public NextActionRule() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}


	public String getAction() {
		return action;
	}


	public void setAction(String action) {
		this.action = action;
	}


	public Integer getTotalRevenue() {
		return totalRevenue;
	}


	public void setTotalRevenue(Integer totalRevenue) {
		this.totalRevenue = totalRevenue;
	}


	public Integer getPeriod() {
		return period;
	}


	public void setPeriod(Integer period) {
		this.period = period;
	}


	
	

}
