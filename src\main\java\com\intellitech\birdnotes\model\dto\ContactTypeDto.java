package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

public class ContactTypeDto implements Serializable{
    private List<ContactTypeDto> contactTypeDtos; 

	
	private static final long serialVersionUID = 1L;
	private Long id;
	private String name;
	public ContactTypeDto() {
		super();
	}
	
	public ContactTypeDto(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
		
	}
	
	public void setAction(int action) {
	}
	
	public void setIcon(String icon) {
	}

	public Long getId() {
		return id;
	}
	
	public void setId(Long id) {
		this.id = id;
	}
	
	public String getName() {
		return name;
	}
	
	public void setName(String name) {
		this.name = name;
	}
	
    public List<ContactTypeDto> getContactTypeDtos() {
        return contactTypeDtos;
    }

    public void setContactTypeDtos(List<ContactTypeDto> contactTypeDtos) {
        this.contactTypeDtos = contactTypeDtos;
    }



}