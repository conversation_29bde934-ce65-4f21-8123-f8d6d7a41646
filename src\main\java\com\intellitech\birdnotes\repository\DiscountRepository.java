package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Discount;
import com.intellitech.birdnotes.model.FreeQuantityRule;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleDto;


@Repository
public interface DiscountRepository extends JpaRepository<Discount, Long> {

	void deleteById(long id);
	
	@Query("Select d from Discount d")
	List<Discount> findAll();

	
	Discount findByProductAndWholesaler(Product p, Prospect w);

}
