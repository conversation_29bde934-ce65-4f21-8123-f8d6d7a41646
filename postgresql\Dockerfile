# this image will be used as birdnotes database
FROM postgres:13

#COPY ./postgresql/birdnotes-12-04-22.sql /docker-entrypoint-initdb.d/
#COPY birdnotes-12-04-22.sql /docker-entrypoint-initdb.d/

#argument
ARG FILES
ARG CLIENT


RUN apt-get update 
RUN apt-get install cron -y
RUN apt-get update -y && apt-get -y install ssh sshpass
RUN ssh-keygen -t rsa -N '' -f ~/.ssh/id_rsa
#RUN sshpass -p "2HYYnaXyWGMTIODIk1Nr" ssh-copy-id -o stricthostkeychecking=no devops@*************** 
#RUN echo 'root:1' | chpasswd

# Apply cron job
#COPY backup_file_script.sh /etc/backup_script
#RUN chmod 777 /etc/backup_script
#RUN echo '0  0    */3 * *   root    sh /etc/backup_script '${CLIENT} >> /etc/crontab
#RUN /sbin/service cron start


RUN mkdir -p /var/run/postgresql && chown -R 70:70 /var/run/postgresql
COPY ${FILES} /docker-entrypoint-initdb.d/ 

EXPOSE 5432
CMD ["postgres"]
