package com.intellitech.birdnotes.service.impl;

import com.intellitech.birdnotes.enumeration.NotificationMethod;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.NotificationRule;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.NotificationRuleDto;
import com.intellitech.birdnotes.model.request.NotificationRuleItem;
import com.intellitech.birdnotes.model.request.NotificationRuleRequest;
import com.intellitech.birdnotes.repository.NotificationRuleRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;

import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class NotificationRuleServiceImpTest {

    @Mock
    private UserRepository mockUserRepository;
    @Mock
    private RoleRepository mockRoleRepository;
    @Mock
    private NotificationRuleRepository mockNotificationRuleRepository;
    @Mock
    private ResourceLoader mockResourceLoader;

    private NotificationRuleServiceImp notificationRuleServiceImpUnderTest;

    @Before
    public void setUp() throws Exception {
        notificationRuleServiceImpUnderTest = new NotificationRuleServiceImp(mockUserRepository, mockRoleRepository,
                mockNotificationRuleRepository);
        notificationRuleServiceImpUnderTest.resourceLoader = mockResourceLoader;
    }

    @Test
    public void testDeleteByEventType() {
        // Setup
        // Run the test
        notificationRuleServiceImpUnderTest.deleteByEventType("eventType");

        // Verify the results
        verify(mockNotificationRuleRepository).deleteByEventType("eventType");
    }

    @Test
    public void testSaveNotification() throws Exception {
        // Setup
        final NotificationRuleRequest notificationRuleRequest = new NotificationRuleRequest();
        notificationRuleRequest.setSelectedEventType("eventType");
        final NotificationRuleItem notificationRuleItem = new NotificationRuleItem();
        notificationRuleItem.setIdRole(0);
        notificationRuleItem.setIdUser(0L);
        notificationRuleItem.setNotificationReceiver("notificationReceiver");
        notificationRuleItem.setNotificationMethod("notificationMethod");
        notificationRuleRequest.setNotificationsRule(Arrays.asList(notificationRuleItem));

        when(mockRoleRepository.findOne(0)).thenReturn(new Role(0, "name"));

        // Configure UserRepository.findOne(...).
        final User user = new User();
        user.setId(0L);
        final Role role = new Role();
        role.setId(0);
        role.setName("name");
        user.setRoles(new HashSet<>(Arrays.asList(role)));
        user.setSuperiors(new HashSet<>(Arrays.asList(new User())));
        when(mockUserRepository.findOne(0L)).thenReturn(user);

        // Run the test
        final boolean result = notificationRuleServiceImpUnderTest.saveNotification(notificationRuleRequest);

        // Verify the results
        assertThat(result).isTrue();
        verify(mockNotificationRuleRepository).deleteByEventType("eventType");
        verify(mockNotificationRuleRepository).save(any(NotificationRule.class));
    }

    @Test
    public void testGetEventXml() throws Exception {
        // Setup
        // Configure ResourceLoader.getResource(...).
        final Resource resource = new ByteArrayResource("content".getBytes());
        when(mockResourceLoader.getResource("classpath:events.xml")).thenReturn(resource);

        // Run the test
        final List<Event> result = notificationRuleServiceImpUnderTest.getEventXml();

        // Verify the results
    }

    @Test
    public void testFindNotificationByEvent() {
        // Setup
        // Configure NotificationRuleRepository.findNotificationByEvent(...).
        final NotificationRule notificationRule = new NotificationRule();
        notificationRule.setEventType("eventType");
        notificationRule.setNotificationMethod(NotificationMethod.PUSH);
        notificationRule.setNotificationReceiver("notificationReceiver");
        final User user = new User();
        user.setId(0L);
        final Role role = new Role();
        role.setId(0);
        role.setName("name");
        user.setRoles(new HashSet<>(Arrays.asList(role)));
        user.setSuperiors(new HashSet<>(Arrays.asList(new User())));
        notificationRule.setUser(user);
        final Role role1 = new Role();
        role1.setId(0);
        role1.setName("name");
        notificationRule.setRole(role1);
        final List<NotificationRule> notificationRules = Arrays.asList(notificationRule);
        when(mockNotificationRuleRepository.findNotificationByEvent("eventType")).thenReturn(notificationRules);

        when(mockRoleRepository.findOne(0)).thenReturn(new Role(0, "name"));

        // Configure UserRepository.findOne(...).
        final User user1 = new User();
        user1.setId(0L);
        final Role role2 = new Role();
        role2.setId(0);
        role2.setName("name");
        user1.setRoles(new HashSet<>(Arrays.asList(role2)));
        user1.setSuperiors(new HashSet<>(Arrays.asList(new User())));
        when(mockUserRepository.findOne(0L)).thenReturn(user1);

        // Run the test
        final List<NotificationRuleDto> result = notificationRuleServiceImpUnderTest.findNotificationByEvent(
                "eventType");

        // Verify the results
    }

    @Test
    public void testFindNotificationByEvent_NotificationRuleRepositoryReturnsNull() {
        // Setup
        when(mockNotificationRuleRepository.findNotificationByEvent("eventType")).thenReturn(null);

        // Run the test
        final List<NotificationRuleDto> result = notificationRuleServiceImpUnderTest.findNotificationByEvent(
                "eventType");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testFindNotificationByEvent_NotificationRuleRepositoryReturnsNoItems() {
        // Setup
        when(mockNotificationRuleRepository.findNotificationByEvent("eventType")).thenReturn(Collections.emptyList());

        // Run the test
        final List<NotificationRuleDto> result = notificationRuleServiceImpUnderTest.findNotificationByEvent(
                "eventType");

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetSuperiorsByRole() {
        // Setup
        // Configure UserRepository.findOne(...).
        final User user = new User();
        user.setId(0L);
        final Role role = new Role();
        role.setId(0);
        role.setName("name");
        user.setRoles(new HashSet<>(Arrays.asList(role)));
        user.setSuperiors(new HashSet<>(Arrays.asList(new User())));
        when(mockUserRepository.findOne(0L)).thenReturn(user);

        // Run the test
        final Set<User> result = notificationRuleServiceImpUnderTest.getSuperiorsByRole(0L, "roleName");

        // Verify the results
    }

    @Test
    public void testGetSuperiorsByRole_UserRepositoryReturnsNull() {
        // Setup
        when(mockUserRepository.findOne(0L)).thenReturn(null);

        // Run the test
//        final Set<User> result = notificationRuleServiceImpUnderTest.getSuperiorsByRole(0L, "roleName");

        // Verify the results
        //       assertThat(result).isEqualTo(Collections.emptySet());
    }
}
