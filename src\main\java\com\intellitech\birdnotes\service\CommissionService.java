package com.intellitech.birdnotes.service;

import java.text.ParseException;
import java.util.List;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.dto.CommissionDto;
import com.intellitech.birdnotes.model.dto.CommissionItemDto;
import com.intellitech.birdnotes.model.dto.DelegateCommissionDto;

public interface CommissionService {
	public Commission saveCommission(CommissionDto commissionDto)throws BirdnotesException;

	public List<CommissionDto> getCommissionByUserProductAndDate(
			CommissionDto commissionRequestDto) throws BirdnotesException, ParseException;

	void delete(Long id) throws BirdnotesException;

	public List<CommissionItemDto> getCommissionItem(
			CommissionDto commissionRequestDto);

	void deleteCommissionItem(Long id) throws BirdnotesException;

	List<DelegateCommissionDto> getDelegateCommissionByDate(CommissionDto commissionRequestDto)
			throws BirdnotesException, ParseException;

	void acceptValidationStep(long commissionValidationId) throws BirdnotesException;

	void refuseValidationStep(long commissionValidationId) throws BirdnotesException;

	
}
