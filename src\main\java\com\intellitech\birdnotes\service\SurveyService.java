package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.util.List;

import com.intellitech.birdnotes.data.dto.SurveyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Criteria;
import com.intellitech.birdnotes.model.Survey;
import com.intellitech.birdnotes.model.dto.SurveyDto;

public interface SurveyService {
	void saveSurvey(SurveyDto surveyDto) throws BirdnotesException;

	List<SurveyDto> getAllSurveys() throws BirdnotesException;

	SurveyFormData getAllDataSurvey(String criteriaFileName) throws IOException, BirdnotesException;

	List<Criteria> getCriteriaXml(String criteriaFileName) throws IOException;

}
