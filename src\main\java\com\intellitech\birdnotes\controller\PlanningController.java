package com.intellitech.birdnotes.controller;

import java.io.File;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.data.dto.PlanningsToShowRequestDto;
import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.OrdersPredictionResponse;
import com.intellitech.birdnotes.model.dto.PlanifiedProspectDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PlanningValidationDto;
import com.intellitech.birdnotes.model.dto.PostponePlanningRequest;
import com.intellitech.birdnotes.model.dto.PlannedActivityRequest;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.PlanningService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController

@RequestMapping("/planning")

public class PlanningController {

	private static final Logger LOG = LoggerFactory.getLogger(ProspectsAffectationController.class);

	@Autowired

	private PlanningService planningService;

	@Autowired
	UserService userService;

	@Autowired
	DelegateService delegateService;

	@Autowired

	CurrentUser currentUser;

	@RequestMapping(value = "getCommentClassification/{comment}", method = RequestMethod.GET)
	public ResponseEntity<String> getCommentClassification(@PathVariable("comment") String comment) {

		try {
			return new ResponseEntity<>(planningService.getCommentClassification(comment), HttpStatus.OK);

		} catch (Exception e) {

			LOG.error("Error in getOrderPrediction", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "getOrderPrediction/{date}/{prospectId}/{productId}/{first}/{row}", method = RequestMethod.GET)
	public ResponseEntity<OrdersPredictionResponse> getOrdersPredictions(@PathVariable("date") Date date,
			@PathVariable("prospectId") Long prospectId, @PathVariable("productId") Long productId, @PathVariable("first") int first, @PathVariable("row") int row ){

        try {
            OrdersPredictionResponse response = planningService.getOrdersPredictions(date, prospectId, productId, first, row);
            return ResponseEntity.ok(response);

		} catch (Exception e) {

			LOG.error("Error in getOrderPrediction", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "startOrdersPredictions/{date}/{prospectId}/{productId}", method = RequestMethod.GET)
	public ResponseEntity<OrdersPredictionResponse> startOrdersPredictions(@PathVariable("date") Date date,
			@PathVariable("prospectId") Long prospectId, @PathVariable("productId") Long productId) {

        try {
            planningService.processProspectsOrdersPrediction(date, prospectId, productId);
            OrdersPredictionResponse response = planningService.getOrdersPredictions(date, prospectId, productId, 0, 10);
            return ResponseEntity.ok(response);

		} catch (Exception e) {

			LOG.error("Error in startPrediction", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "deleteOrdersPredictions/{date}/{prospectId}/{productId}", method = RequestMethod.GET)
	public ResponseEntity<OrdersPredictionResponse> deleteOrdersPredictions(
			@PathVariable("date") Date date, @PathVariable("prospectId") Long prospectId,
			@PathVariable("productId") Long productId) {
        try {
        	
			planningService.deleteOrdersPredictions(date, prospectId, productId);
			return new ResponseEntity<>(planningService.getOrdersPredictions(date, prospectId, productId, 0, 10),
					HttpStatus.OK);


		} catch (Exception e) {

			LOG.error("Error in startPrediction", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "validation/{userId}/{date}", method = RequestMethod.GET)

	public ResponseEntity<PlanningValidationDto> findPlanningValidation(@PathVariable("userId") Long userId,
			@PathVariable("date") Date date) {

		try {

			if (userService.checkHasPermission("PLANNING_VIEW")) {

				List<PlanifiedProspectDto> planifiedProspectDto = planningService.findPlanificationByUserAndDate(userId,
						date, null);
				PlanningValidationDto planningValidationDto = planningService
						.findPlanningValidationByUserAndDate(userId, date);
				if (planningValidationDto != null) {
					planningValidationDto.setPlanifiedProspectDto(planifiedProspectDto);
				} else {
					planningValidationDto = new PlanningValidationDto();
				}

				return new ResponseEntity<>(planningValidationDto, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while getting prospects by delegate and date", e);

			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/findPlanningOfWeeksToShow", method = RequestMethod.POST)

	public ResponseEntity<List<Long>> findPlanningOfWeeksToShow(
			@RequestBody PlanningsToShowRequestDto planningsToShowRequest) {

		try {

			if (userService.checkHasPermission("PLANNING_VIEW")) {

				List<Long> planifiedIds = planningService.findPlanningOfWeeksToShow(planningsToShowRequest.getUserId(),
						planningsToShowRequest.getDate(), planningsToShowRequest.getWeekToShow());

				return new ResponseEntity<>(planifiedIds, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while getting prospects by delegate and date", e);

			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "/save", method = RequestMethod.PUT)

	public ResponseEntity<String> save(@RequestBody PlanningValidationDto planningValidationDto) {

		try {

			if (userService.checkHasPermission("ADD_PLANNING")) {

				planningService.save(planningValidationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/postPlanning", method = RequestMethod.PUT)

	public ResponseEntity<String> save(@RequestBody PostponePlanningRequest postponePlanningRequest) {

		try {

			if (userService.checkHasPermission("ADD_PLANNING")) {

				planningService.postponePlanning(postponePlanningRequest);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while postponing planning", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/savePlanningValidation", method = RequestMethod.PUT)

	public ResponseEntity<String> savePlanningValidation(@RequestBody PlanningValidationDto planningValidationDto) {

		try {

			if (userService.checkHasPermission("ADD_PLANNING")) {

				planningService.savePlanningValidation(planningValidationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/acceptValidationStep", method = RequestMethod.PUT)

	public ResponseEntity<String> acceptValidationStep(@RequestBody PlanningValidationDto planningValidationDto) {

		try {

			if (userService.checkHasPermission("PLANNING_VALIDATION")) {

				planningService.acceptValidationStep(planningValidationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (BirdnotesException e) {

			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);

			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/refuseValidationStep", method = RequestMethod.PUT)

	public ResponseEntity<String> refuseValidationStep(@RequestBody PlanningValidationDto planningValidationDto) {

		try {

			if (userService.checkHasPermission("PLANNING_VALIDATION")) {

				planningService.refuseValidationStep(planningValidationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (BirdnotesException e) {

			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);

			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/reviseValidationStep", method = RequestMethod.PUT)

	public ResponseEntity<String> reviseValidationStep(@RequestBody PlanningValidationDto planningValidationDto) {

		try {

			if (userService.checkHasPermission("PLANNING_VALIDATION")) {

				planningService.reviseValidationStep(planningValidationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (BirdnotesException e) {

			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);

			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "findDelegatesWaitingForValidationByWeek/{date}", method = RequestMethod.GET)

	public ResponseEntity<List<DelegateDto>> findDelegatesWaitingForValidationByWeek(@PathVariable("date") Date date) {

		try {

			if (userService.checkHasPermission("PLANNING_VIEW")) {

				// List<UserDto> userDtos =
				// planningService.findDelegatesWaitingForValidationByWeek(date);
				List<DelegateDto> userDtos = delegateService.findAllDelegates();

				return new ResponseEntity<>(userDtos, HttpStatus.OK);

			}

			else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("Error in getAllDelegates", e);

			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "getPlanningById/{id}", method = RequestMethod.GET)

	public ResponseEntity<List<PlanifiedProspectDto>> findPlanningById(@PathVariable("id") Long id) {

		try {

			if (userService.checkHasPermission("PLANNING_VIEW")) {

				List<PlanifiedProspectDto> planifiedProspects = planningService.findPlanningById(id);

				return new ResponseEntity<>(planifiedProspects, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting planning", e);

			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "delete/{id}", method = RequestMethod.DELETE)

	public ResponseEntity<List<PlanifiedProspectDto>> removePlanning(@PathVariable("id") Long id) {

		try {

			if (userService.checkHasPermission("DELETE_PLANNING")) {

				planningService.deletePlanning(id);

				return new ResponseEntity<>(null, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting planning", e);

			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "deletePlanningAndValidation/{userId}/{date}", method = RequestMethod.DELETE)

	public ResponseEntity<List<PlanifiedProspectDto>> deletePlanningValidation(@PathVariable("userId") Long userId,
			@PathVariable("date") Date date) {

		try {

			if (userService.checkHasPermission("DELETE_PLANNING")) {

				planningService.deletePlanningByDateAndUser(userId, date);

				return new ResponseEntity<>(null, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting planning", e);

			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "import/{userId}", method = RequestMethod.POST)

	public ResponseEntity<Map<String, List<PlanningDto>>> importPlanning(@RequestParam("file") MultipartFile file,
			@PathVariable("userId") Long userId) throws BirdnotesException {
		try {
			File tmpFile = File.createTempFile("import", file.getOriginalFilename());
			file.transferTo(tmpFile);
			if (userService.checkHasPermission("ADD_PLANNING")) {

				Map<String, List<PlanningDto>> planningList = planningService.importPlanning(tmpFile.getPath(), userId);

				return new ResponseEntity<>(planningList, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while importing planning", e);

			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/updatePlannedActivity", method = RequestMethod.PUT)
	public ResponseEntity<String> updatePlannedActivity(@RequestBody PlannedActivityRequest statusRequest) {
		try {
			planningService.updatePlannedActivity(statusRequest);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while update status", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

}
