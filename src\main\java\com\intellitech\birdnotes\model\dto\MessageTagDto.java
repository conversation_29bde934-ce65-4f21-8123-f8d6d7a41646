package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;


public class MessageTagDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long identifier;
	
	private Long visitId;
		
	private Long userId;

	public MessageTagDto() {
		super();
	}


	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	public Long getIdentifier() {
		return identifier;
	}


	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}


	public Long getVisitId() {
		return visitId;
	}


	public void setVisitId(Long visitId) {
		this.visitId = visitId;
	}


	public Long getUserId() {
		return userId;
	}


	public void setUserId(Long userId) {
		this.userId = userId;
	}




	
}
