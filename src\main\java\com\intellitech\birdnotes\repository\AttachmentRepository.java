package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Attachment;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;

public interface AttachmentRepository extends JpaRepository<Attachment, Long> {

	PurchaseOrder findById(Long id);

	
	@Modifying
	@Query("UPDATE Attachment set attachmentName =:nameAttachment, attachmentBase64=:attachmentBase64 WHERE id=:id")
	void update(@Param("nameAttachment") String nameAttachment, @Param("attachmentBase64") String attachmentBase64, @Param("id") Long id);

	@Modifying
	@Query("DELETE Attachment a WHERE a.id in (:attachmentIds)")
	void deleteById(@Param("attachmentIds") List<Long> attachmentIds);

	@Query("SELECT a FROM Attachment a WHERE a.identifier = ?1 And a.delegate.id = ?2")
	Attachment findByIdentifier(Long identifier, Long userId);
	
	@Modifying
	@Query("Delete FROM Attachment a WHERE a.identifier = ?1 And a.delegate.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);
	
	@Query("SELECT a from Attachment a WHERE a.delegate.id  = :userId ")
	List<Attachment> findByUser(@Param("userId") Long userId);
	
	@Modifying
	@Query("DELETE PurchaseOrder po WHERE po.visit.id = ?1")
	void deleteByVisitId(Long visitId);
	
	/*@Override
	@Query("Select n from NoteFrais n ")
	List<NoteFrais> findAll();
	
	@Query("SELECT n from NoteFrais n WHERE n.typeNoteFrais.id=:id ")
	List<NoteFrais> findByTypeNoteFraisId(@Param("id") Long id);

	@Modifying
	@Query("DELETE NoteFrais n WHERE n.id = ?1")
	void deleteById(Long idNoteFrais);

	@Query("SELECT t.id from NoteFrais t WHERE t.id in (:noteFrais) ")
	List<Long> findWhereIdIn(@Param("noteFrais") List<Long> noteFrais);
	
	@Query("SELECT t from NoteFrais t WHERE t.id in (:noteFrais) and t.delegate.id  = :userId ")
	List<NoteFrais> findWhereIdInByUser(@Param("noteFrais") List<Long> noteFrais,@Param("userId") Long userId);
*/
}
