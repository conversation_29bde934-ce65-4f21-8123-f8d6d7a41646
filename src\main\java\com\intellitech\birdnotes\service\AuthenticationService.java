package com.intellitech.birdnotes.service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.UserTransferDto;
import com.intellitech.birdnotes.model.request.AuthenticationRequest;

public interface AuthenticationService {

	UserTransferDto authenticate(AuthenticationRequest authenticationRequest);
	
	boolean checkTokenIsExpired (String authToken) throws BirdnotesException;

	String getLogo();

}
