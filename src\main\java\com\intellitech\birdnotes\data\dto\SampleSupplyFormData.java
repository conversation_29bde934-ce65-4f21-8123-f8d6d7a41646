package com.intellitech.birdnotes.data.dto;

import java.util.List;

import com.intellitech.birdnotes.model.dto.SampleSupplyItemDto;
import com.intellitech.birdnotes.model.dto.UserDto;

public class SampleSupplyFormData {
	private List<UserDto> delegates;
	private List<SampleSupplyItemDto> sampleSupplyItems;

	public List<UserDto> getDelegates() {
		return delegates;
	}

	public void setDelegates(List<UserDto> delegates) {
		this.delegates = delegates;
	}

	public List<SampleSupplyItemDto> getSampleSupplyItems() {
		return sampleSupplyItems;
	}

	public void setSampleSupplyItems(List<SampleSupplyItemDto> sampleSupplyItems) {
		this.sampleSupplyItems = sampleSupplyItems;
	}

}
