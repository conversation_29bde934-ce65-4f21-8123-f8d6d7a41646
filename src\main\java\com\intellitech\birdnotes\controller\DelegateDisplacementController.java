package com.intellitech.birdnotes.controller;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import com.intellitech.birdnotes.model.DelegateDisplacement;
import com.intellitech.birdnotes.service.DelegateDisplacementService;

import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/delegateDisplacements")
public class DelegateDisplacementController {
    private final DelegateDisplacementService service;

    @Autowired
    public DelegateDisplacementController(DelegateDisplacementService service) {
        this.service = service;
    }

    @RequestMapping(value = "/search", method = RequestMethod.GET)
    public List<DelegateDisplacement> search(
            @RequestParam Long delegateId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        return service.findByDelegateAndDateRange(delegateId, startDate, endDate);
    }
}
