package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("specialityService")
@Transactional
public class SpecialityServiceImpl implements SpecialityService {

	private SpecialityRepository specialityRepository;
	private ConvertSpecialityToDto convertSpecialityToDto;
	
	@Autowired
	private StorageService storageService;
	
	@Autowired
	UserService userService;

	
	@Value("${uploadPath}")
	private String uploadPath;
	
	@Value("${specialityPath}")
	private String specialityPath;
	
	

	@Autowired
	SpecialityServiceImpl(SpecialityRepository specialityRepository, ConvertSpecialityToDto convertSpecialityToDto) {
		super();
		this.specialityRepository = specialityRepository;
		this.convertSpecialityToDto = convertSpecialityToDto;
	}

	@Override
	public void addAll(List<SpecialityRequestDto> specialityDtos) throws BirdnotesException {
		for (SpecialityRequestDto specialityDto : specialityDtos) {
			Speciality speciality = new Speciality();
			speciality.setName(specialityDto.getName());
			specialityRepository.save(speciality);
			
		}

	}

	@Override
	public List<SpecialityDto> findAll() throws BirdnotesException {
		List<SpecialityDto> back = new ArrayList<>();
		List<Speciality> allSpecialities = specialityRepository.findAll();
		for (Speciality speciality : allSpecialities) {
			back.add(convertSpecialityToDto.convert(speciality));
		}
		return back;
	}

	@Override
	public Speciality add(SpecialityRequestDto specialityRequestDto, MultipartFile file) throws BirdnotesException {

		Speciality speciality = new Speciality();
		
		if (specialityRequestDto.getName() == null || "".equals(specialityRequestDto.getName())) {
			throw new BirdnotesException(Exceptions.EMPTY_SPECIALITY);
		}
		Speciality result = specialityRepository.findFirstByNameIgnoreCase(specialityRequestDto.getName());
		if (result!=null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}

		speciality.setName(specialityRequestDto.getName());
		speciality.setAction(specialityRequestDto.getAction());
		
		if(file != null) {
			speciality.setIcon(file.getOriginalFilename());
		}
		Speciality specialitySaved = specialityRepository.save(speciality);
		if(file != null) {
			MultipartFile[] files = {file};
			storageService.storeFiles(files , uploadPath + specialityPath + "/" + specialitySaved.getId());	
		}

		return specialitySaved;
	}

	@Override
	public List<SpecialityDto> saveAll(List<SpecialityDto> specialityDtos) throws BirdnotesException {
		if (specialityDtos == null || specialityDtos.isEmpty()) {
			throw new BirdnotesException(Exceptions.EMPTY_OR_NULL_SPECIALITIES);
		}
		for (SpecialityDto specialityDto : specialityDtos) {
			Speciality speciality = new Speciality();
			speciality.setName(specialityDto.getName());
			specialityRepository.save(speciality);
		}
		return specialityDtos;
	}

	@Override
	public void delete(Long id) throws BirdnotesException {
		specialityRepository.deleteById(id);
	}

	@Override
	public Speciality saveSpeciality(SpecialityDto specialityDto,  MultipartFile file) throws BirdnotesException {
		if (specialityDto.getId() == null || specialityDto == null) {
			throw new RuntimeException("specialityDto est null");
		}

		if (specialityDto.getName() == null || specialityDto.getName().isEmpty()) {
			throw new BirdnotesException("nom du speciality est vide");
		}
		
		Speciality result = specialityRepository.findByNameWithDiffId(specialityDto.getName(), specialityDto.getId());
		if( result != null) {
			throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
		}
		
		Speciality speciality = null;
		if(specialityDto.getId() != null) {
			speciality = specialityRepository.findOne(specialityDto.getId());
		}
		if (speciality == null) {
			speciality = new Speciality();
		}

		speciality.setName(specialityDto.getName());
		speciality.setAction(specialityDto.getAction());
		if(file != null) {
			speciality.setIcon(file.getOriginalFilename());
		}
		if(file != null) {
			MultipartFile[] files = {file};
			storageService.deleteFolderContent(uploadPath + specialityPath + "/" + specialityDto.getId());
			storageService.storeFiles(files , uploadPath + specialityPath + "/" + specialityDto.getId());
		}
		return specialityRepository.save(speciality);
	}


	@Override
	public SpecialityDto findSpecialityByName(String name) throws BirdnotesException {
		Speciality speciality = specialityRepository.findByName(name);
		SpecialityDto specialityDto = convertSpecialityToDto.convert(speciality);
		return specialityDto;
	}

	@Override
	public SpecialityDto findSpecialityDto(String specialityName,List<SpecialityDto>specialityDtos)  {
		for (SpecialityDto specialityDto : specialityDtos) {
			if (specialityDto.getName().equalsIgnoreCase(specialityName)) {
				return specialityDto;
			}
		}
		return null;
		
	}

}
