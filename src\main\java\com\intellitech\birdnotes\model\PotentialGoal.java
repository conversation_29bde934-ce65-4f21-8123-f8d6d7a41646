package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.POTENTIAL_GOAL, schema = Common.PUBLIC_SCHEMA)
public class PotentialGoal implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.POTENTIAL_GOAL_SEQUENCE, sequenceName = Sequences.POTENTIAL_GOAL_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.POTENTIAL_GOAL_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	
	@ManyToOne(optional=false)
	@JoinColumn(name = BirdnotesConstants.Columns.POTENTIAL_ID)
	private Potential potential;
	
	@Column(name = Columns.TARGET)
	private Float target;
	
	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.DELEGATE_POTENTIAL_GOAL, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.POTENTIAL_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.USER_ID, nullable = false, updatable = false) })
	private Set<User> delegates;
	
	public PotentialGoal() {
		super();	
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Potential getPotential() {
		return potential;
	}

	public void setPotential(Potential potential) {
		this.potential = potential;
	}

	public Float getTarget() {
		return target;
	}

	public void setTarget(Float target) {
		this.target = target;
	}

	public Set<User> getDelegates() {
		return delegates;
	}

	public void setDelegates(Set<User> delegates) {
		this.delegates = delegates;
	}

	
	
}