package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.OpportunityNote;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.FileNamePath;
import com.intellitech.birdnotes.model.dto.OpportunityNoteDto;
import com.intellitech.birdnotes.model.dto.OpportunityNoteResponseDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.request.ActionMarquetingRequest;
import com.intellitech.birdnotes.repository.OpportunityNoteRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.OpportunityNoteService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

@Service("opportunityNoteService")
@Transactional
public class OpportunityNoteServiceImpl implements OpportunityNoteService {


	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${uploadPath}")
	private String uploadPath;
	@Value("${opportunityNotePath}")
	private String opportunityNotePath;
	
	
	@Autowired
	private OpportunityNoteRepository opportunityNoteRepository;
	
	private NotificationService notificationService;
	
	private NotificationMessageBuilder notificationMessageBuilder;

	private CurrentUser currentUser;
	
	private ConfigurationService configurationService;
	
	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}
	@Autowired
	private UserService userService;
	
	
	@Autowired
	public void setNotificationService(NotificationService notificationService) {
		this.notificationService = notificationService;
	}
	@Autowired
	public void setNotificationMessageBuilder(NotificationMessageBuilder notificationMessageBuilder) {
		this.notificationMessageBuilder = notificationMessageBuilder;
	}
	
    
	@Autowired
	private MessageSource messageSource;

	@Override
	public List<OpportunityNoteDto> findAll() throws BirdnotesException {
		List<OpportunityNoteDto> result = new ArrayList<>();
		List<OpportunityNote> allOpportunityNotes = opportunityNoteRepository.findAll();
		for (OpportunityNote opportunityNote : allOpportunityNotes) {
			OpportunityNoteDto opportunityNoteDto = new OpportunityNoteDto();
			opportunityNoteDto.setId(opportunityNote.getId());
			opportunityNoteDto.setName(opportunityNote.getName());
			opportunityNoteDto.setBudget(opportunityNote.getBudget());
			opportunityNoteDto.setDate(opportunityNote.getDate());
			opportunityNoteDto.setStatus(opportunityNote.getStatus().toString());
			opportunityNoteDto.setDescription(opportunityNote.getDescription());
			opportunityNoteDto.setAttachmentBase64(opportunityNote.getAttachmentBase64());
			opportunityNoteDto.setNameAttachment(opportunityNote.getPieceJointe());
			String path = uploadPath +opportunityNotePath+ File.separator + opportunityNote.getId();
			File filePath = new File(path);
			opportunityNoteDto.setPieceJointe(path);
			String[] file = filePath.list();
			if(file != null) {
				List<FileNamePath> listFichiers = new ArrayList<>();
				for (int i = 0; i < file.length; i++) {
					File fileOpportunityNote = new File(file[i]);
					FileNamePath fileNamePath = new FileNamePath(fileOpportunityNote.getName(),
							uploadUrl+opportunityNotePath+ File.separator + opportunityNote.getId() + File.separator + fileOpportunityNote.getName());
					listFichiers.add(fileNamePath);
					opportunityNoteDto.setNameFile(listFichiers);
				}
			}
			
			//String firstName = opportunityNote.getUser().getFirstName();
			//String lastName = opportunityNote.getUser().getLastName();
			//String userName = firstName + " " + lastName;
			//opportunityNoteDto.setUserName(userName);

			Set<ProspectDto> prospects = new HashSet<>();

			if (opportunityNote.getProspects() != null && !(opportunityNote.getProspects().isEmpty())) {
				for (Prospect prospect : opportunityNote.getProspects()) {
					ProspectDto prospectDto = new ProspectDto();
					prospectDto.setId(prospect.getId());
					prospectDto.setFirstName(prospect.getFirstName());
					prospectDto.setLastName(prospect.getLastName());
					prospects.add(prospectDto);
				}
			}
			opportunityNoteDto.setProspects(prospects);	
		//
			Set<ProspectDto> pharmacies = new HashSet<>();
			if (opportunityNote.getPharmacies() != null && !(opportunityNote.getPharmacies().isEmpty())) {
				for (Prospect pharmacie : opportunityNote.getPharmacies()) {
					ProspectDto pharmacieDto = new ProspectDto();
					pharmacieDto.setId(pharmacie.getId());
					pharmacieDto.setFirstName(pharmacie.getFirstName());
					pharmacieDto.setLastName(pharmacie.getLastName());
					pharmacies.add(pharmacieDto);
				}
			}
			opportunityNoteDto.setPharmacies(pharmacies);
			//		
			Set<ProductDto> products = new HashSet<>();
			if (opportunityNote.getProducts() != null && !(opportunityNote.getProducts().isEmpty())) {
				for (Product product : opportunityNote.getProducts()) {
					ProductDto productDto = new ProductDto();
					productDto.setId(product.getId());
					productDto.setName(product.getName());
					products.add(productDto);
				}
			}
			opportunityNoteDto.setProducts(products);

			result.add(opportunityNoteDto);
		}
		return result;
	}

	@Override
	public List<OpportunityNoteResponseDto> findOpportunityNoteByUser(Long userId) {
		
		List<OpportunityNoteResponseDto> opportunityNoteValidationDtos = new ArrayList<>();
		
		List<OpportunityNote> opportunityNotes = opportunityNoteRepository
				.findOpportunityNoteByUser(userId);
		
		if (opportunityNotes != null && !opportunityNotes.isEmpty()) {
			for (OpportunityNote opportunityNote : opportunityNotes) {
				OpportunityNoteResponseDto opportunityNoteDto = new OpportunityNoteResponseDto();
				opportunityNoteDto.setStatus(opportunityNote.getStatus().name());
				opportunityNoteDto.setName(opportunityNote.getName());
				opportunityNoteDto.setId(opportunityNote.getId());
				opportunityNoteDto.setDescription(opportunityNote.getDescription());
				opportunityNoteValidationDtos.add(opportunityNoteDto);
			}
		}

		return opportunityNoteValidationDtos;

	}

	@Override
	public void delete(Long id) throws BirdnotesException{
		opportunityNoteRepository.deleteById(id);
	}

	@Override
	public void update(OpportunityNoteDto opportunityNoteDto) throws BirdnotesException {

		if (opportunityNoteDto.getId() == null || opportunityNoteDto == null) {
			throw new RuntimeException("opportunityNoteDto est null");
		}

		if (opportunityNoteDto.getName() == null || opportunityNoteDto.getName().isEmpty()) {
			throw new BirdnotesException("nom de l'opportunite note  est vide");
		}

		if (opportunityNoteDto.getBudget() == null) {
			throw new BirdnotesException("Budget de l'opportunity note est vide");
		}

		OpportunityNote opportunityNoteToUpadte = opportunityNoteRepository.findOne(opportunityNoteDto.getId());
		if (opportunityNoteToUpadte == null) {
			throw new BirdnotesException(Exceptions.OPPORTUNITYNOTE_TO_UPDATE_ALREADY_DELETED);
			
		}

		opportunityNoteToUpadte.setId(opportunityNoteDto.getId());
		opportunityNoteToUpadte.setName(opportunityNoteDto.getName());
		opportunityNoteToUpadte.setBudget(opportunityNoteDto.getBudget());
		opportunityNoteToUpadte.setDescription(opportunityNoteDto.getDescription());

		opportunityNoteRepository.save(opportunityNoteToUpadte);

	}

	@Override
	public void changeStatus(ActionMarquetingRequest opportunityNoteStatusRequest) {

		OpportunityNote am = opportunityNoteRepository.findById(opportunityNoteStatusRequest.getId());

		if (UserValidationStatus.ACCEPTED.equals(opportunityNoteStatusRequest.getStatus())) {
			am.setStatus(UserValidationStatus.ACCEPTED);
			notificationMessageBuilder.setMessageType("opportunityNoteValidationNotificationMessage");
			//notificationMessageBuilder.setUser(currentUser.getBirdnotesUser().getUserDto());
			notificationMessageBuilder.setTagetUser(am.getUser());
			Notification notification = notificationMessageBuilder.Build();
			notificationService.generateUsingAllNotificationMethods(notification);

		} else if (UserValidationStatus.REFUSED.equals(opportunityNoteStatusRequest.getStatus())) {
			am.setStatus(UserValidationStatus.REFUSED);
			notificationMessageBuilder.setMessageType("opportunitynoteRefusNotificationMessage");
			//notificationMessageBuilder.setUser(currentUser.getBirdnotesUser().getUserDto());
			notificationMessageBuilder.setTagetUser(am.getUser());
			Notification notification = notificationMessageBuilder.Build();
			notificationService.generateUsingAllNotificationMethods(notification);

		} else {
			am.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);

		}
		opportunityNoteRepository.save(am);
		
	}

}
