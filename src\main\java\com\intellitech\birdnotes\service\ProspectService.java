package com.intellitech.birdnotes.service;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.intellitech.birdnotes.data.dto.InvestigationDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.ExportProspects;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.PatientDto;
import com.intellitech.birdnotes.model.dto.ProspectChangedDto;
import com.intellitech.birdnotes.model.dto.ProspectDistributionList;
import com.intellitech.birdnotes.model.dto.ProspectDistributionRequest;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectsToMoveRequestDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.model.request.ProspectListRequest;

public interface ProspectService {

	List<ProspectDto> findAll(ProspectListRequest prospectListRequest) throws BirdnotesException;

	Prospect add(ProspectDto prospectDto) throws BirdnotesException;

	void addAllImportedProspects(List<ProspectDto> prospectDtos) throws BirdnotesException;

	// List<Prospect> saveAll(List<ProspectDto> prospectDtos) throws
	// BirdnotesException;

	boolean delete(Long idProspect) throws BirdnotesException;

	void updateLocality(ProspectsToMoveRequestDto prospectsToMoveRequestDto) throws BirdnotesException;

	Prospect validateAndConvertProspect(ProspectDto prospectDto, Prospect prospectToUpdate) throws BirdnotesException;

	Prospect updateProspect(ProspectDto prospectDto) throws BirdnotesException;

	void acceptAddingNewProspectRequest(ProspectDto prospectDto) throws BirdnotesException;

	// List<ProspectDto> findProspectBySectorId(Long sectorId) throws
	// BirdnotesException;

	boolean sendInvestigation(InvestigationDto investigationDto) throws BirdnotesException;

	// ProspectDto findById(Long id) throws BirdnotesException;

	List<ProspectChangedDto> getUpdateRequestDifference(Long id) throws BirdnotesException;

	List<ProspectDto> findProspectWithStatus() throws BirdnotesException;

	void acceptProspectUpdateRequest(ProspectDto prospectDto) throws BirdnotesException;

	// List<ProspectDto> findAllPharmacists() throws BirdnotesException;

	// void validateProspect(Prospect prospect) throws BirdnotesException;

	boolean refuseProspect(Long idProspect) throws BirdnotesException;

	List<ProspectDto> findSimilarProspects(ProspectDto prospectDto) throws BirdnotesException;

	void addImportedProspect(ProspectDto prospectDto) throws BirdnotesException;

	ProspectDistributionList getProspectsDistribution(ProspectDistributionRequest prospectDistributionRequest)
			throws BirdnotesException;

	HashMap<String, Object> saveProspectsWhichBecameValid(List<ProspectDto> invalidProspects) throws BirdnotesException;

	boolean validateName(String name);

	boolean validateAddress(String address);

	boolean validatePhone(String phone);

	boolean validateEmail(String email);

	File exportProspects(ExportProspects exportProspects) throws IOException;

	void mergeProspect(ProspectDto prospectDto, ProspectDto prospectToMerge) throws BirdnotesException;

	void mergeProspectForImport(ProspectDto prospectToMergeDto, ProspectDto newProspectDto) throws BirdnotesException;

	List<ProspectDto> getNotVisitedProspect(Long userId, Date startDate, Date endDate) throws BirdnotesException;

	List<ProspectDto> getVisitedProspect(Long userId, Date startDate, Date endDate) throws BirdnotesException;

	List<LabelValueDto> findByName(String name, Long prospectId);

	void refuseValidationStep(long id) throws BirdnotesException;

	void acceptProspectChangeRequestStep(Long validationStatusId, ProspectDto prospectDto) throws BirdnotesException;

	void acceptAddingNewProspectRequestStep(Long validationStatusId, ProspectDto prospectDto) throws BirdnotesException;

	public ProspectDto findProspectById(Long id) throws BirdnotesException;

	List<ProspectDto> getNewAndUpdated(Long userId) throws BirdnotesException;

	Map<String, Object> getProspectUpdateAndNewRequests() throws BirdnotesException;

	Prospect getProspect(Long prospectId);

	List<ProspectDto> findAllPatients(ProspectDistributionRequest patientDistributionRequest) throws BirdnotesException;

	void deletePatient(Long idPatient);

	Prospect savePatient(PatientDto patientDto) throws BirdnotesException;

	List<WholesalerDto> getWholesalersByStatus(Boolean forMobile);
    List<Map<String, Object>> getAllProspectsIdAndFullName(); 


}
