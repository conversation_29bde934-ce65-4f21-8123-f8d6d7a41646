# Exceptions
		 EXCEPTION_MESSAGE = "Cette entit\u00e9 ne peut pas etre supprim\u00e9 car elle est li\u00e9 \u00e0 la table {0}"
		 LIST_OF_NOTE_FRAIS_IS_NULL = La liste des notes frais est null ou empty
		 TOKEN_IS_EMPTY = Token is empty
		 NO_USER_WITH_ID = no user with id =
		 INVALID_CSV_FILE_FORMAT = Format du fichier CSV est inattendu
		 PROSPECT_DATA_NOT_FOUND_IN_CSV_FILE = L\u2019importation a \u00e9t\u00e9 interrompue. Une erreur est servenue \u00e0 cause du manque des donn\u00e9es du prospect \u00e0 la ligne 
		 USER_DTO_IS_NULL = userDto est null
		 POTENTIAL_DTO_IS_NULL = potentialDto est null
		 POTENTIAL_NAME_IS_EMPTY = potential name is empty
		 TYPE_NOTE_FRAIS_NAME_IS_EMPTY = Le nom du type note frais est vide
		 ACTIVITYTYPE_NAME_IS_EMPTY = Le nom de l\u2019activit\u00e9 est vide
		 SESSION_EXPIRE = Votre session est expir\u00e9
		 TYPE_IS_NULL = typeNoteFrais est obligatoire
		 NOTE_FRAIS_COULD_NOT_DELETED = Impossible d\u2019effacer la note frais
		 ACTIVITY_COULD_NOT_DELETED = Impossible d\u2019effacer activit\u00e9
		 PlANNING_VALIDATION_COULD_NOT_DELETED = Impossible d\u2019effacer planning validation
		 RECOVERY_COULD_NOT_DELETED = Impossible d\u2019effacer le recouvrement
		 PURCHASE_ORDER_COULD_NOT_DELETED = Impossible d\u2019effacer le purchase order
		 ACTIONMARKETING_COULD_NOT_DELETED = Impossible d\u2019effacer l\u2019action marketing
				 

		 PLANNING_COULD_NOT_DELETED = Impossible d\u2019effacer la planification
		 TYPE_NOTE_FRAIS_COULD_NOT_DELETED = Impossible d\u2019effacer le type de note frais
		 NOTE_FRAIS_DTO_ID_NULL = noteFraisDto est null
		 ACTIVITY_DTO_ID_NULL = activityDto est null
		 ALREADY_EXIST = Le nom existe d\u00e9j\u00e0
		 SECTOR_TO_UPDATE_ALREADY_DELETED = Le secteur que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 SECTOR_TO_DELETE_ALREADY_DELETED = Le secteur ne peut pas \u00eatre supprim\u00e9
		 GADGET_TO_UPDATE_ALREADY_DELETED = Le gadget que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 GADGET_TO_DELETE_ALREADY_DELETED = Ce cadeau ne peut pas \u00eatre supprim\u00e9, il est attach\u00e9 \u00e0 des visites
		 ACTIONMARKETING_TO_DELETE_ALREADY_DELETED = L\u2019action marketing ne peut pas etre supprim\u00e9
		 OPPORTUNITYNOTE_TO_DELETE_ALREADY_DELETED = la note d\u2019opportunit\u00e9 ne peut pas etre supprim\u00e9
		 ACTIONMARKETING_TO_UPDATE_ALREADY_DELETED = L\u2019action marketing que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 OPPORTUNITYNOTE_TO_UPDATE_ALREADY_DELETED = L\u2019OPPORTUNITY NOTE  que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9

		 NOTEFRAIS_TO_UPDATE_ALREADY_DELETED = La note frais que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9e
		 ACTIVIYUTO_UPDATE_ALREADY_DELETED = L\u2019ativit\u00e9 que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9e
		 GONFIGURATION_TO_UPDATE_ALREDY_DELETED = Le configuration que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9e

		 OK = OK
		 KO = KO
		 LOCALITY_TO_DELETE_ALREADY_DELETED = La localit\u00e9 ne peut pas \u00eatre supprim\u00e9e, elle est attach\u00e9e \u00e0 un prospect
		 PROSPECT_TYPE_TO_DELETE_ALREADY_DELETED = Le type de prospect ne peut pas \u00eatre supprim\u00e9, il est attach\u00e9e \u00e0 un prospect
		 POTENTIAL_TO_DELETE_ALREADY_DELETED = Le potentiel ne peut pas \u00eatre supprim\u00e9, il est attach\u00e9e \u00e0 un prospect
		 SPECIALITY_TO_DELETE_ATTACHED_PROSPECT = La specialit\u00e9 ne peut pas \u00eatre supprim\u00e9e, elle est attach\u00e9e \u00e0 un prospect
		 ESTABLISHMENT_TO_DELETE_ALREADY_DELETED = Etablissement ne peut pas \u00eatre supprim\u00e9, il est attach\u00e9e \u00e0 un prospect
		 TYPE_NOTE_FRAIS_TO_DELETE_ATTACHED_NOTE_FRAIS = Ce type ne peut pas \u00eatre supprim\u00e9, il est attach\u00e9 \u00e0 une note frais
		 EMAIL_ALREADY_EXIST = Adresse email existe d\u00e9ja
		 PRODUCT_NAME_ALREADY_EXIST = Le nom du produit existe d\u00e9ja
		 WHOLESALER_NAME_ALREADY_EXIST = Le nom du grossite existe d\u00e9ja
		 NETWORK_NAME_ALREADY_EXIST = Le nom du r\u00e9seau existe d\u00e9ja
		 PHONE_ALREADY_EXIST = T\u00e9l\u00e9phone existe d\u00e9ja
		 USERNAME_ALREADY_EXIST = Identifiant existe d\u00e9ja
		 PROSPECT_TO_UPDATE_ALREADY_DELETED = Le prospect que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 GSM_ALREADY_EXIST = GSM existe d\u00e9j\u00e0
		 NAME_ALREADY_EXIST = Le nom existe d\u00e9ja
		 EMAIL_ALREADY_EXIST = Le mail existe d\u00e9ja
		 USERNAME_ALREADY_EXIST= Le nom existe d\u00e9ja
		 PHONE_ALREADY_EXIST= Le numero existe d\u00e9ja

		 GAMME_TO_DELETE_ALREADY_DELETED = La gamme ne peut pas etre supprim\u00e9
		 SPECIALITY_TO_DELETE_ALREADY_DELETED = La sp\u00e9cialit\u00e9 ne peut pas etre supprim\u00e9
		 ROLE_TO_DELETE_ALREADY_DELETED = Le role ne peut pas \u00eatre supprim\u00e9

		 LOCALITY_TO_UPDATE_ALREADY_DELETED = La localit\u00e9 que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9e
		 CANNOT_DELETE_PROSPECT = Le prospect s\u00e9lectionn\u00e9 a des visits, donc ne peut pas \u00eatre supprim\u00e9
		 CANNOT_DELETE_DELEGATE = Le d\u00e9l\u00e9g\u00e9 ne peut pas \u00eatre supprim\u00e9 car il existe des secteurs qui sont li\u00e9s \u00e0 ce d\u00e9l\u00e9g\u00e9
		 CANNOT_DELETE_PRODUCT = Le produit ne peut pas \u00eatre supprim\u00e9 car il existe des visites associ\u00e9s a ce produit
		 CANNOT_DELETE_GAMME = La gamme est d\u00e9ja utilis\u00e9e et ne peut pas \u00eatre supprim\u00e9e 
		 CANNOT_DELETE_WHOLESALER = Le grossiste est d\u00e9ja utilis\u00e9 et ne peut pas \u00eatre supprim\u00e9 
		 POTENTIAL_TO_UPDATE_ALREADY_DELETED = Le Potential que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 EMPTY_PROSPECTSAFFECTATION_USER = Affectation user is empty
		 EMPTY_PLANNING_VALIDATION = Planning validation is empty
		 EMPTY_PRODUCT_POTENTIAL = Product potential is empty
		 EMPTY_PROSPECTSAFFECTATION_PROSPECT = Affectation  prospect is empty
		 EMPTY_SECTOR = Sector name is empty
		 EMPTY_SECTOR_USER = Sector user is empty
		 USER_ALREADY_DELETED = Utilisateur d\u00e9j\u00e0 supprim\u00e9
		 PROSPECT_ALREADY_DELETED = prospect d\u00e9j\u00e0 supprim\u00e9
		 AFFECTATION_ALREADY_DELETED = Affectation d\u00e9j\u00e0 supprim\u00e9e
		 PRODUCT_ALREADY_DELETED = produit d\u00e9j\u00e0 supprim\u00e9
		 SECTOR_LINKED_TO_PROSPECT = Le secteur ne peut pas \u00eatre supprim\u00e9 car il existe des prospects li\u00e9s \u00e0 ce secteur
		 SECTOR_LINKED_TO_LOCALITY = Le secteur ne peut pas \u00eatre supprim\u00e9 car il existe des localit\u00e9s qui sont li\u00e9s \u00e0 ce secteur
		 SECTOR_DTO_NULL = sectorDto is null
		 CHOOSE_DELEGATE = Choisir un d\u00e9l\u00e9gu\u00e9
		 EMPTY_LOCALITY_NAME = Locality name is empty
		 EMPTY_SECTOR_ID = Sector id is empty
		 SECTOR_ALREADY_DELETED = secteur d\u00e9j\u00e0 supprim\u00e9
		 LOCALITY_DTO_NULL = localityDto is null
		 EMPTY_NAME = nom est vide!
		 SECTOR_DELETED = Le secteur est supprim\u00e9
		 EMPTY_OR_NULL_SPECIALITIES = Specialities is null or empty
		 EMPTY_SPECIALITY = Speciality name is empty
		 NULL_PRODUCT = product is null
		 NULL_WHOLESALER = wholesaler is null
		 NULL_DTO_PRODUCT = productDto is null
		 NULL_DTO_WHOLESALER = wholesalerDto is null
		 NULL_DTO_PRODUCT_POTENTIAL = productsPotentialDto is null
		 NULL_CHARGE_PLAN = chargePlan is null
		 NULL_NOTIFICATION = notifications is null
		 NO_PRODUCT_WITH_ID = no product with id = 
		 NO_WHOLESALER_WITH_ID = no wholesaler with id = 
		 VALUE_OF_CYCLE_NULL = Cycle value is null
		 NULL_AFFECTATION = Affectattion is null
		 NULL_SPECIALTY = Specialty dto is null
		 NULL_SECTOR = Sector dto is null
		 NULL_LOCALITY = Locality dto is null
		 NULL_TYPE = Prospect type dto is null
		 GAMME_TO_UPDATE_ALREADY_DELETED = La Gamme que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 SPECIALITY_TO_UPDATE_ALREADY_DELETED = La sp\u00e9cialit\u00e9 que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 EMPTY_ACTIONMARKETING_VALIDATION = empty_actionmarketing_validation
		 NULL_POTENTIAL = potential dto is null
		 NULL_GOALS = goals request dto is null
		 ROLE_NAME_IS_EMPTY = le nom du role est obligatoire
		 ROLE_DTO_IS_NULL = RoleDto is null
		 ROLE_TO_UPDATE_ALREADY_DELETED = le role que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 GOAL_TO_UPDATE_ALREADY_DELETED = L\u2019objectif que vous voulez modifier est d\u00e9j\u00e0 supprim\u00e9
		 GOAL_NAME_IS_EMPTY = le nom de l\u2019objectif est obligatoire
		 GOAL_DTO_IS_NULL = GoalDto is null
		 GOAL_DELEGATE_IS_EMPTY = D\u00e9l\u00e9gu\u00e9 est obligatoire
		 GOAL_ITEM_IS_EMPTY = Les objectifs sont obligatoires
		 PRODUCT_PURCHASE_ORDER_TEMPLATE_ITEM_IS_EMPTY = les produits sont obligatoires
		 ROLE_NAME_IS_NUMBER =  le nom du role est une  chaine de caract\u00e9res
		 GOAL_NAME_IS_NUMBER = le nom est un nombre 
		 VALIDATION_TYPE_IS_EMPTY = le type de validation est obligatoire
		 VALIDATION_STEPS_IS_EMPTY = les \u00e9tapes de validation sont obligatoires
		 EVENT_IS_EMPTY = les \u00e9venements sont obligatoires
		 NOTIFICATIONS_IS_EMPTY = les notifications sont obligatoires
		 VALIDATION_STATUS_IS_EMPTY = les \u00e9tapes de validation sont obligatoires
		 NUMBER_OF_VALIDATORS_IS_EMPTY = le nombre de validateurs est obligatoire
		 VALIDATION_INPROGRESS = Vous ne pouvez pas modifier le workflow car il dispose encore de validations en cours
		 USED_EXPENSE_TYPE = Le type que vous voulez supprimer est r\u00e9ferenc\u00e9 par des notes de frais
		 USED_PRSOPECT = Le prospect que vous voulez supprimer poss\u00e8de des visites
		 NULL_SAMPLE = sample is null
		 PRODUCT_IS_EMPTY = Le produit est obligatoire
		 USER_IS_EMPTY = Le d\u00e9l\u00e9gu\u00e9 est obligatoire
		 QUANTITY_IS_EMPTY = la quantit\u00e9  est obligatoire
		 DELIVERY_DATE_IS_EMPTY = La date de livraison est obligatoire
		 PERIOD_IS_EMPTY = La p\u00e9riode est obligatoire
		 LINK_IS_EMPTY = Le lien du rapport est obligatoire
		 REPORTCRON_USER_IS_EMPTY = Les utilisateurs sont obligatoires
		 REPORT_CRON_ID_DTO_IS_NULL = reportCron est vide
		 REPORT_CRON_PERIOD_IS_EMPTY = la p\u00e9riode est obligatoire
		 REPORT_CRON_LINK_IS_EMPTY = le lien du rapport est obligatoire
		 REPORT_CRON_TO_UPDATE_ALREADY_DELETED = Le rapport est d\u00e9ja supprim\u00e9
		 REPORT_CRON_USERS_IS_EMPTY = les utilisateurs sont obligatoires
		 VISIT_PRODUCT_COULD_NOT_DELETED = Could not delete visit product
		 NULL_PROSPECT_TYPE = Le type de prospect est null
		 
# ValueType

		 NUMBER_OF_VISITS = Nombre de visites
		 REPORT_FILLING_RATE = Taux de remplissage rapport
		 BLANKET = Taux R/P
		 SALES = Nombre de stock
		 SAMPLE_NUMBER = Nombre d\u2019\u00e9chantillons
		 NUMBER_OF_ORDERS = Nombre de commandes
		 PROSPECT_SATISFACTION = Satisfaction par commentaire
		 GROSS_SALES = Chiffre d\u2019affaire
		 GADGET = Nombre des cadeaux
		 PROSPECT_PRODUCT_SATISFACTION = Satisfaction produit
		 PRODUCT_ORDER_PRESENTATION = Ordre de pr\u00e9sentation
		 PERCENTAGE = Pourcentage
		 AMOUNT = Montant
		 VISITS_AVERAGE = Moyenne des visites
		 SYNCHRONISATION_DELAY = D\u00e9lai de synchronisation
		 
# GroupType

		 DELEGUE = D\u00e9l\u00e9gu\u00e9
		 PROSPECT = Prospect
		 SECTOR = Secteur
		 LOCALITY = Localit\u00e9
		 ACTIVITY = Activit\u00e9
		 POTENTIAL = Potentiel
		 SPECIALITY = Sp\u00e9cialit\u00e9
		 PRODUCT = Produit
		 SATISFACTION = Satisfaction
		 PRESENTATION_ORDER = Ordre de pr\u00e9sentation
		 
# email and notif 
		sendRememberSynchronizationSubject= Synchronisation
		sendRememberSynchronizationHtmlBody= Bonjour,<br/><br/> Merci de synchroniser vos donn\u00e9es depuis Birdnotes mobile \n\n \
		
		sendMailReportSubject= Dashboards de statistiques
		sendMailReportHtmlBody= Bonjour,<br/><br/> Vous pouvez consulter les dashboards de statistiques de la derni\u00e8re p\u00e9riode sur ce lien : \n\n \
		
		sendWholesalerMailSubject = Commande prospect {0} / {2}
		sendWholesalerMailHtmlBodyWithAttachment = Bonjour,<br/><br/> Nous avons une nouvelle commande pour le prospect {0}. <br/> L\u2019adresse du prospect est {1} (Secteur : {4}, Localit\u00e9 : {5} ). <br/>Les d\u00e9tails de la commande sont pr\u00e9sents dans la pi\u00e8ce jointe. <br/> Merci de confirmer la r\u00e9ception de l\u2019email. <br/><br/>{3}<br/>Cordialement<br/>CRM BirdNotes\n\n \
		
		sendWholesalerMailHtmlBodyWithoutAttachment = Bonjour,<br/><br/> Nous avons une nouvelle commande pour le prospect {0}. <br/> L\u2019adresse du prospect est {1} (Secteur : {4}, Localit\u00e9 : {5}). <br/><br/>{3}<br/>Cordialement<br/>CRM BirdNotes\n\n \
		
		sendCancellationMailSubject = Annulation de la commande prospect {0} / {2}
		sendCancellationMailHtmlBody = Bonjour,<br/><br/> Nous regrettons de vous informer que la commande du prospect {0} est annul\u00e9e. <br/> L\u2019adresse du prospect est {1} (Secteur : {4}, Localit\u00e9 : {5}). <br/><br/>{3}<br/>Cordialement<br/>CRM BirdNotes\n\n \
		
		
		sendCredentialsEmailSubject = Votre compte sur Birdnotes
		sendPredictionEmailSubject = Pr\u00e9diction des commandes
		sendSurveyEmailSubject = Evaluation
		sendSurveyEmailHtmlBody = Bonjour,<br/><br/> Une nouvelle r\u00e9ponse a l enquete de satisfaction {0} a \u00e9t\u00e9 envoy\u00e9e 
		sendPredictionEmailHtmlBody =Bonjour,<br/><br/> La pr\u00e9diction des commandes pour le mois {0} est termin\u00e9 avec succ\u00e8s.Vous pouvez consulter les prospects recommand\u00e9s en cliquant sur le lien suivant : <a href="{1}">Page Pr\u00e9diction des Commandes</a><br/><br/>.
		sendCredentialsEmailHtmlBody=Bonjour,<br/><br/> Birdnotes vous a cr\u00e9e un compte avec les param\u00e8tres suivants:<br/> \n\n \
		<strong> Identifiant: </strong> {0} <br/> <strong> Mot de passe: </strong> {1} <br/> <br/> Cordialement <br/> Birdnotes.
		newIssueEmailHtmlBody=Hello Admin ,<br/><br/>  You have new issue :<br/> \n\n \
		<strong> Delegate: </strong> {0} <br/> <strong> Issue: </strong> {1} <br/> <br/> Cordialement <br/> Birdnotes.
		editPasswordEmailSubject=Modification du mot de passe sur Birdnotes
		editPasswordEmailHtmlBody=Bonjour,<br/><br/> Birdnotes a modifi\u00e9 votre compte:<br/> \n\n \
		<strong> Votre mot de passe devient: </strong> {0} <br/> <br/> Cordialement <br/> Birdnotes.
		sendInvestigationSubject = Enqu\u00eate de satisfaction
		sendInvestigationHtmlBody = {0},<br/>{1}<br/>{2}<br/><br/>Cordialement<br/>{3}<br/>
		editUsernameEmailSubject = Modification identifiant sur Birdnotes
		editUsernameEmailHtmlBody = Bonjour,<br/><br/> Birdnotes a modifi\u00e9 votre compte:<br/> \n\n \
		<strong> Votre identifiant devient: </strong> {0} <br/> <br/> Cordialement <br/> Birdnotes.
		editUsernamePasswordEmailSubject = Modification identifiant et mot de passe sur Birdnotes
		editUsernamePasswordEmailHtmlBody  = Bonjour,<br/><br/> Birdnotes a modifi\u00e9e votre compte:<br/> \n\n \
		<strong> Votre identifiant devient: </strong> {0} <br/> <strong> Votre mot de passe devient: </strong> {1} <br/> <br/> Cordialement <br/> Birdnotes.
		
		planningNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 <a href="{5}/#/planification/{2}">un nouveau planning</a>
		noteFraisNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 <a href="{5}/#/expense-report/{2}" >une nouvelle note de frais </a>
		actionMarketingNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 <a href="{5}/#/action-marketing/{2}" > une nouvelle action marketing</a>
		opportunityNoteNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 une nouvelle note opportunit\u00e9
		locationNotificationMessage = une nouvelle localisation 
		addProspectNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a ajout\u00e9 <a href="{5}/#/change-prospect/{2}" > le nouveau prospect {1}</a>
		updateProspectNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a modifi\u00e9 le prospect {1}
		
		activityNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a ajout\u00e9 <a href="{5}/#/activity/{2}" >une nouvelle activit\u00e9
		
		planificationValidationNotificationMessage = {0} a valid\u00e9 votre planning
		planificationReviseNotificationMessage = {0} a demand\u00e9 de r\u00e9viser le planning
		planificationRefusNotificationMessage= {0} a refus\u00e9 le planning
		
		actionMarketingValidationNotificationMessage = {0} a valid\u00e9 votre action marketing
		actionMarketingRefusNotificationMessage = {0} a refus\u00e9 votre action marketing
		opportunityNoteValidationNotificationMessage = {0} a valid\u00e9 votre note d\u0027 opportunit\u00e9s
		opportunitynoteRefusNotificationMessage = {0} a refus\u00e9 votre note d\u0027 opportunit\u00e9s
		
		
		noteFraisValidationNotificationMessage = {0} a valid\u00e9 votre note de frais
		noteFraisRefusNotificationMessage = {0} a refus\u00e9 votre note de frais
		
		delegateCommissionValidationNotificationMessage = {0} a valid\u00e9 votre commission
		delegateCommissionRefusNotificationMessage = {0} a refus\u00e9 votre commission
		
		prospectsAcceptationNotificationMessage = {0} a accept\u00e9 le prospect {1}
		prospectsModificationAcceptationNotificationMessage = {0} a accept\u00e9 les modifications du prospect {1}
		prospectsRefuseNotificationMessage =  {0} a refus\u00e9 le prospect {1}
		
		prospectsAffectationNotificationMessage = {0} vous a affect\u00e9 le nouveau prospect {1}
		prospectsAffectationDeleteNotificationMessage = {0} vous a supprim\u00e9 le prospect {1}
		
		visitMessageTag = {3} : Le d\u00e9l\u00e9gu\u00e9 {0} a visit\u00e9 le prospect {1} et vous a tag\u00e9 dans le message suivant : {4} 
		importantMessageTag =Le d\u00e9l\u00e9gu\u00e9 {0} a laiss\u00e9 un  commentaire impotant lors de sa visite la {3} au prospect {1} : \n\n {4} 
		urgentMessageTag =Le d\u00e9l\u00e9gu\u00e9 {0} a laiss\u00e9 un  commentaire urgent lors de sa visite le {3} au prospect {1} : {4} | produit : {6}
		reportValidation =Le rapport du d\u00e9l\u00e9gu\u00e9 {0} dat\u00e9 le {3} est {7}
		
		planningValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider un <a href="{5}/#/planification/{2}" >planning</a> cr\u00e9e par  {0}
		prospectValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider un <a href="{5}/#/change-prospect/{2}" >prospect</a> cr\u00e9e par  {0}
		delegateCommissionValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider une <a href="{5}/#/delegate-commission/{2}" >commission de d\u00e9l\u00e9gu\u00e9</a> {0}
		actionMarketingValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider la <a href="{5}/#/action-marketing/{2}" >nouvelle action marketing</a> cr\u00e9e par  {0}
		noteFraisValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider la <a href="{5}/#/expense-report/{2}" >nouvelle note de frais</a> cr\u00e9e par  {0}
		ADD_ACTIVITY = Activite ajoutee avec succes
		
			