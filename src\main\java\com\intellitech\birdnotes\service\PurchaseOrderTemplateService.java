package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateItemDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;

public interface PurchaseOrderTemplateService {
	public PurchaseOrderTemplate savePurchaseOrderTemplate(PurchaseOrderTemplateDto purchaseOrderTemplateDto)throws BirdnotesException;

	public List<PurchaseOrderTemplateDto> getPurchaseOrderTemplateByUserProductAndDate(
			PurchaseOrderTemplateDto purchaseOrderTemplateRequestDto) throws BirdnotesException;

	void delete(Long id) throws BirdnotesException;

	public List<PurchaseOrderTemplateItemDto> getPurchaseOrderTemplateItem(
			PurchaseOrderTemplateDto purchaseOrderTemplateRequestDto);

	boolean deletePurchaseOrderTemplateItem(Long id) throws BirdnotesException;

	List<PurchaseOrderTemplateDto> getPurchaseOrderTemplateByUser(Long userId) throws BirdnotesException;
	
}
