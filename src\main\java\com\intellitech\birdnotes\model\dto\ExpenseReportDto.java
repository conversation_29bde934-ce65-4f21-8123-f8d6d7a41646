package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class ExpenseReportDto implements Serializable {

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	private static final long serialVersionUID = 1L;

	private Long id;

	private Long identifier;

	private Date date;

	private int day;

	private int month;

	private int year;

	private String expenseDate;

	private Float montant;

	private DelegateDto delegateDto;
	private Long delegateId;


	private ExpenseTypeDto expenseTypeDto ;
	
	private String expenseTypeName ;
	
	private Long expenseTypeId;
	
	private String description;

	private String pieceJointe;

	private String attachmentName;

	private String attachmentBase64;

	private String delegateName;

	private List<FileNamePath> nameFile;

	private List<ValidationStatusDto> validationStatusDto;

	private String status;

	private Long activityId;

	private String activityComment;

	private String activityTypeName;

	public ExpenseReportDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Long getDelegateId() {
		return delegateId;
	}

	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Float getMontant() {
		return montant;
	}

	public void setMontant(Float montant) {
		this.montant = montant;
	}

	public ExpenseTypeDto getExpenseTypeDto() {
		return expenseTypeDto;
	}

	public void setExpenseTypeDto(ExpenseTypeDto expenseTypeDto) {
		this.expenseTypeDto = expenseTypeDto;
	}


	public Long getExpenseTypeId() {
		return expenseTypeId;
	}

	public void setExpenseTypeId(Long expenseTypeId) {
		this.expenseTypeId = expenseTypeId;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPieceJointe() {
		return pieceJointe;
	}

	public void setPieceJointe(String pieceJointe) {
		this.pieceJointe = pieceJointe;
	}

	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

	public DelegateDto getDelegateDto() {
		return delegateDto;
	}

	public void setDelegateDto(DelegateDto delegateDto) {
		this.delegateDto = delegateDto;
	}

	public void setNameFile(List<FileNamePath> listeFichiers) {
		this.nameFile = listeFichiers;
	}

	public List<FileNamePath> getNameFile() {
		return nameFile;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public int getDay() {
		return day;
	}

	public void setDay(int day) {
		this.day = day;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public List<ValidationStatusDto> getValidationStatusDto() {
		return validationStatusDto;
	}

	public void setValidationStatusDto(List<ValidationStatusDto> validationStatusDto) {
		this.validationStatusDto = validationStatusDto;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getActivityComment() {
		return activityComment;
	}

	public void setActivityComment(String activityComment) {
		this.activityComment = activityComment;
	}

	public String getActivityTypeName() {
		return activityTypeName;
	}

	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}

	public String getExpenseDate() {
		return expenseDate;
	}

	public void setExpenseDate(String expenseDate) {
		this.expenseDate = expenseDate;
	}

	public Long getActivityId() {
		return activityId;
	}

	public void setActivityId(Long activityId) {
		this.activityId = activityId;
	}

	public String getExpenseTypeName() {
		return expenseTypeName;
	}

	public void setExpenseTypeName(String expenseTypeName) {
		this.expenseTypeName = expenseTypeName;
	}

}
