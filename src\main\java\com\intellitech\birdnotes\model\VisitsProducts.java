package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.Visit_Product, schema = Common.PUBLIC_SCHEMA)
public class VisitsProducts implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String comment;
	private String commentRating;
	private Integer orderQuantity;
	private Integer sampleQuantity;
	private Visit visit;
	private Product product;
	private Integer rank;
	private Integer smily;
	private Integer saleQuantity;
	private boolean urgent ;
	private Integer prescriptionQuantity;
	private Integer freeOrder;
	private Integer labGratuity;
    private PurchaseOrder purchaseOrder;
    private Long identifier;
    
	public VisitsProducts() {
		super();
	}

	@Id
	@SequenceGenerator(name = Sequences.VISIT_PRODUCT_SEQUENCE, sequenceName = Sequences.VISIT_PRODUCT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.VISIT_PRODUCT_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.VISIT_ID, unique = false)
	public Visit getVisit() {
		return visit;
	}

	public void setVisit(Visit visit) {
		this.visit = visit;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, unique = false)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}
	
	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.PURCHASEORDER_ID, unique = false)
	public PurchaseOrder getPurchaseOrder() {
		return purchaseOrder;
	}
	
	public void setPurchaseOrder(PurchaseOrder purchaseOrder) {
		this.purchaseOrder = purchaseOrder;
	}
	
	@Column(name = Columns.COMMENT, length = BirdnotesConstants.Numbers.N_255)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = Columns.ORDER_QUANTITY)
	public Integer getOrderQuantity() {
		return orderQuantity;
	}

	public void setOrderQuantity(Integer orderQuantity) {
		this.orderQuantity = orderQuantity;
	}

	@Column(name = Columns.SAMPLE_QUANTITY)
	public Integer getSampleQuantity() {
		return sampleQuantity;
	}

	public void setSampleQuantity(Integer sampleQuantity) {
		this.sampleQuantity = sampleQuantity;
	}

	@Column(name = Columns.RANK)
	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	@Column(name = Columns.SMILY)
	public Integer getSmily() {
		return smily;
	}

	public void setSmily(Integer smily) {
		this.smily = smily;
	}

	@Column(name = Columns.SALE_QUANTITY)
	public Integer getSaleQuantity() {
		return saleQuantity;
	}

	public void setSaleQuantity(Integer saleQuantity) {
		this.saleQuantity = saleQuantity;
	}
	
	@Column(name = Columns.URGENT)
	public boolean isUrgent() {
		return urgent;
	}

	public void setUrgent(boolean urgent) {
		this.urgent = urgent;
	}
	@Column(name = Columns.PRESCRIPTION_QUANTITY)
	public Integer getPrescriptionQuantity() {
		return prescriptionQuantity;
	}

	public void setPrescriptionQuantity(Integer prescriptionQuantity) {
		this.prescriptionQuantity = prescriptionQuantity;
	}

	@Column(name = Columns.FREE_ORDER)
	public Integer getFreeOrder() {
		return freeOrder;
	}

	public void setFreeOrder(Integer freeOrder) {
		this.freeOrder = freeOrder;
	}
	@Override
	public String toString() {
		return "VisitsProducts [id=" + id + ", comment=" + comment + ", orderQuantity=" + orderQuantity
				+ ", sampleQuantity=" + sampleQuantity + ", visit=" + visit + ", product=" + product + ", rank=" + rank
				+ ", smily=" + smily + ", saleQuantity=" + saleQuantity + ", urgent=" + urgent
				+ ", prescriptionQuantity=" + prescriptionQuantity  + ", freeOrder=" + freeOrder + "]";
	}

	@Column(name = Columns.LAB_GRATUITY)
	public Integer getLabGratuity() {
		return labGratuity;
	}

	public void setLabGratuity(Integer labGratuity) {
		this.labGratuity = labGratuity;
	}
	@Column(name = Columns.IDENTIFIER)
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	@Column(name = Columns.COMMENT_RATING, length = BirdnotesConstants.Numbers.N_64)
	public String getCommentRating() {
		return commentRating;
	}

	public void setCommentRating(String commentRating) {
		this.commentRating = commentRating;
	}

	
}
