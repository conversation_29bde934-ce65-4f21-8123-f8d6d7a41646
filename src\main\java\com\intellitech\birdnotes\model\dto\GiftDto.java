package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

import com.intellitech.birdnotes.enumeration.GiftType;

public class GiftDto implements Serializable{
	
	private static final long serialVersionUID = 1L;
	private Long id;
	private String name;
	private Float price;
	private String type ;
	


	public GiftDto(Long id, String name, Float prix) {
		super();
		this.id = id;
		this.name = name;
		this.price = prix;
	}

	public GiftDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public Float getPrice() {
		return price;
	}

	public void setPrice(Float price) {
		this.price = price;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	
	
}
