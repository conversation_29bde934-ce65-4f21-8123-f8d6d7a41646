package com.intellitech.birdnotes.model.dto;
import java.io.Serializable;
public class SageProductItemDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private String  product_code;
	private Long available_quantity;
	
	public SageProductItemDto() {
		super();
	}
	
	public SageProductItemDto(String product_code, Long available_quantity) {
		super();
		this.product_code = product_code;
		this.available_quantity = available_quantity;
	}

	public String getProduct_code() {
		return product_code;
	}

	public void setProduct_code(String product_code) {
		this.product_code = product_code;
	}

	public Long getAvailable_quantity() {
		return available_quantity;
	}

	public void setAvailable_quantity(Long available_quantity) {
		this.available_quantity = available_quantity;
	}


}
