package com.intellitech.birdnotes.controller;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.ExitStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.JobParametersBuilder;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.batch.item.file.FlatFileParseException;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.batchprocessing.BatchConfiguration;
import com.intellitech.birdnotes.data.dto.InvestigationDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.DelegateVisitCartographyRequest;
import com.intellitech.birdnotes.model.dto.ExportProspects;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.PatientDto;
import com.intellitech.birdnotes.model.dto.ProspectChangedDto;
import com.intellitech.birdnotes.model.dto.ProspectDistributionList;
import com.intellitech.birdnotes.model.dto.ProspectDistributionRequest;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectsToMoveRequestDto;
import com.intellitech.birdnotes.model.request.ProspectListRequest;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/prospect")
public class ProspectController {

	private static final Logger LOG = LoggerFactory.getLogger(ProspectController.class);
	@Autowired
	BatchConfiguration batchConfiguration;
	@Autowired
	DelegateService delegateService;

	@Autowired
	JobLauncher jobLauncher;
	@Autowired
	Job job;

	@Autowired
	private ProspectService prospectService;
	@Autowired
	UserService userService;

	@Autowired
	private DownloadDataService downloadDataService;

	@Autowired
	private CurrentUser currentUser;

	@RequestMapping(value = "/getAllDataForProspect", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForProspect() {
		try {
			if (userService.checkHasPermission("PROSPECT_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				return new ResponseEntity<>(downloadDataService.findAllDataForProspect(birdnotesUser), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the prospect lists", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getByName/{name}/{prospectId}", method = RequestMethod.GET)
	public ResponseEntity<List<LabelValueDto>> getByName(@PathVariable("name") String name,
			@PathVariable("prospectId") Long prospectId) {

		try {

			List<LabelValueDto> prospects = prospectService.findByName(name, prospectId);
			return new ResponseEntity<>(prospects, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects lists", e);
			return new ResponseEntity<>(new ArrayList<LabelValueDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveProspectsWhichBecameValid", method = RequestMethod.POST)
	public ResponseEntity<HashMap<String, Object>> saveProspectsWhichBecameValid(
			@RequestBody List<ProspectDto> invalidProspects) {
		try {
			if (userService.checkHasPermission("PROSPECT_ADD")) {
				HashMap<String, Object> toReturn = prospectService.saveProspectsWhichBecameValid(invalidProspects);
				return new ResponseEntity<>(toReturn, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update prospects", e);
			return new ResponseEntity<>(new HashMap<>(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting invalid prospects list", e);
			return new ResponseEntity<>(new HashMap<>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getProspectWithStatus", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectDto>> findProspectWithStatus() {
		try {
			if (userService.checkHasPermission("PROSPECT_VIEW")) {
				List<ProspectDto> toReturn = prospectService.findProspectWithStatus();
				return new ResponseEntity<>(toReturn, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects lists with status", e);
			return new ResponseEntity<>(new ArrayList<ProspectDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getProspectUpdateAndNewRequests", method = RequestMethod.GET)
	public ResponseEntity<String> getProspectUpdateAndNewRequests() {
		try {
			if (userService.checkHasPermission("LIST_OF_UPDATE_REQUESTS_VIEW")) {
				prospectService.getProspectUpdateAndNewRequests();
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while accepting all data for the prospect lists", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while accepting all data for the prospect lists", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/updateLocality", method = RequestMethod.PUT)
	public ResponseEntity<String> updateLocality(@RequestBody ProspectsToMoveRequestDto prospectsToMoveRequestDto)
			throws BirdnotesException {

		try {

			prospectService.updateLocality(prospectsToMoveRequestDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update prospects", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while updating the prospects", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/importProspects/{importStep}/{geocodeAdress}", method = RequestMethod.POST)
	public ResponseEntity<Object> importProspect(@RequestParam("file") MultipartFile file,
			@PathVariable("importStep") String importStep, @PathVariable("geocodeAdress") String geocodeAdress)
			throws BirdnotesException {

		Map<String, String> error = new HashMap<>();
		if (userService.checkHasPermission("IMPORT_PROSPECT_VIEW")) {
			try {

				File tmpFile = File.createTempFile("import", file.getOriginalFilename());
				file.transferTo(tmpFile);
				String filePath = tmpFile.getPath();
				JobParametersBuilder jobParamBuilder = new JobParametersBuilder();
				jobParamBuilder.addString("geocodeAdress", geocodeAdress);
				jobParamBuilder.addString("inputFile", filePath);
				jobParamBuilder.addString("importStep", importStep);
				jobParamBuilder.addLong("time", System.currentTimeMillis()).toJobParameters();
				JobParameters jobParameters = jobParamBuilder.toJobParameters();

				JobExecution execution = jobLauncher.run(job, jobParameters);
				ExitStatus status = execution.getExitStatus();
				if (ExitStatus.COMPLETED.getExitCode().equals(status.getExitCode())) {
					return new ResponseEntity<>(batchConfiguration.getInvalidProspectsData(), HttpStatus.OK);
				} else {
					List<Throwable> exceptions = execution.getAllFailureExceptions();
					for (final Throwable throwable : exceptions) {
						if (throwable instanceof BeanCreationException) {
							throw (BeanCreationException) throwable;
						}
						if (throwable instanceof FlatFileParseException) {
							throw (FlatFileParseException) throwable;
						}
					}
				}
			} catch (BeanCreationException ex) {
				error.put("message", Exceptions.INVALID_CSV_FILE_FORMAT);
				return new ResponseEntity<>(error, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			} catch (FlatFileParseException ex) {
				error.put("message", Exceptions.PROSPECT_DATA_NOT_FOUND_IN_CSV_FILE + (ex.getLineNumber() - 1));
				return new ResponseEntity<>(error, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			} catch (Exception ex) {
				LOG.error("An exception occurred while importing prospects", ex);
				return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
			}
		} else {
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		}
		return null;

	}

	@RequestMapping(value = "/findSimilarProspects", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> findSimilarProspect(@RequestBody ProspectDto prospectDto) {
		try {

			List<ProspectDto> toReturn = prospectService.findSimilarProspects(prospectDto);
			return new ResponseEntity<>(toReturn, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting duplicates prospects", e);
			return new ResponseEntity<>(new ArrayList<ProspectDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllImportedProspects(@RequestBody List<ProspectDto> prospectDtos) {
		try {
			prospectService.addAllImportedProspects(prospectDtos);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add imported prospects", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding imported prospects", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/mergeProspectForImport", method = RequestMethod.POST)
	public ResponseEntity<String> mergeProspectForImport(@RequestBody ProspectDto prospectToMerge) {
		try {
			prospectService.mergeProspectForImport(prospectToMerge, null);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when merge prospect", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred ", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/merge", method = RequestMethod.POST)
	public ResponseEntity<String> mergeProspect(@RequestBody List<ProspectDto> prospects) {
		try {
			if (userService.checkHasPermission("PROSPECT_MERGE")) {
				ProspectDto prospectDto = prospects.get(0);
				ProspectDto prospectToMerge = prospects.get(1);
				prospectService.mergeProspect(prospectDto, prospectToMerge);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when merge prospect", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred ", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/exportProspects", method = RequestMethod.POST)
	public ResponseEntity<Object> exportProspects(@RequestBody ExportProspects exportProspects) {
		try {
			File file = prospectService.exportProspects(exportProspects);
			InputStreamResource resource = new InputStreamResource(new FileInputStream(file));
			HttpHeaders headers = new HttpHeaders();
			headers.add("Content-Disposition", String.format("attachment; filename=\"%s\"", file.getName()));
			headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
			headers.add("Pragma", "no-cache");
			headers.add("Expires", "0");
			return ResponseEntity.ok().headers(headers).contentLength(file.length())
					.contentType(MediaType.parseMediaType("application/txt")).body(resource);

		} catch (Exception e) {
			LOG.error("An exception occurred while export prospects File", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/getVisitedProspect", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> getVisitedProspect(
			@RequestBody DelegateVisitCartographyRequest visitedProspectsRequest) {
		try {
			List<ProspectDto> nearestProspectList = prospectService.getVisitedProspect(
					visitedProspectsRequest.getUserId(), visitedProspectsRequest.getStartDate(),
					visitedProspectsRequest.getEndDate());
			// List<ProspectDto> nearestProspectList = prospectService.getNearestProspect(
			// nearestProspectsRequest.getXlat(),
			// nearestProspectsRequest.getYlat(),
			// nearestProspectsRequest.getXlng(),nearestProspectsRequest.getYlng(),
			// nearestProspectsRequest.getDate());

			return new ResponseEntity<>(nearestProspectList, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while getting nearest prospects to delegate", e);
			return new ResponseEntity<>(new ArrayList<ProspectDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/getNotVisitedProspect", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> getNotVisitedProspect(
			@RequestBody DelegateVisitCartographyRequest notVisitedProspectsRequest) {
		try {
			List<ProspectDto> nearestProspectList = prospectService.getNotVisitedProspect(
					notVisitedProspectsRequest.getUserId(), notVisitedProspectsRequest.getStartDate(),
					notVisitedProspectsRequest.getEndDate());

			return new ResponseEntity<>(nearestProspectList, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while getting nearest prospects to delegate", e);
			return new ResponseEntity<>(new ArrayList<ProspectDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/saveProspect", method = RequestMethod.POST)
	public ResponseEntity<String> saveProspect(@RequestBody ProspectDto prospectDto) {
	    try {
	        if (userService.checkHasPermission("PROSPECT_ADD")) {
	            Prospect savedProspect = prospectService.add(prospectDto);
	            if (savedProspect != null) {
	                return new ResponseEntity<>(savedProspect.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }
	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving prospect", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving prospect", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/addImportedProspect", method = RequestMethod.POST)
	public ResponseEntity<String> addImportedProspect(@RequestBody ProspectDto prospectDto) {
		try {
			prospectService.addImportedProspect(prospectDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when add prospect", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new prospect", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteProspect(@PathVariable("id") Long idProspect) {
		try {
			if (userService.checkHasPermission("PROSPECT_DELETE")) {
				prospectService.delete(idProspect);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("An exception occurred while deleting the prospect with id =" + idProspect, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/refuseProspect/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> refuseProspect(@PathVariable("id") Long idProspect) {
		try {
			prospectService.refuseValidationStep(idProspect);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when refusing prospect", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
		} catch (Exception e) {
			LOG.error("An exception occurred while refusing the prospect with id =" + idProspect, e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/updateProspect", method = RequestMethod.PUT)
	public ResponseEntity<String> updateProspect(@RequestBody ProspectDto prospectDto) {

		try {
			if (userService.checkHasPermission("PROSPECT_EDIT")) {
				prospectService.updateProspect(prospectDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update prospect", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while updating prospect", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/acceptProspect/{validationStatusId}", method = RequestMethod.PUT)
	public ResponseEntity<String> acceptProspect(@PathVariable("validationStatusId") Long validationStatusId,
			@RequestBody ProspectDto prospectDto) {

		try {
			prospectService.acceptAddingNewProspectRequestStep(validationStatusId, prospectDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update prospect", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while updating prospect", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/updateProspectChanged/{validationStatusId}", method = RequestMethod.PUT)
	public ResponseEntity<String> updateProspectChanged(@PathVariable("validationStatusId") Long validationStatusId,
			@RequestBody ProspectDto prospectDto) {

		try {
			prospectService.acceptProspectChangeRequestStep(validationStatusId, prospectDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information update prospect changed", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while updating prospect", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "sendInvestigation", method = RequestMethod.POST)
	public ResponseEntity<String> sendInvestigation(@RequestBody InvestigationDto investigationDto) {
		try {
			boolean result = prospectService.sendInvestigation(investigationDto);
			if (result) {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			}
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.NO_CONTENT);
		} catch (Exception e) {
			LOG.error("An exception occurred while send investigation", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "findChange/{id}", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectChangedDto>> findChange(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("LIST_OF_UPDATE_REQUESTS_VALIDATION")) {
				List<ProspectChangedDto> toReturn = prospectService.getUpdateRequestDifference(id);
				return new ResponseEntity<>(toReturn, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects  lists" + e.getMessage(), e);
			return new ResponseEntity<>(new ArrayList<ProspectChangedDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getProspectChangesNumber", method = RequestMethod.GET)
	public ResponseEntity<Integer> getProspectChangesNumber() {
		try {
			List<ProspectDto> toReturn = prospectService.findProspectWithStatus();
			return new ResponseEntity<>(toReturn.size(), HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects lists with status", e);
			return new ResponseEntity<>(new Integer(0), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getProspectsWithPagination", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> getProspectsWithPagination(
			@RequestBody ProspectListRequest prospectListRequest) {
		try {
			return new ResponseEntity<>(prospectService.findAll(prospectListRequest), HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the prospect lists", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findProspectById/{id}", method = RequestMethod.GET)
	public ResponseEntity<ProspectDto> getProspectById(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("PROSPECT_VIEW")) {
				return new ResponseEntity<>(prospectService.findProspectById(id), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospect", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getProspectsDistribution", method = RequestMethod.POST)
	public ResponseEntity<ProspectDistributionList> getProspectsDistribution(
			@RequestBody ProspectDistributionRequest prospectDistributionRequest) {
		try {
			if (userService.checkHasPermission("PROSPECT_VIEW")) {

				ProspectDistributionList back = prospectService.getProspectsDistribution(prospectDistributionRequest);
				// List<ProspectDto> prospectList =
				// prospectService.findAll(prospectDistributionRequest.getProspectListRequest());
				// back.setProspectList(prospectList);

				return new ResponseEntity<>(back, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getProspectDistribution", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/getAllDataForNewProspect", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForNewProspect() {
		try {
			if (userService.checkHasPermission("PROSPECT_ADD")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForNewProspect(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for add new prospect", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllUsers", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("PROSPECT_ADD") || userService.checkHasPermission("PROSPECT_EDIT")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<DelegateDto> userDtos = delegateService.findAllDelegates();

				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllPatients", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> getAllPatients(
			@RequestBody ProspectDistributionRequest patientDistributionRequest) {

		try {
			if (userService.checkHasPermission("PATIENT_VIEW")) {
				List<ProspectDto> patients = prospectService.findAllPatients(patientDistributionRequest);
				return new ResponseEntity<>(patients, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all patients", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deletePatient/{patientId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteAutomationRule(@PathVariable("patientId") Long idPatient) {
		try {
			if (userService.checkHasPermission("PATIENT_DELETE")) {
				prospectService.delete(idPatient);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in delete patient", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "savePatient", method = RequestMethod.POST)
	public ResponseEntity<Long> savePatient(@RequestBody PatientDto patient) {
		try {
			if (userService.checkHasPermission("PATIENT_EDIT") || userService.checkHasPermission("PATIENT_ADD")) {
				Prospect patientSaved = prospectService.savePatient(patient);
				if (patientSaved != null) {
					return new ResponseEntity<>(patientSaved.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving patientSaved", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving patientSaved", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
