package com.intellitech.birdnotes.service.impl;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledFuture;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.support.CronTrigger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.BirdNotesConfig;
import com.intellitech.birdnotes.enumeration.OrderValidation;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.convertor.ConfigurationToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.DtoToConfigurationConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ReportCronDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.LocationService;
import com.intellitech.birdnotes.service.PlanningService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ReportCronService;
import com.intellitech.birdnotes.service.ReportValidationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("configurationService")
@Transactional
public class ConfigurationServiceImpl implements ConfigurationService {

	private ConfigurationRepository configurationRepository;

	private ConfigurationToDtoConvertor configurationToDtoConvertor;

	private DtoToConfigurationConvertor dtoToConfigurationConvertor;
	
    private JdbcTemplate jdbcTemplate;
    
    
    
	@Autowired
	private TaskScheduler taskScheduler;
	


	
	private Map<String, ScheduledFuture<?>> scheduledTasks = new HashMap<>();

	@Autowired
	private UserService userService;

	@Autowired
	private DelegateService delegateService;

	@Autowired
	private ReportValidationService reportValidationService;
	
	@Autowired
	private ReportCronService reportCronService;

	@Autowired
	private PlanningService planningService;

	@Autowired
	private ProductService productService;

	@Autowired
    private LocationService locationService;
 
	@Autowired
    private DelegateRepository delegateRepository;
	
	@Value("${erp.updateFrequency}")
	private String erpUpdateFrequency;

	
	private static final Logger LOG = LoggerFactory.getLogger(ConfigurationServiceImpl.class);
	@Autowired
	public ConfigurationServiceImpl(ConfigurationRepository configurationRepository,
			ConfigurationToDtoConvertor configurationToDtoConvertor,
			DtoToConfigurationConvertor dtoToConfigurationConvertor, JdbcTemplate jdbcTemplate) {
		super();
		this.configurationRepository = configurationRepository;
		this.configurationToDtoConvertor = configurationToDtoConvertor;
		this.dtoToConfigurationConvertor = dtoToConfigurationConvertor;
		this.jdbcTemplate = jdbcTemplate;
		
	}
	
	public void setUpCrons() throws BirdnotesException {

		ConfigurationDto configuration = findConfiguration();

		BirdnotesUtils.configuration = configuration;
		
			startOrderPredictionCron(configuration.getOrderPredictionCron());      
			reportValidationCron(configuration.getReportValidationCron());
			reportingReminderCron(configuration.getReportingReminderCron());
			expenseDistanceCron(configuration.getExpenseDistanceCron());
			erpSyncCron(configuration.getErpSyncCron());
			calculateDailyDistance(configuration.getDisplacementCron());
			
	}
	
	

	
    public void updateCronExpression(ConfigurationDto newConfig, ConfigurationDto oldConfig ) {
    	
    	    	
    	if(!Objects.toString(oldConfig.getOrderPredictionCron(), "").equals(Objects.toString(newConfig.getOrderPredictionCron(), ""))) {
    		
    		removeScheduledTasksString("OrderPredictionCron");	
    		startOrderPredictionCron(newConfig.getOrderPredictionCron());       	        
    		
    	}
    	
    	if(!Objects.toString(oldConfig.getErpSyncCron(), "").equals(Objects.toString(newConfig.getErpSyncCron(), ""))) {
    		
    		removeScheduledTasksString("ErpSyncCron");	
    		erpSyncCron(newConfig.getErpSyncCron());       	        
    		
    	}
    	
    	if(!Objects.toString(oldConfig.getReportValidationCron(), "").equals(Objects.toString(newConfig.getReportValidationCron(), ""))) {
    		
    		removeScheduledTasksString("ReportValidationCron");	
    		reportValidationCron(newConfig.getReportValidationCron());       	        
    		
    	}
    	
    	if( !Objects.toString(oldConfig.getReportingReminderCron(), "").equals(Objects.toString(newConfig.getReportingReminderCron(), ""))) {
    		
    		removeScheduledTasksString("ReportingReminderCron");	
    		reportingReminderCron(newConfig.getReportingReminderCron());       	        
    		
    	}
    	
    	if(!Objects.toString(oldConfig.getExpenseDistanceCron(), "").equals(Objects.toString(newConfig.getExpenseDistanceCron(), ""))) {
    		
    		 removeScheduledTasksString("ExpenseDistanceCron");	
    		 expenseDistanceCron(newConfig.getExpenseDistanceCron());       	        
    		
    	}
    	
    	if(!Objects.toString(oldConfig.getDisplacementCron(), "").equals(Objects.toString(newConfig.getDisplacementCron(), ""))) {
    		
    		removeScheduledTasksString("DisplacementCron");	
    		calculateDailyDistance(newConfig.getDisplacementCron());       	        
    		
    	}
        

    }
    
	private void removeScheduledTasksString(String taskName) {
		
		if (scheduledTasks.get(taskName) != null) {
			scheduledTasks.get(taskName).cancel(false); 
			scheduledTasks.remove(taskName);
        }
	}
    
       private void calculateDailyDistance(String cronString) {
		
    	   if (cronString != null && !cronString.equals("")) {	
    		   
			CronTrigger displacementCronTrigger = new CronTrigger(cronString);
			Runnable runnable = () -> {

				try {
					delegateService.calculateDailyDistance(new Date());

				} catch (Exception e) {
					LOG.error("Error in DisplacementCron", e);
				}
			};
			taskScheduler.schedule(runnable, displacementCronTrigger);
		}
	}
    
    
    private void startOrderPredictionCron(String cronString) {
    	
    	if (cronString != null && !cronString.equals("")) {	
	    	CronTrigger cronTrigger = new CronTrigger(cronString);
	        Runnable runnable = () -> {
	            try {
	            	planningService.processProspectsOrdersPrediction(new Date(), null, null);
	            } catch (Exception e) {
	                LOG.error("Error in processProspectsOrdersPrediction", e);
	            }
	        };
	        
	        scheduledTasks.put("OrderPredictionCron", taskScheduler.schedule(runnable, cronTrigger));
        
    	}
    }
    
    private void reportValidationCron(String cronString) {
    	
        if (cronString != null && !cronString.equals("")) {	
            CronTrigger cronTrigger = new CronTrigger(cronString);
            Runnable runnable = () -> {
                try {
                	delegateService.sendMailForSynchronization();
                } catch (Exception e) {
                    LOG.error("Error in reportValidationCron", e);
                }
            };
            
            scheduledTasks.put("ReportValidationCron", taskScheduler.schedule(runnable, cronTrigger));
        }
    }
    
    private void reportingReminderCron(String cronString) {
    	
        if (cronString != null && !cronString.equals("")) {
            CronTrigger cronTrigger = new CronTrigger(cronString);
            Runnable runnable = () -> {
                try {
                    List<Delegate> delegates = delegateRepository.findAll();
                    for (Delegate delegate : delegates) {
                        reportValidationService.validateVisitReport(new Date(), delegate);
                    }
                } catch (Exception e) {
                    LOG.error("Error in reportingReminderCron", e);
                }
            };

            scheduledTasks.put("ReportingReminderCron", taskScheduler.schedule(runnable, cronTrigger));
        }
    }
    
    private void expenseDistanceCron(String cronString) {
    	
        if (cronString != null && !cronString.equals("")) {
            CronTrigger cronTrigger = new CronTrigger(cronString);
            Runnable runnable = () -> {
                try {
                    List<UserDto> delegates = userService.findUsers();
                    for (UserDto delegate : delegates) {
                        locationService.addExpensesByDistance(new Date(), delegate);
                    }
                } catch (Exception e) {
                    LOG.error("Error in expenseDistanceCron", e);
                }
            };

            scheduledTasks.put("ExpenseDistanceCron", taskScheduler.schedule(runnable, cronTrigger));
        }
    }
    
    private void erpSyncCron(String cronString) {
    	
        if (cronString != null && !cronString.equals("")) {
            CronTrigger cronTrigger = new CronTrigger(cronString);
            Runnable runnable = () -> {
                try {
                    productService.updateQuantityFromErp();
                } catch (Exception e) {
                    LOG.error("Error in erpSyncCron", e);
                }
            };

            scheduledTasks.put("ErpSyncCron", taskScheduler.schedule(runnable, cronTrigger));
        }
    }

	@Override
	public ConfigurationDto findByName(String name) {
		Configuration configuration = configurationRepository.findByName(name);
		return configurationToDtoConvertor.convert(configuration);
	}

	@Override
	public ConfigurationDto save(ConfigurationDto configurationDto) throws BirdnotesException {
		Configuration configuration = dtoToConfigurationConvertor.convert(configurationDto);
		Configuration savedConfig = configurationRepository.save(configuration);
		
		return configurationToDtoConvertor.convert(savedConfig);
	}

	@Override
	public void updateConfiguration(ConfigurationDto configurationDto) throws BirdnotesException {
		if (configurationDto == null || configurationDto.getId() == null) {
			throw new BirdnotesException("configurationDto is null");
		}

		Configuration configurationToUpdate = configurationRepository.findOne(1);
		ConfigurationDto oldCronExpression = findConfiguration();
		Configuration oldConfig = configurationRepository.findOne(1);
		if (configurationToUpdate == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.GONFIGURATION_TO_UPDATE_ALREDY_DELETED);
		}

		configurationToUpdate.setName(configurationDto.getName());
		configurationToUpdate.setSyncro(configurationDto.getSyncro());
		configurationToUpdate.setAutoSync(configurationDto.getAutoSync());
		configurationToUpdate.setAutoExpense(configurationDto.getAutoExpense());
		configurationToUpdate.setOrderValidation(OrderValidation.valueOf(configurationDto.getOrderValidation()));
		configurationToUpdate.setSyncCycle(configurationDto.getSyncCycle());
		configurationToUpdate.setAcceptedPointingDistance(configurationDto.getAcceptedPointingDistance());
		configurationToUpdate.setLockAfterSync(configurationDto.getLockAfterSync());
		configurationToUpdate.setOpenReportPeriod(configurationDto.getOpenReportPeriod());
		configurationToUpdate.setOpenExpensePeriod(configurationDto.getOpenExpensePeriod());
		configurationToUpdate.setSyncro(configurationDto.getSyncro());
		configurationToUpdate.setMultiWholesaler(configurationDto.getMultiWholesaler());
		configurationToUpdate.setCycle(configurationDto.getCycle());
		configurationToUpdate.setLogo(configurationDto.getLogo());
		configurationToUpdate.setSendSyncReminder(configurationDto.getSendSyncReminder());
		configurationToUpdate.setSyncReminderPeriod(configurationDto.getSyncReminderPeriod());
		configurationToUpdate.setHolidays(configurationDto.getHolidays());
		configurationToUpdate.setCommentsDictionary(configurationDto.getCommentsDictionary());
		configurationToUpdate.setBiPanels(configurationDto.getBiPanels());
		configurationToUpdate.setErpParams(configurationDto.getErpParams());
		configurationToUpdate.setServerPath(configurationDto.getServerPath());
		configurationToUpdate.setMlServerUrl(configurationDto.getMlServerUrl());
		configurationToUpdate.setBackendUrl(configurationDto.getBackendUrl());
		configurationToUpdate.setDefaultLatitude(configurationDto.getDefaultLatitude());
		configurationToUpdate.setDefaultLongitude(configurationDto.getDefaultLongitude());
		configurationToUpdate.setFieldParameters(configurationDto.getFieldParameters());
		configurationToUpdate.setBiServerUrl(configurationDto.getBiServerUrl());
		configurationToUpdate.setCommentsRatingNotification(configurationDto.getCommentsRatingNotification());
		configurationToUpdate.setOrderPredictionCron(configurationDto.getOrderPredictionCron());
		configurationToUpdate.setDisplacementCron(configurationDto.getDisplacementCron());
		configurationToUpdate.setErpSyncCron(configurationDto.getErpSyncCron());
		configurationToUpdate.setReportValidationCron(configurationDto.getReportValidationCron());
		configurationToUpdate.setReportingReminderCron(configurationDto.getReportingReminderCron());
		configurationToUpdate.setExpenseDistanceCron(configurationDto.getExpenseDistanceCron());
		configurationToUpdate.setErpType(configurationDto.getErpType());
		configurationToUpdate.setErpUrl(configurationDto.getErpUrl());
		configurationToUpdate.setReportingStartingTime(configurationDto.getReportingStartingTime());
		configurationToUpdate.setReportingEndingTime(configurationDto.getReportingEndingTime());
		configurationToUpdate.setDelayedReportingTolerence(configurationDto.getDelayedReportingTolerence());
		configurationToUpdate.setNoGeolocationTolerence(configurationDto.getNoGeolocationTolerence());
		configurationToUpdate.setHost(configurationDto.getHost());
		configurationToUpdate.setPort(configurationDto.getPort());
		configurationToUpdate.setEmail(configurationDto.getEmail());
		configurationToUpdate.setEmailPassword(configurationDto.getEmailPassword());
		configurationToUpdate.setLanguage(configurationDto.getLanguage());
		configurationRepository.save(configurationToUpdate);
		this.updateCronExpression(configurationDto,  oldCronExpression );
		
	}

	@Override
	public Configuration add(ConfigurationDto configurationDto) throws BirdnotesException {
		Configuration configuration = dtoToConfigurationConvertor.convert(configurationDto);
		Configuration result = configurationRepository.findByName(configurationDto.getName());
		if (result != null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}

		return configurationRepository.save(configuration);

	}

	@Override
	public ConfigurationDto findConfiguration() {
		Configuration configuration = configurationRepository.findOne(1);
		return configurationToDtoConvertor.convert(configuration);

	}
	
	/*@Override
	public void executeDeletionScript() {
		 try {
	            ClassPathResource resource = new ClassPathResource("sql/deletion_script.sql");
	            BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
	            StringBuilder sql = new StringBuilder();
	            String line;

	            while ((line = reader.readLine()) != null) {
	                sql.append(line);
	                sql.append("\n");
	            }

	            jdbcTemplate.execute(sql.toString());

	        } catch (IOException e) {
	            throw new RuntimeException("Failed to read SQL file: ", e);
	        }
	    }*/
	
		@Override
		public void executeDeletionScript(String scriptType) {
	        try {
	        	ClassPathResource resource;
	        	if(scriptType.equals("deleteProspectData")) {
	        		resource = new ClassPathResource("sql/prospect_deletion_script.sql");
	        	}else {
	        		resource = new ClassPathResource("sql/date_deletion_script.sql");
	        	}
	            
	            BufferedReader reader = new BufferedReader(new InputStreamReader(resource.getInputStream()));
	            String sqlContent = reader.lines().collect(Collectors.joining("\n"));
	            jdbcTemplate.execute(sqlContent);
	            reader.close();
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
	    }

	


}
