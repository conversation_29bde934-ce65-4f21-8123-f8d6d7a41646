package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class CommissionItemDto {
	private Long id;
	private Long commissionId;
	private Float threshold;
	private Float value;
	//private List<Long> wholesalersId;
	
	public CommissionItemDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getCommissionId() {
		return commissionId;
	}

	public void setCommissionId(Long commissionId) {
		this.commissionId = commissionId;
	}

	public Float getThreshold() {
		return threshold;
	}

	public void setThreshold(Float threshold) {
		this.threshold = threshold;
	}

	public Float getValue() {
		return value;
	}

	public void setValue(Float value) {
		this.value = value;
	}

	/*public List<Long> getWholesalersId() {
		return wholesalersId;
	}

	public void setWholesalersId(List<Long> wholesalersId) {
		this.wholesalersId = wholesalersId;
	}*/



	
}
