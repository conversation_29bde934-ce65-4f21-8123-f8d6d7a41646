package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.NotificationDto;
import com.intellitech.birdnotes.model.request.IssueRequest;
import com.intellitech.birdnotes.model.request.NotificationRequest;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.NotificationService;

@RestController
@RequestMapping("/notifications")
public class NotificationController {

	private static final Logger LOG = LoggerFactory.getLogger(NotificationController.class);

	@Autowired
	private NotificationService notificationService;

	@Autowired
	private CurrentUser currentUser;

	@Autowired
	private UserRepository userRepository;

	@RequestMapping(value = "testNotification", method = RequestMethod.GET)
	public ResponseEntity<String> testNotification() {

		String oneSignalUserId = "************************************";
		String message = "Hello Again !";
		Map<String, String> data = new HashMap<>();

		// notificationService.sentOneSignalNotification(oneSignalUserId,data, message
		// );

		return ResponseEntity.ok().body("ok");
	}

	@RequestMapping(value = "findAllNotification", method = RequestMethod.POST)
	public ResponseEntity<List<NotificationDto>> findAllNotification(
			@RequestBody NotificationRequest notificationRequest) {

		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			LOG.error("Session Expired");
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} else {
			User user = userRepository.findOne(birdnotesUser.getUserDto().getId());

			try {
				// List<NotificationDto> notification = notificationService.findByUser(user,
				// issueRequest);
				List<NotificationDto> notification = notificationService.findAll(user, notificationRequest);
				return new ResponseEntity<>(notification, HttpStatus.OK);
			} catch (Exception e) {
				LOG.error("An exception occurred while getting all notifications", e);
				return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
			}
		}
	}

	@RequestMapping(value = "getNotificationNumber", method = RequestMethod.POST)
	public int getNotificationNumber(@RequestBody IssueRequest issueRequest) {

		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			LOG.error("Session Expired");
			return 0;
		} else {
			User user = userRepository.findOne(birdnotesUser.getUserDto().getId());

			try {
				return notificationService.getNotificationNumber(user);
			} catch (Exception e) {
				LOG.error("An exception occurred while getting all notifications", e);
				return 0;
			}
		}
	}

	@RequestMapping(value = "updateNotificationStatus", method = RequestMethod.POST)
	public ResponseEntity<String> updateNotificationStatus() {

		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			LOG.error("Session Expired");
			return ResponseEntity.ok().body("Session Expired");
		} else {
			User user = userRepository.findOne(birdnotesUser.getUserDto().getId());

			try {
				notificationService.updateNotificationStatus(user);
				return ResponseEntity.ok().body("notifications status updated");
			} catch (Exception e) {
				LOG.error("An exception occurred while updating notifications status", e);
				return ResponseEntity.ok().body("An exception occurred while updating notifications status");
			}
		}

	}

}
