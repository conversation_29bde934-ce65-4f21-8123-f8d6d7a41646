package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.FREE_QUANTITY_RULE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class FreeQuantityRule implements Serializable {
	
	private static final long serialVersionUID = 1L;
		
	@Id
	@SequenceGenerator(name = Sequences.FREE_QUANTITY_SEQUENCE, sequenceName = Sequences.FREE_QUANTITY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.FREE_QUANTITY_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.NAME)
	private String name;
	
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PRODUCT_FREE_QUANTITY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.FREE_QUANTITY_RULE_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, nullable = false, updatable = false) })
	private Set<Product> products; 
	
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.POTENTIAL_FREE_QUANTITY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.FREE_QUANTITY_RULE_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.POTENTIAL_ID, nullable = false, updatable = false) })
	private List<Potential> potentials;
	
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PROSPECT_TYPE_FREE_QUANTITY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.FREE_QUANTITY_RULE_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_TYPE_ID, nullable = false, updatable = false) })
	private List<ProspectType> prospectTypes;
	

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	


	public Set<Product> getProducts() {
		return products;
	}

	public void setProducts(Set<Product> products) {
		this.products = products;
	}

	public List<Potential> getPotentials() {
		return potentials;
	}

	public void setPotentials(List<Potential> potentials) {
		this.potentials = potentials;
	}
	
	

	public List<ProspectType> getProspectTypes() {
		return prospectTypes;
	}

	public void setProspectTypes(List<ProspectType> prospectTypes) {
		this.prospectTypes = prospectTypes;
	}

	public FreeQuantityRule() {
		super();

	}
}
