package com.intellitech.birdnotes.model.dto;

public class MinimizedChargePlan {
	
	private Long productId;
	private Long specialityId;
	private Integer rank ;
	
	
	public MinimizedChargePlan() {
		super();
	}
	public MinimizedChargePlan(Long productId, Long specialityId, Integer rank) {
		super();
		this.productId = productId;
		this.specialityId = specialityId;
		this.rank = rank;
	}
	public Long getProductId() {
		return productId;
	}
	public void setProductId(Long productId) {
		this.productId = productId;
	}
	public Long getSpecialityId() {
		return specialityId;
	}
	public void setSpecialityId(Long specialityId) {
		this.specialityId = specialityId;
	}
	public Integer getRank() {
		return rank;
	}
	public void setRank(Integer rank) {
		this.rank = rank;
	}

}
