package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.util.List;

import com.intellitech.birdnotes.data.dto.AutomationRuleFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.AutomationRule;
import com.intellitech.birdnotes.model.dto.AutomationRuleDto;

public interface AutomationRuleService {
	AutomationRule saveAutomationRule(AutomationRuleDto automationRuleRequest) throws BirdnotesException;

	void deleteAutomationRule(Long automationRuleId);

	List<AutomationRuleDto> findAll() throws BirdnotesException;

	AutomationRuleFormData getAllDataAutomationRuleForm() throws IOException, BirdnotesException;

	void generatePlanningsFromRules(List<AutomationRule> automationRuleList, Activity activity);

}
