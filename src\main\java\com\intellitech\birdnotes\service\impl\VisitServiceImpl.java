package com.intellitech.birdnotes.service.impl;

import static java.time.temporal.TemporalAdjusters.firstDayOfMonth;
import static java.time.temporal.TemporalAdjusters.lastDayOfMonth;

import java.io.File;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.Year;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.builder.VisitHistoryBuilder;
import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.ReportMessageRequestDto;
import com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto;

import com.intellitech.birdnotes.enumeration.DelegateProspectLocationStatus;
import com.intellitech.birdnotes.enumeration.GroupType;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.enumeration.WorkType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Discount;
import com.intellitech.birdnotes.model.Holiday;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.ConvertVisitToDto;
import com.intellitech.birdnotes.model.convertor.PurchaseOrderToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.RecoveryToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ActivityByPeriod;
import com.intellitech.birdnotes.model.dto.ActivityCalanderRequestDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.CoverageDto;
import com.intellitech.birdnotes.model.dto.FileNamePath;
import com.intellitech.birdnotes.model.dto.GoalSum;
import com.intellitech.birdnotes.model.dto.KeyValueDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.model.dto.MinimizedUserDto;
import com.intellitech.birdnotes.model.dto.MinimizedUserDtoV1;
import com.intellitech.birdnotes.model.dto.ProspectDistribution;
import com.intellitech.birdnotes.model.dto.ProspectDistributionList;
import com.intellitech.birdnotes.model.dto.ProspectDistributionRequest;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectDtoForStatisticTable;
import com.intellitech.birdnotes.model.dto.ProspectMessage;
import com.intellitech.birdnotes.model.dto.RecoveryDto;
import com.intellitech.birdnotes.model.dto.StatisticActivity;
import com.intellitech.birdnotes.model.dto.TotalCountDto;
import com.intellitech.birdnotes.model.dto.VisitAveragePerDayDto;
import com.intellitech.birdnotes.model.dto.VisitCountDto;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryDto;
import com.intellitech.birdnotes.model.dto.VisitHistorySummaryDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryGroupDto;
import com.intellitech.birdnotes.model.dto.VisitKey;
import com.intellitech.birdnotes.model.dto.VisitProspectDto;
import com.intellitech.birdnotes.model.dto.VisitRequestDto;
import com.intellitech.birdnotes.repository.ActivityRepository;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.DiscountRepository;
import com.intellitech.birdnotes.repository.HolidayRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.RecoveryRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ActivityService;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.GoalManagementService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.VisitService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesUtils;

@Service("visitService")
@Transactional
public class VisitServiceImpl implements VisitService {

	@SuppressWarnings("unused")
	private static final Logger LOG = LoggerFactory.getLogger(VisitServiceImpl.class);

	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");

	private VisitRepository visitRepository;

	private UserRepository userRepository;

	private DelegateRepository delegateRepository;

	private UserService userService;

	private PurchaseOrderRepository purchaseOrderRepository;

	private ProspectRepository prospectRepository;

	private ConvertProspectToDto convertProspectToDto;

	private DynamicQueries dynamicQueries;

	private ConfigurationRepository configureRepository;
	
	private HolidayRepository holidayRepository;

	private ActivityService activityService;

	private PlanningRepository planningRepository;

	private ActivityRepository activityRepository;

	private CurrentUser currentUser;

	private RecoveryRepository recoveryRepository;

	private ConvertVisitToDto convertVisitToDto;

	private RecoveryToDtoConvertor recoveryToDtoConvertor;

	private PurchaseOrderToDtoConvertor purchaseOrderToDtoConvertor;

	private WholesalerToDtoConvertor wholesalerToDtoConvertor;

	private ConfigurationService configurationService;
	
	private DiscountRepository discountRepository;

	private ProspectService prospectService;
	private GoalManagementService goalManagementService;
	
	
	@Value("${userPath}")
	private String userImagePath;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${purchaseOrderPath}")
	private String purchaseOrderPath;

	@Value("${generatedDocumentPath}")
	private String generatedDocumentPath;

	@Value("${recoveryPath}")
	private String recoveryPath;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${commentsRatesFilePath}")
	private String commentsRatesFilePath;

	private VisitHistoryBuilder visitHistoryBuilder = new VisitHistoryBuilder();

	@SuppressWarnings("unchecked")
	@Override
	public VisitHistorySummaryDto getVisitHistory(VisitRequestDto visitRequestDto) throws BirdnotesException {
		Date startDate = null;
		Date endDate = null;
		Period period = null;
		try {
			startDate = format.parse(visitRequestDto.getFirstDate());
			endDate = format.parse(visitRequestDto.getLastDate());
		} catch (ParseException e) {
			LOG.error("Error in parsing dates ", e);
		}
		List<VisitHistoryDto> visitsList = getVisitsList(visitRequestDto, startDate, endDate);

		period = BirdnotesUtils.getPeriod(startDate, endDate);

		//comment classification
		List<LabelValueDto> commentRatingAverage = visitRepository.getCommentRatingAverage(startDate, endDate);
		Double sumOfComment = commentRatingAverage.stream().mapToDouble(n -> n.getValue()).sum();
		for(LabelValueDto commentRating : commentRatingAverage){
			commentRating.setValue((commentRating.getValue()/sumOfComment) * 100);
		}		
		
		Map<Long, Float> fillingRateByUser = getFillingRateByUser(visitsList, startDate, endDate);
		
		Map<Long, Float> workindDaysPerUser = getUsersWorkingDays(startDate, endDate);

		List<VisitHistoryGroupDto> groupList = getGroupList(visitsList, fillingRateByUser,workindDaysPerUser, visitRequestDto);
		//first and row == null -> show only the summary table without details
		if(visitRequestDto.isPagination()) {
			if(visitRequestDto.getFirst() == null && visitRequestDto.getRows() == null) {
				visitHistoryBuilder.buildNumberSearchAndGroup(null, groupList);

				visitHistoryBuilder.buildPeriodeDto(period.getDays() + 1, period.getMonths(), period.getYears());
				
				visitHistoryBuilder.buildCommentRatingAverage(commentRatingAverage);
			}//first and row != null -> show only the detail table, summary table is already shown
			else {
				visitHistoryBuilder.buildResultat(visitsList);	
			}
		}else {
			visitHistoryBuilder.buildResultat(visitsList);
		}
		
		return visitHistoryBuilder.getVisitHistorySummaryDto();
	}

	private List<VisitHistoryDto> getVisitsList(VisitRequestDto visitRequestDto, Date startDate, Date endDate) {
		List<VisitsProducts> visitsProducts;
		Long vpCount = 0L;
		List<VisitHistoryDto> visitsList = new ArrayList<>();
		ConfigurationDto config = configurationService.findConfiguration();
		StringBuilder query = new StringBuilder();
		StringBuilder queryCount = new StringBuilder();
		Map<String, Object> parameters;

		query.append("SELECT vp from VisitsProducts vp ");
		queryCount.append("SELECT count(vp) from VisitsProducts vp ");
		Map<String, Object> queryAndParam = queryFiltersAndParameters(visitRequestDto, null);

		parameters = (Map<String, Object>) queryAndParam.get(BirdnotesConstants.VisitHistory.PARAMETERS);

		query.append((StringBuilder) queryAndParam.get(BirdnotesConstants.VisitHistory.QUERY));
		queryCount.append((StringBuilder) queryAndParam.get(BirdnotesConstants.VisitHistory.QUERY));
		query.append(" Order by vp.visit.visitDate, vp.visit.delegate.firstName");

		visitsProducts = dynamicQueries.findVisits(query.toString(), parameters, visitRequestDto.getFirst(),visitRequestDto.getRows());
		vpCount = dynamicQueries.findVisitProductsCount(queryCount.toString(), parameters);
		List<Long> plannedProspectIds = new ArrayList<>();

		if (visitRequestDto.getSelectedUser() == 0) {
			plannedProspectIds = planningRepository.findPlanedProspectIdsByUser(startDate, endDate);
		} else {
			plannedProspectIds = planningRepository.findPlanedProspectIds(visitRequestDto.getSelectedUser(), startDate,
					endDate);
		}

		for (VisitsProducts visitProduct : visitsProducts) {
			VisitHistoryDto visitHistoryDto = null;
			visitHistoryDto = new VisitHistoryDto(visitProduct, config, uploadUrl, userImagePath);
			visitHistoryDto.setVpCount(vpCount);
			setProspect(visitProduct, visitHistoryDto);
			setPurchaseOrder(visitProduct, visitHistoryDto);
			setRecovery(visitProduct, visitHistoryDto);
			setLocation(visitProduct, visitHistoryDto);
			setPurchaseOrderAttachments(visitHistoryDto);
			setRecoveryAttachment(visitHistoryDto);

			// calculate distance
			calculateDistance(visitHistoryDto, (double) config.getAcceptedPointingDistance());
			// highlight planned prospect
			if (plannedProspectIds.contains(visitHistoryDto.getProspectDto().getId())) {
				visitHistoryDto.setProspectPlanned(true);
			} else {
				visitHistoryDto.setProspectPlanned(false);
			}
			visitsList.add(visitHistoryDto);
		}
		
		return visitsList;
	}

	private void setPurchaseOrderAttachments(VisitHistoryDto visitHistoryDto) {
		if (visitHistoryDto.getPurchaseOrder() != null) {
			if (visitHistoryDto.getPurchaseOrder().getAttachmentName() != null
					&& !visitHistoryDto.getPurchaseOrder().getAttachmentName().isEmpty()) {
				FileNamePath fileNamePath = new FileNamePath(visitHistoryDto.getPurchaseOrder().getAttachmentName(),
						uploadUrl + purchaseOrderPath + File.separator + visitHistoryDto.getPurchaseOrder().getId()
								+ File.separator + visitHistoryDto.getPurchaseOrder().getAttachmentName());
				visitHistoryDto.setNameFile(fileNamePath);
			}
			FileNamePath generatedPurchaseOrderPath = new FileNamePath("generatedPurchaseOrder.pdf",
					uploadUrl + generatedDocumentPath + File.separator + visitHistoryDto.getPurchaseOrder().getId()
							+ File.separator + "generatedPurchaseOrder.pdf");
			visitHistoryDto.setGeneratedPurchaseOrder(generatedPurchaseOrderPath);
		}
	}

	private void setRecovery(VisitsProducts visitProduct, VisitHistoryDto visitHistoryDto) {
		if (visitProduct.getPurchaseOrder() != null) {
			List<Recovery> recoveries = recoveryRepository.findByPurchaseOrder(visitProduct.getPurchaseOrder().getId());
			List<RecoveryDto> recoveryDto = new ArrayList<>();
			for (Recovery recovery : recoveries) {
				try {
					recoveryDto.add(recoveryToDtoConvertor.convert(recovery));
				} catch (BirdnotesException e) {
					LOG.error("Exception in convert recovery", e);
				}
			}
			visitHistoryDto.getPurchaseOrder().setRecoveries(recoveryDto);

		}
	}

	private void setPurchaseOrder(VisitsProducts visitProduct, VisitHistoryDto visitHistoryDto) {
		if (visitProduct.getPurchaseOrder() != null) {

			try {

				visitHistoryDto.setPurchaseOrder(purchaseOrderToDtoConvertor.convert(visitProduct.getPurchaseOrder()));
			} catch (BirdnotesException e) {
				LOG.error("Exception in convert purchase order", e);
			}
			try {
				visitHistoryDto.setWholesaler(
						convertProspectToDto.convert(visitProduct.getPurchaseOrder().getWholesaler()));
			} catch (BirdnotesException e) {
				LOG.error("Exception in convert wholesaler", e);
			}
			Discount discount = discountRepository.findByProductAndWholesaler(visitProduct.getProduct(), visitProduct.getPurchaseOrder().getWholesaler());
			if(discount != null) {
				visitHistoryDto.setDiscount(discount.getPercentage());				
			}
			visitHistoryDto.setWholesalerName(visitProduct.getPurchaseOrder().getWholesaler().getFirstName()+ " " +visitProduct.getPurchaseOrder().getWholesaler().getLastName() );
			visitHistoryDto.setOrderPlacementMethod(visitProduct.getPurchaseOrder().getPlacementMethod());

		}
	}

	private void setProspect(VisitsProducts VisitProduct, VisitHistoryDto visitHistoryDto) {
		try {
			visitHistoryDto.setProspectDto(convertProspectToDto.convert(VisitProduct.getVisit().getProspect()));
		} catch (BirdnotesException e) {
			LOG.error("Exception in convert prospect ", e);
		}
		visitHistoryDto.setProspectName(visitHistoryDto.getProspectDto().getFullName());
		visitHistoryDto.setProspectSector(visitHistoryDto.getProspectDto().getSectorDto().getName());
		visitHistoryDto.setProspectLocality(visitHistoryDto.getProspectDto().getLocalityDto().getName());
		visitHistoryDto.setProspectPotential(visitHistoryDto.getProspectDto().getPotentialDto().getName());
		visitHistoryDto.setProspectActivity(visitHistoryDto.getProspectDto().getActivity());

	}

	private void setRecoveryAttachment(VisitHistoryDto visitHistoryDto) {
		if (visitHistoryDto.getPurchaseOrder() != null && visitHistoryDto.getPurchaseOrder().getRecoveries() != null) {
			for (RecoveryDto recoveryDto : visitHistoryDto.getPurchaseOrder().getRecoveries()) {
				if (recoveryDto.getAttachmentName() != null && !recoveryDto.getAttachmentName().isEmpty()) {
					FileNamePath fileNamePath = new FileNamePath(recoveryDto.getAttachmentName(),
							uploadUrl + recoveryPath + File.separator + recoveryDto.getId() + File.separator
									+ recoveryDto.getAttachmentName());
					recoveryDto.setNameFile(fileNamePath);
				}
			}
		}
	}

	private void setLocation(VisitsProducts visitProduct, VisitHistoryDto visitHistoryDto) {
		Location location = visitProduct.getVisit().getLocation();
		if (location != null) {
			LocationDto locationDto = new LocationDto();
			locationDto.setDate(location.getDate());
			locationDto.setLatitude(location.getLatitude());
			locationDto.setLongitude(location.getLongitude());
			locationDto.setDelegate(location.getDelegate());
			visitHistoryDto.setLocation(locationDto);
		}
	}

	// Calculate distance between location of delegate and prospect
	public void calculateDistance(VisitHistoryDto visitHistoryDto, Double acceptedDistance) {
		if (visitHistoryDto.getLocation() != null && visitHistoryDto.getLocation().getLatitude() != null
				&& visitHistoryDto.getLocation().getLatitude() != 0
				&& visitHistoryDto.getProspectDto().getLatitude() != null
				&& visitHistoryDto.getProspectDto().getLatitude() != 0) {
			double distance = BirdnotesUtils.distanceBetweenTwoPositions(
					visitHistoryDto.getProspectDto().getLongitude(), visitHistoryDto.getProspectDto().getLatitude(),
					visitHistoryDto.getLocation().getLongitude(), visitHistoryDto.getLocation().getLatitude());
			visitHistoryDto.setLocationDistance(distance);

			if (distance < acceptedDistance) {
				visitHistoryDto.setLocationValidation(DelegateProspectLocationStatus.VALID.toString());
			} else {
				visitHistoryDto.setLocationValidation(DelegateProspectLocationStatus.NOT_VALID.toString());
			}

		} else {
			visitHistoryDto.setLocationValidation(DelegateProspectLocationStatus.MISSING_DATA.toString());
		}
	}

	private Map<Long, Float> getFillingRateByUser(List<VisitHistoryDto> visitsList, Date startDate, Date endDate) {
		Map<Long, Set<String>> visitDaysByUsers = new HashMap<>();
		Map<Long, Float> fillingRateByUser = null;
		for (VisitHistoryDto visitHistoryDto : visitsList) {
			Long userId = visitHistoryDto.getDelegateId();
			if (visitDaysByUsers.get(userId) == null) {
				Set<String> visitDates = new HashSet<>();
				visitDaysByUsers.put(userId, visitDates);
			}
			visitDaysByUsers.get(userId).add(visitHistoryDto.getVisitDate());
		}
		fillingRateByUser = reportFillingRate(visitDaysByUsers, startDate, endDate);

		return fillingRateByUser;

	}

	private Map<Long, Float> reportFillingRate(Map<Long, Set<String>> visitDaysByUsers, Date startDate, Date endDate) {
		Map<Long, Float> usersWorkingDays = getUsersWorkingDays(startDate, endDate);
		Map<Long, Float> fillingRateByUser = new HashMap<>();
		for (Map.Entry<Long, Set<String>> entry : visitDaysByUsers.entrySet()) {
			if (usersWorkingDays.containsKey(entry.getKey())) {
				float userWorkingDays = usersWorkingDays.get(entry.getKey());
				if (userWorkingDays == 0) {
					userWorkingDays = entry.getValue().size();
				}
				float fillingRate = (entry.getValue().size() / userWorkingDays) * 100;
				fillingRateByUser.put(entry.getKey(), fillingRate);
			}

		}
		return fillingRateByUser;
	}

	@Override
	 public Map<Long, Float> getUsersWorkingDays(Date startDate, Date endDate) {
		
		Map<Long, Float> usersWorkingDaysMap = new HashMap<Long, Float>();
		short holidays = getHolidayDaysCount(startDate, endDate);
		int totalBusinessDays = 0;

		List<Long> usersIds = userService.getSubUsersIds();
		List<StatisticActivity> usersActivities = activityRepository.getActivitiesGroupByUser(startDate, endDate, usersIds);
		Map<Long, Long> usersActivitiesMap = usersActivities.stream()
				.collect(Collectors.toMap(StatisticActivity::getUserId, StatisticActivity::getHourNumber));

		for (Long usersId : usersIds) {

			Delegate delegate = delegateRepository.findDelegateByUserId(usersId);
			if (delegate != null) {
				if (delegate.getWorkingDaysPerWeek() == 5) {
					totalBusinessDays = BirdnotesUtils.getWorkingDaysBetweenTwoDatesIncludingTheEndDate(startDate,
							endDate, false);
				} else {
					totalBusinessDays = BirdnotesUtils.getWorkingDaysBetweenTwoDatesIncludingTheEndDate(startDate,
							endDate, true);
				}
				float activityDays = usersActivitiesMap.get(usersId) != null
						? activityDays = usersActivitiesMap.get(usersId) / 8
						: 0;

				float totalDays = totalBusinessDays - holidays - activityDays;
				
				usersWorkingDaysMap.put(delegate.getId(), totalDays);
			}
		}

		return usersWorkingDaysMap;

	}

	@Override
	public List<VisitDto> getVisitReport(ReportMessageRequestDto messageRequest) {
		List<VisitDto> visitDtoList = new ArrayList<>();
		List<Visit> visits = visitRepository.findVisitReport(messageRequest.getStartDate(),
				messageRequest.getLastDate(), messageRequest.getUserId());
		for (Visit visit : visits) {
			try {
				VisitDto visitDto = convertVisitToDto.convert(visit);
				Prospect prospect = prospectRepository.findById(visit.getProspect().getId());
				visitDto.setProspectDto(convertProspectToDto.convert(prospect));
				visitDtoList.add(visitDto);
			} catch (BirdnotesException e) {
				LOG.error("Error in convertion visit to dto ", e);
				;
			}
		}
		return visitDtoList;
	}

	private short getHolidayDaysCount(Date startDate, Date endDate) {
		List<Holiday> holidays = holidayRepository.findByDate(startDate, endDate);
		short nbHolidays = 0;
		int year = Year.now().getValue();
		for(Holiday holiday : holidays) {
			if(holiday.getHolidayType().toString().equals("PUNCTUAL") ) {
				nbHolidays ++;
			}else {
				try {
					Date holidayDate = format.parse(Integer.toString(holiday.getDay())+"/"+Integer.toString(holiday.getMonth())+"/"+Integer.toString(year));
					if (holidayDate.after(startDate) && holidayDate.before(endDate)) {
						nbHolidays++;
					}
					
				} catch (ParseException e) {
					LOG.error("Exception in parse getHolidayDaysCount", e);
				}
			}
		}
		return nbHolidays;
		/*if (configureRepository.findById(1).getHolidays() != null) {
			String[] holidaysList = configureRepository.findById(1).getHolidays().split("\n");
			for (String holidayString : holidaysList) {
				Date holidayDate;
				try {
					holidayDate = format.parse(holidayString);
					if (holidayDate.after(startDate) && holidayDate.before(endDate)) {
						nbHolidays++;
					}
				} catch (ParseException e) {
					LOG.error("Exception in parse getHolidayDaysCount", e);
				}

			}
		}
		return nbHolidays;*/
	}

	private CoverageDto buildCoverageDto(String name, int totalBusinessDays, float totalConnexeActivities,
			short holidays, float prospectsTotalCount, float cycle, float workingDaysPerWeek, long visitExecuted,
			float dailyTarget) {

		CoverageDto coverageDto = new CoverageDto();
		coverageDto.setName(name);
		coverageDto.setTotalBusinessDays(totalBusinessDays);
		coverageDto.setTotalConnexeActivities(totalConnexeActivities);
		coverageDto.setHolidays(holidays);
		coverageDto.setDaysWorked(totalBusinessDays - (totalConnexeActivities + holidays));
		coverageDto.setProspectsTotalCount(prospectsTotalCount);
		coverageDto.setCycle(cycle);
		coverageDto.setWorkingDaysPerWeek(workingDaysPerWeek);
		coverageDto.setDailyTarget(dailyTarget);
		coverageDto.setVisitExecuted(visitExecuted);
		coverageDto.setVisitObjectif(coverageDto.getDailyTarget() * coverageDto.getDaysWorked());
		coverageDto.setCoverage(visitExecuted / coverageDto.getVisitObjectif());
		coverageDto.setVisitAverage(visitExecuted / coverageDto.getDaysWorked());
		return coverageDto;
	}

	@SuppressWarnings("unchecked")
	@Override
	public VisitHistorySummaryDto getCoverage(VisitRequestDto coverageRequestDto) throws BirdnotesException {
		List<CoverageDto> coverageList = new ArrayList<>();
		float prospectsTotalCount = 0L;
		float workingDaysPerWeek = 5;
		int totalBusinessDays = 0;
		int cycle = 4;
		float totalConnexeActivities = 0;
		Date startDate = null;
		Date endDate = null;
		int totalWorkingDays = 0;

		CoverageDto coverageDto = null;
		ProspectDistributionList prospectDistributionList = null;
		List<PlanningObjectiveCountDto> planifiedProspects = null;
		List<GoalSum> goalSumList = new ArrayList<>();
		ProspectDistributionRequest prospectDistributionRequest = new ProspectDistributionRequest();

		setProspectDistributionRequest(coverageRequestDto, prospectDistributionRequest);

		try {
			startDate = format.parse(coverageRequestDto.getFirstDate());
			endDate = format.parse(coverageRequestDto.getLastDate());
		} catch (ParseException e) {
			LOG.error("Exception in parse last date when getHolidayDaysCount", e);
		}
		Period period = BirdnotesUtils.getPeriod(startDate, endDate);
		
		short holidays = getHolidayDaysCount(startDate, endDate);

		List<VisitHistoryGroupDto> visitsGroupedAndFiltred = getVisitCountGroupedByFilter(coverageRequestDto, startDate,
				endDate);

		if (coverageRequestDto.getSelectedUser() == 0) {

			List<Delegate> delegates = delegateRepository.getAllDelegates();
			for (Delegate delegate : delegates) {
				totalWorkingDays = delegate.getWorkingDaysPerWeek();
				if (delegate.getCycle() != null) {
					cycle = delegate.getCycle();
				} else {
					cycle = 4;
				}
				totalBusinessDays = getTotalBusinessDays(delegate, totalWorkingDays, startDate, endDate);
				getTotalConnexeActivities(coverageRequestDto, delegate, totalConnexeActivities);

			}
		}else{
			Delegate delegate = null;
			if(coverageRequestDto.getSelectedUser() == 0) {
				delegate = delegateRepository.findDelegateByUserId(visitsGroupedAndFiltred.get(0).getId());
			}else {
				delegate = delegateRepository.findById(coverageRequestDto.getSelectedUser());
			}
			totalBusinessDays = getTotalBusinessDays(delegate, totalWorkingDays, startDate, endDate);
			getTotalConnexeActivities(coverageRequestDto, delegate, totalConnexeActivities);
			totalConnexeActivities = totalConnexeActivities / visitsGroupedAndFiltred.size();
			totalWorkingDays = delegate.getWorkingDaysPerWeek();
			if (delegate.getCycle() != null) {
				cycle = delegate.getCycle();
			} else {
				cycle = 4;
			}
		}

		for (VisitHistoryGroupDto visitCountGroup : visitsGroupedAndFiltred) {
			if (coverageRequestDto.getSelectedGroup().equals(GroupType.DELEGUE.getId().longValue())
					|| coverageRequestDto.getSelectedUser() != 0) {
				Delegate delegate = null;
				if(coverageRequestDto.getSelectedUser() == 0) {
					delegate = delegateRepository.findById(visitCountGroup.getId());
				}else {
					delegate = delegateRepository.findById(coverageRequestDto.getSelectedUser());
				}
				
				totalBusinessDays = getTotalBusinessDays(delegate, totalWorkingDays, startDate, endDate);
				getTotalConnexeActivities(coverageRequestDto, delegate, totalConnexeActivities);

			}

			if (coverageRequestDto.getCoverageTargetType().equals("portfolioTarget")) {
				prospectDistributionList = prospectService.getProspectsDistribution(prospectDistributionRequest);
				coverageDto = getCoverageByPortfolio(prospectDistributionList, prospectsTotalCount, visitCountGroup,
						totalBusinessDays, totalConnexeActivities, holidays, cycle, workingDaysPerWeek);

			} else if (coverageRequestDto.getCoverageTargetType().equals("plannedTarget")) {
				planifiedProspects = getPlanifiedProspects(startDate, endDate,
						coverageRequestDto.getSelectedGroup().intValue(), coverageRequestDto);
				coverageDto = getCoverageByPlanning(planifiedProspects, prospectsTotalCount, visitCountGroup,
						totalBusinessDays, totalConnexeActivities, holidays, cycle, workingDaysPerWeek);

			} else if (coverageRequestDto.getCoverageTargetType().equals("goalTarget")) {
				goalSumList = goalManagementService.getGoalsSum(coverageRequestDto);
				coverageDto = getCoverageByGoal(goalSumList, prospectsTotalCount, visitCountGroup, totalBusinessDays,
						totalConnexeActivities, holidays, cycle, workingDaysPerWeek);
			}
			coverageDto.setVisitObjectif(coverageDto.getDailyTarget() * coverageDto.getDaysWorked());
			coverageList.add(coverageDto);
		}

		Collections.sort(coverageList);
		visitHistoryBuilder.buildNumberSearchAndGroup(null, coverageList);
		visitHistoryBuilder.buildResultat(null);
		visitHistoryBuilder.buildPeriodeDto(period.getDays() + 1, period.getMonths(), period.getYears());
		return visitHistoryBuilder.getVisitHistorySummaryDto();
	}

	private void setProspectDistributionRequest(VisitRequestDto coverageRequestDto,
			ProspectDistributionRequest prospectDistributionRequest) {
		prospectDistributionRequest.setSelectedGroup(coverageRequestDto.getSelectedGroup());
		prospectDistributionRequest.setSelectedUsers(userService.getSubUsersIds());
		if (coverageRequestDto.getSelectedUser() != null && coverageRequestDto.getSelectedUser() != 0) {
			List<Long> selectedUsers = new ArrayList<>();
			selectedUsers.add(coverageRequestDto.getSelectedUser());
			prospectDistributionRequest.setSelectedUsers(selectedUsers);
		}

		if (coverageRequestDto.getSelectedSector() != null && coverageRequestDto.getSelectedSector() != 0) {
			List<Long> selectedSectors = new ArrayList<>();
			selectedSectors.add(coverageRequestDto.getSelectedSector());
			prospectDistributionRequest.setSelectedSectors(selectedSectors);
		}

		if (coverageRequestDto.getSelectedLocality() != null && coverageRequestDto.getSelectedLocality() != 0) {
			List<Long> selectedLocalities = new ArrayList<>();
			selectedLocalities.add(coverageRequestDto.getSelectedLocality());
			prospectDistributionRequest.setSelectedLocalities(selectedLocalities);
		}

		if (coverageRequestDto.getSelectedActivity() != null && !coverageRequestDto.getSelectedActivity().equals("")) {
			List<String> selectedActivities = new ArrayList<>();
			selectedActivities.add(coverageRequestDto.getSelectedActivity());
			prospectDistributionRequest.setSelectedActivities(selectedActivities);
		}

		if (coverageRequestDto.getSelectedPotential() != null && coverageRequestDto.getSelectedPotential() != 0) {
			List<Long> selectedPotentials = new ArrayList<>();
			selectedPotentials.add(coverageRequestDto.getSelectedPotential());
			prospectDistributionRequest.setSelectedPotentials(selectedPotentials);
		}

		if (coverageRequestDto.getSelectedSpeciality() != null && coverageRequestDto.getSelectedSpeciality() != 0) {
			List<Long> selectedSpecialities = new ArrayList<>();
			selectedSpecialities.add(coverageRequestDto.getSelectedSpeciality());
			prospectDistributionRequest.setSelectedSpecialities(selectedSpecialities);
		}

		prospectDistributionRequest.setDistribution(true);

	}

	private CoverageDto getCoverageByPortfolio(ProspectDistributionList prospectDistributionList, float prospectsTotalCount,
			VisitHistoryGroupDto visitCountGroup, int totalBusinessDays,
			float totalConnexeActivities, short holidays, int cycle, float workingDaysPerWeek) {
		for (ProspectDistribution prospectDistribution : prospectDistributionList.getProspectCountBySector()) {
			if (prospectDistribution.getGroupOfSearchValue().equals(visitCountGroup.getName())) {
				prospectsTotalCount = prospectDistribution.getCount();
				break;
			}
		}
		
		return buildCoverageDto(visitCountGroup.getName(), totalBusinessDays, totalConnexeActivities, holidays,
				prospectsTotalCount, cycle, workingDaysPerWeek, visitCountGroup.getCount().longValue(),
				prospectsTotalCount / cycle / workingDaysPerWeek);

	}

	private CoverageDto getCoverageByPlanning(List<PlanningObjectiveCountDto> planifiedProspects, float prospectsTotalCount,
			VisitHistoryGroupDto visitCountGroup, int totalBusinessDays,
			float totalConnexeActivities, short holidays, int cycle, float workingDaysPerWeek) {
		for (PlanningObjectiveCountDto planifiedProspect : planifiedProspects) {

			if (planifiedProspect.getGroupName().equals(visitCountGroup.getName())) {
				prospectsTotalCount = planifiedProspect.getCount();
				break;
			}
		}
		return buildCoverageDto(visitCountGroup.getName(), totalBusinessDays, totalConnexeActivities, holidays,
				prospectsTotalCount, cycle, workingDaysPerWeek, visitCountGroup.getCount().longValue(),
				(float) prospectsTotalCount / workingDaysPerWeek);

	}

	private CoverageDto getCoverageByGoal(List<GoalSum> goalSumList, float prospectsTotalCount, 
			VisitHistoryGroupDto visitCountGroup, int totalBusinessDays, float totalConnexeActivities, 
			short holidays, int cycle, float workingDaysPerWeek) {
		for (GoalSum goalSum : goalSumList) {
			if (goalSum.getGroupName() != null) {
				if (goalSum.getGroupName().equals(visitCountGroup.getName())) {
					prospectsTotalCount = goalSum.getValue();// workingDaysPerWeek * totalBusinessDays;
					break;
				}
			}
		}
		return buildCoverageDto(visitCountGroup.getName(), totalBusinessDays, totalConnexeActivities, holidays,
				prospectsTotalCount, cycle, workingDaysPerWeek, visitCountGroup.getCount().longValue(),
				prospectsTotalCount / cycle / workingDaysPerWeek);

	}

	private int getTotalBusinessDays(Delegate delegate, int totalWorkingDays, Date startDate, Date endDate) {
		int totalBusinessDays = 0;
		if (totalWorkingDays == 5) {
			totalBusinessDays = BirdnotesUtils.getWorkingDaysBetweenTwoDatesIncludingTheEndDate(startDate, endDate,
					false);
		} else {
			totalBusinessDays = BirdnotesUtils.getWorkingDaysBetweenTwoDatesIncludingTheEndDate(startDate, endDate,
					true);
		}
		return totalBusinessDays;
	}

	private void getTotalConnexeActivities(VisitRequestDto coverageRequestDto, Delegate delegate,
			float totalConnexeActivities) {
		ActivityCalanderRequestDto activityCalanderRequestDto = new ActivityCalanderRequestDto(
				coverageRequestDto.getFirstDate(), coverageRequestDto.getLastDate(), delegate.getId());
		List<VisitHistoryGroupDto> activityList = null;

		try {
			activityList = activityService.getStatisticActivity(activityCalanderRequestDto)
					.getVisitHistoryGroupDtoList();
		} catch (BirdnotesException e) {
			LOG.error("Exception in getStatisticActivity ", e);
		}
		if (activityList != null) {
			for (VisitHistoryGroupDto item : activityList) {
				totalConnexeActivities += item.getCount();
			}
		} else {
			totalConnexeActivities = 0;
		}
	}

	private Map<String, Object> queryFiltersAndParameters(VisitRequestDto visitRequestDto, String groupByWhereTable) {

		Map<String, Object> parametersAndQueryBuilder = new HashMap<>();
		Map<String, Object> parameters = new HashMap<>();
		StringBuilder query = new StringBuilder();
		StringBuilder whereQuery = new StringBuilder();
		whereQuery.append(
				" where (date(" + groupByWhereTable + ".visitDate) between date(:initialDate) and date(:finalDate))");
		query.append(" where (date(vp.visit.visitDate) between date(:initialDate) and date(:finalDate))");

		try {
			parameters.put(BirdnotesConstants.QueryBuild.INITIAL_DATE, format.parse(visitRequestDto.getFirstDate()));
		} catch (ParseException e) {
			LOG.error("Exception when parse firstDate parametre", e);
		}
		try {
			parameters.put(BirdnotesConstants.QueryBuild.FINAL_DATE, format.parse(visitRequestDto.getLastDate()));
		} catch (ParseException e) {
			LOG.error("Exception when parse lastDate parametre", e);
		}
		checkWithSelectUser(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false,
				visitRequestDto.isOrder());
		checkWithSelectProspect(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false);
		checkWithSelectSector(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false);
		checkWithSelectedLocality(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false);
		checkWithSelectedActivity(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false);
		checkWithSelectedPotential(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false);
		checkWithSelectedSpeciality(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, false);
		checkWithSelectedProduct(visitRequestDto, query, whereQuery, parameters);
		checkWithSelectedProductSatisfaction(visitRequestDto, query, whereQuery, parameters);
		checkWithSelectedPresentationOrder(visitRequestDto, query, whereQuery, parameters);
		checkWithSelectedCommentRating(visitRequestDto, query, whereQuery, parameters);
		checkWithComment(visitRequestDto, query, whereQuery, parameters);
		parametersAndQueryBuilder.put(BirdnotesConstants.VisitHistory.QUERY, query);
		parametersAndQueryBuilder.put(BirdnotesConstants.VisitHistory.WHERE_QUERY, whereQuery);
		parametersAndQueryBuilder.put(BirdnotesConstants.VisitHistory.PARAMETERS, parameters);

		return parametersAndQueryBuilder;

	}

	private Map<String, Object> planifiedProspectQueryFiltersAndParameters(VisitRequestDto visitRequestDto,
			String groupByWhereTable) {

		Map<String, Object> parametersAndQueryBuilder = new HashMap<>();
		Map<String, Object> parameters = new HashMap<>();
		StringBuilder query = new StringBuilder();
		StringBuilder whereQuery = new StringBuilder();
		whereQuery.append(
				" where (date(" + groupByWhereTable + ".date) between date(:initialDate) and date(:finalDate))");

		query.append(" where (date(p.date) between date(:initialDate) and date(:finalDate))");

		try {
			parameters.put(BirdnotesConstants.QueryBuild.INITIAL_DATE, format.parse(visitRequestDto.getFirstDate()));
		} catch (ParseException e) {
			LOG.error("Exception when parse firstDate parametre", e);
		}
		try {
			parameters.put(BirdnotesConstants.QueryBuild.FINAL_DATE, format.parse(visitRequestDto.getLastDate()));
		} catch (ParseException e) {
			LOG.error("Exception when parse lastDate parametre", e);
		}
		checkWithSelectUser(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true, false);
		checkWithSelectProspect(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true);
		checkWithSelectSector(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true);
		checkWithSelectedLocality(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true);
		checkWithSelectedActivity(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true);
		checkWithSelectedPotential(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true);
		checkWithSelectedSpeciality(visitRequestDto, query, whereQuery, groupByWhereTable, parameters, true);
		checkWithSelectedProduct(visitRequestDto, query, whereQuery, parameters);
		checkWithSelectedProductSatisfaction(visitRequestDto, query, whereQuery, parameters);
		checkWithSelectedPresentationOrder(visitRequestDto, query, whereQuery, parameters);
		parametersAndQueryBuilder.put(BirdnotesConstants.VisitHistory.QUERY, query);
		parametersAndQueryBuilder.put(BirdnotesConstants.VisitHistory.WHERE_QUERY, whereQuery);
		parametersAndQueryBuilder.put(BirdnotesConstants.VisitHistory.PARAMETERS, parameters);

		return parametersAndQueryBuilder;

	}

	private List<PlanningObjectiveCountDto> getPlanifiedProspects(Date firstDate, Date lastDate, int groupBy,
			VisitRequestDto visitRequestDto) throws BirdnotesException {

		StringBuilder query = new StringBuilder();
		query.append("SELECT ");
		Map<String, Object> queryAndParam = planifiedProspectQueryFiltersAndParameters(visitRequestDto, "p");
		Map<String, Object> parameters;
		List<PlanningObjectiveCountDto> planningObjectiveCountDto = new ArrayList<>();
		parameters = (Map<String, Object>) queryAndParam.get("parameters");

		if (groupBy == GroupType.DELEGUE.getId() || groupBy == 0) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_DELEGATE_ID
					+ ", CONCAT(p.delegate.firstName,' ',p.delegate.lastName) , "
					+ "count(p.delegate.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(" GROUP BY CONCAT(p.delegate.firstName,' ',p.delegate.lastName), p"
					+ BirdnotesConstants.QueryBuild.PLANNING_DELEGATE_ID);
		} else if (groupBy == GroupType.PROSPECT.getId()) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_PROSPECT_ID
					+ ", CONCAT(p.prospect.firstName,' ',p.prospect.lastName) , "
					+ "count(p.prospect.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(" GROUP BY CONCAT(p.prospect.firstName,' ',p.prospect.lastName), p"
					+ BirdnotesConstants.QueryBuild.PLANNING_PROSPECT_ID);
		} else if (groupBy == GroupType.SECTOR.getId()) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_SECTOR_ID + ", p.prospect.sector.name , "
					+ "count(p.prospect.sector.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(" GROUP BY p.prospect.sector.name, p" + BirdnotesConstants.QueryBuild.PLANNING_SECTOR_ID);
		} else if (groupBy == GroupType.LOCALITY.getId()) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_LOCALITY_ID + ", p.prospect.locality.name , "
					+ "count(p.prospect.locality.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(" GROUP BY p.prospect.locality.name, p" + BirdnotesConstants.QueryBuild.PLANNING_LOCALITY_ID);

		} else if (groupBy == GroupType.ACTIVITY.getId()) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_ACTIVITY + ", count(p.prospect.activity)) "
					+ "from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(" GROUP BY p" + BirdnotesConstants.QueryBuild.PLANNING_ACTIVITY);

		} else if (groupBy == GroupType.POTENTIAL.getId()) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_POTENTIAL_ID + ", p.prospect.potential.name , "
					+ "count(p.prospect.potential.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(
					" GROUP BY p.prospect.potential.name, p" + BirdnotesConstants.QueryBuild.PLANNING_POTENTIAL_ID);

		} else if (groupBy == GroupType.SPECIALITY.getId()) {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_SPECIALITY_ID + ", p.prospect.speciality.name , "
					+ "count(p.prospect.speciality.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(
					" GROUP BY p.prospect.speciality.name, p" + BirdnotesConstants.QueryBuild.PLANNING_SPECIALITY_ID);

		} /*
			 * else if (groupBy == GroupType.PRODUCT.getId() ) {
			 * query.append("GROUP BY"+BirdnotesConstants.QueryBuild.USER_ID); }
			 */
		else {
			query.append("new com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto(p"
					+ BirdnotesConstants.QueryBuild.PLANNING_DELEGATE_ID
					+ ", CONCAT(p.delegate.firstName,' ',p.delegate.lastName) , "
					+ "count(p.delegate.id)) from Planning p inner join p.prospect pro ");
			query.append((StringBuilder) queryAndParam.get("query"));
			query.append(" GROUP BY CONCAT(p.delegate.firstName,' ',p.delegate.lastName), p"
					+ BirdnotesConstants.QueryBuild.PLANNING_DELEGATE_ID);
		}
		planningObjectiveCountDto = dynamicQueries.findPlanifiedProspects(query.toString(), parameters);
		/*
		 * List<PlanningDto> planifiedProspectsDto = new ArrayList<>() ; for (Planning
		 * planning : planifiedProspects) {
		 * planifiedProspectsDto.add(convertPlanningToDto.convert(prospect)); }
		 */
		return planningObjectiveCountDto;
	}

	private void checkWithSelectedProductSatisfaction(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, Map<String, Object> parameters) {
		if (visitRequestDto.getSelectedProductSatisfaction() != null
				&& visitRequestDto.getSelectedProductSatisfaction() != -1) {
			query.append(" and vp.smily = :smily");
			whereQuery.append("");
			parameters.put("smily", visitRequestDto.getSelectedProductSatisfaction());
		}
	}

	private void checkWithSelectedCommentRating(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, Map<String, Object> parameters) {

		if (visitRequestDto.getSelectedCommentRating() != null
				&& !visitRequestDto.getSelectedCommentRating().isEmpty()) {
			query.append(" and vp.commentRating = :commentRating");
			whereQuery.append("");
			parameters.put("commentRating", visitRequestDto.getSelectedCommentRating());
		}
	}
	
	private void checkWithComment(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, Map<String, Object> parameters) {

		if (visitRequestDto.getComment() != null
				&& !visitRequestDto.getComment().isEmpty()) {
			query.append(" and (vp.comment = :comment OR vp.visit.generalNote = :comment ) ");
			whereQuery.append("");
			parameters.put("comment", visitRequestDto.getComment());
		}
	}

	private void checkWithSelectedPresentationOrder(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, Map<String, Object> parameters) {
		if (visitRequestDto.getSelectedPresentationOrder() != null
				&& visitRequestDto.getSelectedPresentationOrder() != 0) {
			query.append(" and vp.rank = :rank");
			whereQuery.append("");
			parameters.put("rank", visitRequestDto.getSelectedPresentationOrder());
		}
	}

	private void checkWithSelectedProduct(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, Map<String, Object> parameters) {
		if (visitRequestDto.getSelectedProduct() != null && visitRequestDto.getSelectedProduct() != 0) {
			query.append(" and vp.product.id = :productId");
			whereQuery.append("");
			parameters.put(BirdnotesConstants.QueryBuild.PRODUCT_ID, visitRequestDto.getSelectedProduct());
		}
	}

	private void checkWithSelectedSpeciality(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, String groupByWhereTable, Map<String, Object> parameters, boolean planning) {
		if (visitRequestDto.getSelectedSpeciality() != null && visitRequestDto.getSelectedSpeciality() != 0) {
			if (planning == false) {
				query.append(" and vp.visit.prospect.speciality.id = :specialityId");
				whereQuery.append(BirdnotesConstants.QueryBuild.AND + groupByWhereTable
						+ ".prospect.speciality.id = :specialityId");
				parameters.put(BirdnotesConstants.QueryBuild.SPECIALTY_ID, visitRequestDto.getSelectedSpeciality());
			} else {
				query.append(" and p.prospect.speciality.id = :specialityId");
				whereQuery.append(BirdnotesConstants.QueryBuild.AND + groupByWhereTable
						+ ".prospect.speciality.id = :specialityId");
				parameters.put(BirdnotesConstants.QueryBuild.SPECIALTY_ID, visitRequestDto.getSelectedSpeciality());
			}
		}

	}

	private void checkWithSelectedPotential(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, String groupByWhereTable, Map<String, Object> parameters, boolean planning) {
		if (visitRequestDto.getSelectedPotential() != null && visitRequestDto.getSelectedPotential() != 0) {
			if (planning == false) {
				query.append(" and vp.visit.prospect.potential.id = :potential");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.potential.id = :potential");
				parameters.put(BirdnotesConstants.QueryBuild.POTENTIAL, visitRequestDto.getSelectedPotential());
			} else {
				query.append(" and p.prospect.potential.id = :potential");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.potential.id = :potential");
				parameters.put(BirdnotesConstants.QueryBuild.POTENTIAL, visitRequestDto.getSelectedPotential());
			}
		}

	}

	private void checkWithSelectedActivity(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, String groupByWhereTable, Map<String, Object> parameters, boolean planning) {
		if (visitRequestDto.getSelectedActivity() != null && !"".equals(visitRequestDto.getSelectedActivity())) {
			if (planning == false) {
				query.append(" and vp.visit.prospect.activity = :activity");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.activity = :activity");
				parameters.put(BirdnotesConstants.QueryBuild.ACTIVITY, visitRequestDto.getSelectedActivity());
			} else {
				query.append(" and p.prospect.activity = :activity");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.activity = :activity");
				parameters.put(BirdnotesConstants.QueryBuild.ACTIVITY, visitRequestDto.getSelectedActivity());
			}
		}

	}

	private void checkWithSelectedLocality(VisitRequestDto visitRequestDto, StringBuilder query,
			StringBuilder whereQuery, String groupByWhereTable, Map<String, Object> parameters, boolean planning) {
		if (visitRequestDto.getSelectedLocality() != null && visitRequestDto.getSelectedLocality() != 0) {
			if (planning == false) {
				query.append(" and vp.visit.prospect.locality.id = :localityId");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.locality.id = :localityId");
				parameters.put(BirdnotesConstants.QueryBuild.LOCALITY_ID, visitRequestDto.getSelectedLocality());
			} else {
				query.append(" and p.prospect.locality.id = :localityId");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.locality.id = :localityId");
				parameters.put(BirdnotesConstants.QueryBuild.LOCALITY_ID, visitRequestDto.getSelectedLocality());
			}
		}
	}

	private void checkWithSelectSector(VisitRequestDto visitRequestDto, StringBuilder query, StringBuilder whereQuery,
			String groupByWhereTable, Map<String, Object> parameters, boolean planning) {
		if (visitRequestDto.getSelectedSector() != null && visitRequestDto.getSelectedSector() != 0) {
			if (planning == false) {
				query.append(" and vp.visit.prospect.sector.id = :sectorId");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.sector.id = :sectorId");
				parameters.put(BirdnotesConstants.QueryBuild.SECTOR_ID, visitRequestDto.getSelectedSector());
			} else {
				query.append(" and p.prospect.sector.id = :sectorId");
				whereQuery.append(
						BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.sector.id = :sectorId");
				parameters.put(BirdnotesConstants.QueryBuild.SECTOR_ID, visitRequestDto.getSelectedSector());
			}
		}

	}

	private void checkWithSelectProspect(VisitRequestDto visitRequestDto, StringBuilder query, StringBuilder whereQuery,
			String groupByWhereTable, Map<String, Object> parameters, boolean planning) {
		if (visitRequestDto.getSelectedProspect() != null && visitRequestDto.getSelectedProspect() != 0) {
			if (planning == false) {
				query.append(" and vp.visit.prospect.id = :prospectId");
				whereQuery.append(BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.id = :prospectId");
				parameters.put(BirdnotesConstants.QueryBuild.PROSPECT_ID, visitRequestDto.getSelectedProspect());
			} else {
				query.append(" and p.prospect.id = :prospectId");
				whereQuery.append(BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".prospect.id = :prospectId");
				parameters.put(BirdnotesConstants.QueryBuild.PROSPECT_ID, visitRequestDto.getSelectedProspect());
			}
		}
	}

	private void checkWithSelectUser(VisitRequestDto visitRequestDto, StringBuilder query, StringBuilder whereQuery,
			String groupByWhereTable, Map<String, Object> parameters, boolean planning, boolean order) {

		if (visitRequestDto.getSelectedUser() != null && visitRequestDto.getSelectedUser() != 0) {
			if (planning == false) {
				query.append(" and vp.visit.delegate.id = :delegateId");
				whereQuery.append(BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".delegate.id = :delegateId");
				parameters.put(BirdnotesConstants.QueryBuild.DELEGATE_ID, visitRequestDto.getSelectedUser());
			} else {
				query.append(" and p.delegate.id = :delegateId");
				whereQuery.append(BirdnotesConstants.QueryBuild.AND + groupByWhereTable + ".delegate.id = :delegateId");
				parameters.put(BirdnotesConstants.QueryBuild.DELEGATE_ID, visitRequestDto.getSelectedUser());
			}
		} else {
			if (planning) {

				query.append(" and p.delegate.user.id in (:subUsers)");
				parameters.put(BirdnotesConstants.QueryBuild.SUB_USERS, userService.getSubUsersIds());

			} else {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				Long currentUserId = birdnotesUser.getUserDto().getId();
				Prospect prospect = prospectRepository.findWholesalerByprospect(currentUserId);
				if (prospect == null || prospect.getAppUser() == null) {

					query.append(" and vp.visit.delegate.user.id in (:subUsers)");
					parameters.put(BirdnotesConstants.QueryBuild.SUB_USERS, userService.getSubUsersIds());
				} else {

					query.append(" and (vp.purchaseOrder.wholesaler.id =:wholesalerId)");
					// parameters.put(BirdnotesConstants.QueryBuild.SUB_USERS,
					// userService.getSubUsersIds());
					parameters.put(BirdnotesConstants.QueryBuild.WHOLESALER_ID, prospect.getWholesaler().getId());
				}

			}

		}

	}

	/**
	 * calcul the period between the first and the last date
	 * 
	 * @param firstDate
	 * @param lastDate
	 * @return return the period
	 * @throws ParseException
	 */

	public List<VisitHistoryGroupDto> getGroupList(List<VisitHistoryDto> visitsList, Map<Long, Float> fillingRateByUser, Map<Long, Float> workindDaysPerUser,
			VisitRequestDto visitRequestDto) {

		List<VisitHistoryGroupDto> groupList = new ArrayList<>();
		if (!visitsList.isEmpty()) {
			groupList = getDataOfVisitsWithFiltrage(visitsList, fillingRateByUser, workindDaysPerUser, visitRequestDto);
		}
		Collections.sort(groupList);
		return groupList;
	}

	public List<VisitHistoryGroupDto> getVisitCountGroupedByFilter(VisitRequestDto visitRequestDto, Date startDate,
			Date endDate) {

		List<VisitHistoryDto> visitsList = getVisitsList(visitRequestDto, startDate, endDate);
		return getGroupList(visitsList, null,null, visitRequestDto);
	}

	private List<VisitHistoryGroupDto> getDataOfVisitsWithFiltrage(List<VisitHistoryDto> visitsList,
			Map<Long, Float> fillingRateByUser, Map<Long, Float>workindDaysPerUser, VisitRequestDto visitRequestDto) {

		List<VisitHistoryGroupDto> groupList = new ArrayList<>();

		Map<String, KeyValueDto> quantityByGroup = new HashMap<>();

		int selectedValue = visitRequestDto.getSelectedValue().intValue();
		int groupBy = visitRequestDto.getSelectedGroup().intValue();
		List<VisitHistoryGroupDto> visitsCount = new ArrayList<>();

		if (selectedValue == ValueType.NUMBER_OF_VISITS.getId() || selectedValue == 0) {
			visitsCount = getVisitsCount(groupBy, visitsList);
			if(fillingRateByUser != null) {
				for (VisitHistoryGroupDto group : visitsCount) {
					group.setReportFillingRate(fillingRateByUser.get(group.getId()));
				}
			}
			if(workindDaysPerUser != null) {
				for (VisitHistoryGroupDto group : visitsCount) {
					group.setWorkindDaysPerUser(workindDaysPerUser.get(group.getId()));
				}
			}
			return visitsCount;

		} else if (selectedValue == ValueType.SAMPLE_NUMBER.getId()) {
			return getSamplesCountDto(groupBy, visitsList);
		} else if (selectedValue == ValueType.SALES.getId()) {
			return searchWithSale(groupBy, visitsList, quantityByGroup);
		} else if (selectedValue == ValueType.NUMBER_OF_ORDERS.getId()) {
			return searchWithOrderNumber(groupBy, visitsList, quantityByGroup);
		} else if (selectedValue == ValueType.BLANKET.getId()) {

			visitsCount = getVisitsCount(groupBy, visitsList);
			return getObjectiveCount(groupBy, visitRequestDto, visitsCount);

		}
		// else if (selectedValue == ValueType.PROSPECT_SATISFACTION.getId()) {
		// return searchwithCommentsSatisfaction(visitsList);

		// }
		else if (selectedValue == ValueType.GROSS_SALES.getId()) {
			Map<String, KeyValueDto> salesByGroup = new HashMap<>();
			return searchWithSalesRevenue(groupBy, visitsList, salesByGroup);

		} else if (selectedValue == ValueType.PROSPECT_PRODUCT_SATISFACTION.getId()) {

			return getProspectProductSatisfaction(GroupType.PRODUCT.getId(), visitsList);

		} else if (selectedValue == ValueType.PRODUCT_ORDER_PRESENTATION.getId()) {

			return getPresentationOrdersDto(groupBy, visitsList);

		} else if (selectedValue == ValueType.SYNCHRONISATION_DELAY.getId()) {

			return getSynchronisationDelay(GroupType.DELEGUE.getId(), visitsList);

		} else {
			return groupList;
		}

	}

	private List<VisitHistoryGroupDto> getSynchronisationDelay(int groupBy, List<VisitHistoryDto> visitsList) {

		List<VisitHistoryGroupDto> synchronisationDelay = new ArrayList<>();
		Map<String, KeyValueDto> synchronisationDelayGroupped = new HashMap<>();
		synchronisationDelayGroupped = getSynchronisationDelayByGroup(groupBy, visitsList);

		for (String key : synchronisationDelayGroupped.keySet()) {
			Float average = synchronisationDelayGroupped.get(key).getValue() / synchronisationDelayGroupped.get(key).getCount();
			VisitHistoryGroupDto ps = new VisitHistoryGroupDto(key, average,
					synchronisationDelayGroupped.get(key).getId());
			synchronisationDelay.add(ps);
		}
		return synchronisationDelay;
	}

	private Map<String, KeyValueDto> getSynchronisationDelayByGroup(int groupBy, List<VisitHistoryDto> visitsList) {
		Map<String, KeyValueDto> quantityByGroup = new HashMap<>();
		for (VisitHistoryDto visit : visitsList) {

			updateSynchronisationDelay(quantityByGroup, getKeyValueId(groupBy, visit), Float.valueOf((float)visit.getSynchronisationDelay()));
		}

		return quantityByGroup;
	}

	private void updateSynchronisationDelay(Map<String, KeyValueDto> result, KeyValueDto groupValueId, Float quantity) {
		
		KeyValueDto totalCount = result.get(groupValueId.getName());
		if (totalCount == null) {
			totalCount = new KeyValueDto();
			totalCount.setValue(Float.valueOf(0));
			totalCount.setCount(0);
		}
		totalCount.setCount(totalCount.getCount()+1);
		totalCount.setValue(totalCount.getValue()+ quantity);
		totalCount.setId(groupValueId.getId());
		result.put(groupValueId.getName(), totalCount);
	}

	private List<VisitHistoryGroupDto> getPresentationOrdersDto(int groupBy, List<VisitHistoryDto> visitsList) {

		List<VisitHistoryGroupDto> statList = new ArrayList<>();
		Map<String, Integer> presentationOrders = getPresentationOrders(groupBy, visitsList);

		for (String key : presentationOrders.keySet()) {
			VisitHistoryGroupDto e = new VisitHistoryGroupDto(key, new Float(presentationOrders.get(key)));
			statList.add(e);
		}
		return statList;
	}

	private Map<String, Integer> getPresentationOrders(int groupBy, List<VisitHistoryDto> visitsList) {
		Map<String, Integer> result = new HashMap<>();

		for (VisitHistoryDto visit : visitsList) {
			Integer presentationOrder = result.get(visit.getRank().toString());
			if (presentationOrder == null) {
				presentationOrder = Integer.valueOf(0);
			}

			presentationOrder++;
			result.put(visit.getRank().toString(), presentationOrder);
		}
		return result;
	}
	
	private List<VisitHistoryGroupDto> getVisitsCount(int groupBy, List<VisitHistoryDto> visitsList) {
		VisitHistoryGroupDto visitHistoryGroupDto = null;
		List<VisitHistoryGroupDto> visitsCount = new ArrayList<>();
		Map<String, Set<VisitKey>> visitNumberByGroup = new HashMap<>();
		searchWithNumberOfVisit(groupBy, visitsList, visitNumberByGroup);
		for (Map.Entry<String, Set<VisitKey>> entry : visitNumberByGroup.entrySet()) {
			if(groupBy == 1) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getDelegateId());			
			}else if(groupBy == 2 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getProspectId());
			}else if(groupBy == 3 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getSectorId());
			}else if(groupBy == 4 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getLocalityId());
			}else if(groupBy == 5 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getActivity());
			}else if(groupBy == 6 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getPotentialId());
			}else if(groupBy == 7 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getSpecialityId());
			}else if(groupBy == 8 ) {
				visitHistoryGroupDto = new VisitHistoryGroupDto(entry.getKey(),
						(float) visitNumberByGroup.get(entry.getKey()).size(),
						visitNumberByGroup.get(entry.getKey()).iterator().next().getProductId());
			}
			visitsCount.add(visitHistoryGroupDto);
		}
				
		return visitsCount;
	}

	private List<VisitHistoryGroupDto> getObjectiveCount(int groupBy, VisitRequestDto visitRequestDto,
			List<VisitHistoryGroupDto> visitCount) {
		List<PlanningObjectiveCountDto> planifiedProspects = new ArrayList<>();
		try {
			planifiedProspects = getPlanifiedProspects(format.parse(visitRequestDto.getFirstDate()),
					format.parse(visitRequestDto.getLastDate()), groupBy, visitRequestDto);

		} catch (BirdnotesException e) {
			LOG.error("Error in getObjectiveCount", e);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		float couvrage = 0;
		List<VisitHistoryGroupDto> planningObjective = new ArrayList<>();
		for (PlanningObjectiveCountDto planningObjectiveCountDto : planifiedProspects) {
			// int index = visitCount.indexOf(planningObjectiveCountDto.getGroupName());
			List<VisitHistoryGroupDto> result = new ArrayList<>();
			result = visitCount.stream().filter(item -> item.getName().equals(planningObjectiveCountDto.getGroupName()))
					.collect(Collectors.toList());

			if (result.size() != 0) {
				couvrage = (float) result.get(0).getCount() / (float) planningObjectiveCountDto.getCount();
				planningObjective.add(new VisitHistoryGroupDto(planningObjectiveCountDto.getGroupName(), result.get(0).getId(), 
						(float) result.get(0).getCount(), (float) planningObjectiveCountDto.getCount(), couvrage));
			} else {
				planningObjective.add(new VisitHistoryGroupDto(planningObjectiveCountDto.getGroupName(), 0L,  (float) 0,
						(float) planningObjectiveCountDto.getCount(), (float) 0));
			}
		}
		return planningObjective;
	}

	private List<VisitHistoryGroupDto> getProspectProductSatisfaction(int groupBy, List<VisitHistoryDto> visitsList) {

		List<VisitHistoryGroupDto> prospectProductSatisfaction = new ArrayList<>();
		Map<String, KeyValueDto> productSatisfaction = new HashMap<>();
		productSatisfaction = getProductSatisfactionByGroup(groupBy, visitsList);
		// Statisfaction formula total satisfaction * 100 / 5 * count
		for (String key : productSatisfaction.keySet()) {
			Float average = productSatisfaction.get(key).getValue() / productSatisfaction.get(key).getCount();
			VisitHistoryGroupDto ps = new VisitHistoryGroupDto(key, average * 20, productSatisfaction.get(key).getId());
			prospectProductSatisfaction.add(ps);
		}
		return prospectProductSatisfaction;
	}

	private Map<String, KeyValueDto> getProductSatisfactionByGroup(int groupBy, List<VisitHistoryDto> visitsList) {
		Map<String, KeyValueDto> quantityByGroup = new HashMap<>();
		for (VisitHistoryDto visit : visitsList) {
			updateProductSatisfaction(quantityByGroup, getKeyValueId(groupBy, visit), visit.getSmily());
		}

		return quantityByGroup;
	}

	private void updateProductSatisfaction(Map<String, KeyValueDto> result, KeyValueDto groupValueId, Integer quantity) {

		KeyValueDto totalCount = result.get(groupValueId.getName());
		if (totalCount == null) {
			totalCount = new KeyValueDto();
			totalCount.setValue(Float.valueOf(0));
			totalCount.setCount(0);
		}
		totalCount.setCount(totalCount.getCount()+1);
		totalCount.setValue(totalCount.getValue()+ quantity);
		totalCount.setId(groupValueId.getId());
		result.put(groupValueId.getName(), totalCount);
	}

	private List<VisitHistoryGroupDto> searchWithOrderNumber(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		
		List<VisitHistoryGroupDto> groupList = new ArrayList<>();
		searchOrderNumberWithDelegate(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithProspect(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithSector(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithLocality(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithActivity(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithPotential(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithSpeciality(selectGroup, visitsList, quantityByGroup);
		searchOrderNumberWithProduct(selectGroup, visitsList, quantityByGroup);
		for (String key : quantityByGroup.keySet()) {
			VisitHistoryGroupDto e = new VisitHistoryGroupDto(key, quantityByGroup.get(key).getValue(), quantityByGroup.get(key).getId());
			groupList.add(e);
		}
		return groupList;
	}

	private List<VisitHistoryGroupDto> searchWithSalesRevenue(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		List<VisitHistoryGroupDto> groupList = new ArrayList<>();
		searchSalesRevenueByDelegate(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenueByProspect(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenueBySector(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenueByLocality(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenyeByActivity(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenueByPotential(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenueBySpeciality(selectGroup, visitsList, quantityByGroup);
		searchSalesRevenueByProduct(selectGroup, visitsList, quantityByGroup);
		for (String key : quantityByGroup.keySet()) {
			VisitHistoryGroupDto e = new VisitHistoryGroupDto(key, quantityByGroup.get(key).getValue(), quantityByGroup.get(key).getId());
			groupList.add(e);
		}
		return groupList;
	}

	private void searchOrderNumberWithProduct(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.PRODUCT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getOrderQuantity()));
			}
		}
	}

	private void searchSalesRevenueByProduct(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.PRODUCT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}
	}

	private void searchOrderNumberWithSpeciality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.SPECIALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity()));
			}
		}
	}

	private void searchSalesRevenueBySpeciality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.SPECIALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}
	}

	private void searchOrderNumberWithPotential(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.POTENTIAL.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity()));
			}
		}
	}

	private void searchSalesRevenueByPotential(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.POTENTIAL.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}
	}

	private void searchOrderNumberWithActivity(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.ACTIVITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getOrderQuantity()));
			}
		}

	}

	private void searchSalesRevenyeByActivity(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.ACTIVITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}

	}

	private void searchOrderNumberWithLocality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.LOCALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity()));
			}
		}
	}

	private void searchSalesRevenueByLocality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.LOCALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}
	}

	private void searchOrderNumberWithSector(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.SECTOR.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity()));
			}
		}

	}

	private void searchSalesRevenueBySector(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.SECTOR.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}

	}

	private void searchOrderNumberWithProspect(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.PROSPECT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup,
						getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity()));
			}
		}

	}

	private void searchSalesRevenueByProspect(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.PROSPECT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup,
						getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}

	}

	private void searchOrderNumberWithDelegate(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.DELEGUE.getId() || selectGroup == 0) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getOrderQuantity()));
			}
		}

	}

	private void searchSalesRevenueByDelegate(int selectGroup, List<VisitHistoryDto> visitsList,

			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.DELEGUE.getId() || selectGroup == 0) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getOrderQuantity() * visit.getPrice()));
			}
		}

	}

	private List<VisitHistoryGroupDto> searchWithSale(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		List<VisitHistoryGroupDto> groupList = new ArrayList<>();
		searchWithSaleAndDelegate(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndProspect(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndSector(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndLocality(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndActivity(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndPotential(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndSpeciality(selectGroup, visitsList, quantityByGroup);
		searchWithSaleAndProduct(selectGroup, visitsList, quantityByGroup);

		for (String key : quantityByGroup.keySet()) {
			VisitHistoryGroupDto e = new VisitHistoryGroupDto(key, quantityByGroup.get(key).getValue(), quantityByGroup.get(key).getId());
			groupList.add(e);
		}
		return groupList;

	}

	private void searchWithSaleAndProduct(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.PRODUCT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getSaleQuantity()));
			}
		}
	}

	private void searchWithSaleAndSpeciality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.SPECIALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getSaleQuantity()));
			}
		}

	}

	private void searchWithSaleAndPotential(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.POTENTIAL.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getSaleQuantity()));
			}
		}

	}

	private void searchWithSaleAndActivity(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.ACTIVITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getSaleQuantity()));
			}
		}

	}

	private void searchWithSaleAndLocality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.LOCALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getSaleQuantity()));
			}
		}

	}

	private void searchWithSaleAndSector(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.SECTOR.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getSaleQuantity()));
			}
		}
	}

	private void searchWithSaleAndProspect(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.PROSPECT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup,
						getKeyValueId(selectGroup, visit),
						Float.valueOf((float)visit.getSaleQuantity()));
			}
		}

	}

	private void searchWithSaleAndDelegate(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, KeyValueDto> quantityByGroup) {
		if (selectGroup == GroupType.DELEGUE.getId() || selectGroup == 0) {
			for (VisitHistoryDto visit : visitsList) {
				updateQuantity(quantityByGroup, getKeyValueId(selectGroup, visit), Float.valueOf((float)visit.getSaleQuantity()));
			}
		}
	}

	private List<VisitHistoryGroupDto> getSamplesCountDto(int groupBy, List<VisitHistoryDto> visitsList) {

		List<VisitHistoryGroupDto> statList = new ArrayList<>();
		Map<String, KeyValueDto> samplesCount = getSampleCount(groupBy, visitsList);
		
		for (String key : samplesCount.keySet()) {
			VisitHistoryGroupDto e = new VisitHistoryGroupDto(key, samplesCount.get(key).getValue(), samplesCount.get(key).getId());
			statList.add(e);
		}
		return statList;
	}

	private Map<String, KeyValueDto> getSampleCount(int groupBy, List<VisitHistoryDto> visitsList) {
		Map<String, KeyValueDto> quantityByGroup = new HashMap<>();
		for (VisitHistoryDto visit : visitsList) {
			if(visit.getSampleQuantity() != null)
				updateQuantity(quantityByGroup, getKeyValueId(groupBy, visit), Float.valueOf((float)visit.getSampleQuantity()));
		}
		return quantityByGroup;
	}

	private KeyValueDto getKeyValueId(int groupBy, VisitHistoryDto visit) {
		KeyValueDto groupNameId = null;
		if (groupBy == GroupType.DELEGUE.getId() || groupBy == 0) {
			groupNameId = new KeyValueDto(visit.getDelegateName(), visit.getDelegateId());
		} else if (groupBy == GroupType.PROSPECT.getId()) {
			groupNameId = new KeyValueDto(visit.getProspectDto().getFirstName() + ' ' + visit.getProspectDto().getLastName(), visit.getProductId());
		} else if (groupBy == GroupType.SECTOR.getId()) {
			groupNameId = new KeyValueDto(visit.getProspectDto().getSectorDto().getName(), visit.getProspectDto().getSectorDto().getId());
		} else if (groupBy == GroupType.LOCALITY.getId()) {
			groupNameId = new KeyValueDto(visit.getProspectDto().getLocalityDto().getName(), visit.getProspectDto().getLocalityDto().getId());
		} else if (groupBy == GroupType.ACTIVITY.getId()) {
			if(visit.getProspectDto().getActivity().equals("P")) {
				groupNameId = new KeyValueDto(visit.getProspectDto().getActivity(), 0L);
			}
			else {
				groupNameId = new KeyValueDto(visit.getProspectDto().getActivity(), 0L);
			}
		} else if (groupBy == GroupType.POTENTIAL.getId()) {
			groupNameId = new KeyValueDto(visit.getProspectDto().getPotentialDto().getName(), visit.getProspectDto().getPotentialDto().getId());
		} else if (groupBy == GroupType.SPECIALITY.getId()) {
			groupNameId = new KeyValueDto(visit.getProspectDto().getSpecialityDto().getName(), visit.getProspectDto().getSpecialityDto().getId());
		} else if (groupBy == GroupType.PRODUCT.getId()) {
			groupNameId = new KeyValueDto(visit.getProductName(), visit.getProductId());
		}
		return groupNameId;
		
	}

	private void searchWithNumberOfVisit(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		searchNumberOfVisitWithDelegate(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithProspect(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithSector(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithLocality(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithActivity(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithPotential(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithSpeciality(selectGroup, visitsList, visitNumberByGroup);
		searchNumberOfVisitWithProduct(selectGroup, visitsList, visitNumberByGroup);

	}

	private void searchNumberOfVisitWithProduct(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.PRODUCT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getProductName());
			}

		}

	}

	private void searchNumberOfVisitWithSpeciality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.SPECIALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getProspectDto().getSpecialityDto().getName());
			}
		}

	}

	private void searchNumberOfVisitWithPotential(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.POTENTIAL.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getProspectDto().getPotentialDto().getName());
			}
		}

	}

	private void searchNumberOfVisitWithActivity(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.ACTIVITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getProspectDto().getActivity());
			}
		}
	}

	private void searchNumberOfVisitWithLocality(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.LOCALITY.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getProspectDto().getLocalityDto().getName());
			}
		}
	}

	private void searchNumberOfVisitWithSector(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.SECTOR.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getProspectDto().getSectorDto().getName());
			}
		}
	}

	private void searchNumberOfVisitWithDelegate(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.DELEGUE.getId() || selectGroup == 0) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit, visit.getDelegateName());
			}
		}

	}

	private void searchNumberOfVisitWithProspect(int selectGroup, List<VisitHistoryDto> visitsList,
			Map<String, Set<VisitKey>> visitNumberByGroup) {
		if (selectGroup == GroupType.PROSPECT.getId()) {
			for (VisitHistoryDto visit : visitsList) {
				updateVisitNbr(visitNumberByGroup, visit,
						visit.getProspectDto().getFirstName() + ' ' + visit.getProspectDto().getLastName());

			}
		}
	}

	private void updateQuantity(Map<String, KeyValueDto> result, KeyValueDto groupValueId, Float visitQuantity) {

		KeyValueDto quantity = result.get(groupValueId.getName());
		if (quantity == null) {
			quantity = new KeyValueDto();
			quantity.setValue(Float.valueOf(0));
		}
		quantity.setId(groupValueId.getId());
		if (visitQuantity != 0) {
			quantity.setValue(quantity.getValue() + visitQuantity);
			
		}

		result.put(groupValueId.getName(), quantity);
	}

	private void updateVisitNbr(Map<String, Set<VisitKey>> result, VisitHistoryDto visit, String key) {
		Set<VisitKey> visitskey = result.get(key);
		if (visitskey == null) {
			visitskey = new HashSet<>();
		}
		visitskey.add(new VisitKey(visit.getVisitDate(), visit.getDelegateId(), visit.getProspectDto().getId(), visit.getProspectDto().getSectorDto().getId(),
				visit.getProspectDto().getLocalityDto().getId(), visit.getProspectDto().getPotentialDto().getId(), visit.getProspectDto().getActivity(), 
				visit.getProductId(), visit.getProspectDto().getSpecialityDto().getId()));
	
		result.put(key, visitskey);
	}

	@Override
	public List<VisitAveragePerDayDto> visitAveragePerDay(Date mondayDate) {

		List<VisitAveragePerDayDto> back = new ArrayList<>();
		Date saturdayDate = BirdnotesUtils.addDaysToDate(mondayDate, 5);

		String startDate = format.format(mondayDate);
		String endDate = format.format(saturdayDate);

		List<MinimizedUserDtoV1> allUsers = delegateRepository.getDelegatesV1();
		List<VisitCountDto> visitDtos = visitRepository.visitAveragePerWeek(mondayDate, saturdayDate);

		if (visitDtos != null && !visitDtos.isEmpty()) {
			allUsers.stream().forEach(user -> setAverageValueByUser(visitDtos, user, back, startDate, endDate)

			);
		} else {
			allUsers.stream().forEach(e -> {
				VisitAveragePerDayDto visitAveragePerDayDto = new VisitAveragePerDayDto(
						e.getFirstName() + " " + e.getLastName(), 0.0, 0L, "0");
				back.add(visitAveragePerDayDto);
			});
		}
		return back;
	}

	private void setAverageValueByUser(List<VisitCountDto> visitDtos, MinimizedUserDtoV1 user,
			List<VisitAveragePerDayDto> back, String startDate, String endDate) {
		Optional<VisitCountDto> optionalVisitDto = visitDtos.stream()
				.filter(visit -> visit.getUserId().equals(user.getId())).findAny();
		if (optionalVisitDto.isPresent()) {
			VisitAveragePerDayDto visitAveragePerDayDto = new VisitAveragePerDayDto();
			visitAveragePerDayDto.setDelegateName(
					optionalVisitDto.get().getFirstName() + " " + optionalVisitDto.get().getLastName());

			ActivityCalanderRequestDto activityCalanderRequestDto = new ActivityCalanderRequestDto(startDate, endDate,
					user.getId());
			float sumActivity = 0;
			List<VisitHistoryGroupDto> activityList;
			try {
				activityList = activityService.getStatisticActivity(activityCalanderRequestDto)
						.getVisitHistoryGroupDtoList();

				if (activityList != null) {
					for (VisitHistoryGroupDto item : activityList) {

						sumActivity += item.getCount();
					}
				} else {
					sumActivity = 0;
				}

				setAverageValuePerUserType(user, visitAveragePerDayDto, optionalVisitDto, sumActivity);

				convertVisitAveragePerDayDto(visitAveragePerDayDto, optionalVisitDto);
				back.add(visitAveragePerDayDto);

			} catch (BirdnotesException e) {
				LOG.error("Exception in setAverageValueByUser", e);
			}
		} else {
			VisitAveragePerDayDto visitAveragePerDayDto = new VisitAveragePerDayDto(
					user.getFirstName() + " " + user.getLastName(), 0.0, 0L, "0");
			back.add(visitAveragePerDayDto);
		}

	}

	private void setAverageValuePerUserType(MinimizedUserDtoV1 user, VisitAveragePerDayDto visitAveragePerDayDto,
			Optional<VisitCountDto> optionalVisitDto, float sumActivity) {
		if (optionalVisitDto.isPresent()) {
			if (WorkType.PHARMACEUTICAL.equals(user.getWorkType())) {
				visitAveragePerDayDto.setAverageValue(optionalVisitDto.get().getCountVisit() / (6.0 - sumActivity));
			} else {
				visitAveragePerDayDto.setAverageValue(optionalVisitDto.get().getCountVisit() / (5.0 - sumActivity));
			}
		}
	}

	private VisitAveragePerDayDto convertVisitAveragePerDayDto(VisitAveragePerDayDto visitAveragePerDayDto,
			Optional<VisitCountDto> optionalVisitDto) {
		if (optionalVisitDto.isPresent()) {
			visitAveragePerDayDto.setSum(optionalVisitDto.get().getCountVisit());
		}
		visitAveragePerDayDto.setAverage(String.format("%.2f", visitAveragePerDayDto.getAverageValue()));
		return visitAveragePerDayDto;
	}

	private List<VisitProspectDto> buildVisitProspect(List<VisitCountDto> visitSectorDtos) {
		List<VisitProspectDto> visitProspectDtos = new ArrayList<>();
		List<Long> userIds = new ArrayList<>();

		if (!visitSectorDtos.isEmpty()) {
			for (VisitCountDto visitDto : visitSectorDtos) {
				VisitProspectDto visitProspectDto = new VisitProspectDto();
				userIds.add(visitDto.getUserId());
				String firstName = visitDto.getFirstName();
				String lastName = visitDto.getLastName();
				Long countVisit = visitDto.getCountVisit();
				visitProspectDto.setDelegateName(firstName + " " + lastName);
				visitProspectDto.setNbVisitPerWeek(countVisit);
				List<Prospect> prospects = prospectRepository.findBySectorId(visitDto.getSecteurId());
				if (prospects != null) {
					visitProspectDto.setPercentVisitPerWeek(
							String.format("%.2f", (countVisit.doubleValue() / prospects.size()) * 100) + " %");
				} else {
					visitProspectDto.setPercentVisitPerWeek("0 %");
				}
				visitProspectDtos.add(visitProspectDto);
			}
		}
		return visitProspectDtos;
	}

	@Override
	public List<VisitProspectDto> visitProspect(Date mondayDate, Long specialityId) {

		Date fridayDate = BirdnotesUtils.addDaysToDate(mondayDate, 4);

		List<VisitCountDto> visitSectorDtos;
		if (specialityId != null && specialityId != 0) {
			visitSectorDtos = visitRepository.visitAveragePerWeekWithSector(mondayDate, fridayDate, specialityId);
		} else {
			visitSectorDtos = visitRepository.visitAveragePerWeekWithSector(mondayDate, fridayDate);
		}

		List<VisitProspectDto> visitProspectDtos;
		List<Long> userIds = new ArrayList<>();

		visitProspectDtos = buildVisitProspect(visitSectorDtos);

		if (userIds.isEmpty()) {
			List<MinimizedUserDto> users = delegateRepository.getDelegates();
			if (!users.isEmpty()) {
				for (MinimizedUserDto user : users) {
					VisitProspectDto visitProspectDto = new VisitProspectDto();
					visitProspectDto.setDelegateName(user.getFirstName() + " " + user.getLastName());
					visitProspectDto.setNbVisitPerWeek(0L);
					visitProspectDto.setPercentVisitPerWeek("0 %");
					visitProspectDtos.add(visitProspectDto);
				}
			}
		} else {
			List<MinimizedUserDto> users = delegateRepository.getDelegatesExcept(userIds);
			if (!users.isEmpty()) {
				for (MinimizedUserDto user : users) {
					VisitProspectDto visitProspectDto = new VisitProspectDto();
					visitProspectDto.setDelegateName(user.getFirstName() + " " + user.getLastName());
					visitProspectDto.setNbVisitPerWeek(0L);
					visitProspectDto.setPercentVisitPerWeek("0 %");
					visitProspectDtos.add(visitProspectDto);
				}
			}
		}

		return visitProspectDtos;
	}

	@Override
	public List<VisitAveragePerDayDto> visitAveragePerMonth(Integer month, Integer year) {

		List<VisitAveragePerDayDto> back = new ArrayList<>();
		// get all users because, we should return the user that doesn't have
		// any visits also
		List<MinimizedUserDtoV1> allUsers = delegateRepository.getDelegatesV1();
		List<VisitCountDto> visitDtos = visitRepository.visitAveragePerMonth(month, year);

		if (visitDtos != null && !visitDtos.isEmpty()) {

			// get the first and last day of the month
			LocalDate initial = LocalDate.of(year, month, 7);
			LocalDate start = initial.with(firstDayOfMonth());
			LocalDate end = initial.with(lastDayOfMonth());
			String startDate = start.getDayOfMonth() + "/" + start.getMonthValue() + "/" + start.getYear();
			String endDate = end.getDayOfMonth() + "/" + end.getMonthValue() + "/" + end.getYear();

			// calculate the number of working days
			int numberOfWorkingDaysWithoutSaturday = BirdnotesUtils.getWorkingDaysBetweenTwoDates(
					Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()),
					Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant()), true);
			int numberOfWorkingDaysWithSaturday = BirdnotesUtils.getWorkingDaysBetweenTwoDates(
					Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant()),
					Date.from(end.atStartOfDay(ZoneId.systemDefault()).toInstant()), false);

			allUsers.stream().forEach(user -> {
				Optional<VisitCountDto> optionalVisitDto = visitDtos.stream()
						.filter(visit -> visit.getUserId().equals(user.getId())).findAny();

				if (optionalVisitDto.isPresent()) {
					VisitAveragePerDayDto visitAveragePerDayDto = new VisitAveragePerDayDto();
					visitAveragePerDayDto.setDelegateName(
							optionalVisitDto.get().getFirstName() + " " + optionalVisitDto.get().getLastName());

					ActivityCalanderRequestDto activityCalanderRequestDto = new ActivityCalanderRequestDto(startDate,
							endDate, user.getId());
					float sumActivity = 0;
					List<VisitHistoryGroupDto> activityList;
					try {
						activityList = activityService.getStatisticActivity(activityCalanderRequestDto)
								.getVisitHistoryGroupDtoList();
						if (activityList != null) {
							for (VisitHistoryGroupDto item : activityList) {

								sumActivity += item.getCount();
							}
						} else {
							sumActivity = 0;
						}

						setAverageValuePerWorkTypeUser(user, visitAveragePerDayDto, optionalVisitDto,
								numberOfWorkingDaysWithSaturday, numberOfWorkingDaysWithoutSaturday, sumActivity);

						convertVisitAveragePerDayDto(visitAveragePerDayDto, optionalVisitDto);
						back.add(visitAveragePerDayDto);
					} catch (BirdnotesException e) {
						LOG.error("Exception in visitAveragePerMonth", e);
					}

				} else {
					VisitAveragePerDayDto visitAveragePerDayDto = new VisitAveragePerDayDto(
							user.getFirstName() + " " + user.getLastName(), 0.0, 0L, "0");
					back.add(visitAveragePerDayDto);
				}

			});
		} else {
			allUsers.stream().forEach(e -> {
				VisitAveragePerDayDto visitAveragePerDayDto = new VisitAveragePerDayDto(
						e.getFirstName() + " " + e.getLastName(), 0.0, 0L, "0");
				back.add(visitAveragePerDayDto);
			});
		}
		return back;
	}

	private void setAverageValuePerWorkTypeUser(MinimizedUserDtoV1 user, VisitAveragePerDayDto visitAveragePerDayDto,
			Optional<VisitCountDto> optionalVisitDto, int numberOfWorkingDaysWithSaturday,
			int numberOfWorkingDaysWithoutSaturday, float sumActivity) {
		if (optionalVisitDto.isPresent()) {
			if (WorkType.PHARMACEUTICAL.equals(user.getWorkType())) {
				visitAveragePerDayDto.setAverageValue((optionalVisitDto.get().getCountVisit() + 0.0)
						/ (numberOfWorkingDaysWithSaturday - sumActivity));
			} else

				visitAveragePerDayDto.setAverageValue((optionalVisitDto.get().getCountVisit() + 0.0)
						/ (numberOfWorkingDaysWithoutSaturday - sumActivity));
		}
	}

	@Autowired
	public void setActivityRepository(ActivityRepository activityRepository) {
		this.activityRepository = activityRepository;
	}

	@Override
	public void validatePurchaseOrder(Long purchaseOrderId) {
		purchaseOrderRepository.updateStatus(purchaseOrderId, UserValidationStatus.ACCEPTED);
	}

	@Override
	public void refusePurchaseOrder(Long purchaseOrderId) {
		purchaseOrderRepository.updateStatus(purchaseOrderId, UserValidationStatus.REFUSED);
	}

	@Override
	public List<ActivityByPeriod> activityByPeriod(Date startDate, Date endDate) {

		return visitRepository.activityByPeriod(startDate, endDate);
	}

	@Override
	public List<ActivityByPeriod> activityByPotential(Date startDate, Date endDate) {

		return visitRepository.activityByPotential(startDate, endDate);
	}

	@Override
	public List<ActivityByPeriod> activityByDelegate(Date startDate, Date endDate) {
		return visitRepository.activityByDelegate(startDate, endDate);
	}

	@Override
	public List<ActivityByPeriod> activityBySpeciality(Date startDate, Date endDate) {
		return visitRepository.activityBySpeciality(startDate, endDate);
	}

	@Override
	public List<LabelValueDto> getProspectsSatisfaction(Date startDate, Date endDate) {
		return visitRepository.getProspectsSatisfaction(startDate, endDate);
	}

	@Override
	public List<ProspectMessage> getUrgentMessages(Date startDate, Date endDate) {

		List<ProspectMessage> prospectMessages = new ArrayList();

		List<VisitHistoryDto> visits = visitRepository.findUrgentMessages(startDate, endDate);

		for (VisitHistoryDto visitHistoryDto : visits) {

			ProspectMessage prospectMessage = new ProspectMessage();
			prospectMessage.setDelegateName(visitHistoryDto.getDelegateName());
			prospectMessage.setProductName(visitHistoryDto.getProductName());
			prospectMessage.setProspectName(visitHistoryDto.getProspectDto().getFirstName() + " "
					+ visitHistoryDto.getProspectDto().getLastName());
			prospectMessage.setMessageDate(visitHistoryDto.getVisitDate());
			prospectMessage.setMessageText(visitHistoryDto.getComment());
			prospectMessage.setSectorName(visitHistoryDto.getProspectDto().getSectorDto().getName());
			prospectMessages.add(prospectMessage);
		}

		return prospectMessages;
	}

	@Override
	public Float getSalesRevenuee(Date startDate, Date endDate) {

		return visitRepository.getSalesRevenuee(startDate, endDate);
	}

	public void setFormat(SimpleDateFormat format) {
		this.format = format;
	}

	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	@Autowired
	public void setVisitRepository(VisitRepository visitRepository) {
		this.visitRepository = visitRepository;
	}

	@Autowired
	public void setConfigurationService(ConfigurationService configurationService) {
		this.configurationService = configurationService;
	}

	@Autowired
	public void setDiscountRepository(DiscountRepository discountRepository) {
		this.discountRepository = discountRepository;
	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}

	@Autowired
	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	@Autowired
	public void setProspectRepository(ProspectRepository prospectRepository) {
		this.prospectRepository = prospectRepository;
	}

	@Autowired
	public void setConvertProspectToDto(ConvertProspectToDto convertProspectToDto) {
		this.convertProspectToDto = convertProspectToDto;
	}

	@Autowired
	public void setDynamicQueries(DynamicQueries dynamicQueries) {
		this.dynamicQueries = dynamicQueries;
	}

	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}
	
	
	@Autowired
	public void setHolidayRepository(HolidayRepository holidayRepository) {
		this.holidayRepository = holidayRepository;
	}

	@Autowired
	public void setRecoveryRepository(RecoveryRepository recoveryRepository) {
		this.recoveryRepository = recoveryRepository;
	}

	@Autowired
	public void setConvertVisitToDto(ConvertVisitToDto convertVisitToDto) {
		this.convertVisitToDto = convertVisitToDto;
	}

	@Autowired
	public void setPurchaseOrderToDtoConvertor(PurchaseOrderToDtoConvertor purchaseOrderToDtoConvertor) {
		this.purchaseOrderToDtoConvertor = purchaseOrderToDtoConvertor;
	}

	@Autowired
	public void setWholesalerToDtoConvertor(WholesalerToDtoConvertor wholesalerToDtoConvertor) {
		this.wholesalerToDtoConvertor = wholesalerToDtoConvertor;
	}

	@Autowired
	public void setRecoveryToDtoConvertor(RecoveryToDtoConvertor recoveryToDtoConvertor) {
		this.recoveryToDtoConvertor = recoveryToDtoConvertor;
	}

	@Autowired
	public void setPurchaseOrderRepository(PurchaseOrderRepository purchaseOrderRepository) {
		this.purchaseOrderRepository = purchaseOrderRepository;
	}

	@Autowired
	public void setPlanningRepository(PlanningRepository planningRepository) {
		this.planningRepository = planningRepository;
	}

	@Autowired
	public void setActivityService(ActivityService activityService) {
		this.activityService = activityService;
	}

	@Autowired
	public void setProspectService(ProspectService prospectService) {
		this.prospectService = prospectService;
	}

	@Autowired
	public void setGoalManagementService(GoalManagementService goalManagementService) {
		this.goalManagementService = goalManagementService;
	}

	public void setUploadPath(String uploadPath) {
		this.uploadPath = uploadPath;
	}

	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}

	public void setPurchaseOrderPath(String purchaseOrderPath) {
		this.purchaseOrderPath = purchaseOrderPath;
	}

	public void setGeneratedDocumentPath(String generatedDocumentPath) {
		this.generatedDocumentPath = generatedDocumentPath;
	}

	public void setRecoveryPath(String recoveryPath) {
		this.recoveryPath = recoveryPath;
	}

	public void setCommentsRatesFilePath(String commentsRatesFilePath) {
		this.commentsRatesFilePath = commentsRatesFilePath;
	}

	public void setVisitHistoryBuilder(VisitHistoryBuilder visitHistoryBuilder) {
		this.visitHistoryBuilder = visitHistoryBuilder;
	}

}
