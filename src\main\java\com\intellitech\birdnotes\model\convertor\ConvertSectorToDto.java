package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.dto.SectorDto;

@Component("convertSectorToDto")
public class ConvertSectorToDto {

	private static final Logger LOG = LoggerFactory.getLogger(ConvertSectorToDto.class);

	public SectorDto convert(Sector sector) throws BirdnotesException {

		if (sector == null) {
			LOG.error("sector is null");
			throw new BirdnotesException("sector is null");
		}

		SectorDto sectorDto = new SectorDto();
		sectorDto.setName(sector.getName());
		sectorDto.setId(sector.getId());
		sectorDto.setLatitude(sector.getLatitude());
		sectorDto.setLongitude(sector.getLongitude());
		return sectorDto;
	}
}