package com.intellitech.birdnotes.model.dto;

public class SpecialityDto extends KeyValueDto {
 
	private int action;
	
	private String icon;


	private static final long serialVersionUID = 1L;

	public SpecialityDto() {
		super();
		
	}

	public SpecialityDto(String name, Long id ) {
		super(name, id);
	}
	public SpecialityDto(String name, Long id, int action ) {
		super(name, id);
		this.action = action;
	}
	public int getAction() {
		return action;
	}

	public void setAction(int action) {
		this.action = action;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

}