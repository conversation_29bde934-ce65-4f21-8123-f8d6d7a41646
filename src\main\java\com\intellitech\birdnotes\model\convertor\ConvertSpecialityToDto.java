package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;

@Component("convertSpecialityToDto")
public class ConvertSpecialityToDto {
	
	private ConfigurationRepository configureRepository;
	
	@Value("${uploadUrl}")
	private String uploadUrl;
	
	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}
	
	
	@Value("${specialityPath}")
	private String specialityPath;
	
	private static final Logger LOG = LoggerFactory.getLogger(ConvertSpecialityToDto.class);

	public SpecialityDto convert(Speciality speciality) throws BirdnotesException {

		if (speciality == null) {
			LOG.error("speciality is null");
			throw new BirdnotesException("speciality is null");
		}
		SpecialityDto specialityDto = new SpecialityDto();
		specialityDto.setName(speciality.getName());
		specialityDto.setId(speciality.getId());
		if(speciality.getAction()!=null) {
			specialityDto.setAction(speciality.getAction());
		}
		if(speciality.getIcon() != null) {
			Configuration config = configureRepository.findById(1);
			specialityDto.setIcon(config.getBackendUrl() + uploadUrl + specialityPath + "/"+ speciality.getId() + "/" +  speciality.getIcon());
		}
		
			
		return specialityDto;
	}
}