package com.intellitech.birdnotes.util;

public interface BirdnotesConstants {

	interface Configuration {
		String MOBILE_LOG_FILE_NAME = "birdnotes-mobile.log";
	}

	interface Columns {
		String ROUTER_LINK = "router_link";
		String LABEL = "label";
		String NOTE_ID = "note_id";
		String EVALUATION_DATE = "evaluation_date";
		String EVALUATION_CRITERIA_VALUE = "evaluation_criteria_value";
		String EVALUATION_CRITERIA_NAME = "evaluation_criteria_name";

		String PARENT_ID ="parent_id";
		String START_TIME = "start_time";
		String END_TIME = "end_time";

		String START_DATE = "start_date";
		String LOAD_PLAN_ID = "load_plan_id";
		String SAMPLE_SUPPLLY_ID = "sample_supply_id";
		String END_DATE = "end_date";
		String MISSION_ID = "mission_id";
		String ESTABLISHMENT_ID = "establishment_id";
		String IMAGE = "image";
		String WEEK_WORKING_DAYS = "week_working_days";
		String CAR_TYPE = "car_type";
		String CAR_REGISTRATION = "car_registration";
		String ORDER_VALIDATION = "order_validation";
		String MESSAGE_DATE = "message_date";
		String PACKAGE_NAME = "package_name";
		String PERCENTAGE = "percentage";
		String FREE_QUANTITY_RULE_ID = "free_quantity_rule_id";
		String THRESHOLD = "threshold";
		String ID = "id";
		String MARKETING_ACTION_TYPE_ID = "marketing_action_type_id";
		String YEAR = "year";
		String MONTHLY_BUDGET = "monthlyBudget";
		String NAME = "name";
		String DELEGATE_NUMBER = "delegate_number";
		String ACTION = "action";
		String TOTALREVENUE = "total_revenue";
		String TEXT = "text";
		String STATUE = "statue";
		String LOGO = "logo";
		String SYNCRO = "syncro";
		String AUTOSYNC = "autoSync";
		String AUTOEXPENSE = "autoExpense";
		String LOCK_AFTER_SYNC = "lock_after_sync";
		String OPEN_REPORT_PERIOD = "open_report_period";
		String OPEN_EXPENSE_PERIOD = "open_expense_period";
		String MULTI_WHOLESALER = "multi_wholesaler";
		String CYCLE = "cycle";
		String SYNCCYCLE = "sync_cycle";
		String ACCEPTED_POINTING_DISTANCE = "accepted_pointing_distance";
		String STATUS = "status";
		String IDPROSPECT = "idprospect";
		String Quantity = "quantity";
		String TYPE = "type";
		String BUDGET = "budget";
		String ACTIVITY = "activity";
		String ACTIVITIES = "activities";
		String ADDRESS = "address";
		String USERNAME = "username";
		String PASS = "password";
		String FIRST_NAME = "first_name";
		String LAST_NAME = "last_name";
		String SECTOR = "sector";
		String GSM = "gsm";
		String PHONE = "phone";
		String EMAIL = "email";
		String DISCOUNT = "discount";
		String HISTORIQUE = "historique";
		String NOTE = "note";
		String SOCIAL_MEDIA = "socialMedia";
		String TAX_ID_NUMBER = "taxIdNumber";
		String NOTES = "notes";
		String GRADE = "grade";
		String ACTIVE = "active";
		String SECRETARY = "secretary";
		String COMMENT = "comment";
		String ORDER_QUANTITY = "order_quantity";
		String FREE_QUANTITY = "free_quantity";
		String SAMPLE_QUANTITY = "sample_quantity";
		String LOCALITY = "locality";
		String DELEGATE_ID = "delegate_id";
		String DOUBLE_VISIT_ID = "double_visit_id";
		// String EXPENSE_TYPE_ID = "expense_type_id";
		String PROSPECT_TYPE_ID = "prospect_type_id";
		String INTEREST_ID = "interest_id";
		String CONTACT_TYPE_ID = "contact_type_id";
		String NOTEFRAIS_ID = "noteFrais_id";
		String DELEGATE_COMMISSION_ID = "delegate_commission_id";
		String SPECIALITY_ID = "Speciality_id";
		String PROSPECT_ID = "prospect_id";
		String PROSPECTS_ID = "prospects_id";
		String SECTOR_ID = "sector_id";
		String POTENTIAL_ID = "potential_id";
		String POTENTIALLABO_ID = "potentialLabo_id";
		String VISIT_ID = "visit_id";
		String PRE_AFFECTATION_ID = "pre_affectation_id";
		String ATTACHMENT_ID = "attachment_id";
		String PAYMENT = "payment";
		String AMOUNT = "amount";
		String RECOVERY_DATE = "recovery_date";
		String PRODUCT_ID = "product_ID";
		String WHOLESALER_ID = "wholesaler_ID";
		String PURCHASEORDER_ID = "purchaseOrder_id";
		String VISIT_PRODUCT_ID = "visit_product_id";
		String TARGET_USER_ID = "target_user_ID";
		String PRICE = "price";
		String VAT = "vat";
		String MILEAGE = "mileage";
		String PERMISSION_NAME = "permission_name";
		String BUYING_PRICE = "buying_price";
		String NUMBER_OF_CAPSULES = "Number_of_capsules";
		String STOCK = "stock";
		String OBJECTIVE_PER_WEEK = "objective_per_week";
		String DESCRIPTION = "description";
		String CATEGORY_ID = "category_id";
		String RANGE_ID = "range_id";
		String PREFERENCE_ID = "preference_id";
		String HIRING_DATE = "hiring_date";
		String SPECIFIC_WEEK = "specific_week";
		String LOCALITY_ID = "locality_id";
		String USER_ID = "user_id";
		String EXPENSE_TYPE_ID = "expense_type_id";
		String IS_ADMIN = "is_admin";
		String ROLE_ID = "role_id";
		String VISIT_DATE = "visit_date";
		String RANK = "rank";
		String MENUITEM = "menuItem";
		String BIRTHDAY_DATE = "birthday_date";
		String CIN = "cin";
		String GENDER = "gender";
		String CONTRACT_TYPE = "contract_type";
		String CREATION_DATE = "creation_date";
		String UPDATE_DATE = "update_date";
		String UPDATE_POSITION_DATE = "update_position_date";
		String WORK_TYPE = "work_type";
		String GENERAL_NOTE = "general_note";
		String LATITUDE = "latitude";
		String USER_APP_ID = "user_app_id";
		String LONGITUDE = "longitude";
		String DISTANCE = "distance";
		String DATE="date";
		String DURATION = "duration";
		String MAP_ADDRESS = "map_address";
		String VALUE = "value";
		String SMILY = "smily";
		String LAST_SYNCRO = "last_syncro";
		String SALE_QUANTITY = "sale_quantity";
		String ATTACHMENT_NAME = "piece_jointe";
		String ATTACHEMENT_BASE64 = "attachment_base64";
		String DESCRIPTION_EXPENSE_REPORT = "description";
		String URGENT = "urgent";
		String PRESCRIPTION_QUANTITY = "prescription_quantity";
		String FREE_ORDER = "freeOrder";
		String ACTIVITY_TYPE_ID = "activity_type_id";
		String ACTIVITY_DATE = "activity_date";
		String NETWORK_ID = "network_id";
		String PLANNING_DATE = "planning_date";
		String PLANNING_TIME = "planning_time";
		String ACTION_MARKETING_ID = "action_marketing_id";
		String ISSUE_DATE = "issue_date";
		String ACTION_MARKETING_DATE = "action_marketing_date";
		String OPPORTUNITY_NOTE_DATE = "opportunity_note_date";
		String POSITION_DATE = "position_date";
		String PATIENT_NUMBER = "patient_number";
		String NOTIFICATION_DATE = "notification_date";
		String SUPERVISOR_ID = "supervisor";
		String GADGET_ID = "gadget_id";
		String GADGET_QUANTITY = "gadget_quantity";
		String RESPONSIBLE = "responsible";
		String TARGET = "target";
		String VERSION = "version";
		String QUANTITY_UNIT = "quantity_unit";
		String ONESIGNAL_USER_ID = "onesignal_user_id";
		String MOBILE_APP_VERSION = "mobile_app_version";
		String ICON = "icon";
		String BI_PANELS = "bi_panels";
		String GOAL_ITEM_ID = "Goal_item_id";
		String GOAL_ID = "Goal_id";
		String activity = "activity";
		String CHECKED = "checked";
		String ITEMS_ORDER = "item_order";
		String FIRST_DATE = "first_date";
		String LAST_DATE = "last_date";
		String MONTH = "month";
		String GOAL_MONTH = "goal_month";
		String GOAL_TYPE = "goal_type";
		String MIN_VALIDATORS_NUMBER = "min_validators_number";
		String VALIDATION_TYPE = "validation_type";
		String EVENT_TYPE = "event_type";
		String NOTIFICATION_METHOD = "notification_method";
		String VALIDATION_STEP_ID = "validation_step_id";
		String PLANNING_ID = "planning_id";
		String VALIDATION_STATUS_DATE = "validation_creation_date";
		String NOTIFICATION_RECEIVER = "notification_receiver";
		String PLANNING_VALIDATION_ID = "planning_validation_id";
		String SAMPLE_DATE = "sample_date";
		String QUANTITY = "quantity";
		String DATE_MANUFACTURE = "date_manufacture";
		String EXPIRATION_DATE = "expiration_date";
		String DELIVERY_DATE = "delivery_date";
		String BATCH_NUMBER = "batch_number";
		String PERIOD = "period";
		String LINK = "link";
		String REPORT_CRON_ID = "report_cron_id";
		String LAB_GRATUITY = "lab_gratuity";
		String IDENTIFIER = "identifier";
		String WEEK_NUMBER = "week_number";
		String PLACEMENT_METHOD = "placement_method";
		String COMMENT_RATING = "comment_rating";
		String PREDICTION_DATE = "prediction_date";
		String ORDER_QUNATITY_PREDICTION = "order_quantity_prediction";
		String CODE = "code";
		String POSITION_ID = "position_id";
		String SCRAPPING_ID = "scrapping_id";
		String PURCHASE_ORDER_TEMPLATE_ID = "purchase_order_template_id";
		String GIFT_ID = "gift_id";
		String COMMISSION_ID = "commission_id";
		String PHOTO = "photo";
		String ACTIVITY_ID = "activity_id";
		String COMMENT_RATING_NOTIFICATION = "comment_rating_notification";
		String ORDER_PREDICTION_CRON = "order_prediction_cron";
		String DISPLACEMENT_CRON = "displacement_cron";
		String ERP_SYNC_CRON = "erp_sync_cron";
		String REPORT_VALIDATION_CRON = "report_validation_cron";
		String REPORTING_REMINDER_CRON = "reporting_reminder_cron";
		String EXPENSE_DISTANCE_CRON = "expense_distance_cron";
		String ERP_TYPE = "erp_type";
		String ERP_URL = "erp_url";
		String WEIGHT = "weight";
		String FEATURES = "features";
		String ACTION_TYPE = "action_type";
		String PERIODICITY = "periodicity";
		String START_AFTER = "start_after";
		String REPEATING_PERIOD = "repeating_period";
		String REPETATION_EACH = "repetation_each";
		String REPORTING_STARTING_TIME = "reporting_starting_time";
		String REPORTING_ENDING_TIME = "reporting_ending_time";
		String ACTIVITY_TYPE_EVENT = "activity_type_event";
		String DELAYED_REPORTING_TOLERENCE = "delayed_reporting_tolerence";
		String NO_GEOLOCATION_TOLERENCE = "no_geolocation_tolerence";
		String DOCTOR_ID = "dotor_id";
		String HOST = "host";
		String PORT = "port";
		String EMAIL_PASSWORD = "email_password";
		String INVALID_VISITS_NUMBER = "invalid_visits_number";
		String VALID_VISITS_NUMBER = "valid_visits_number";
		String VALID_GEOLOCATED_VISITS_NUMBER = "valid_geolocated_visits_number";
		String INVALID_GEOLOCATED_VISITS_NUMBER = "invalid_geolocated_visits_number";
		String DAY = "day";
		String HOLIDAY_TYPE = "holiday_type";
		String HOLIDAY_DATE = "holiday_date";
		String COMPAGNON_ID = "compagnion_id";
	}

	interface Tables {

		String Note = "Note";
		String FAVORITE_MENU_ITEM = "Favorite_Menu_Item";
		String Survey = "Survey";
		String LOAD_PLAN_ITEM = "LOAD_PLAN_ITEM";
		String PRESENTATION_TIME_TRACKING = "presentation_time_tracking";
		String SAMPLE_SUPPLY_ITEM = "sample_supply_item";
		String MISSION = "mission";
		String MESSAGE_TAG = "message_tag";
		String MESSAGE = "message";
		String DISCOUNT = "discount";
		String ESTABLISHMENT = "establishment";
		String FREE_QUANTITY_RULE_ITEM = "free_quantity_rule_item";
		String POTENTIAL_FREE_QUANTITY = "potential_free_quantity";
		String PROSPECT_TYPE_FREE_QUANTITY = "prospect_type_free_quantity";
		String PRODUCT_FREE_QUANTITY = "product_free_quantity";
		String FREE_QUANTITY_RULE = "free_quantity_rule";
		String COMMISSION = "commission";
		String DELEGATE_COMMISSION = "delegate_commission";
		String COMMISSION_ITEM = "commission_item";
		String PRODUCT_COMMISSION = "product_commission";
		String WHOLESALER_COMMISSION = "wholesaler_commission";
		String USER_COMMISSION = "user_commission";
		String GIFT_PURCHASE_ORDER_TEMPLATE = "gift_purchase_order_template";
		String USER_PURCHASE_ORDER_TEMPLATE = "user_purchase_order_template";
		String USER_PROSPECT = "user_prospects";
		String PROSPECT = "prospect";
		String EXPENSE_TYPE = "expense_type";
		String PROSPECTTYPE = "prospect_type";
		String NEXTACTIONRULE = "next_action_rule";
		String PREFERENCE = "preference";
		String PROSPECT_ACTIVITY= "prospect_activity";
		String BUDGETALLOCATION = "budget_allocation";
		String INTEREST = "interest";
		String MARKETING_ACTION_TYPE = "marketing_action_type";
		String ContactType = "contact_type";
		String EXPENSE_REPORT = "expense_report";
		String PURCHASEORDER = "purchase_order";
		String VISIT = "visit";
		String MARKETING = "marketing";
		String ACTIONMARKETING = "action_marketing";
		String OPPORTUNITYNOTE = "opportunity_note";
		String POSITION = "position";
		String ROLE_PERMISSIONS = "role_permissions";
		String CATEGORY = "category";
		String PREAFFECTATION = "pre_affectation";
		String ROLE = "role";
		String USER = "user";
		String DELEGATE = "delegate";
		String SPECIALITY = "speciality";
		String SECTOR = "sector";
		String Visit_Product = "visits_products";
		String RECOVERY = "recovery";
		String ATTACHMENT = "attachment";
		String PRODUCT = "product";
		String WHOLESALER = "wholesaler";
		String CATEGORIES_PRODUCTS = "categories_products";
		String RANGES_PRODUCTS = "ranges_products";
		String SECTORS_NOTES = "sectors_notes";
		String SPECIALITIES_NOTES = "specialities_notes";
		String GAMMES_PROSPECTS = "gammes_prospects";
		String TYPENOTEFRAIS_NOTEFRAIS = "typeNoteFrais_NoteFrais";
		String GAMMES_USERS = "gammes_users";
		String LOCALITY = "locality";
		String USERS_ROLES = "users_roles";
		String CONFIGURATION = "configuration";
		String POTENTIAL = "potential";
		String POTENTIAL_LABO = "potentialLabo";
		String RANGE = "range";
		String USER_GOALS = "user_goals";
		String EXPENSE_TYPE_USERS = "expense_type_users";
		String GIFT = "gift";
		String ISSUE = "issue";
		String ACTIVITY = "activity";
		String NOTIFICATION = "notification";
		String ACTIVITYTYPE = "activity_type";
		String NETWORK = "network";
		String DELEGATE_TYPE = "delegate_type";
		String USER_RANGE = "user_range";
		String PROSPECT_RANGE = "prospect_range";
		String PROSPECT_PREFERENCE = "prospect_preference";
		String PROSPECT_INTEREST = "prospect_interest";
		String PROSPECT_CONTACT_TYPE = "prospect_contact_type";
		String PRODUCT_POTENTIAL = "product_Potential";
		String PURCHASE_ORDER_TEMPLATE = "purchase_order_template";
		String PURCHASE_ORDER_TEMPLATE_ITEM = "purchase_order_template_item";
		String PLANNING = "planning";
		String PLANNING_VALIDATION = "planning_validation";
		String DELEGATE_DISPLACEMENT= "delegate_displacement";
		String LOAD_PLAN = "load_plan";
		String ACTIONMARKETING_PRODUCTS = "action_marketing_products";
		String ACTIONMARKETING_PROSPECTS = "action_marketing_prospects";
		String OPPORTUNITYNOTE_PRODUCTS = "opportunity_note_products";
		String OPPORTUNITYNOTE_PROSPECTS = "opportunity_note_prospects";
		String OPPORTUNITYNOTE_PHARMACIES = "opportunity_note_pharmacies";
		String POTENTIAL_GOAL = "potential_goal";
		String DELEGATE_POTENTIAL_GOAL = "delegate_potential_goal";
		String GOAL_ITEM = "goal_item";
		String GOAL = "goal";
		String VALIDATION_STEP = "validation_step";
		String NOTIFICATION_RULE = "notification_rule";
		String VALIDATION_STATUS = "validation_status";
		String SAMPLE_SUPPLY = "sample_supply";
		String REPORT_CRON = "report_cron";
		String GIFT_SUPPLY = "gift_supply";
		String ProspectOrderPrediction = "prospect_order_prediction";
		String AUTOMATION_RULE = "automation_rule";
		String REPORT_VALIDATION = "report_validation";
		String HOLIDAY = "holiday";
	}

	interface Common {
		String PUBLIC_SCHEMA = "public";
		String OK = "OK";
		String KO = "KO";
		String NOT_ALLOWED = "NOT_ALLOWED";
		int NB_SCHEDULED_VISIT = 60;
		String ORDER_LABEL = "Ordre";
		String COVERAGE = "Couverture";
	}

	interface Sequences {
		String NOTE_SEQUENCE = "note_sequence";

		String SURVEY_SEQUENCE = "survey_sequence";
		String LOAD_PLAN_ITEM_SEQUENCE = "load_plan_item_sequence";
		String SAMPLE_SUPPLY_ITEM_SEQUENCE = "sample_supply_item_sequence";
		String ESTABLISHMENT_SEQUENCE = "establishment_sequence";
		String MESSAGE_SEQUENCE = "message_sequence";
		String MESSAGE_TAG_SEQUENCE = "message_tag_sequence";
		String DISCOUNT_SEQUENCE = "discount_sequence";
		String FREE_QUANTITY_RULE_ITEM_SEQUENCE = "free_quantity_rule_item_sequence";
		String FREE_QUANTITY_SEQUENCE = "free_quantity_sequence";
		String COMMISSION_SEQUENCE = "commission_sequence";
		String DELEGATE_COMMISSION_SEQUENCE = "delegate_commission_sequence";
		String COMMISSION_ITEM_SEQUENCE = "commission_item_sequence";
		String PROSPECT_SEQUENCE = "prospect_id_sequence";
		String EXPENSE_TYPE_SEQUENCE = "expense_type_id_sequence";
		String EXPENSE_REPORT_SEQUENCE = "expense_report_id_sequence";
		String PROSPECT_TYPE_SEQUENCE = "prospect_type_id_sequence";
		String NEXT_ACTION_RULE_SEQUENCE = "next_action_rule_id_sequence";
		String PREFERENCE_SEQUENCE = "preference_id_sequence";
		String PROSPECT_ACTIVITY_SEQUENCE= "prospect_activity_id_sequence";
		String BUDGET_ALLOCATION_SEQUENCE = "budget_allocation_id_sequence";
		String INTEREST_SEQUENCE = "interest_id_sequence";
		String MARKETING_ACTION_TYPE_SEQUENCE = "marketing_action_type_id_sequence";
		String CONTACT_TYPE_SEQUENCE ="contact_type_id_sequence";
		String MISSION_SEQUENCE = "mission_id_sequence";
		String PRESENTATION_TIME_TRACKING_SEQUENCE = "presentation_time_tracking_id_sequence";
		String PURCHASEORDER_SEQUENCE = "purchase_order_id_sequence";
		String ATTACHMENT_SEQUENCE = "attachment_id_sequence";
		String ACTIONMARKETING_SEQUENCE = "action_marketing_id_sequence";
		String OPPORTUNITYNOTE_SEQUENCE = "opportunity_note_id_sequence";
		String LOCALISATION_SEQUENCE = "localisation_id_sequence";
		String MARKETING_SEQUENCE = "marketing_id_sequence";
		String ROLE_SEQUENCE = "role_id_sequence";
		String VISIT_SEQUENCE = "visit_id_sequence";
		String PURCHASE_ORDER_TEMPLATE_SEQUENSE = "purchase_order_template_sequence";
		String SPECIALITY_SEQUENCE = "speciality_id_sequence";
		String SECTOR_SEQUENCE = "sector_id_sequence";
		String USER_SEQUENCE = "user_id_sequence";
		String PRODUCT_SEQUENCE = "product_id_sequence";
		String WHOLESALER_SEQUENCE = "wholesaler_id_sequence";
		String CATEGORY_SEQUENCE = "category_id_sequence";
		String FAVORITE_SEQUENCE = "favorite_id_sequence";
		String PREAFFECTATION_SEQUENCE = "pre_affectation_id_sequence";
		String DELEGATE_SEQUENCE = "delegate_id_sequence";
		String VISIT_PRODUCT_SEQUENCE = "visit_product_id_sequence";
		String PURCHASE_ORDER_TEMPLATE_ITEM_SEQUENCE = "purchase_order_template_item_sequence";
		String LOCALITY_SEQUENCE = "locality_id_sequence";
		String CONFIGURATION_SEQUENCE = "configuration_id_sequence";
		String POTENTIAL_SEQUENCE = "potential_id_sequence";
		String ISSUE_SEQUENCE = "issue_id_sequence";
		String ACTIVITY_SEQUENCE = "activity_id_sequence";
		String NOTIFICATION_SEQUENCE = "notification_id_sequence";
		String ACTIVITY_TYPE_SEQUENCE = "activity_type_id_sequence";
		String EXPENSE_TYPE_USERS_SEQUENCE = "expense_type_users_id_sequence";
		String NETWORK_SEQUENCE = "network_id_sequence";
		String DELEGATE_TYPE_SEQUENCE = "delegate_type_id_sequence";
		String POTENTIAL_LABO_SEQUENCE = "potential_labo_id_sequence";
		String RANGE_SEQUENCE = "range_id_sequence";
		String GIFT_SEQUENCE = "gift_id_sequence";
		String AFFECTATION_SEQUENCE = "affectation_id_sequence";
		String PRODUCT_POTENTIAL_SEQUENCE = "product_potential_id_sequence";
		String PLANNIG_SEQUENCE = "planning_sequence";
		String PLANNIG_VALIDATION_SEQUENCE = "planning_validation_sequence";
		String DELEGATE_DISPLACEMENT_SEQUENCE = "delegate_displacement_sequence";
		String LOAD_PLAN_SEQUENCE = "load_plan_sequence";
		String POTENTIAL_GOAL_SEQUENCE = "potential_goal_sequence";
		String GOAL_ITEM_SEQUENCE = "goal_item_sequence";
		String GOAL_SEQUENCE = "goal_sequence";
		String VALIDATION_STEP_SEQUENCE = "validation_step_sequence";
		String NOTIFICATION_RULE_SEQUENCE = "notification_rule_sequence";
		String VALIDATION_STATUS_SEQUENCE = "validation_status_sequence";
		String SAMPLE_SUPPLY_SEQUENCE = "sample_supply_sequence";
		String REPORT_CRON_SEQUENCE = "report_cron_sequence";
		String GIFT_SUPPLY_SEQUENCE = "gift_supply_sequence";
		String PROSPECT_ORDER_PREDICTION__SEQUENCE = "prospect_order_prediction_sequence";
		String AUTOMATION_RULE_SEQUENCE = "automation_rule_sequence";
		String REPORT_VALIDATION_SEQUENCE = "report_validation_sequence";
		String HOLIDAY_SEQUENCE = "holiday_sequence";
	}

	interface Numbers {
		int N_1 = 1;
		int N_14 = 14;
		int N_35 = 35;
		int N_85 = 85;
		int N_64 = 64;
		int N_120 = 120;
		int N_180 = 180;
		int N_255 = 255;
		int N_1024 = 1024;
		double LAT_DEFAULT_VALUE = 36.862499;
		double LNG_DEFAULT_VALUE = 10.195556;
	}

	interface Exceptions {
		String LIST_OF_NOTE_FRAIS_IS_NULL = "liste des note frais est null ou empty";
		String TOKEN_IS_EMPTY = "Token is empty";
		String NO_USER_WITH_ID = "no user with id =";
		String INVALID_CSV_FILE_FORMAT = "Format du fichier CSV est inattendu";
		String PROSPECT_DATA_NOT_FOUND_IN_CSV_FILE = "L'importation a été interrompue. Une erreur est servenue à cause du manque des données du prospect à la ligne ";
		String USER_DTO_IS_NULL = "userDto est null";
		String POTENTIAL_DTO_IS_NULL = "potentialDto est null";
		String POTENTIAL_NAME_IS_EMPTY = "potential name is empty";
		String TYPE_NOTE_FRAIS_NAME_IS_EMPTY = "Le nom du type note frais est vide";
		String ACTIVITYTYPE_NAME_IS_EMPTY = "Le nom de l'activité est vide";
		String SESSION_EXPIRE = "Votre session est expiré";
		String TYPE_IS_NULL = "typeNoteFrais est obligatoire";
		String NOTE_FRAIS_COULD_NOT_DELETED = "Impossible d'effacer la note frais";
		String ACTIVITY_COULD_NOT_DELETED = "Impossible d'effacer activité";
		String PlANNING_VALIDATION_COULD_NOT_DELETED = "Impossible d'effacer planning validation";
		String RECOVERY_COULD_NOT_DELETED = "Impossible d'effacer le recouvrement";
		String PURCHASE_ORDER_COULD_NOT_DELETED = "Impossible d'effacer le purchase order";
		String ACTIONMARKETING_COULD_NOT_DELETED = "Impossible d'effacer actionMarketing";
		String OPPORTUNITÉNOTE_COULD_NOT_DELETED = "Impossible d'effacer OpportunityNote";

		String PLANNING_COULD_NOT_DELETED = "Impossible d'effacer la planification";
		String TYPE_NOTE_FRAIS_COULD_NOT_DELETED = "Impossible d'effacer le type de note frais";
		String NOTE_FRAIS_DTO_ID_NULL = "noteFraisDto est null";
		String ACTIVITY_DTO_ID_NULL = "activityDto est null";
		String ALREADY_EXIST = "Le nom existe déjà";
		String SECTOR_TO_UPDATE_ALREADY_DELETED = "Le secteur que vous voulez modifier est déjà supprimé";
		String SECTOR_TO_DELETE_ALREADY_DELETED = "Le secteur ne peut pas etre supprimé";
		String GADGET_TO_UPDATE_ALREADY_DELETED = "Le gadget que vous voulez modifier est déjà supprimé";
		String GADGET_TO_DELETE_ALREADY_DELETED = "Le gadget ne peut pas etre supprimé";
		String ACTIONMARKETING_TO_DELETE_ALREADY_DELETED = "L'action marketing ne peut pas etre supprimé";
		String OPPORTUNITYNOTE_TO_DELETE_ALREADY_DELETED = "la note d'opportunité ne peut pas etre supprimé";
		String ACTIONMARKETING_TO_UPDATE_ALREADY_DELETED = "L'action marketing que vous voulez modifier est déjà supprimé";
		String OPPORTUNITYNOTE_TO_UPDATE_ALREADY_DELETED = "L'OPPORTUNITY NOTE  que vous voulez modifier est déjà supprimé";

		String NOTEFRAIS_TO_UPDATE_ALREADY_DELETED = "La note frais que vous voulez modifier est déjà supprimé";
		String ACTIVIYUTO_UPDATE_ALREADY_DELETED = "L'ativité que vous voulez modifier est déjà suppriméé";
		String GONFIGURATION_TO_UPDATE_ALREDY_DELETED = "Le configuration que vous voulez modifier est déjà supprimé";

		String OK = "OK";
		String KO = "KO";
		String LOCALITY_TO_DELETE_ALREADY_DELETED = "La localité ne peut pas être supprimé, elle est attaché à un prospect";
		String TYPE_NOTE_FRAIS_TO_DELETE_ATTACHED_NOTE_FRAIS = "Ce type ne peut pas être supprimé, il est attaché à une note frais";
		String EMAIL_ALREADY_EXIST = "Adresse email existe déja";
		String PRODUCT_NAME_ALREADY_EXIST = "Le nom du produit existe déja";
		String WHOLESALER_NAME_ALREADY_EXIST = "Le nom du grossite existe déja";
		String NETWORK_NAME_ALREADY_EXIST = "Le nom du réseau existe déja";
		String PHONE_ALREADY_EXIST = "Téléphone existe déja";
		String USERNAME_ALREADY_EXIST = "Identifiant existe déja";
		String PROSPECT_TO_UPDATE_ALREADY_DELETED = "Le prospect que vous voulez modifier est déjà supprimé";
		String GSM_ALREADY_EXIST = "GSM existe déjà";

		String GAMME_TO_DELETE_ALREADY_DELETED = "La gamme ne peut pas etre supprimé";
		String SPECIALITY_TO_DELETE_ALREADY_DELETED = "La spécialité ne peut pas etre supprimé";
		String ROLE_TO_DELETE_ALREADY_DELETED = "Le role ne peut pas etre supprimé";

		String LOCALITY_TO_UPDATE_ALREADY_DELETED = "La localité que vous voulez modifier est déjà supprimée";
		String CANNOT_DELETE_PROSPECT = "Le prospect sélectionné a des visits, donc ne peut pas être supprimé";
		String CANNOT_DELETE_DELEGATE = "Le délégé ne peut pas être supprimé car il existe des secteurs qui sont liés à ce délégé";
		String CANNOT_DELETE_PRODUCT = "Le produit ne peut pas être supprimé car il existe des visites associés a ce produit";
		String CANNOT_DELETE_GAMME = "La gamme est déja utilisée et ne peut pas être supprimée ";
		String CANNOT_DELETE_WHOLESALER = "Le grossiste est déja utilisé et ne peut pas être supprimé ";
		String POTENTIAL_TO_UPDATE_ALREADY_DELETED = "Le Potential que vous voulez modifier est déjà supprimé";
		String EMPTY_PROSPECTSAFFECTATION_USER = "Affectation user is empty";
		String EMPTY_PLANNING_VALIDATION = "Planning validation is empty";
		String EMPTY_PRODUCT_POTENTIAL = "Product potential is empty";
		String EMPTY_PROSPECTSAFFECTATION_PROSPECT = "Affectation  prospect is empty";
		String EMPTY_SECTOR = "Sector name is empty";
		String EMPTY_SECTOR_USER = "Sector user is empty";
		String USER_ALREADY_DELETED = "Utilisateur déjà supprimé";
		String PROSPECT_ALREADY_DELETED = "prospect déjà supprimé";
		String AFFECTATION_ALREADY_DELETED = "Affectation déjà supprimée";
		String PRODUCT_ALREADY_DELETED = "produit déjà supprimé";
		String SECTOR_LINKED_TO_PROSPECT = "Le secteur ne peut pas être supprimé car il existe des prospects liés à ce secteur";
		String SECTOR_LINKED_TO_LOCALITY = "Le secteur ne peut pas être supprimé car il existe des localités qui sont liés à ce secteur";
		String SECTOR_DTO_NULL = "sectorDto is null";
		String CHOOSE_DELEGATE = "Choisir un délégué";
		String EMPTY_LOCALITY_NAME = "Locality name is empty";
		String EMPTY_SECTOR_ID = "Sector id is empty";
		String SECTOR_ALREADY_DELETED = "secteur déjà supprimé";
		String LOCALITY_DTO_NULL = "localityDto is null";
		String EMPTY_NAME = "nom est vide!";
		String SECTOR_DELETED = "Le secteur est supprimé";
		String EMPTY_OR_NULL_SPECIALITIES = "Specialities is null or empty";
		String EMPTY_SPECIALITY = "Speciality name is empty";
		String NULL_PRODUCT = "product is null";
		String NULL_WHOLESALER = "wholesaler is null";
		String NULL_DTO_PRODUCT = "productDto is null";
		String NULL_DTO_WHOLESALER = "wholesalerDto is null";
		String NULL_DTO_PRODUCT_POTENTIAL = "productsPotentialDto is null";
		String NULL_CHARGE_PLAN = "chargePlan is null";
		String NULL_NOTIFICATION = "notifications is null";
		String NO_PRODUCT_WITH_ID = "no product with id = ";
		String NO_WHOLESALER_WITH_ID = "no wholesaler with id = ";
		String VALUE_OF_CYCLE_NULL = "Cycle value is null";
		String NULL_AFFECTATION = "Affectattion is null";
		String NULL_SPECIALTY = "Specialty dto is null";
		String NULL_SECTOR = "Sector dto is null";
		String NULL_LOCALITY = "Locality dto is null";
		String NULL_TYPE = "Prospect type dto is null";
		String GAMME_TO_UPDATE_ALREADY_DELETED = "La Gamme que vous voulez modifier est déjà supprimé";
		String SPECIALITY_TO_UPDATE_ALREADY_DELETED = "La spécialité que vous voulez modifier est déjà supprimé";
		String EMPTY_ACTIONMARKETING_VALIDATION = "empty_actionmarketing_validation";
		String NULL_POTENTIAL = "potential dto is null";
		String NULL_GOALS = "goals request dto is null";
		String ROLE_NAME_IS_EMPTY = "le nom du role est obligatoire";
		String FAVORITE_IS_EMPTY = "le favoris est obligatoire";
		String ROLE_DTO_IS_NULL = "RoleDto is null";
		String ROLE_TO_UPDATE_ALREADY_DELETED = "le role que vous voulez modifier est déjà supprimé";
		String GOAL_TO_UPDATE_ALREADY_DELETED = "L'objectif que vous voulez modifier est déjà supprimé";
		String GOAL_NAME_IS_EMPTY = "le nom de l'objectif est obligatoire";
		String GOAL_DTO_IS_NULL = "GoalDto is null";
		String GOAL_DELEGATE_IS_EMPTY = "Délégué est obligatoire";
		String GOAL_ITEM_IS_EMPTY = "Les objectifs sont obligatoires";
		String PRODUCT_PURCHASE_ORDER_TEMPLATE_ITEM_IS_EMPTY = "les produits sont obligatoires";
		String ROLE_NAME_IS_NUMBER = " le nom du role est une  chaine de caractéres";
		String GOAL_NAME_IS_NUMBER = "le nom est un nombre ";
		String VALIDATION_TYPE_IS_EMPTY = "le type de validation est obligatoire";
		String VALIDATION_STEPS_IS_EMPTY = "les étapes de validation sont obligatoires";
		String EVENT_IS_EMPTY = "les évenements sont obligatoires";
		String NOTIFICATIONS_IS_EMPTY = "les notifications sont obligatoires";
		String VALIDATION_STATUS_IS_EMPTY = "les étapes de validation sont obligatoires";
		String NUMBER_OF_VALIDATORS_IS_EMPTY = "le nombre de validateurs est obligatoire";
		String VALIDATION_INPROGRESS = "Vous ne pouvez pas modifier le workflow car il dispose encore de validations en cours";
		String USED_EXPENSE_TYPE = "Le type que vous voulez supprimer est réferencé par des notes de frais";
		String USED_PRSOPECT = "Le prospect que vous voulez supprimer possède des visites";
		String NULL_SAMPLE = "sample is null";
		String PRODUCT_IS_EMPTY = "le produit est obligatoire";
		String USER_IS_EMPTY = "le délégué est obligatoire";
		String QUANTITY_IS_EMPTY = "la quantité  est obligatoire";
		String DELIVERY_DATE_IS_EMPTY = "le date de livraison  est obligatoire";
		String PERIOD_IS_EMPTY = "la période est obligatoire";
		String LINK_IS_EMPTY = "le lien du rapport est obligatoire";
		String REPORTCRON_USER_IS_EMPTY = "les utilisateurs sont obligatoire";
		String REPORT_CRON_ID_DTO_IS_NULL = "reportCron est vide";
		String REPORT_CRON_PERIOD_IS_EMPTY = "la période est obligatoire";
		String REPORT_CRON_LINK_IS_EMPTY = "le lien du rapport est obligatoire";
		String REPORT_CRON_TO_UPDATE_ALREADY_DELETED = "déja supprimé";
		String REPORT_CRON_USERS_IS_EMPTY = "les utilisateurs sont obligatoires";
		String VISIT_PRODUCT_COULD_NOT_DELETED = "Could not delete visit product";
		String NULL_PROSPECT_TYPE = "le type de prospect est null";
		String PROSPECT_TYPE_DTO_IS_NULL = "prospectTypeDto est null";
		String NEXT_ACTION_RULE_DTO_IS_NULL = "nextActionRuleDto est null";
		String PREFERENCE_DTO_IS_NULL = "preferenceDto est null";
		String BUDGET_ALLOCATION_DTO_IS_NULL = "budgetAllocationDto est null";
		String PROSPECT_TYPE_NAME_IS_EMPTY = "prospectType name is empty";
		String NEXT_ACTION_RULE_TOTAL_REVENUE_IS_EMPTY = "nextActionRule total revenue is empty";
		String NEXT_ACTION_RULE_PERIOD_IS_EMPTY = "nextActionRule period is empty";
		String NEXT_ACTION_RULE_ACTION_IS_EMPTY = "nextActionRule action is empty";
		String PREFERENCE_NAME_IS_EMPTY = "preference name is empty";
		String INTEREST_DTO_IS_NULL = "interestDto est null";
		String MARKETING_ACTION_TYPE_DTO_IS_NULL = "marketingActionTypeDto est null";
		String CONTACT_TYPE_DTO_IS_NULL = "contactTypeDto est null";
		String INTEREST_NAME_IS_EMPTY = "interest name is empty";
		String MARKETING_ACTION_TYPE_NAME_IS_EMPTY = "marketing action type name is empty";
		String CONTACT_TYPE_NAME_IS_EMPTY = "contact type name is empty";
		String BUDGET_ALLOCATION_YEAR_IS_EMPTY ="budgetAllocation year is empty";
		String BUDGET_ALLOCATION_MONTHLY_BUDGET_IS_EMPTY ="budgetAllocation monthly budget is empty";
		

	}

	interface Headers {
		String AUTH_TOKEN_HEADER_NAME = "authorization";
	}

	interface Gender {
		String MEN = "homme";
		String WOMEN = "femme";
	}

	interface ContractType {
		String CDI = "CDI";
		String SIVP = "SIVP";
		String CPP = "CPP";
		String CDD = "CDD";

	}

	interface WorkType {
		String MEDICAL = "MEDICAL";
		String PHARMACEUTICAL = "PHARMACEUTICAL";
		String ADMIN = "ADMIN";

	}

	interface NotificationMethod {
		String PUSH = "PUSH";
		String EMAIL = "EMAIL";
		String APP_NOTIF = "APP_NOTIF";
		String SMS = "SMS";

	}

	interface ValueType {
		String NUMBER_OF_VISITS = "Nombre de visites";
		String REPORT_FILLING_RATE = "Taux de remplissage rapport";
		String BLANKET = "Taux R/P";
		String SALES = "Nombre de stock";
		String SAMPLE_NUMBER = "Nombre d'échantillons";
		String NUMBER_OF_ORDERS = "Nombre de commandes";
		String PROSPECT_SATISFACTION = "Satisfaction par commentaire";
		String GROSS_SALES = "Chiffre d'affaire";
		String GADGET = "Nombre des gadgets";
		String PROSPECT_PRODUCT_SATISFACTION = "Satisfaction produit";
		String PRODUCT_ORDER_PRESENTATION = "Ordre de présentation";
		String PERCENTAGE = "Pourcentage";
		String AMOUNT = "Montant";
		// String VISITS_AVERAGE = "Moyenne des visites";
		String SYNCHRONISATION_DELAY = "Délai de synchronisation";
	}

	interface GroupType {
		String DELEGUE = "Délégué";
		String PROSPECT = "Prospect";
		String SECTOR = "Secteur";
		String LOCALITY = "Localité";
		String ACTIVITY = "Activité";
		String POTENTIAL = "Potentiel";
		String SPECIALITY = "Spécialité";
		String PRODUCT = "Produit";
		String SATISFACTION = "satisfaction";
		String PRESENTATION_ORDER = "Ordre de présentation";

	}

	interface QueryBuild {
		String START_VISIT_HISTORY_GROUP_QUERY = "SELECT new com.intellitech.birdnotes.model.dto.VisitHistoryGroupDto(";
		String GROUP_BY = " GROUP BY (";
		String VISIT_TABLE = "vp.visit";
		String FROM_VISIT_TABLE = " from Visit v";
		String FROM_VISITS_PRODUCTS_TABLE = " from VisitsProducts vp";

		String FROM_PLANNING_TABLE = "from Planning p ";
		String AND = " and ";
		String PROSPECT_ID = "prospectId";
		String SECTOR_ID = "sectorId";
		String LOCALITY_ID = "localityId";
		String ACTIVITY = "activity";
		String POTENTIAL = "potential";
		String SPECIALTY_ID = "specialityId";
		String PRODUCT_ID = "productId";
		String COUNT = "COUNT(";
		String SUM = "SUM(";
		String VISIT_PRODUCT = "vp";
		String VISIT = "v";
		String ID_COLUNM = ".id))";
		String SALE_QUANTITY_COLUNM = ".saleQuantity))";
		String SAMPLE_QUANTITY_COLUNM = ".sampleQuantity))";
		String ORDER_QUANTITY_COLUNM = ".orderQuantity))";
		String CONCAT = "CONCAT(";
		String FIRST_NAME_COLUNM = ".user.firstName";
		String LAST_NAME_COLUNM = ".user.lastName";
		String PROSPECT_NAME = ".prospect.name";
		String PROSPECT_SECTOR_NAME = ".prospect.sector.name";
		String PROSPECT_LOCALITY_NAME = ".prospect.locality.name";
		String PROSPECT_ACTIVITY = ".prospect.activity";
		String PROSPECT_POTENTIAL_NAME = ".prospect.potential";
		String PROSPECT_SPECIALTY_NAME = ".prospect.speciality.name";
		String PRODUCT_NAME = ".product.name";
		String INITIAL_DATE = "initialDate";
		String FINAL_DATE = "finalDate";

		String FIRST_DATE = "firstDate";
		String LAST_DATE = "lastDate";
		String USER_ID = "userId";
		String DELEGATE_ID = "delegateId";
		String PLANNING_USER_ID = ".user.id";
		String PLANNING_DELEGATE_ID = ".delegate.id";
		String PLANNING_PROSPECT_ID = ".prospect.id";
		String PLANNING_SECTOR_ID = ".prospect.sector.id";
		String PLANNING_LOCALITY_ID = ".prospect.locality.id";
		String PLANNING_ACTIVITY = ".prospect.activity";
		String PLANNING_POTENTIAL_ID = ".prospect.potential.id";
		String PLANNING_SPECIALITY_ID = ".prospect.speciality.id";
		String PRESCRIPTION_QUANTITY_COLUNM = ".prescriptionQuantity))";
		String SUPERVISOR_ID = "supervisorId";
		String SUPERIOR_ID = "superiorId";
		String SUB_USERS = "subUsers";
		String WHOLESALER_ID = "wholesalerId";

	}

	interface VisitHistory {
		String PARAMETERS = "parameters";
		String QUERY = "query";
		String WHERE_QUERY = "whereQuery";
		String COVERAGE_LIST = "coverageList";
		String TOTAL_COVERAGE = "totalCoverage";
		String DELEGATES = "delegates";
		String PRODUCTS = "products";
		String PROSPECTS = "prospects";
		String GROUPS = "groups";
		String VALUES = "values";
		String LOCALITIES = "localities";
		String SPECIALTIES = "specialties";
		String PRESENTATIO_ORDER = "presentation_order";
		String PRODUCT_SATISFACTION = "product_satisfaction";
		String POTENTIALS = "potentials";
		String COMMENT_RATINGS = "commentRatings";
	}

	interface ExpenseReport {
		String DELEGATES = "delegates";
		String EXPENSE_TYPE = "expenseType";
		String EXPENSE_REPORT = "expenseReport";
		String VALIDATIONS_STATUS = "validation_status";
	}

	interface Prospect {
		String PROSPECTS = "prospects";
		String PROSPECTS_COUNT = "prospectsCount";
		String PROSPECTS_CHANGED = "prospectChanged";
		String DUPLICATE_PROSPECTS = "duplicatePropects";
		String SPECIALTYS = "speciality";
		String TYPES = "types";
		String DELEGATES = "delegates";
		String POTENTIALS = "potentials";
		String SECTORS = "sectors";
		String LOCALITYS = "localitys";
		String PROSPECT_ACTIVITIES = "prospectActivities";
		String ESTABLISHMENTS = "establishments";
		String PROSPECT_TYPES = "prospectTypes";
	}

	interface Sector {
		String DELEGATES = "delegates";
		String SECTORS = "sectors";
	}

	interface Locality {
		String SECTORS = "sectors";
		String LOCALITYS = "localitys";
	}

	interface Periodicity {
		String ONCE = "ONCE";
		String PERIODIC = "PERIODIC";

	}

}
