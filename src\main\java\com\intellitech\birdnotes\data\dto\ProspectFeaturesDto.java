package com.intellitech.birdnotes.data.dto;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;

public class ProspectFeaturesDto {
	
	private BigInteger prospectId;
	private BigInteger productId;
	private Double visitMonth;
	private BigInteger cumulCmd;
	private BigInteger cumulEch;
	private BigInteger cumulVisit;
	private Float potential;
    private String locality;
    private String speciality;
    private String product;
    private Date fistVisitDate;
    private Date lastVisitDate;
	public float frequency;
    public long recency;
    public BigInteger cumulStock;
    public BigDecimal moySatisf;
    private Integer budget ;
    private String marketingActionType;
    private String activity;
    private Integer visitCount ;

    
    
    
	public BigInteger getProspectId() {
		return prospectId;
	}
	public void setProspectId(BigInteger prospectId) {
		this.prospectId = prospectId;
	}
	public BigInteger getProductId() {
		return productId;
	}
	public void setProductId(BigInteger productId) {
		this.productId = productId;
	}
	public Double getVisitMonth() {
		return visitMonth;
	}
	public void setVisitMonth(Double visitMonth) {
		this.visitMonth = visitMonth;
	}
	public BigInteger getCumulCmd() {
		return cumulCmd;
	}
	public void setCumulCmd(BigInteger cumulCmd) {
		this.cumulCmd = cumulCmd;
	}
	public BigInteger getCumulEch() {
		return cumulEch;
	}
	public void setCumulEch(BigInteger cumulEch) {
		this.cumulEch = cumulEch;
	}
	public BigInteger getCumulVisit() {
		return cumulVisit;
	}
	public void setCumulVisit(BigInteger cumulVisit) {
		this.cumulVisit = cumulVisit;
	}
	public Float getPotential() {
		return potential;
	}
	public void setPotential(Float potential) {
		this.potential = potential;
	}
	public String getLocality() {
		return locality;
	}
	public void setLocality(String locality) {
		this.locality = locality;
	}
	public String getSpeciality() {
		return speciality;
	}
	public void setSpeciality(String speciality) {
		this.speciality = speciality;
	}
	public String getProduct() {
		return product;
	}
	public void setProduct(String product) {
		this.product = product;
	}
	public Date getFistVisitDate() {
		return fistVisitDate;
	}
	public void setFistVisitDate(Date fistVisitDate) {
		this.fistVisitDate = fistVisitDate;
	}
	public Date getLastVisitDate() {
		return lastVisitDate;
	}
	public void setLastVisitDate(Date lastVisitDate) {
		this.lastVisitDate = lastVisitDate;
	}
	public float getFrequency() {
		return frequency;
	}
	public void setFrequency(float frequency) {
		this.frequency = frequency;
	}
	public long getRecency() {
		return recency;
	}
	public void setRecency(long recency) {
		this.recency = recency;
	}
	public BigInteger getCumulStock() {
		return cumulStock;
	}
	public void setCumulStock(BigInteger cumulStock) {
		this.cumulStock = cumulStock;
	}
	public BigDecimal getMoySatisf() {
		return moySatisf;
	}
	public void setMoySatisf(BigDecimal moySatisf) {
		this.moySatisf = moySatisf;
	}
	public Integer getBudget() {
		return budget;
	}
	public void setBudget(Integer budget) {
		this.budget = budget;
	}
	public String getMarketingActionType() {
		return marketingActionType;
	}
	public void setMarketingActionType(String marketingActionType) {
		this.marketingActionType = marketingActionType;
	}
	public String getActivity() {
		return activity;
	}
	public void setActivity(String activity) {
		this.activity = activity;
	}
	public Integer getVisitCount() {
		return visitCount;
	}
	public void setVisitCount(Integer visitCount) {
		this.visitCount = visitCount;
	}

    
  
}
