package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.dto.RoleDto;

@Component("convertRoleToDto")
public class ConvertRoleToDto{
	private static final Logger LOG = LoggerFactory.getLogger(ConvertRoleToDto.class);

	public RoleDto convert(Role role) throws BirdnotesException {

		if (role == null) {
			LOG.error("role is null");
			throw new BirdnotesException("role is null");
		}
		RoleDto roleDto = new RoleDto();
		roleDto.setId(role.getId());
		roleDto.setName(role.getName());
		
		return roleDto;
	}
}

