package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Issue;
import com.intellitech.birdnotes.model.dto.IssueDto;
import com.intellitech.birdnotes.model.request.IssueRequest;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.IssueRepository;
import com.intellitech.birdnotes.service.IssueService;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Service("issueService")
@Transactional
public class IssueServiceImpl implements IssueService {
	private static final Logger LOG = LoggerFactory.getLogger(IssueServiceImpl.class);
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");

	@Autowired
	private IssueRepository issueRepository;
	
	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${mobileAppLogsPath}")
	private String mobileAppLogsPath;
		
	@Autowired
	private ConfigurationRepository configureRepository;


	@Override
	public List<IssueDto> findAll(IssueRequest issueRequest) throws BirdnotesException {
		Configuration config = configureRepository.findById(1);
		List<IssueDto> result = new ArrayList<>();
		List<Issue> allIssues = null;
		try {
			allIssues = issueRepository.findAll((issueRequest.getFirstDate()),
					(issueRequest.getLastDate()));
		} catch (Exception e) {
			LOG.error("An exception occurred ", e);
		}
		String logRootPath = uploadUrl + mobileAppLogsPath + File.separator ;

		for (Issue issue : allIssues) {
			IssueDto i = new IssueDto();
			i.setDate(issue.getDate());
            i.setId(issue.getId());
			i.setDescription(issue.getDescription());
			i.setUsername(issue.getUsername());
			i.setFilePath(config.getBackendUrl() + logRootPath + issue.getId()+ File.separator + BirdnotesConstants.Configuration.MOBILE_LOG_FILE_NAME);
			result.add(i);
		}
		return result;
	}

}
