package com.intellitech.birdnotes.batchprocessing;

import java.util.HashMap;

import org.springframework.batch.item.file.mapping.FieldSetMapper;
import org.springframework.batch.item.file.transform.FieldSet;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.enumeration.ImportStep;
import com.intellitech.birdnotes.model.ImportProspectDto;

@Component
class ProspectFieldSetMapper implements FieldSetMapper<ImportProspectDto> {

	public ImportProspectDto mapFieldSet(FieldSet fieldSet) {

		ImportProspectDto importProspectDto = new ImportProspectDto();
		HashMap<String, String> prospectData = new HashMap<>();
		prospectData.put("firstName", fieldSet.readString(0));
		prospectData.put("lastName", fieldSet.readString(1));
		prospectData.put("speciality", fieldSet.readString(2));
		prospectData.put("phone", fieldSet.readString(3));
		prospectData.put("gsm", fieldSet.readString(4));
		prospectData.put("address", fieldSet.readString(5));
		prospectData.put("sector", fieldSet.readString(6));
		prospectData.put("locality", fieldSet.readString(7));
		prospectData.put("activity", fieldSet.readString(8));
		prospectData.put("potential", fieldSet.readString(9));
		prospectData.put("type", fieldSet.readString(10));
		prospectData.put("email", fieldSet.readString(11));
		prospectData.put("note", fieldSet.readString(12));
		prospectData.put("delegates", fieldSet.readString(13));
		prospectData.put("longitude", fieldSet.readString(14));
		prospectData.put("latitude", fieldSet.readString(15));
		prospectData.put("grade", fieldSet.readString(16));
		prospectData.put("scrappingId", fieldSet.readString(17));
		prospectData.put("establishment", fieldSet.readString(18));
		importProspectDto.setProspectMap(prospectData);
		return importProspectDto;
	}
}
