package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.Sector;

@Repository
public interface EstablishmentRepository extends JpaRepository<Establishment, Long>{

	Establishment findByName(String name);

	@Modifying
	@Query("DELETE from Establishment WHERE id=:id")
	void deleteByID(@Param("id") Long id);
	
	@Override
	@Query("SELECT e from Establishment e ORDER BY e.name")
	List<Establishment> findAll();
	
	@Query("SELECT e from Establishment e where e.sector.id in (:sectorIds) OR e.sector.id is null ORDER BY e.name")
	List<Establishment> findEstablishmentByAffectedSectors(@Param("sectorIds") List<Long> sectorIds);
	
	Sector findFirstByNameIgnoreCase(String name);
	
	@Query("SELECT e from Establishment e where  LOWER(name) = LOWER(?1) AND id != ?2")
	Establishment findByNameAndAnotherId(String name, Long id);
	
	@Query("SELECT s.id from Establishment s WHERE s.id in (:establishments) order by s.name")
	List<Long> findWhereIdIn(@Param("establishments") List<Long> establishments);
	
	@Query("SELECT s.id FROM Establishment s")
	List<Long> getAllSectorsIds();
	
	@Query("SELECT s.name FROM Establishment s")
	List<String> findAllEstablishmentsNames();
}