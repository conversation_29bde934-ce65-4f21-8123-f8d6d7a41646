package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import com.intellitech.birdnotes.model.ProspectActivity;


@Repository
public interface ProspectActivityRepository extends JpaRepository<ProspectActivity, Long>{
	
	@Override
	@Query("SELECT pa from ProspectActivity pa ORDER BY pa.name")
	List<ProspectActivity> findAll();
	
	@Query("SELECT pa from ProspectActivity pa where  LOWER(name) = LOWER(?1)")
	ProspectActivity findByName(String name);
}