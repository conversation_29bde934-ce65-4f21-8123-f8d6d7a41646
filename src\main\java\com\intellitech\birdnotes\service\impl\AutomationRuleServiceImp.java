package com.intellitech.birdnotes.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.dao.XmlReader;
import com.intellitech.birdnotes.data.dto.AutomationRuleFormData;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Action;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.AutomationRule;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.AutomationRuleDto;
import com.intellitech.birdnotes.repository.ActivityRepository;
import com.intellitech.birdnotes.repository.ActivityTypeRepository;
import com.intellitech.birdnotes.repository.AutomationRuleRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.PlanningValidationRepository;
import com.intellitech.birdnotes.service.ActivityTypeService;
import com.intellitech.birdnotes.service.AutomationRuleService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesUtils;

@Service("automationRuleService")
@Transactional
public class AutomationRuleServiceImp implements AutomationRuleService {
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	private AutomationRuleRepository automationRuleRepository;
	private ActivityTypeRepository activityTypeRepository;
	private PlanningValidationRepository planningValidationRepository;
	private PlanningRepository planningRepository;
	private ActivityRepository activityRepository;
	private ActivityTypeService activityTypeService;
	private XmlReader xmlReader;

	@Autowired
	public AutomationRuleServiceImp(AutomationRuleRepository automationRuleRepository,
			ActivityTypeRepository activityTypeRepository, PlanningValidationRepository planningValidationRepository,
			PlanningRepository planningRepository, ActivityRepository activityRepository,
			ActivityTypeService activityTypeService, XmlReader xmlReader) {
		super();

		this.automationRuleRepository = automationRuleRepository;
		this.activityTypeRepository = activityTypeRepository;
		this.planningValidationRepository = planningValidationRepository;
		this.planningRepository = planningRepository;
		this.activityRepository = activityRepository;
		this.activityTypeService = activityTypeService;
		this.xmlReader = xmlReader;
	}

	@Autowired
	ResourceLoader resourceLoader;

	@Override
	public AutomationRule saveAutomationRule(AutomationRuleDto automationRuleDto) throws BirdnotesException {
		AutomationRule automationRule = null;
		if (automationRuleDto.getId() != null) {
			automationRule = automationRuleRepository.findOne(automationRuleDto.getId());
		}
		if (automationRule == null) {
			automationRule = new AutomationRule();
		}
		automationRule.setEventType(automationRuleDto.getEventType());
		automationRule.setActionType(automationRuleDto.getActionType());
		ActivityType activityType = activityTypeRepository.findOne(automationRuleDto.getActivityTypeId());
		automationRule.setActivityType(activityType);
		automationRule.setPeriodicity(automationRuleDto.getPeriodicity());
		automationRule.setRepeatingPeriod(automationRuleDto.getRepeatingPeriod());
		automationRule.setRepetationEach(automationRuleDto.getRepetationEach());
		automationRule.setStartAfter(automationRuleDto.getStartAfter());
		if (automationRuleDto.getActivityTypeEvent() != null) {
			ActivityType activityTypeEvent = activityTypeRepository.findOne(automationRuleDto.getActivityTypeEvent());
			automationRule.setActivityTypeEvent(activityTypeEvent);
		}
		return automationRuleRepository.save(automationRule);
	}

	@Override
	public void generatePlanningsFromRules(List<AutomationRule> automationRuleList, Activity activity) {

		for (AutomationRule automationRule : automationRuleList) {
			Date firstPlanningDate = BirdnotesUtils.addDaysToDate(activity.getActivityDate(),
					automationRule.getStartAfter().intValue());
			firstPlanningDate = BirdnotesUtils.postponeIfWeekend(firstPlanningDate,
					activity.getDelegate().getWorkingDaysPerWeek());
			
			Planning firstPlanning = createPlanning(activity, automationRule.getActivityType(), firstPlanningDate);
			//createActivity(activity, automationRule.getActivityType(), firstPlanningDate, firstPlanning);
			
			if (BirdnotesConstants.Periodicity.PERIODIC.equals(automationRule.getPeriodicity().getName())) {
				Date date = firstPlanningDate;
				Date executingDate = null;
				do {
					date = BirdnotesUtils.addDaysToDate(date, automationRule.getRepetationEach().intValue());
					executingDate = BirdnotesUtils.postponeIfWeekend(date,
							activity.getDelegate().getWorkingDaysPerWeek());
					Planning planning = createPlanning(activity, automationRule.getActivityType(), executingDate);
					//createActivity(activity, automationRule.getActivityType(), executingDate, planning);

				} while (date.getTime() < BirdnotesUtils
						.addDaysToDate(activity.getActivityDate(), automationRule.getRepeatingPeriod()).getTime());
			}
		}
	}

	public Planning createPlanning(Activity activityEvent, ActivityType activityType, Date planningDate) {
		Planning planning = new Planning();
		Activity activity = new Activity();
		activity.setActivityDate(planningDate);
		activity.setActivityType(activityType);
		activity.setHourNumber((long) 0);
		activity.setDelegate(activityEvent.getDelegate());
		activity.setIdentifier(new Date().getTime());
		activity.setProspect(activityEvent.getProspect());
		
		planning.setActivity(activity);
		planning.setDate(planningDate);
		planning.setIdentifier(new Date().getTime());
		planning.setDelegate(activity.getDelegate());
		planning.setProspect(activity.getProspect());
		activity.setPlanning(planning);
		activityRepository.save(activity);
		planningRepository.save(planning);
		Date mondayOfWeek = BirdnotesUtils.getMondayOfWeek(planningDate);
		PlanningValidation planningValidation = planningValidationRepository
				.findByUserAndDate(activity.getDelegate().getId(), mondayOfWeek);
		if (planningValidation == null) {
			planningValidation = new PlanningValidation();
			planningValidation.setDate(mondayOfWeek);
			planningValidation.setDelegate(activity.getDelegate());
			planningValidation.setIdentifier(new Date().getTime());
			planningValidation.setSpecificWeek(false);
			planningValidation.setStatus(UserValidationStatus.ACCEPTED);
			planningValidationRepository.save(planningValidation);
		}

		return planning;
	}

	/*public void createActivity(Activity activity, ActivityType activityType, Date activityDate, Planning planning) {
		Activity periodicActivity = new Activity();
		periodicActivity.setActivityDate(activityDate);
		periodicActivity.setActivityType(activityType);
		periodicActivity.setDelegate(activity.getDelegate());
		periodicActivity.setHourNumber((long) 0);
		periodicActivity.setPlanning(planning);
		periodicActivity.setIdentifier(new Date().getTime());
		periodicActivity.setProspect(activity.getProspect());
		activityRepository.save(periodicActivity);

	}*/

	@Override
	public List<AutomationRuleDto> findAll() throws BirdnotesException {
		List<AutomationRuleDto> result = new ArrayList<>();
		List<AutomationRule> allAutomationsRule = automationRuleRepository.findAll();
		if (allAutomationsRule != null && !allAutomationsRule.isEmpty()) {
			for (AutomationRule automationRule : allAutomationsRule) {
				AutomationRuleDto automationRuleDto = new AutomationRuleDto();
				automationRuleDto.setId(automationRule.getId());
				automationRuleDto.setEventType(automationRule.getEventType());
				automationRuleDto.setActionType(automationRule.getActionType());
				automationRuleDto.setActivityTypeId(automationRule.getActivityType().getId());
				automationRuleDto.setActivityTypeName(automationRule.getActivityType().getName());
				automationRuleDto.setPeriodicity(automationRule.getPeriodicity());
				automationRuleDto.setRepeatingPeriod(automationRule.getRepeatingPeriod());
				automationRuleDto.setRepetationEach(automationRule.getRepetationEach());
				automationRuleDto.setStartAfter(automationRule.getStartAfter());
				if (automationRule.getActivityTypeEvent() != null) {
					automationRuleDto.setActivityTypeEvent(automationRule.getActivityTypeEvent().getId());
				}
				result.add(automationRuleDto);

			}
		}

		return result;
	}

	@Override
	public AutomationRuleFormData getAllDataAutomationRuleForm() throws IOException, BirdnotesException {
		List<Event> events = xmlReader.getEventXml();
		List<Action> actions = xmlReader.getActionXml();
		List<ActivityTypeDto> activitiesType = activityTypeService.findAll();
		AutomationRuleFormData automationRuleFormData = new AutomationRuleFormData();
		automationRuleFormData.setEvents(events);
		automationRuleFormData.setActions(actions);
		automationRuleFormData.setActivitiesType(activitiesType);

		return automationRuleFormData;
	}

	@Override
	public void deleteAutomationRule(Long automationRuleId) {
		automationRuleRepository.delete(automationRuleId);

	}

}
