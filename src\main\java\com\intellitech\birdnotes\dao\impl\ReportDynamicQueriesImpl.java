package com.intellitech.birdnotes.dao.impl;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.repository.ProspectRepository;

@Component("reportDynamicQueriesImpl")
public class ReportDynamicQueriesImpl {
	@PersistenceContext
	private EntityManager entityManager;
	
	@Autowired
	ProspectRepository prospectRepository;
	
	@SuppressWarnings("unchecked")
	public List<Prospect> findProspect(String query, Map<String, Object> parameters) {

		Query result = entityManager.createQuery(query, Prospect.class);
		for (Entry<String, Object> keyValue : parameters.entrySet()) {
			result.setParameter(keyValue.getKey(), keyValue.getValue());
		}

		return result.getResultList();
	}
	
		
	
}
