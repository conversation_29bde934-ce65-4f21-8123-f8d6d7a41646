package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

import com.intellitech.birdnotes.model.GiftSupply;

public class GiftSupplyDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long giftId;
	private Long  delegateId;
	private Integer id;
	private String giftName;
	private String delegateName;
	private Long quantity;
	private Date deliveryDate;
	
	
	
	public GiftSupplyDto(GiftSupply gadgetSupply) {
		this.id = gadgetSupply.getId();
		this.delegateId = gadgetSupply.getDelegate().getId();
		this.giftId = gadgetSupply.getGift().getId();
		this.quantity = gadgetSupply.getQuantity();
		this.deliveryDate = gadgetSupply.getDeliveryDate();
		this.giftName = gadgetSupply.getGift().getName();
		this.delegateName = gadgetSupply.getDelegate().getFirstName() +' '+gadgetSupply.getDelegate().getLastName();
		
		
	}
	
	

	public GiftSupplyDto() {
		super();
	}



	public Long getGiftId() {
		return giftId;
	}



	public void setGiftId(Long giftId) {
		this.giftId = giftId;
	}



	public Long getDelegateId() {
		return delegateId;
	}



	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}



	public Integer getId() {
		return id;
	}



	public void setId(Integer id) {
		this.id = id;
	}



	public String getGiftName() {
		return giftName;
	}

	public void setGiftName(String giftName) {
		this.giftName = giftName;
	}



	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

	public Long getQuantity() {
		return quantity;
	}

	public void setQuantity(Long quantity) {
		this.quantity = quantity;
	}

	public Date getDeliveryDate() {
		return deliveryDate;
	}

	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}




}
