package com.intellitech.birdnotes.data.dto;

import java.text.SimpleDateFormat;

public class ExpenseReportItemDto {
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
	private String expenseDate;
	private String expenseTypeName;
	private String activityTypeName;
	private String description;
	private Float price;

	public ExpenseReportItemDto(String expenseDate, String expenseTypeName, String activityTypeName, String description,
			Float price) {
		super();
		this.expenseDate = format.format(expenseDate);
		this.expenseTypeName = expenseTypeName;
		this.activityTypeName = activityTypeName;
		this.description = description;
		this.price = price;

	}

	public String getExpenseDate() {
		return expenseDate;
	}

	public void setExpenseDate(String expenseDate) {
		this.expenseDate = expenseDate;
	}

	public String getExpenseTypeName() {
		return expenseTypeName;
	}

	public void setExpenseTypeName(String expenseTypeName) {
		this.expenseTypeName = expenseTypeName;
	}

	public String getActivityTypeName() {
		return activityTypeName;
	}

	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Float getPrice() {
		return price;
	}

	public void setPrice(Float price) {
		this.price = price;
	}

}
