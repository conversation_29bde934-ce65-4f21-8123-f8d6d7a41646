package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.convertor.ConvertTypeNoteFraisToDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.TypeNoteFraisRequestDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.ExpenseReportRepository;
import com.intellitech.birdnotes.repository.ExpenseTypeRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ExpenseTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("typeNoteFraisService")
@Transactional
public class ExpenseTypeServiceImpl implements ExpenseTypeService {

	private ExpenseTypeRepository expenseTypeRepository;

	private ConvertTypeNoteFraisToDto convertTypeNoteFraisToDto;

	private ExpenseReportRepository noteFraisRepository;

	private UserRepository userRepository;

	private DelegateRepository delegateRepository;
	
	@Autowired
	private MessageSource messageSource;
	@Autowired
	private ConfigurationService configurationService;
	@Autowired
	UserService userService;

	@Autowired
	public ExpenseTypeServiceImpl(ExpenseTypeRepository expenseTypeRepository,
			ConvertTypeNoteFraisToDto convertTypeNoteFriasToDto, ExpenseReportRepository noteFraisRepository,
			UserRepository userRepository, DelegateRepository delegateRepository) {
		this.expenseTypeRepository = expenseTypeRepository;
		this.convertTypeNoteFraisToDto = convertTypeNoteFriasToDto;
		this.noteFraisRepository = noteFraisRepository;
		this.userRepository = userRepository;
		this.delegateRepository = delegateRepository;

	}

	@Override
	public List<ExpenseTypeDto> findAll() throws BirdnotesException {
		List<ExpenseTypeDto> back = new ArrayList<>();
		List<ExpenseType> allTypeNoteFrais = expenseTypeRepository.findAll();
		for (ExpenseType typeNoteFrais : allTypeNoteFrais) {
			ExpenseTypeDto typeNoteFraisDto = new ExpenseTypeDto();
			typeNoteFraisDto.setId(typeNoteFrais.getId());
			typeNoteFraisDto.setName(typeNoteFrais.getName());
			typeNoteFraisDto.setPrice(typeNoteFrais.getPrice());
			typeNoteFraisDto.setRequiredAttachment(typeNoteFrais.getRequiredAttachment());
			if (typeNoteFrais.getMileage() != 0) {
				typeNoteFraisDto.setMileage(typeNoteFrais.getMileage());
			}
			List<Long> delegatesId = new ArrayList<>();
			for (Delegate delegate : typeNoteFrais.getDelegates()) {
				delegatesId.add(delegate.getId());
			}
			typeNoteFraisDto.setDelegatesId(delegatesId);

			back.add(typeNoteFraisDto);
		}
		return back;
	}

	@Override
	public List<ExpenseTypeDto> findByUser(Long userId) {
		List<ExpenseTypeDto> back = new ArrayList<>();
		List<ExpenseType> allTypeNoteFrais = expenseTypeRepository.findByUser(userId);
		for (ExpenseType typeNoteFrais : allTypeNoteFrais) {
			ExpenseTypeDto typeNoteFraisDto = new ExpenseTypeDto();
			typeNoteFraisDto.setId(typeNoteFrais.getId());
			typeNoteFraisDto.setName(typeNoteFrais.getName());
			typeNoteFraisDto.setPrice(typeNoteFrais.getPrice());
			typeNoteFraisDto.setRequiredAttachment(typeNoteFrais.getRequiredAttachment());
			if (typeNoteFrais.getMileage() != 0) {
				typeNoteFraisDto.setMileage(typeNoteFrais.getMileage());
			}
			List<Long> delegatesId = new ArrayList<>();
			for (Delegate delegate : typeNoteFrais.getDelegates()) {
				delegatesId.add(delegate.getId());
			}
			typeNoteFraisDto.setDelegatesId(delegatesId);

			back.add(typeNoteFraisDto);
		}
		return back;
	}

	@Override
	public void add(TypeNoteFraisRequestDto typeNoteFraisDto) throws BirdnotesException {

		ExpenseType typeNoteFrais = new ExpenseType();

		if (typeNoteFraisDto.getName() == null || "".equals(typeNoteFraisDto.getName())) {
			throw new BirdnotesException(Exceptions.TYPE_NOTE_FRAIS_NAME_IS_EMPTY);
		}
		ExpenseType result = expenseTypeRepository.findByName(typeNoteFraisDto.getName());
		if (result != null) {
			ConfigurationDto config = configurationService.findConfiguration();
			Locale locale = new Locale(config.getLanguage());
			throw new BirdnotesException(messageSource.getMessage("ALREADY_EXIST", null, locale));
		}
		typeNoteFrais.setName(typeNoteFraisDto.getName());
		typeNoteFrais.setPrice(typeNoteFraisDto.getPrice());
		typeNoteFrais.setMileage(typeNoteFraisDto.getMileage());
		typeNoteFrais.setRequiredAttachment(typeNoteFraisDto.getRequiredAttachment());
		List<Delegate> delegates = new ArrayList<>();
		for (Long delegateId : typeNoteFraisDto.getDelegatesId()) {
			Delegate delegate = delegateRepository.findOne(delegateId);
			delegates.add(delegate);
		}
		typeNoteFrais.setDelegates(delegates);
		ExpenseType savedExpenseType = expenseTypeRepository.save(typeNoteFrais);

	}

	@Override
	public void delete(Long idTypeNoteFrais) throws BirdnotesException{

		expenseTypeRepository.deleteByID(idTypeNoteFrais);

	}

	@Override
	public ExpenseType saveTypeNoteFrais(ExpenseTypeDto typeNoteFraisDto) throws BirdnotesException {

		if (typeNoteFraisDto == null || typeNoteFraisDto.getId() == null) {
			throw new BirdnotesException(Exceptions.TYPE_IS_NULL);
		}

		ExpenseType typeNoteFrais = expenseTypeRepository.findByNameAndAnotherId(typeNoteFraisDto.getName(),
				typeNoteFraisDto.getId());
		if (typeNoteFrais != null) {
			throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
		}
		if (typeNoteFraisDto.getId() != null) {
			typeNoteFrais = expenseTypeRepository.findOne(typeNoteFraisDto.getId());

		}
		if (typeNoteFrais == null) {
			typeNoteFrais = new ExpenseType();
		}

		typeNoteFrais.setId(typeNoteFraisDto.getId());
		typeNoteFrais.setName(typeNoteFraisDto.getName());
		typeNoteFrais.setPrice(typeNoteFraisDto.getPrice());
		typeNoteFrais.setMileage(typeNoteFraisDto.getMileage());
		typeNoteFrais.setRequiredAttachment(typeNoteFraisDto.getRequiredAttachment());

		List<Delegate> users = new ArrayList<>();
		for (Long userId : typeNoteFraisDto.getDelegatesId()) {
			Delegate user = delegateRepository.findOne(userId);
			users.add(user);
		}
		typeNoteFrais.setDelegates(users);
		ExpenseType savedExpenseType = expenseTypeRepository.save(typeNoteFrais);
		return savedExpenseType;

	}

}
