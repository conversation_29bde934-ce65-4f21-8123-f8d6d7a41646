package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ProspectAndUserDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private List<KeyValueDto> prospects;
	private List<KeyValueDto> users;
	
	public ProspectAndUserDto() {
		prospects = new ArrayList<>();
		users = new ArrayList<>();	
	}
	
	public ProspectAndUserDto(List<KeyValueDto> prospects, List<KeyValueDto> users) {
		this.prospects = prospects;
		this.users = users;	
	}
	
	public List<KeyValueDto> getProspects() {
		return prospects;
	}
	public void setProspects(List<KeyValueDto> prospects) {
		this.prospects = prospects;
	}
	public List<KeyValueDto> getUsers() {
		return users;
	}
	public void setUsers(List<KeyValueDto> users) {
		this.users = users;
	}
}