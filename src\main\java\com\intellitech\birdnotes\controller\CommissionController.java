package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.CommissionDto;
import com.intellitech.birdnotes.model.dto.CommissionItemDto;
import com.intellitech.birdnotes.model.dto.DelegateCommissionDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.CommissionService;
import com.intellitech.birdnotes.service.SampleSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.WholesalerService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/commission")
public class CommissionController {
	private static final Logger LOG = LoggerFactory.getLogger(CommissionController.class);
	@Autowired
	private CommissionService commissionService;
	@Autowired
	UserService userService;
	
	@Autowired
	private GadgetService gadgetService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	private ProductService productService;
	
	@Autowired
	private ProspectService prospectService;
	@Autowired
	private WholesalerService wholesalerService;
	
	@RequestMapping(value = "/saveCommission", method = RequestMethod.POST)
	public ResponseEntity<String> saveCommission(@RequestBody CommissionDto commissionDto) {

	    try {
	        if (userService.checkHasPermission("COMMISSION_ADD") || userService.checkHasPermission("COMMISSION_EDIT")) {
	            Commission savedCommission = commissionService.saveCommission(commissionDto);
	            if (savedCommission != null) {
	                return new ResponseEntity<>(savedCommission.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving saveCommission", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving saveCommission", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteCommission(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("COMMISSION_DELETE")) {
	            commissionService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting commission", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the commission with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "/deleteCommissionItem/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteCommissionItem(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("COMMISSION_DELETE")) {
	            commissionService.deleteCommissionItem(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting commission item", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the commission item with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/getCommissionByUserProductAndDate", method = RequestMethod.POST)

	public ResponseEntity<List<CommissionDto>> getCommissionByUserProductAndDate(
			@RequestBody CommissionDto commissionRequestDto) {

		try {
			if (userService.checkHasPermission("COMMISSION_VIEW")) {
				List<CommissionDto> commissionDto = commissionService
						.getCommissionByUserProductAndDate(commissionRequestDto);
				return new ResponseEntity<>(commissionDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting all CommissionDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "/getDelegateCommissionByDate", method = RequestMethod.POST)

	public ResponseEntity<List<DelegateCommissionDto>> getDelegateCommissionByDate(
			@RequestBody CommissionDto commissionRequestDto) {

		try {
			if (userService.checkHasPermission("DELEGATE_COMMISSION_VIEW")) {
				List<DelegateCommissionDto> commissionDto = commissionService
						.getDelegateCommissionByDate(commissionRequestDto);
				return new ResponseEntity<>(commissionDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting all DelegateCommissionDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "/getCommissionItem", method = RequestMethod.POST)

	public ResponseEntity<List<CommissionItemDto>> getCommissionItem(
			@RequestBody CommissionDto commissionRequestDto) {

		try {
			if (userService.checkHasPermission("COMMISSION_VIEW")) {
				List<CommissionItemDto> commissionItemDto = commissionService
						.getCommissionItem(commissionRequestDto);
				return new ResponseEntity<>(commissionItemDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting CommissionItemDto", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	

	
	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("COMMISSION_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("COMMISSION_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<UserDto> userDtos = userService.getSubUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "getAllWholesalers", method = RequestMethod.GET)
	public ResponseEntity<List<WholesalerDto>> getAllWholesalers() {
		try {
			if (userService.checkHasPermission("COMMISSION_VIEW")) {
				List<WholesalerDto> wholesalerDtos = prospectService.getWholesalersByStatus(false);
				return new ResponseEntity<>(wholesalerDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllWholsalers", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	
	@RequestMapping(value = "/acceptValidationStep/{id}", method = RequestMethod.PUT)
	public ResponseEntity<String> acceptValidationStep(@PathVariable("id") long id) {

		try {
			if (userService.checkHasPermission("DELEGATE_COMMISSION_VALIDATION")) {
				commissionService.acceptValidationStep(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when accepting validation Status", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while accepting validation Status", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/refuseValidationStep/{id}", method = RequestMethod.PUT)
	public ResponseEntity<String> refuseValidationStep(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("DELEGATE_COMMISSION_VALIDATION")) {
				commissionService.refuseValidationStep(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when refusing validation Status", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while refusing validation Status", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
