package com.intellitech.birdnotes.model.dto;

import java.util.List;

import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;

public class OrdersPredictionResponse {
	private List<ProspectOrderPredictionResponse> records;
    private long totalCount;

    public OrdersPredictionResponse(List<ProspectOrderPredictionResponse> records, long totalCount) {
        this.records = (List<ProspectOrderPredictionResponse>) records;
        this.totalCount = totalCount;
    }

    public List<ProspectOrderPredictionResponse> getRecords() {
        return (List<ProspectOrderPredictionResponse>) records;
    }

    public void setRecords(List<ProspectOrderPredictionResponse> records) {
        this.records = (List<ProspectOrderPredictionResponse>) records;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }
}
