package com.intellitech.birdnotes.model.dto;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class SageProductListDto implements Serializable {
	
	private static final long serialVersionUID = 1L;
    private List<SageProductItemDto> products = new ArrayList<>();
    
	public List<SageProductItemDto> getItems() {
		return products;
	}
	public List<SageProductItemDto> getProducts() {
		return products;
	}
	public void setProducts(List<SageProductItemDto> products) {
		this.products = products;
	}



}
