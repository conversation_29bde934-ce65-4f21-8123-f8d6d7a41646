package com.intellitech.birdnotes.model;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class ActionList {
	private List<Action> action;

	public ActionList() {

	}

	public ActionList(List<Action> action) {
		super();
		this.action = action;
	}

	@XmlElement
	public List<Action> getAction() {
		return action;
	}

	public void setAction(List<Action> action) {
		this.action = action;
	}
}
