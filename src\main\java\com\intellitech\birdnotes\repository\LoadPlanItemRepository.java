package com.intellitech.birdnotes.repository;

import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.LoadPlan;
import com.intellitech.birdnotes.model.LoadPlanItem;

@Repository
public interface LoadPlanItemRepository extends JpaRepository<LoadPlanItem, Long> {

	@Query("SELECT DISTINCT pi from LoadPlanItem pi where pi.loadPlan.active = true ")
	List<LoadPlanItem>findItemOfActiveLoadPlan();
	
	@Query("SELECT c.product.name from LoadPlanItem c join c.product.ranges pg join pg.users u where u.id =:userId and c.speciality.id =:specialityId and pg.id in (:rangeIds) and c.loadPlan.id =:loadPlanId order by c.rank ")
	Set<String> findProductsBySpeciality(@Param("userId")Long userId, @Param("specialityId")Long specialityId, @Param("rangeIds")List<Integer> rangeIds, @Param("loadPlanId")Long loadPlanId);

	@Query("SELECT DISTINCT pi.product.id from LoadPlanItem pi where pi.loadPlan.active = true ")
	List<Long> findProductInChargePlan();
	
	@Modifying
	@Query("Delete from LoadPlanItem c where c.speciality.id in (:specialityIds) and c.product.id in (:productIds) ")
	void deleteByRangeAndSpeciality(@Param("specialityIds")List<Long> specialityIds, @Param("productIds")List<Long> productIds);
}
