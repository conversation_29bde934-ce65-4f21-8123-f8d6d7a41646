package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Locale;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.UserService;

/**
 * 
 * <AUTHOR> <PERSON>
 *
 */

@RestController
@RequestMapping("/downloadData")
public class DownloadDataController {
	private static final Logger LOG = LoggerFactory.getLogger(DownloadDataController.class);

	@Autowired
	private DownloadDataService downloadDataService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	UserService userService;

	@GetMapping("/getDataToVisitHistory")
	public ResponseEntity<Map<String, Object>> getDataToVisitHistory(@RequestHeader(name = "Accept-Language", required = false) Locale locale) {
		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW") || userService.checkHasPermission("COUVERTURE_VIEW")|| userService.checkHasPermission("ORDER_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				return new ResponseEntity<>(downloadDataService.loadDataToVisitHistory(birdnotesUser,locale), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in loading the data for Visit History : " + e.getMessage(), e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/getDataForProspectDistribution")
	public ResponseEntity<Map<String, Object>> getDataForProspectDistribution(@RequestHeader(name = "Accept-Language", required = false) Locale locale) {
		try {
			if (userService.checkHasPermission("PROSPECT_VIEW") || userService.checkHasPermission("PROSPECT_MAPPING_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				return new ResponseEntity<>(downloadDataService.loadDataForProspectDistribution(birdnotesUser, locale),
						HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in loading the data for Prospect Distribution : " + e.getMessage(), e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	


	@RequestMapping(value = "/getAllDataForProspect", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForProspect() {
		try {
			if (userService.checkHasPermission("PROSPECT_VIEW") || userService.checkHasPermission("PROSPECT_MAPPING_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				return new ResponseEntity<>(downloadDataService.findAllDataForProspect(birdnotesUser), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the prospect lists", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getProspectChangeRequests", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getProspectChangeRequests() {
		try {
			if (userService.checkHasPermission("LIST_OF_UPDATE_REQUESTS_VIEW")) {
				return new ResponseEntity<>(downloadDataService.getProspectUpdateRequests(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the prospect lists", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllDataForSector", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForSector() {
		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForSector(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the sectors lists", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllDataForLocality", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForLocality() {
		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForLocality(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the list of locality", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllDataForNewProspect", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForNewProspect() {
		try {
			if (userService.checkHasPermission("HISTORY_VISIT_VIEW")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForNewProspect(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for add new prospect", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
