package com.intellitech.birdnotes;

import java.io.IOException;
import java.util.Date;

import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.GenericFilterBean;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.TokenUtils;

public class AuthTokenFilter extends GenericFilterBean {

	private static final Logger LOG = LoggerFactory.getLogger(AuthTokenFilter.class);

	private UserDetailsService birdnotesUserDetailsService;

	public AuthTokenFilter(UserDetailsService birdnotesUserDetailsService) {
		this.birdnotesUserDetailsService = birdnotesUserDetailsService;
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
	 {
		try {
			HttpServletRequest httpServletRequest = (HttpServletRequest) request;
			HttpServletResponse httpServletResponse = (HttpServletResponse) response;
		
			String authToken = httpServletRequest.getHeader(BirdnotesConstants.Headers.AUTH_TOKEN_HEADER_NAME);
		
			if (StringUtils.hasText(authToken)) {
				String username = TokenUtils.getUserNameFromToken(authToken);
		
				UserDetails details = birdnotesUserDetailsService.loadUserByUsername(username);
				if (TokenUtils.validateToken(authToken, details)) {
					UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(details, details.getPassword(), details.getAuthorities());
					
					SecurityContextHolder.getContext().setAuthentication(token);
				}
			}
			filterChain.doFilter(request, response);
		} catch (IOException e) {
			LOG.error("IO Exception caused by Broken pipe or Connection reset by peer" );
		}
		catch (Exception ex) {
			LOG.error("An exception occurred at in doFilter "+ new Date(), ex);
			
		}
	}

}
