package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.GiftSupply;

public interface GiftSupplyRepository extends JpaRepository<GiftSupply, Integer> {
	@Query("SELECT g  from GiftSupply g  where  (DATE(g.deliveryDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) AND g.delegate.id=:userId  AND g.gift.id=:gadgetId AND g.delegate.user.id IN (:subUsersIds)")
	List<GiftSupply> findGadgetsSupplyByUserDateAndGadget(@Param("firstDate") Date firstDate,
			@Param("lastDate") Date lastDate, @Param("userId") Long userId, @Param("gadgetId") Integer gadgetId,@Param("subUsersIds") List <Long> subUsersIds);
	
	
}
