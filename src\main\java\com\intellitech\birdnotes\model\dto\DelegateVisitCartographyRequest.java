package com.intellitech.birdnotes.model.dto;

import java.util.Date;

public class DelegateVisitCartographyRequest {
	private	Double xlat;
	private	Double xlng;
	private	Double ylat;
	private	Double ylng;
	private Date date;


	private Long userId;
	private Date startDate;
	private Date endDate;
	


	public DelegateVisitCartographyRequest() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Date getStartDate() {
		return startDate;
	}



	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}



	public Date getEndDate() {
		return endDate;
	}



	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
	public Long getUserId() {
		return userId;
	}



	public void setUserId(Long userId) {
		this.userId = userId;
	}



	public double getXlat() {
		return xlat;
	}

	public void setXlat(double xlat) {
		this.xlat = xlat;
	}

	public double getXlng() {
		return xlng;
	}

	public void setXlng(double xlng) {
		this.xlng = xlng;
	}

	public double getYlat() {
		return ylat;
	}

	public void setYlat(double ylat) {
		this.ylat = ylat;
	}

	public double getYlng() {
		return ylng;
	}

	public void setYlng(double ylng) {
		this.ylng = ylng;
	}
	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}
	
	
}
