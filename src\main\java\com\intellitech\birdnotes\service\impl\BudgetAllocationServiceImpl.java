package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.BudgetAllocation;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.BudgetAllocationDto;
import com.intellitech.birdnotes.repository.BudgetAllocationRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.BudgetAllocationService;
import com.intellitech.birdnotes.service.UserService;

@Service("budgetAllocationService")
@Transactional
public class BudgetAllocationServiceImpl implements BudgetAllocationService {

    private BudgetAllocationRepository budgetAllocationRepository;
    Logger log = LoggerFactory.getLogger(this.getClass().getName());
    
    @Autowired
    UserService userService;
    
    @Autowired
    private UserRepository userRepository;

    @Autowired
    BudgetAllocationServiceImpl(BudgetAllocationRepository budgetAllocationRepository) {
        super();
        this.budgetAllocationRepository = budgetAllocationRepository;
    }

    @Override
    public List<BudgetAllocationDto> findAll() throws BirdnotesException {
        List<BudgetAllocationDto> back = new ArrayList<>();
        List<BudgetAllocation> allTypes = budgetAllocationRepository.findAll();
        for (BudgetAllocation budgetAllocation : allTypes) {
            BudgetAllocationDto budgetAllocationDto = new BudgetAllocationDto();
            budgetAllocationDto.setId(budgetAllocation.getId());
            budgetAllocationDto.setYear(budgetAllocation.getYear());
            budgetAllocationDto.setMonthlyBudget(budgetAllocation.getMonthlyBudget());
            budgetAllocationDto.setType(budgetAllocation.getType());
            budgetAllocationDto.setUsername(budgetAllocation.getUser().getUsername());
            
            if (budgetAllocation.getUser() != null) {
                budgetAllocationDto.setUserId(budgetAllocation.getUser().getId());
            } 
            
            back.add(budgetAllocationDto);
        }
        return back;
    }


    @Override
    public BudgetAllocationDto findBudgetAllocationDto(Long id) throws BirdnotesException {
        BudgetAllocation allocation = budgetAllocationRepository.findOne(id);
        if (allocation == null) {
            throw new BirdnotesException("Budget allocation not found");
        }
        BudgetAllocationDto dto = new BudgetAllocationDto();
        dto.setId(allocation.getId());
        dto.setYear(allocation.getYear());
        dto.setMonthlyBudget(allocation.getMonthlyBudget());
        dto.setType(allocation.getType());
        return dto;
    }
    @Override
    public BudgetAllocation saveBudgetAllocation(BudgetAllocationDto budgetAllocationDto) throws BirdnotesException {
        BudgetAllocation budgetAllocation = null;
        if (budgetAllocationDto.getId() != null) {
            budgetAllocation = budgetAllocationRepository.findOne(budgetAllocationDto.getId());
        }
        if (budgetAllocation == null) {
            budgetAllocation = new BudgetAllocation();
        }
        
        budgetAllocation.setYear(budgetAllocationDto.getYear());
        budgetAllocation.setMonthlyBudget(budgetAllocationDto.getMonthlyBudget());
        budgetAllocation.setType(budgetAllocationDto.getType());

        if (budgetAllocationDto.getUserId() != null) {
            User user = userRepository.findById(budgetAllocationDto.getUserId());
            if (user == null) {
                throw new RuntimeException("User not found");
            }
            budgetAllocation.setUser(user);
        } else {
            throw new IllegalArgumentException("User ID is required for Budget Allocation");
        }

        return budgetAllocationRepository.save(budgetAllocation);
    }
    
    @Override
    public void delete(long id) throws BirdnotesException {
        budgetAllocationRepository.delete(id);
    }
}