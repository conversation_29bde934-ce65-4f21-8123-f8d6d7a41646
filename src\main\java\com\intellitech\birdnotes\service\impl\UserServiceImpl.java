package com.intellitech.birdnotes.service.impl;


import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;

import java.util.Comparator;
import java.util.Date;
import java.util.HashSet;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.Locale;


import javax.annotation.PostConstruct;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.dao.DataIntegrityViolationException;
import com.intellitech.birdnotes.util.BirdnotesUtils;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Locality;

import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.RolePermission;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;

import com.intellitech.birdnotes.model.dto.LocalityDto;

import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.UserDtoRequest;
import com.intellitech.birdnotes.model.dto.VisitHistoryParamDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.ProspectsAffectationRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ImportService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.thread.ThreadSendEmail;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Service("userService")
@Transactional
public class UserServiceImpl implements UserService {

	public CurrentUser getCurrentUser() {
		return currentUser;
	}

	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	private static final Logger LOG = LoggerFactory.getLogger(UserServiceImpl.class);

	private static final String IMGBALISE = " <br/> <img src=";
	private UserRepository userRepository;
	private VisitRepository visitRepository;
	private RoleRepository roleRepository;
	private RangeRepository gammeRepository;
	private UserToDtoConvertor userToDtoConvertor;
	private BCryptPasswordEncoder bCryptPasswordEncoder;
	private JavaMailSender javaMailSender;
	private ConvertProspectToDto convertProspectToDto;
	private ProspectsAffectationRepository prospectSAffectationRepository;
	private ConfigurationService configurationService;

	@Autowired
	private ImportService importService;
	
	@Autowired
	private MessageSource messageSource;

	@Autowired
	private CurrentUser currentUser;
	
	@Autowired
	UserService userService;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${logoPath}")
	private String logoPath;

	private String pathLogo;

	@Value("${editUsernameEmailHtmlBody}")
	private String editUsernameEmailHtmlBody;


	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${userPath}")
	private String userPath;

	public UserServiceImpl() {
		super();

	}

	@PostConstruct
	private void init() {
		ConfigurationDto config = configurationService.findConfiguration();
		if (config != null) {
			pathLogo = config.getBackendUrl() + uploadUrl + logoPath + "/" + config.getLogo();
		}

	}

	@Autowired
	public void setProspectSAffectationRepository(ProspectsAffectationRepository prospectSAffectationRepository) {
		this.prospectSAffectationRepository = prospectSAffectationRepository;
	}

	@Value("${editUsernamePasswordEmailSubject}")
	private String editUsernamePasswordEmailSubject;

	@Value("${sendCredentialsEmailSubject}")
	private String sendCredentialsEmailSubject;

	@Value("${sendCredentialsEmailHtmlBody}")
	private String sendCredentialsEmailHtmlBody;

	@Value("${sendRememberSynchronizationSubject}")
	private String sendRememberSynchronizationSubject;

	@Value("${sendRememberSynchronizationHtmlBody}")
	private String sendRememberSynchronizationHtmlBody;

	@Value("${editPasswordEmailSubject}")
	private String editPasswordEmailSubject;

	@Value("${editPasswordEmailHtmlBody}")
	private String editPasswordEmailHtmlBody;

	@Value("${editUsernamePasswordEmailHtmlBody}")
	private String editUsernamePasswordEmailHtmlBody;

	@Value("${editUsernameEmailSubject}")
	private String editUsernameEmailSubject;

	@Value("${sendMailReportSubject}")
	private String sendMailReportSubject;

	@Value("${sendMailReportHtmlBody}")
	private String sendMailReportHtmlBody;

	@Autowired
	public void setConfigurationService(ConfigurationService configurationService) {
		this.configurationService = configurationService;
	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	@Autowired
	public void setRoleRepository(RoleRepository roleRepository) {
		this.roleRepository = roleRepository;
	}

	@Autowired
	public void setGammeRepository(RangeRepository gammeRepository) {
		this.gammeRepository = gammeRepository;
	}

	@Autowired
	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}

	@Autowired
	public void setbCryptPasswordEncoder(BCryptPasswordEncoder bCryptPasswordEncoder) {
		this.bCryptPasswordEncoder = bCryptPasswordEncoder;
	}

	@Autowired
	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}



	@Autowired
	public void setConvertProspectToDto(ConvertProspectToDto convertProspectToDto) {
		this.convertProspectToDto = convertProspectToDto;
	}

	
	public void setEditUsernameEmailHtmlBody(String editUsernameEmailHtmlBody) {
		this.editUsernameEmailHtmlBody = editUsernameEmailHtmlBody;
	}

	public void setEditUsernamePasswordEmailSubject(String editUsernamePasswordEmailSubject) {
		this.editUsernamePasswordEmailSubject = editUsernamePasswordEmailSubject;
	}

	public void setSendCredentialsEmailSubject(String sendCredentialsEmailSubject) {
		this.sendCredentialsEmailSubject = sendCredentialsEmailSubject;
	}

	public void setSendCredentialsEmailHtmlBody(String sendCredentialsEmailHtmlBody) {
		this.sendCredentialsEmailHtmlBody = sendCredentialsEmailHtmlBody;
	}

	public void setEditPasswordEmailSubject(String editPasswordEmailSubject) {
		this.editPasswordEmailSubject = editPasswordEmailSubject;
	}

	public void setEditPasswordEmailHtmlBody(String editPasswordEmailHtmlBody) {
		this.editPasswordEmailHtmlBody = editPasswordEmailHtmlBody;
	}

	public void setEditUsernamePasswordEmailHtmlBody(String editUsernamePasswordEmailHtmlBody) {
		this.editUsernamePasswordEmailHtmlBody = editUsernamePasswordEmailHtmlBody;
	}

	public void setEditUsernameEmailSubject(String editUsernameEmailSubject) {
		this.editUsernameEmailSubject = editUsernameEmailSubject;
	}

	public void setUploadUrl(String uploadUrl) {
		this.uploadUrl = uploadUrl;
	}

	private void convert(UserDtoRequest userDto, User user) {
		user.setEmail(userDto.getEmail());
		user.setUsername(userDto.getUsername());
	}

	@Override
	public User findUserById(Long id) {

		User user = userRepository.findOne(id);
		return user;
	}

	@Override
	@Transactional(readOnly = true)
	public List<UserDto> findAllDelegatesWithoutGoal(GoalRequest goalsRequestDto) {

		List<UserDto> userDtos = getSubUsers();

		List<UserDto> userWithoutGoal = new ArrayList<>();

		/*
		 * if (userDtos != null && !userDtos.isEmpty()) {
		 * 
		 * //userWithoutGoal =
		 * userRepository.findUsersWithoutGoal(goalsRequestDto.getFirstDate(),
		 * goalsRequestDto.getLastDate(), subUserIds);
		 * 
		 * 
		 * for (UserDto user : userDtos) {
		 * 
		 * if(user.getGoals() == null) {
		 * 
		 * userWithoutGoal.add(user);
		 * 
		 * }else {
		 * 
		 * for(GoalDto goal : user.getGoals()) { if( //selected period do not include
		 * goal of user with the same type
		 * (((goalsRequestDto.getFirstDate().compareTo(goal.getFirstDate())<=0 &&
		 * goalsRequestDto.getLastDate().compareTo(goal.getFirstDate())<=0) ||
		 * (goalsRequestDto.getFirstDate().compareTo(goal.getLastDate())>=0 &&
		 * goalsRequestDto.getLastDate().compareTo(goal.getLastDate())>=0)) &&
		 * goalsRequestDto.getType().equals(goal.getGoalType())) || //the same selected
		 * period include goal with different type
		 * ((goalsRequestDto.getFirstDate().compareTo(goal.getFirstDate())>=0 &&
		 * goalsRequestDto.getLastDate().compareTo(goal.getFirstDate())<=0) &&
		 * !goalsRequestDto.getType().equals(goal.getGoalType()))) {
		 * if(!userWithoutGoal.contains(user)) { userWithoutGoal.add(user); }
		 * 
		 * }
		 * 
		 * }
		 * 
		 * 
		 * }
		 * 
		 * 
		 * 
		 * }
		 * 
		 * 
		 * }
		 */

		return userDtos;

	}

	@Override
	public List<UserDto> getSubUsers() {
		List<User> subUsers = new ArrayList<User>();
		return this.getSubUsers(currentUser.getBirdnotesUser().getUserDto().getId(), subUsers);
	}

	@Override
	public List<Long> getSubUsersIds() {
		return getSubUsers().stream().map(UserDto::getId).collect(Collectors.toList());
	}
	
	@Override
	public List<Long> getSubDelegatesIds() {
		return getSubUsers().stream().map(UserDto::getDelegateId).collect(Collectors.toList());
	}

	public List<UserDto> getSubUsers(Long superior, List<User> subUsers) {

		Long currentUserId = currentUser.getBirdnotesUser().getUserDto().getId();
		List<User> users = userRepository.getUsersBySuperior(superior);
		List<UserDto> userDtos = new ArrayList<UserDto>();
		if (users.size() > 0) {
			subUsers.addAll(users);
			for (User user : users) {
				getSubUsers(user.getId(), subUsers);
			}
		}
		userDtos.add(currentUser.getBirdnotesUser().getUserDto());
		for (User user : users) {
			userDtos.add(userToDtoConvertor.convert(user));
		}
		return userDtos;
	}

	@Override
	public List<UserDto> getOnlyUsers(){
		List <UserDto> allSubUsers = getSubUsers();
		List <UserDto> users = new ArrayList<>();
		for(UserDto userDto : allSubUsers) {
			if(userDto.getDelegateId() == null && userDto.getProspectId() == null) {
				users.add(userDto);
			}
		}		
		return users;
	}
	
	@Override
	public List<UserDto> findAllUsers() {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		Integer currentRoleRank = roleRepository.findRoleByUserId(birdnotesUser.getUserDto().getId());
		if (currentRoleRank == 1) {
			List<User> users = userRepository.findAll();
			List<UserDto> userDtos = new ArrayList<UserDto>();
			if (users.size() > 0) {
				for (User user : users) {
					userDtos.add(userToDtoConvertor.convert(user));
				}
			}
			return userDtos;
		} else {
			return getSubUsers();
		}

	}

	@Override
	public List<UserDto> findUsers() {
		List<User> users = userRepository.findAll();
		List<UserDto> userDtos = new ArrayList<UserDto>();
		if (users.size() > 0) {
			for (User user : users) {
				userDtos.add(userToDtoConvertor.convert(user));
			}
		}
		return userDtos;

	}

	/*
	 * @Override
	 * 
	 * @Transactional(readOnly = true) public List<UserDto>
	 * getAllDelegatesByUserRole(BirdnotesUser birdnotesUser) {
	 * 
	 * List<UserDto> listUsersDto = new ArrayList<>(); Set<Role> userRoles =
	 * birdnotesUser.getUserDto().getRoles(); List<String> userRolesNames = new
	 * ArrayList<>(); for (Role role : userRoles) {
	 * userRolesNames.add(role.getName()); } listUsersDto = getSubUsers();
	 * 
	 * return listUsersDto; }
	 */

	

	public List<User> getAllLastSyncro() {
		List<User> delegatesLastSyncro = userRepository.findAllLastSynchro();
		return delegatesLastSyncro;
	}

	@Override
	@Transactional(readOnly = true)
	public List<UserDto> getAllUsers() {

		List<UserDto> userDtos = getSubUsers();
		return userDtos;
	}
	
	@Override

	public void updateUser(UserDtoRequest userDto) throws BirdnotesException {
		
		if (checkEmailIsUniqueAndIdNotIn(userDto.getEmail(), userDto.getId())) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.EMAIL_ALREADY_EXIST);
		}

		if (checkPhoneIsUniqueAndIdNotIn(userDto.getPhone(), userDto.getId())) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.PHONE_ALREADY_EXIST);
		}

		if (checkUsernameIsUniqueAndIdNotIn(userDto.getUsername(), userDto.getId())) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.USERNAME_ALREADY_EXIST);
		}

		User user = userRepository.findOne(userDto.getId());
		if (user == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NO_USER_WITH_ID + userDto.getId());
		}
		if (!user.getPassword().equals(userDto.getPassword())) {
			String hashPassword = bCryptPasswordEncoder.encode(userDto.getPassword());
			user.setPassword(hashPassword);
		}
		user.setEmail(userDto.getEmail());
		user.setUsername(userDto.getUsername());
		user.setPhone(userDto.getPhone());
		Set<User> superiors = userRepository.getUserByIds(userDto.getSuperiors());
		user.setSuperiors(superiors);
		List<Role> roles = roleRepository.findAll();
		if (roles != null && !roles.isEmpty()) {
			fillRoles(roles, userDto, user);
		}
		userRepository.save(user);
	}

	@Override
	public User saveUser(UserDtoRequest userDto) throws BirdnotesException {
		User user = null;
		if (userDto == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.USER_DTO_IS_NULL);
		}
		if(userDto.getId() != null && userDto.getId() != 0) {
			user = userRepository.findById(userDto.getId());		
		}
		if (user == null) {
			user = new User();
		}
		if (checkEmailIsUnique(userDto.getEmail(), userDto.getId())) {
			throw new BirdnotesException(userService.getTranslatedLabel("EMAIL_ALREADY_EXIST"));
		}

		if (checkUsernameIsUnique(userDto.getUsername(), userDto.getId())) {
			throw new BirdnotesException(userService.getTranslatedLabel("USERNAME_ALREADY_EXIST"));
		}
		if (checkPhoneIsUnique(userDto.getPhone(),userDto.getId())) {
			throw new BirdnotesException(userService.getTranslatedLabel("PHONE_ALREADY_EXIST"));
		}

		user.setEmail(userDto.getEmail());
		user.setUsername(userDto.getUsername());
		user.setPhone(userDto.getPhone());
		user.setActive(userDto.getActive());
		if (userDto.getPassword() != null  && !userDto.getPassword().equals("")) {		
			if(!bCryptPasswordEncoder.matches(userDto.getPassword(), user.getPassword())) {
				String hashPassword = bCryptPasswordEncoder.encode(userDto.getPassword());
				user.setPassword(hashPassword);
			}
		}
		List<Role> roles = roleRepository.findAll();
		if (roles != null && !roles.isEmpty()) {
			fillRoles(roles, userDto, user);
		}
		List<Range> ranges = gammeRepository.findAll();
		if(ranges !=null && !ranges.isEmpty()) {
			fillRanges(ranges, userDto, user);
		}
		if (userDto.getSuperiors() != null && userDto.getSuperiors().size() != 0) {
			Set<User> superiors = userRepository.getUserByIds(userDto.getSuperiors());
			user.setSuperiors(superiors);
		}

		User userSaved = userRepository.save(user);
		return userSaved;
	}

	private void fillRoles(List<Role> roles, UserDtoRequest userDto, User user) {

		Set<Role> roleUser = new HashSet<>();
		for (Integer roleId : userDto.getRoleIds()) {
			for (Role role : roles) {
				if (role.getId().equals(roleId)) {
					roleUser.add(role);
				}
			}
		}
		user.setRoles(roleUser);

	}
	
	private void fillRanges(List<Range> ranges, UserDtoRequest userDto, User user) {

		Set<Range> userRange = new HashSet<>();
		for (Integer rangeId : userDto.getRangeIds()) {
			
			for (Range gamme : ranges) {
				if (gamme.getId().equals(rangeId)) {
					userRange.add(gamme);
				}
			}
			
		}
		user.setRanges(userRange);

	}

	@Override
	public void sendCredentials(UserDto userDto) {

	    String sendCredentialsEmailSubject = userService.getTranslatedLabel("sendCredentialsEmailSubject");
	    String sendCredentialsEmailHtmlBody = userService.getTranslatedLabel("sendCredentialsEmailHtmlBody");

	    
	    MessageFormat messageFormat = new MessageFormat(sendCredentialsEmailHtmlBody);
	    String[] args = { userDto.getUsername(), userDto.getPassword() };
	    String htmlBody = messageFormat.format(args) + IMGBALISE + pathLogo + "> ";
	    
	    String[] to = { userDto.getEmail() };

	    ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, sendCredentialsEmailSubject, htmlBody);
	    Thread thread = new Thread(threadSendEmail);
	    thread.start();
	}




	@Override
	public List<User> saveAll(List<UserDto> userDtos) throws BirdnotesException {
		List<User> usersSaved = new ArrayList<>();
		for (UserDto userDto : userDtos) {
			usersSaved.add(saveUser(userDto));
		}
		return usersSaved;
	}

	@Override
	public void deleteUser(Long userId) throws BirdnotesException {
	    User user = userRepository.findById(userId);
	    user.getSuperiors().clear();
	    user.getRoles().clear();
	    userRepository.save(user);

			userRepository.delete(userId);
	

	}


	private void sentMailofUpdateUser(Integer userIsUpdated, boolean usernameIsUpdated, boolean passwordIsUpdated,
			UserDtoRequest userDto) {
		
        String editPasswordEmailSubject = userService.getTranslatedLabel("editPasswordEmailSubject");
        String editPasswordEmailHtmlBody = userService.getTranslatedLabel("editPasswordEmailHtmlBody");
        String editUsernameEmailHtmlBody = userService.getTranslatedLabel("editUsernameEmailHtmlBody");
        String editUsernameEmailSubject = userService.getTranslatedLabel("editUsernameEmailSubject");
        String editUsernamePasswordEmailHtmlBody = userService.getTranslatedLabel("editUsernamePasswordEmailHtmlBody");
        String editUsernamePasswordEmailSubject = userService.getTranslatedLabel("editUsernamePasswordEmailSubject");

        
		String htmlBody = "";
		String emailSubject = "";
		if (userIsUpdated == 1 && passwordIsUpdated) {
			MessageFormat messageFormat = new MessageFormat(editPasswordEmailHtmlBody);
			String[] args = { userDto.getPassword() };
			htmlBody = messageFormat.format(args) + IMGBALISE + pathLogo + "> ";
			emailSubject = editPasswordEmailSubject;
		}

		if (userIsUpdated == 1 && usernameIsUpdated) {
			MessageFormat messageFormat = new MessageFormat(editUsernameEmailHtmlBody);
			String[] args = { userDto.getUsername() };
			htmlBody = "<img src=" + pathLogo + " /> <br/> " + messageFormat.format(args);
			emailSubject = editUsernameEmailSubject;
		}

		if (userIsUpdated == 1 && usernameIsUpdated && passwordIsUpdated) {
			MessageFormat messageFormat = new MessageFormat(editUsernamePasswordEmailHtmlBody);
			String[] args = { userDto.getUsername(), userDto.getPassword() };
			htmlBody = messageFormat.format(args) + IMGBALISE + pathLogo + "> ";
			emailSubject = editUsernamePasswordEmailSubject;
		}

		if (!"".equals(htmlBody)) {
			String[] to = { userDto.getEmail() };
			ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, emailSubject, htmlBody);
			Thread thread = new Thread(threadSendEmail);
			thread.start();
		}
	}


	@Override
	public UserDto findByUsername(String username) {
		User user = userRepository.findByUsername(username);

		return userToDtoConvertor.convert(user);
	}

	private boolean checkEmailIsUnique(String email, Long id) {
		User user = userRepository.findByEmail(email, id);

		return user != null;
	}

	private boolean checkEmailIsUniqueAndIdNotIn(String email, Long id) {
		User user = userRepository.findByEmailAndId(email, id);

		return user != null;
	}

	private boolean checkPhoneIsUniqueAndIdNotIn(String phone, Long id) {
		User user = userRepository.findByPhoneAndId(phone, id);

		return user != null;
	}

	private boolean checkPhoneIsUnique(String phone , Long id) {
		User user = userRepository.findByPhoneWithDiffId(phone, id);
		return user != null;
	}

	private boolean checkUsernameIsUnique(String username, Long id) {
		User user = userRepository.findByNameWithDiffId(username, id);
		return user != null;
	}

	private boolean checkUsernameIsUniqueAndIdNotIn(String username, Long id) {
		User user = userRepository.findByUsernameAndId(username, id);

		return user != null;
	}

	@Override
	public VisitHistoryParamDto findHistoryParams(List<Long> userIds, String type) {

		List<SectorDto> sectors = new ArrayList<>();
		List<LocalityDto> localities = new ArrayList<>();
		List<ProspectDto> prospects = new ArrayList<>();
		List<SpecialityDto> specialities = new ArrayList<>();

		findSectorAndLocality(userIds, sectors, localities);

		if (type == null || !type.equals("PLANNING")) {
			List<Prospect> prospectsFromDB = prospectSAffectationRepository.findByUser(userIds);
			// List<Prospect> prospectsFromDB = prospectRepository.findByUser(userId);

			if (prospectsFromDB != null && !prospectsFromDB.isEmpty()) {
				for (Prospect prospect : prospectsFromDB) {
					try {
						prospects.add(convertProspectToDto.convert(prospect));
					} catch (BirdnotesException e) {
						LOG.error("Cannot convert from prospect to dto" + e);
					}
				}
			}
		}

		List<Speciality> specialityFromDB = prospectSAffectationRepository.findDelegatesSpecialities(userIds);
		if (specialityFromDB != null && !specialityFromDB.isEmpty()) {
			for (Speciality speciality : specialityFromDB) {
				SpecialityDto specialityDto = new SpecialityDto();
				specialityDto.setId(speciality.getId());
				specialityDto.setName(speciality.getName());
				specialities.add(specialityDto);
			}
		}
		return getVisitHistoryParamDto(localities, prospects, sectors, specialities);
	}

	private void findSectorAndLocality(List<Long> userIds, List<SectorDto> sectors, List<LocalityDto> localities) {
		List<Sector> userSectors = prospectSAffectationRepository.findDelegatesSectors(userIds);
		if (userSectors != null && !userSectors.isEmpty()) {
			for (Sector sector : userSectors) {
				SectorDto sectorDto = new SectorDto();
				sectorDto.setId(sector.getId());
				sectorDto.setName(sector.getName());
				sectors.add(sectorDto);

			}
			findLocality(userIds, localities);
			localities = localities.stream().sorted(Comparator.comparing(LocalityDto::getName))
					.collect(Collectors.toList());
		}

	}

	private void findLocality(List<Long> userIds, List<LocalityDto> localities) {
		List<Locality> userLocalities = prospectSAffectationRepository.findDelegateslocalities(userIds);
		if (userLocalities != null && !userLocalities.isEmpty()) {
			for (Locality locality : userLocalities) {
				LocalityDto localityDto = new LocalityDto();
				localityDto.setId(locality.getId());
				localityDto.setName(locality.getName());
				localityDto.setSectorId(locality.getSector().getId());
				localities.add(localityDto);
			}

		}
	}

	private VisitHistoryParamDto getVisitHistoryParamDto(List<LocalityDto> localities, List<ProspectDto> prospects,
			List<SectorDto> sectors, List<SpecialityDto> specialities) {
		VisitHistoryParamDto back = new VisitHistoryParamDto();
		back.setLocalities(localities);
		back.setProspects(prospects);
		back.setSectors(sectors);
		back.setSpecialities(specialities);
		return back;
	}

	@Autowired
	public void setVisitRepository(VisitRepository visitRepository) {
		this.visitRepository = visitRepository;
	}

	@Override
	public List<UserDto> getAllSupervisors(List<Integer> roleId) {
		List<UserDto> supervisorsDtos = new ArrayList<>();
		if (roleId != null && roleId.size() != 0) {
			Integer minRank = roleRepository.getMinRole(roleId);

			List<User> supervisors = userRepository.getAllSupervisors(minRank);
			for (User supervisor : supervisors) {
				supervisorsDtos.add(new UserDto(supervisor));
			}
		}

		return supervisorsDtos;

	}

	@Override
	public UserDto findUserDto(String userName, List<UserDto> userDtos) {
		for (UserDto userDto : userDtos) {
			String userfullName = userDto.getFirstName() + " " + userDto.getLastName();
			if (userfullName.equalsIgnoreCase(userName)) {
				return userDto;
			}
		}
		return null;
	}

	@Override
	public Set<Role> getRolesOfCurrentUser() {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		
			return birdnotesUser.getUserDto().getRoles();
		}
	
	

	@Override
	public Set<String> getAllPermissionsOfCurrentUser() {
		Set<String> permsName = new HashSet<>();
		Set<Role> roles = null;
		roles = getRolesOfCurrentUser();
		
		if(roles != null && roles.size() != 0) {
			for (Role role : roles) {
				Set<RolePermission> roleperm = role.getRolePermissions();
				for (RolePermission rolePermName : roleperm) {
					permsName.add(rolePermName.getPermissionName());
				}
			}
		}
		return permsName;

	}

	@Override
	public boolean checkHasPermission(String permissionName) {

		Set<String> allPermissionsOfCurrentUser = getAllPermissionsOfCurrentUser();
		for (String perm : allPermissionsOfCurrentUser) {
			if (perm.equals(permissionName)) {
				return true;
			}
		}
		return false;
	}

	@Override
	public void sendMailReport(String reportLink, List<UserDto> users) {

	    if (users.size() > 0) {

	        String sendMailReportHtmlBody = userService.getTranslatedLabel("sendMailReportHtmlBody");
	        String sendMailReportSubject = userService.getTranslatedLabel("sendMailReportSubject");


	        MessageFormat messageFormat = new MessageFormat(sendMailReportHtmlBody);

	        for (UserDto user : users) {
	            String[] args = { user.getUsername() };
	            String htmlBody = messageFormat.format(args) + "<br>" + reportLink + 
	                              "<br><br> Cordialement" + IMGBALISE + pathLogo + "> ";
	            String[] to = { user.getEmail() };

	            ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, sendMailReportSubject, htmlBody);
	            Thread thread = new Thread(threadSendEmail);
	            thread.start();
	        }
	    }
	}


	@Override
	public Map<String, List<UserDto>> importUser(String filePath) throws BirdnotesException {
		try {
			Map<String, List<UserDto>> userDtos = importService.getUserData(filePath);
			if (userDtos.get("validData") != null) {
				for (UserDto userDto : userDtos.get("validData")) {
					User user = new User();
					user.setEmail(userDto.getEmail());
					user.setPassword(userDto.getPassword());
					user.setUsername(userDto.getUsername());					
					String roleString = userDto.getRolesString();
					String[] roles = roleString.split(",");
					Set<Role> rolesList = new HashSet<>();
					for (String role : roles) {
							rolesList.add(roleRepository.findByName(role.trim()));
					}
					user.setRoles(rolesList);
					String supervisorsString = userDto.getSupervisorsString();
					String[] supervisors = supervisorsString.split(",");
					Set<User> supervisorsList = new HashSet<>();
					for (String supervisor : supervisors) {						
							supervisorsList.add(userRepository.findByUsername(supervisor.trim()));						
					}
					user.setSuperiors(supervisorsList);
					userRepository.save(user);
				}
			}
			return userDtos;
		} catch (Exception e) {
			LOG.error("Error in importing user ", e);
		}
		return null;

	}
	
	@Override
	public String handleDataIntegrityViolationException(DataIntegrityViolationException e) {
		
		ConfigurationDto config = configurationService.findConfiguration();
        Locale locale = new Locale(config.getLanguage());
		String tableName  = BirdnotesUtils.extractTableWithConstraint(e);
		Object[] args = {tableName};
		String exceptionMessage = messageSource.getMessage("EXCEPTION_MESSAGE",args , locale);
		return exceptionMessage ;
	}
	
	@Override
	public String getTranslatedLabel(String key) {
		
		ConfigurationDto config = configurationService.findConfiguration();
        Locale locale = new Locale(config.getLanguage());
		return  messageSource.getMessage(key ,null , locale) ;
	}
	



}
