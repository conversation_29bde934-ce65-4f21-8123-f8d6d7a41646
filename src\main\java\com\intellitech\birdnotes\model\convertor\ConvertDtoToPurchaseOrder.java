package com.intellitech.birdnotes.model.convertor;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.WholesalerRepository;
import com.intellitech.birdnotes.service.ProspectService;

@Component("convertDtoToPurchaseOrder")
public class ConvertDtoToPurchaseOrder {

		private static final Logger LOG = LoggerFactory.getLogger(ConvertDtoToPurchaseOrder.class);

		@Autowired
		private ProspectService prospectService;
		
		@Autowired
		private WholesalerRepository wholesalerRepository;
		
		@Autowired
		private VisitRepository visitRepository;
		
		public PurchaseOrder convert(PurchaseOrderDto purchaseOrderDto) throws BirdnotesException {

			if (purchaseOrderDto == null) {
				LOG.error("purchaseOrder is null");
				throw new BirdnotesException("purchaseOrder is null");
			}

			PurchaseOrder purchaseOrder = new PurchaseOrder();
			purchaseOrder.setId(purchaseOrderDto.getId());
			purchaseOrder.setIdentifier(purchaseOrderDto.getIdentifier());
			purchaseOrder.setAttachmentBase64(purchaseOrderDto.getAttachmentBase64());
			purchaseOrder.setAttachmentName(purchaseOrderDto.getAttachmentName());
			purchaseOrder.setPlacementMethod(purchaseOrderDto.getPlacementMethod());
			purchaseOrder.setMailSent(purchaseOrderDto.getMailSent());
			
			Visit visit = visitRepository.findByIdentifier(purchaseOrderDto.getVisitId(), purchaseOrderDto.getDelegate().getId());
			if(visit != null) {
				purchaseOrder.setVisit(visit);
			}
			else {
				throw new BirdnotesException("visit is null in ConvertDtoToPurchaseOrder ");
			}
			Prospect  wholesaler = prospectService.getProspect(purchaseOrderDto.getWholesalerId());
			if(wholesaler != null) {
				purchaseOrder.setWholesaler(wholesaler);
			}
			else {
				throw new BirdnotesException("wholesaler is null in ConvertDtoToPurchaseOrder ");
			}
			
			//wholesalerDto.setShopEmail(wholesaler.getShopEmail());
			return purchaseOrder;

		}
}
