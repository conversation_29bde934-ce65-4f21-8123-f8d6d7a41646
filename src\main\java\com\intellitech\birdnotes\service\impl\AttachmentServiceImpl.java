package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Attachment;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;

import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.AttachmentDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.repository.AttachmentRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.AttachmentRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.AttachmentService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.AttachmentService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("attachmentService")
@Transactional
public class AttachmentServiceImpl implements AttachmentService {

	private AttachmentRepository attachmentRepository;
	
	

	@Autowired
	AttachmentServiceImpl(AttachmentRepository attachmentRepository) {
		super();
		this.attachmentRepository = attachmentRepository;
	}

	@Override

	public List<AttachmentDto> getAllByUser(Long userId) throws BirdnotesException {

		List<AttachmentDto> back = new ArrayList<>();

		List<Attachment> attachmentList;

		attachmentList = attachmentRepository.findByUser(userId);


		if (attachmentList != null && !attachmentList.isEmpty()) {

			for (Attachment attachment : attachmentList) {
				AttachmentDto attachmentDto = new AttachmentDto();
				attachmentDto.setIdentifier(attachment.getIdentifier()); 
				attachmentDto.setAttachmentBase64(attachment.getAttachmentBase64());
				attachmentDto.setAttachmentName(attachment.getAttachmentName());
				back.add((attachmentDto));
				
			}

		}

		return back;

	}



	
}
