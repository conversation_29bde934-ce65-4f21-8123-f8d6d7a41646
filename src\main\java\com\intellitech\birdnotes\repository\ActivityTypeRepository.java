package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.ProspectType;

public interface ActivityTypeRepository extends JpaRepository<ActivityType, Long> {
	ActivityType findByName(String name);
	
	@Override
	@Query("SELECT a from ActivityType a order by a.name ASC")
	List<ActivityType> findAll();
	
	@Query("SELECT a from ActivityType a where  LOWER(name) = LOWER(?1) AND id != ?2")
	ActivityType findByNameAndAnotherId(String name, Long id);

	
	
	

}
