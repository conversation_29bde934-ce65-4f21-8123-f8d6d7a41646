package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalSum;
import com.intellitech.birdnotes.model.dto.VisitRequestDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.data.dto.GoalFormData;

public interface GoalManagementService {

	Goal saveGoal(GoalRequest goalManagementRequest) throws BirdnotesException;
	List<GoalDto> findAll() throws BirdnotesException;
	void delete (Long id) throws BirdnotesException;
	void update(GoalDto goalManagementDto) throws BirdnotesException;
	GoalDto findGoalByName(String name) throws BirdnotesException;
	GoalDto getGoal (Long id)throws BirdnotesException;
	List<GoalSum> getGoalsSum(VisitRequestDto coverageRequestDto) throws BirdnotesException ;
	GoalFormData getAllDataGoalForm();
}
