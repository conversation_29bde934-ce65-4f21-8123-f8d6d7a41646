package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


import com.intellitech.birdnotes.model.MarketingActionType;
import com.intellitech.birdnotes.model.Prospect;

@Repository
public interface MarketingActionTypeRepository extends JpaRepository<MarketingActionType, Long> {
	
	
	
	@Override
	@Query("SELECT mt from MarketingActionType mt order by mt.name ASC")
	List<MarketingActionType> findAll();

	MarketingActionType findByName(String name);

	@Modifying
	@Query("DELETE FROM MarketingActionType where id=:id")
	void deleteById(@Param ("id") Long id);
    @Query("SELECT mt.id from MarketingActionType mt")
	List<Long> getAllMarketingActionTypeIds();
    
	@Query("SELECT mt from MarketingActionType mt where  LOWER(name) = LOWER(?1) AND id != ?2")
	MarketingActionType findByNameAndAnotherId(String name, Long id);
	


	
	
}