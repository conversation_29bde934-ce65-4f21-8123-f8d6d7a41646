package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class RoleDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Integer id;
	private String name;
	private boolean checked;

	public RoleDto() {
		super();

	}

	// Ajouté pour le test
	public RoleDto(int id, String name) {
		super();
		this.id = id;
		this.name = name;

	}

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public boolean isChecked() {
		return checked;
	}

	public void setChecked(boolean checked) {
		this.checked = checked;
	}

	public void setName(String name) {
		this.name = name;
	}
}
