package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class VisitHistoryParamDto {
	
	private List<SectorDto> sectors;
	private List<LocalityDto> localities;
	private List<ProspectDto> prospects;
	private List<SpecialityDto> specialities;
	
	public VisitHistoryParamDto(List<SectorDto> sectors, List<LocalityDto> localities, List<ProspectDto> prospects,
			List<SpecialityDto> specialities) {
		super();
		this.sectors = sectors;
		this.localities = localities;
		this.prospects = prospects;
		this.specialities = specialities;
	}
	
	public VisitHistoryParamDto() {
		super();
	}

	public List<SectorDto> getSectors() {
		return sectors;
	}
	public void setSectors(List<SectorDto> sectors) {
		this.sectors = sectors;
	}
	public List<LocalityDto> getLocalities() {
		return localities;
	}
	public void setLocalities(List<LocalityDto> localities) {
		this.localities = localities;
	}
	public List<ProspectDto> getProspects() {
		return prospects;
	}
	public void setProspects(List<ProspectDto> prospects) {
		this.prospects = prospects;
	}
	public List<SpecialityDto> getSpecialities() {
		return specialities;
	}
	public void setSpecialities(List<SpecialityDto> specialities) {
		this.specialities = specialities;
	}

	
}
