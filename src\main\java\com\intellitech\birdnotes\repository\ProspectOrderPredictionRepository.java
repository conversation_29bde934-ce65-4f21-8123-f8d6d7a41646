package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.model.ProspectOrderPrediction;
import com.intellitech.birdnotes.model.dto.KeyValueDto;



public interface ProspectOrderPredictionRepository extends JpaRepository<ProspectOrderPrediction, Long> {
	
	   @Modifying
	   @Query(value = "insert into prospect_order_prediction ( prediction_date, features, prospect_id, product_id, order_quantity_prediction  ) VALUES (?1, ?2, ?3, ?4, ?5)", nativeQuery = true)
	   void insert(Date predictionDate, String features,  long prospectId,  long productId, long orderQuantityPrediction);
	   
	   void deleteByPredictionDate(Date predictionDate);

	   @Query("SELECT new com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse(pop) From ProspectOrderPrediction pop where MONTH(pop.predictionDate)  =:month AND YEAR(pop.predictionDate) =:year ")
	   List<ProspectOrderPredictionResponse> getOrdersPredictions(@Param("month") Integer month, @Param("year") Integer year);

	   @Modifying
	   @Query("Delete From ProspectOrderPrediction pop where MONTH(pop.predictionDate)  =:month AND YEAR(pop.predictionDate) =:year ")
	   void deleteOrdersPredictions(@Param("month") Integer month, @Param("year") Integer year);

	   @Query("SELECT new com.intellitech.birdnotes.model.dto.KeyValueDto(pop.prospect.id, sum(pop.orderQuantityPrediction)) From ProspectOrderPrediction pop where pop.prospect.id IN (:prospectIds) AND MONTH(pop.predictionDate)  =:month AND YEAR(pop.predictionDate) =:year GROUP BY pop.prospect.id ")
	   List<KeyValueDto> getOrdersPredictionsByProspectIds(@Param("month") Integer month, @Param("year") Integer year,@Param("prospectIds")  List<Long> prospectIds);
}
