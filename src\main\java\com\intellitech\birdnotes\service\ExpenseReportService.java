package com.intellitech.birdnotes.service;

import java.io.FileNotFoundException;
import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;

import net.sf.jasperreports.engine.JRException;

public interface ExpenseReportService {
	List<ExpenseReportDto> findAll() throws BirdnotesException;

	ExpenseReport add(ExpenseReportDto noteFraisDto) throws BirdnotesException;

	void delete(Long idNoteFrais) throws BirdnotesException;

	void updateNoteFrais(ExpenseReportDto noteFraisDto) throws BirdnotesException;

	List<ExpenseReportDto> findByUser(Long userId) throws BirdnotesException;

	List<ExpenseReportDto> findAllByUser(Long userId, List<Long> expenseReportList) throws BirdnotesException;

	List<ExpenseReportDto> getAllByUser(Long userId) throws BirdnotesException;

	List<LabelValueDto> getExpensesReportByDelegate(Date startDate, Date endDate);

	List<LabelValueDto> getExpensesReportByType(Date startDate, Date endDate);

	ExpenseReportDto findNoteFraisById(Long id) throws BirdnotesException;

	void acceptValidationStep(long id) throws BirdnotesException;

	void refuseValidationStep(long id) throws BirdnotesException;

	List<ExpenseReportDto> findExpenseValidationByUser(List<Long> expenseWaitingValidation, Long UserId)
			throws BirdnotesException;

	List<ExpenseReportDto> findByDate(Date firstDate, Date lastDate, Long userId) throws BirdnotesException;

	void generateExpenseReport(String fileName, Date startDate, Date endDate, Long userId)
			throws FileNotFoundException, JRException, BirdnotesException;

	String generateExpensePDF(Date startDate, Date endDate, Long userId) throws BirdnotesException;

	ExpenseReport saveExpenseReport(ExpenseReportDto noteFraisDto) throws BirdnotesException;
}
