package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.LoadPlan;
import com.intellitech.birdnotes.model.dto.LoadPlanDto;
import com.intellitech.birdnotes.service.LoadPlanService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/loadPlan")
public class LoadPlanController {
	private static final Logger LOG = LoggerFactory.getLogger(LoadPlanController.class);

	@Autowired
	private LoadPlanService loadPlanService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/getAllLoadPlans", method = RequestMethod.GET)
	public ResponseEntity<List<LoadPlanDto>> getAllLoadPlans() {
		try {
			List<LoadPlanDto> result = loadPlanService.getAllLoadPlans();
			return new ResponseEntity<>(result, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all loadPlans ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveLoadPlan", method = RequestMethod.POST)
	public ResponseEntity<Long> saveLoadPlan(@RequestBody LoadPlanDto loadPlanDto) {
		try {

			LoadPlan loadPlanSaved = loadPlanService.saveLoadPlan(loadPlanDto);
			if (loadPlanSaved != null) {
				return new ResponseEntity<>(loadPlanSaved.getId(), HttpStatus.OK);
			}

			return new ResponseEntity<>(null, HttpStatus.OK);

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while saving loadPlan", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while saving loadPlan ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deleteLoadPlan/{loadPlanId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteLoadPlan(@PathVariable("loadPlanId") Long loadPlanId) {
		try {

			loadPlanService.deleteLoadPlan(loadPlanId);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting Load Plan", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);


		} catch (Exception e) {
			LOG.error("Error in delete loadPlan", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
