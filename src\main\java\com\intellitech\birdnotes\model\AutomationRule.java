package com.intellitech.birdnotes.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.Periodicity;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.AUTOMATION_RULE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class AutomationRule {

	@Id
	@SequenceGenerator(name = Sequences.AUTOMATION_RULE_SEQUENCE, sequenceName = Sequences.AUTOMATION_RULE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.AUTOMATION_RULE_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.EVENT_TYPE)
	private String eventType;

	@Column(name = Columns.ACTION_TYPE)
	private String actionType;

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.ACTIVITY_TYPE_ID)
	private ActivityType activityType;

	@Column(name = Columns.PERIODICITY)
	private Periodicity periodicity;

	@Column(name = Columns.START_AFTER)
	private Float startAfter;

	@Column(name = Columns.REPETATION_EACH)
	private Integer repetationEach;

	@Column(name = Columns.REPEATING_PERIOD)
	private Integer repeatingPeriod;

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.ACTIVITY_TYPE_EVENT)
	private ActivityType activityTypeEvent;

	public AutomationRule(Long id, String eventType, String actionType, ActivityType activityType,
			Periodicity periodicity, Float startAfter, Integer repetationEach, Integer repeatingPeriod,
			ActivityType activityTypeEvent) {
		super();
		this.id = id;
		this.eventType = eventType;
		this.actionType = actionType;
		this.activityType = activityType;
		this.periodicity = periodicity;
		this.startAfter = startAfter;
		this.repetationEach = repetationEach;
		this.repeatingPeriod = repeatingPeriod;
		this.activityTypeEvent = activityTypeEvent;
	}

	public AutomationRule() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public ActivityType getActivityType() {
		return activityType;
	}

	public void setActivityType(ActivityType activityType) {
		this.activityType = activityType;
	}

	public Periodicity getPeriodicity() {
		return periodicity;
	}

	public void setPeriodicity(Periodicity periodicity) {
		this.periodicity = periodicity;
	}

	public Float getStartAfter() {
		return startAfter;
	}

	public void setStartAfter(Float startAfter) {
		this.startAfter = startAfter;
	}

	public Integer getRepetationEach() {
		return repetationEach;
	}

	public void setRepetationEach(Integer repetationEach) {
		this.repetationEach = repetationEach;
	}

	public Integer getRepeatingPeriod() {
		return repeatingPeriod;
	}

	public void setRepeatingPeriod(Integer repeatingPeriod) {
		this.repeatingPeriod = repeatingPeriod;
	}

	public ActivityType getActivityTypeEvent() {
		return activityTypeEvent;
	}

	public void setActivityTypeEvent(ActivityType activityTypeEvent) {
		this.activityTypeEvent = activityTypeEvent;
	}

}
