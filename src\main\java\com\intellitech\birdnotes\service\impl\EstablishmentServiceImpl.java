package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.repository.EstablishmentRepository;
import com.intellitech.birdnotes.repository.ProspectsAffectationRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.EstablishmentService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("establishmentService")
@Transactional
public class EstablishmentServiceImpl implements EstablishmentService {

	private EstablishmentRepository establishmentRepository;
	private ProspectsAffectationRepository prospectsAffectationRepository;
	private SectorRepository sectorRepository;
	
	@Autowired
	UserService userService;

	@Autowired
	EstablishmentServiceImpl(EstablishmentRepository establishmentRepository, ConvertSpecialityToDto convertSpecialityToDto,
			SectorRepository sectorRepository,ProspectsAffectationRepository prospectsAffectationRepository) {
		super();
		this.establishmentRepository = establishmentRepository;
		this.prospectsAffectationRepository  = prospectsAffectationRepository;
		this.sectorRepository = sectorRepository;
	}



	@Override
	public List<EstablishmentDto> findAll() throws BirdnotesException {
		List<EstablishmentDto> back = new ArrayList<>();
		List<Establishment> establishments= establishmentRepository.findAll();
		for (Establishment establishment : establishments) {
			EstablishmentDto establishmentDto = new EstablishmentDto();
			establishmentDto.setId(establishment.getId());
			establishmentDto.setName(establishment.getName());
			establishmentDto.setActivity(establishment.getActivity());
			if(establishment.getSector() != null) {
				establishmentDto.setSectorId(establishment.getSector().getId());
				establishmentDto.setSectorName(establishment.getSector().getName());
			}
			
			
			back.add(establishmentDto);
		}
		return back;
	}

	@Override
	public List<EstablishmentDto> findEstablishmentByAffectedSectors(Long userId) throws BirdnotesException {
		List<EstablishmentDto> back = new ArrayList<>();
		List<Long> sectorIds = prospectsAffectationRepository.findUserSectors(userId);
		
		if(sectorIds != null && sectorIds.size() > 0) {
			
			List<Establishment> establishments= establishmentRepository.findEstablishmentByAffectedSectors(sectorIds);
			for (Establishment establishment : establishments) {
				EstablishmentDto establishmentDto = new EstablishmentDto();
				establishmentDto.setId(establishment.getId());
				establishmentDto.setName(establishment.getName());
				establishmentDto.setActivity(establishment.getActivity());
				back.add(establishmentDto);
			}
		}
		
		return back;
	}
	
	@Override
	public Establishment saveEstablishment(EstablishmentDto establishmentRequest) throws BirdnotesException {
	    if (establishmentRequest == null || establishmentRequest.getId() == null) {
	        throw new BirdnotesException(Exceptions.NULL_WHOLESALER);
	    }

	    Establishment result = establishmentRepository.findByNameAndAnotherId(establishmentRequest.getName(), establishmentRequest.getId());
	    if (result != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    Establishment establishment = null;
	    if (establishmentRequest.getId() != null) {
	        establishment = establishmentRepository.findOne(establishmentRequest.getId());
	    }
	    if (establishment == null) {
	        establishment = new Establishment();
	    }

	    establishment.setName(establishmentRequest.getName());
	    establishment.setActivity(establishmentRequest.getActivity());
	    establishment.setId(establishmentRequest.getId());
	    
	    if (establishmentRequest.getSectorId() != null) {
	        Sector sector = sectorRepository.findOne(establishmentRequest.getSectorId());
	        if (sector != null) {
	            establishment.setSector(sector);
	        }
	    }

	    return establishmentRepository.save(establishment);
	}

	
	@Override
	public void deleteEstablishment(Long establishmentId) throws BirdnotesException {
		/*Long countEstablishmentPerVisitProduct = visitsProductsRepository.countEstablishmentPerVisitProduct(establishmentId);
		if (countEstablishmentPerVisitProduct > 0) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.CANT_DELETE_WHOLESALER);
		}*/
		establishmentRepository.delete(establishmentId);
	}

	@Override
	public Establishment updateEstablishment(EstablishmentDto establishmentDto) throws BirdnotesException {

		if (establishmentDto == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_DTO_WHOLESALER);
		}

		Establishment establishmentToUpdate = establishmentRepository.findOne(establishmentDto.getId());

		if (establishmentToUpdate == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NO_WHOLESALER_WITH_ID + establishmentDto.getId());
		}

		establishmentToUpdate.setName(establishmentDto.getName());
		establishmentToUpdate.setActivity(establishmentDto.getActivity());
		establishmentToUpdate.setId(establishmentDto.getId());
		if(establishmentDto.getSectorId() != null) {
			Sector sector = sectorRepository.findOne(establishmentDto.getSectorId());
			if(sector!=null) {
				establishmentToUpdate.setSector(sector);
			}
		}
		
		
		
		//establishmentToUpdate.setSaleEmail(establishmentDto.getSaleEmail());

		return establishmentRepository.save(establishmentToUpdate);
	}
	
	@Override
	public EstablishmentDto findEstablishmentDto(String establishmentName,List<EstablishmentDto>establishmentDtos)   {
		for (EstablishmentDto establishmentDto : establishmentDtos) {
			if (establishmentDto.getName().equalsIgnoreCase(establishmentName)) {
				return establishmentDto;
			}
		}
		return null;
	}

	@Override
	public void saveAllEstablishments(List<EstablishmentDto> establishmentDtos) throws BirdnotesException {
		for(EstablishmentDto establishmentDto:establishmentDtos) {
		Establishment establishment = new Establishment();
		if (establishmentDto.getName() == null || establishmentDto.getName().equals("")) {
			throw new BirdnotesException("Establishment name is empty");
		}
		
		establishment.setName(establishmentDto.getName());
		establishment.setActivity(establishmentDto.getActivity());
		establishmentRepository.save(establishment);
		}
	}


}
