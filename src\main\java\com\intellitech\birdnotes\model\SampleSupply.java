package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.SAMPLE_SUPPLY, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class SampleSupply implements Serializable {
	private static final long serialVersionUID = 1L;

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public SampleSupply() {
		super();

	}

	// Ajouté pour le test
	public SampleSupply(Long id, Delegate delegate, Date deliveryDate) {
		super();
		this.id = id;
		this.delegate = delegate;
		this.deliveryDate = deliveryDate;

	}

	@Id
	@SequenceGenerator(name = Sequences.SAMPLE_SUPPLY_SEQUENCE, sequenceName = Sequences.SAMPLE_SUPPLY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.SAMPLE_SUPPLY_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@JsonIgnore
	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	private Delegate delegate;

	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.DELIVERY_DATE)
	private Date deliveryDate;

	public Date getDeliveryDate() {
		return deliveryDate;
	}

	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}

	@OneToOne(optional = true)
	@JoinColumn(name = Columns.MISSION_ID)
	private Mission mission;

	public Mission getMission() {
		return mission;
	}

	public void setMission(Mission mission) {
		this.mission = mission;
	}

	@JsonIgnore
	@OneToMany(mappedBy = "sampleSupply", cascade = CascadeType.ALL)
	private List<SampleSupplyItem> sampleSupplyItems;

	public List<SampleSupplyItem> getSampleSupplyItems() {
		return sampleSupplyItems;
	}

	public void setSampleSupplyItems(List<SampleSupplyItem> sampleSupplyItems) {
		this.sampleSupplyItems = sampleSupplyItems;
	}

}
