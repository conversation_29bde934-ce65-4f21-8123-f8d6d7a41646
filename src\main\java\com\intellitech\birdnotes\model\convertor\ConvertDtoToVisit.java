package com.intellitech.birdnotes.model.convertor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;

@Component("convertDtoToVisit")
public class ConvertDtoToVisit {
	
	@Autowired
	private ProspectRepository prospectRepository;
	@Autowired
	private GiftRepository gadgetRepository;
	
	@Autowired
	private DelegateRepository delegateRepository;

	@Autowired
	private CurrentUser currentUser;
	
	
	public Visit convert(VisitDto visitDto) throws BirdnotesException {
		if(visitDto == null) {
			throw new BirdnotesException("visitdto is null");
		}
		Visit visit = new Visit();
		visit.setGadgetQuantity(visitDto.getGadgetQuantity());
		visit.setGeneralNote(visitDto.getGeneralNote());
		visit.setId(visitDto.getId());
		visit.setVisitDate(visitDto.getVisitDate());
		visit.setPatientNumber(visitDto.getPatientNumber());
		visit.setIdentifier(visitDto.getIdentifier());
		
		Prospect prospect = prospectRepository.findById(visitDto.getProspectId());
		visit.setProspect(prospect);
		Gift gadget = gadgetRepository.findOne(visitDto.getGadgetId());
		visit.setGadget(gadget);
		Delegate delegate = delegateRepository.findDelegateByUserId(currentUser.getBirdnotesUser().getUserDto().getId());
		visit.setDelegate(delegate);
		

		return visit;
	}

}
