package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.PURCHASE_ORDER_TEMPLATE, schema = Common.PUBLIC_SCHEMA)
public class PurchaseOrderTemplate implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Date firstDate;
	private Date lastDate; 
	private String name;
	private List<Gift> gifts ;
	private List<Delegate> delegates ;
	private Set<PurchaseOrderTemplateItem> purchaseOrderTemplateItem;
	
	

	@Id
	@SequenceGenerator(name = Sequences.PURCHASE_ORDER_TEMPLATE_SEQUENSE, sequenceName = Sequences.PURCHASE_ORDER_TEMPLATE_SEQUENSE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PURCHASE_ORDER_TEMPLATE_SEQUENSE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	
	@Temporal(TemporalType.DATE)
	@Column(name = Columns.FIRST_DATE)
	public Date getFirstDate() {
		return firstDate;
	}

	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.LAST_DATE)
	public Date getLastDate() {
		return lastDate;
	}

	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}
	
	
	@Column(name = Columns.NAME)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.GIFT_PURCHASE_ORDER_TEMPLATE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PURCHASE_ORDER_TEMPLATE_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.GIFT_ID, nullable = false, updatable = false) })
	public List<Gift> getGifts() {
		return gifts;
	}

	public void setGifts(List<Gift> gifts) {
		this.gifts = gifts;
	}
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.USER_PURCHASE_ORDER_TEMPLATE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PURCHASE_ORDER_TEMPLATE_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID, nullable = false, updatable = false) })
	public List<Delegate> getDelegates() {
		return delegates;
	}

	public void setDelegates(List<Delegate> delegates) {
		this.delegates = delegates;
	}
	
	@OneToMany (fetch = FetchType.LAZY, mappedBy = "purchaseOrderTemplate")
	@JsonIgnore
	public Set<PurchaseOrderTemplateItem> getPurchaseOrderTemplateItem() {
		return purchaseOrderTemplateItem;
	}

	public void setPurchaseOrderTemplateItem(Set<PurchaseOrderTemplateItem> purchaseOrderTemplateItem) {
		this.purchaseOrderTemplateItem = purchaseOrderTemplateItem;
	}
	
}