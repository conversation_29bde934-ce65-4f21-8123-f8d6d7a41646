package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.SPECIALITY, schema = Common.PUBLIC_SCHEMA)
public class Speciality implements Serializable{

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.SPECIALITY_SEQUENCE, sequenceName = Sequences.SPECIALITY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.SPECIALITY_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Column(name = Columns.NAME, length = BirdnotesConstants.Numbers.N_255,unique=true)
	private String name;
	
	@Column(name = Columns.ACTION)
	private Integer action;
	
	@Column(name = Columns.ICON)
	private String icon;

	@OneToMany (mappedBy = "speciality")
	private List<GoalItem> goalItems;

	
	public Speciality() {
		super();
	}

	public Speciality(Long id, String name, Integer action) {
		super();
		this.id = id;
		this.name = name;
		this.action= action;
	}
	


	public Integer getAction() {
		return action;
	}

	public void setAction(Integer action) {
		this.action = action;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	@Override
	public String toString() {
		return "Speciality [id=" + id + ", name=" + name + "]";
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

	public List<GoalItem> getGoalItems() {
		return goalItems;
	}

	public void setGoalItems(List<GoalItem> goalItems) {
		this.goalItems = goalItems;
	}
}
