package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.FavoriteMenuItem;
import com.intellitech.birdnotes.model.dto.FavoriteDto;

@Component("ConvertFavoriteToDto")
public class ConvertFavoriteToDto {
	private static final Logger LOG = LoggerFactory.getLogger(ConvertFavoriteToDto.class);

	public FavoriteDto convert(FavoriteMenuItem favorite) throws BirdnotesException {

		if (favorite == null) {
			LOG.error("favorite is null");
			throw new BirdnotesException("favorite is null");
		}
		FavoriteDto favoriteDto = new FavoriteDto();
		favoriteDto.setId(favorite.getId());
		favoriteDto.setLabel(favorite.getLabel());

		return favoriteDto;
	}
}
