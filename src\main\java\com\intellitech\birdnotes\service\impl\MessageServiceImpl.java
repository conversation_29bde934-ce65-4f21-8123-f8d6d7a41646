package com.intellitech.birdnotes.service.impl;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Message;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.ConvertActivityToDto;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ActivityCalanderRequestDto;
import com.intellitech.birdnotes.model.dto.ActivityDataDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.MessageDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateDto;
import com.intellitech.birdnotes.model.dto.StatisticActivity;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryGroupDto;
import com.intellitech.birdnotes.repository.ActivityRepository;
import com.intellitech.birdnotes.repository.MessageRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ActivityService;
import com.intellitech.birdnotes.service.MessageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("messageService")
@Transactional
public class MessageServiceImpl implements MessageService {
	Logger log = LoggerFactory.getLogger(this.getClass().getName());



	private UserRepository userRepository;
	
	private MessageRepository messageRepository;
	
	private UserService userService;
	
	private UserToDtoConvertor userToDtoConvertor;

	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");

	private CurrentUser currentUser;
	
	@Autowired
	private DynamicQueries dynamicQueries;
	

	@Autowired
	public void setUserRepository(UserRepository userRepository, UserService userService,  MessageRepository messageRepository, UserToDtoConvertor userToDtoConvertor ) {
		this.userRepository = userRepository;
		this.userService = userService;
		this.messageRepository = messageRepository;
		this.userToDtoConvertor = userToDtoConvertor;
	}

	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	
		@Override
		public List<MessageDto> getMessageByDateAndUser(Date startDate, Date endDate, Long userId) {
			
			List<MessageDto> messagesDto = new ArrayList<>();
			List<Message> messages;
			if(userId != 0) {
				messages = messageRepository.findByDateAndUser(startDate, endDate, userId);
			}else {
				messages = messageRepository.findByDate(startDate, endDate);
			}
			
			
			for(Message message : messages) {
				MessageDto messageDto = new MessageDto();
				messageDto.setDate(message.getDate());
				messageDto.setId(message.getId());
				messageDto.setText(message.getText());
				messageDto.setType(message.getType().toString());
				messageDto.setUserId(message.getUser().getId());
				messageDto.setUserDto(userToDtoConvertor.convert(message.getUser()));
				messagesDto.add(messageDto);
			}
			return messagesDto;
			
		}
		
		
		
		
}
