package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.model.dto.TypeNoteFraisRequestDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.ExpenseTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/expenseType")
public class ExpenseTypeController {
	private static final Logger LOG = LoggerFactory.getLogger(ExpenseTypeController.class);

	@Autowired
	private ExpenseTypeService typeNoteFraisService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	UserService userService;
	@Autowired
	DelegateService delegateService;

	@RequestMapping(value = "findAllExpenseType", method = RequestMethod.GET)
	public ResponseEntity<List<ExpenseTypeDto>> findAllTypeNoteFrais() {
		try {
			if (userService.checkHasPermission("TYPE_EXPENSE_VIEW")) {
				List<ExpenseTypeDto> expenseTypeDto = typeNoteFraisService.findAll();
				return new ResponseEntity<>(expenseTypeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all type note frais", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addTypeNoteFrais(@RequestBody TypeNoteFraisRequestDto typeNoteFraisRequestDto) {
		try {
			if (userService.checkHasPermission("TYPE_EXPENSE_ADD")) {
				typeNoteFraisService.add(typeNoteFraisRequestDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new type note de frais", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteTypeNoteFrais(@PathVariable("id") Long idTypeNoteFrais) {
		try {
			if (userService.checkHasPermission("TYPE_EXPENSE_DELETE")) {
				typeNoteFraisService.delete(idTypeNoteFrais);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("An exception occurred while deleting the TypeNoteFrais with id =" + idTypeNoteFrais, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "saveExpenseType", method = RequestMethod.POST)
	public ResponseEntity<String> saveExpenseType(@RequestBody ExpenseTypeDto expenseTypeDto) {

	    try {
	        if (userService.checkHasPermission("TYPE_EXPENSE_EDIT")) {
	            ExpenseType savedExpenseType = typeNoteFraisService.saveTypeNoteFrais(expenseTypeDto);
	            if (savedExpenseType != null) {
	                return new ResponseEntity<>(savedExpenseType.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving expense type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving expense type", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("TYPE_EXPENSE_ADD")) {
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}
			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	

}
