package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import com.intellitech.birdnotes.enumeration.Period;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.data.dto.GoalsRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;
import com.intellitech.birdnotes.model.OpportunityNote;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.PotentialGoal;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.ConvertGoalToDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;
import com.intellitech.birdnotes.model.dto.OpportunityNoteResponseDto;
import com.intellitech.birdnotes.repository.GoalItemRepository;
import com.intellitech.birdnotes.repository.GoalRepository;
import com.intellitech.birdnotes.repository.PotentialGoalRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.GoalsService;
import com.intellitech.birdnotes.service.VisitService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("goalstService")
@Transactional
public class GoalsServiceImpl implements GoalsService {

	@Autowired
	private PotentialGoalRepository potentialGoalRepository;

	@Autowired
	private UserRepository userRepository;

	@Autowired
	private GoalItemRepository goalItemRepository;
	
	@Autowired
	private ConvertGoalToDto convertGoalManagementToDto;
	
	@Autowired
	private VisitService visitService;
	/*@Override
	public boolean saveGoalsByPotentials(GoalsRequestDto goalsRequestDto) throws BirdnotesException {		

		if (goalsRequestDto == null) {
			throw new BirdnotesException(Exceptions.NULL_PRODUCT);
		}
		
		List <PotentialGoal> entities = new ArrayList<>();
		
		for (GoalDto goal : goalsRequestDto.getGoals()) {
			
			PotentialGoal potentialGoal = new PotentialGoal();			
			Potential potential = new Potential();
			potential.setId(goal.getId());
			potentialGoal.setPotential(potential );
			potentialGoal.setTarget(goal.getTarget());
			Set<User> users = userRepository.getUserByIds(goalsRequestDto.getUsersIds());
			potentialGoal.setDelegates(users);
			entities.add(potentialGoal);
		}		
		potentialGoalRepository.save(entities);
		return true;
	}*/


	@Override
	public List<GoalItemDto> getGoalItemByUser(Long userId) throws BirdnotesException {
		Float nbBusinessDay = 1F;
		List<GoalItemDto> goalItemDtos = new ArrayList<>();
		List<GoalItem> goalItems = new ArrayList<>();
		Set<Goal> goals = userRepository.findGoalsOfUser(userId);
		if(goals!=null && goals.size() > 0) {
			for(Goal goal : goals) {
				if(goal.getPeriod().equals(Period.DAY)) {				
					Map<Long, Float> usersWorkingDaysMap = new HashMap<Long, Float>();
					Calendar calendar = Calendar.getInstance();
			        // Obtenir le premier jour du mois en modifiant le jour au 1er
			        calendar.set(Calendar.DAY_OF_MONTH, 1);
			        Date firstDayOfMonth = calendar.getTime();
			        // Obtenir le dernier jour du mois en modifiant le jour au dernier jour du mois
			        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
			        Date lastDayOfMonth = calendar.getTime();
					usersWorkingDaysMap = visitService.getUsersWorkingDays(firstDayOfMonth, lastDayOfMonth);
					if (usersWorkingDaysMap.containsKey(userId)) {
			            // Obtenir la valeur associée à cette clé
						nbBusinessDay = usersWorkingDaysMap.get(userId);
			            
			        }
				}
				Set<GoalItem> goalItemList = goalItemRepository.findByGoal(goal);
				goalItems.addAll(goalItemList);
			}
		}
		if (goalItems != null && !goalItems.isEmpty() ) {
			for (GoalItem goalItem : goalItems) {
				if(goalItem.getValue()!=0) {
					GoalItemDto goalItemDto = new GoalItemDto();
					goalItemDto.setGoal(goalItem.getGoal().getId());
					if(goalItem.getActivity()!=null) {
						goalItemDto.setActivity(goalItem.getActivity());
					}
					if(goalItem.getPotential()!=null) {
						goalItemDto.setPotentialId(goalItem.getPotential().getId());
					}
					if(goalItem.getProduct()!=null) {
						goalItemDto.setProductId(goalItem.getProduct().getId());
					}
					if(goalItem.getSector()!=null) {
						goalItemDto.setSectorId(goalItem.getSector().getId());
					}
					if(goalItem.getSpeciality()!=null) {
						goalItemDto.setSpecialityId(goalItem.getSpeciality().getId());
					}
					if(goalItem.getProspectType()!=null) {
						goalItemDto.setProspectTypeId(goalItem.getProspectType().getId());
					}

					goalItemDto.setValue(goalItem.getValue() * Integer.valueOf(nbBusinessDay.intValue()));
					
					goalItemDtos.add(goalItemDto);
				}
				
			}
		}

		return goalItemDtos;
		
	}


	@Override
	public List<GoalDto> getGoalByUser(Long userId) throws BirdnotesException {
		List<GoalDto> goalDtos = new ArrayList<>();
				
		Set<Goal> goals = userRepository.findGoalsOfUser(userId);
		for(Goal goal : goals) {
			com.intellitech.birdnotes.model.dto.GoalDto goalDto = convertGoalManagementToDto.convert(goal);
			goalDtos.add(goalDto);
		}
		return goalDtos;
		
	}
}
