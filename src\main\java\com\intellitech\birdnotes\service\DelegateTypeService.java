package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.DelegateType;
import com.intellitech.birdnotes.model.dto.DelegateTypeDto;


public interface DelegateTypeService {
	List<DelegateType> findAll() throws BirdnotesException;
	DelegateType savedelegateType(DelegateTypeDto delegateTypeDto) throws BirdnotesException;
	void delete(long id) throws BirdnotesException;
}