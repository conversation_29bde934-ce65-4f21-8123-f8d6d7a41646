package com.intellitech.birdnotes.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.NotificationMethod;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.NOTIFICATION_RULE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class NotificationRule {
	
	//Ajouté pour le test
	public NotificationRule(Integer id, String eventType, NotificationMethod notificationMethod,
			String notificationReceiver, User user, Role role) {
		super();
		this.id = id;
		this.eventType = eventType;
		this.notificationMethod = notificationMethod;
		this.notificationReceiver = notificationReceiver;
		this.user = user;
		this.role = role;
	}
	
	
	@Id
	@SequenceGenerator(name = Sequences.NOTIFICATION_RULE_SEQUENCE, sequenceName = Sequences.NOTIFICATION_RULE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.NOTIFICATION_RULE_SEQUENCE)
	@Column(name = Columns.ID)
	private Integer id;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = Columns.EVENT_TYPE)
	private String eventType;

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = Columns.NOTIFICATION_METHOD)
	private NotificationMethod notificationMethod;

	public NotificationMethod getNotificationMethod() {
		return notificationMethod;
	}

	public void setNotificationMethod(NotificationMethod notificationMethod) {
		this.notificationMethod = notificationMethod;
	}

	@Column(name = Columns.NOTIFICATION_RECEIVER)
	private String notificationReceiver;

	public String getNotificationReceiver() {
		return notificationReceiver;
	}

	public void setNotificationReceiver(String notificationReceiver) {
		this.notificationReceiver = notificationReceiver;
	}

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.ROLE_ID)
	private Role role;

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public NotificationRule() {
		super();
	}

}
