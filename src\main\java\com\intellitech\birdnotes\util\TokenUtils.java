package com.intellitech.birdnotes.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.codec.Hex;



public class TokenUtils {

	private static final String MAGIC_KEY = "obfuscate";
	private static final long SIX_DAY = 1000L* 60 * 60 * 24 * 6;
	//private static final long ONE_DAY = 1000L* 60 * 60 * 24 * 1;
	//private static final long SEVEN_DAY = 1000L* 60;
	//private static final long ONE_DAY = 1000L* 60 * 60 * 24 * 1;
	//private static final long ONE_MINUTE = 1000L* 60;
	private static final long ONE_MONTH = 1000L* 60 * 60 * 24 * 30;


	private TokenUtils() {
		throw new IllegalStateException("Utility class");
	}

	public static String createTokenForUser(UserDetails userDetails) {
		long expirationDate = System.currentTimeMillis() + ONE_MONTH;
		return userDetails.getUsername() + ":" + expirationDate + ":" + computeSignature(userDetails, expirationDate);
	}

	public static String computeSignature(UserDetails userDetails, long expirationDate) {
		StringBuilder signatureBuilder = new StringBuilder();
		signatureBuilder.append(userDetails.getUsername()).append(":");
		signatureBuilder.append(expirationDate).append(":");
		signatureBuilder.append(userDetails.getPassword()).append(":");
		signatureBuilder.append(TokenUtils.MAGIC_KEY);

		MessageDigest digest;
		try {
			digest = MessageDigest.getInstance("MD5");
		} catch (NoSuchAlgorithmException e) {
			throw new IllegalStateException("No MD5 algorithm available!");
		}
		return new String(Hex.encode(digest.digest(signatureBuilder.toString().getBytes())));
	}

	public static String getUserNameFromToken(String authToken) {

		if (authToken == null) {
			return null;
		}
		String[] params = authToken.split(":");
		return params[0];
	}

	public static boolean validateToken(String authToken, UserDetails userDetails) {
		String[] params = authToken.split(":");
		long expirationDate = Long.parseLong(params[1]);
		String signature = params[2];
		String signatureToMatch = computeSignature(userDetails, expirationDate);
		return expirationDate >= System.currentTimeMillis() && signature.equals(signatureToMatch);
	}
}
