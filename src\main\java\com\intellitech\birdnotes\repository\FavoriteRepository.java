package com.intellitech.birdnotes.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.FavoriteMenuItem;

@Repository
public interface FavoriteRepository extends JpaRepository<FavoriteMenuItem, Long> {

	// FavoriteMenuItem findByLabel(String label);

	FavoriteMenuItem findFirstByLabelIgnoreCase(String label);

	/*
	 * @Query("SELECT r from Favorite r order by r.rank asc") List<FavoriteMenuItem>
	 * findAll();
	 */

}
