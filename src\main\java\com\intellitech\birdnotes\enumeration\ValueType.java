package com.intellitech.birdnotes.enumeration;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;

import com.intellitech.birdnotes.model.dto.ValueTypeDto;
import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum ValueType {

	NUMBER_OF_VISITS(1,BirdnotesConstants.ValueType.NUMBER_OF_VISITS), 
	//VISITS_AVERAGE(2 ,BirdnotesConstants.ValueType.VISITS_AVERAGE),
	NUMBER_OF_ORDERS(5,BirdnotesConstants.ValueType.NUMBER_OF_ORDERS),
	BLANKET(2 ,BirdnotesConstants.ValueType.BLANKET),

	SAMPLE_NUMBER(4,BirdnotesConstants.ValueType.SAMPLE_NUMBER),
	SALES(3,BirdnotesConstants.ValueType.SALES), 
//	PROSPECT_SATISFACTION(6,BirdnotesConstants.ValueType.PROSPECT_SATISFACTION),
	GROSS_SALES(7,BirdnotesConstants.ValueType.GROSS_SALES),
	PROSPECT_PRODUCT_SATISFACTION(8 ,BirdnotesConstants.ValueType.PROSPECT_PRODUCT_SATISFACTION),
	PRODUCT_ORDER_PRESENTATION(9 ,BirdnotesConstants.ValueType.PRODUCT_ORDER_PRESENTATION),
	SYNCHRONISATION_DELAY(10 ,BirdnotesConstants.ValueType.SYNCHRONISATION_DELAY);

	private String type;
	private Integer id;

	private ValueType(int id,String type) {
		this.id = id;
		this.type = type;
	}

	public Integer getId() {
		return id;
	}

	public String getType() {
		return type;
	}
	
	@Override
	public String toString() {
		String valueType = "";
		if (BirdnotesConstants.ValueType.NUMBER_OF_VISITS.equals(type)) {
			valueType = "NUMBER_OF_VISITS";
		}else if (BirdnotesConstants.ValueType.BLANKET.equals(type)) {
			valueType = "BLANKET";
		}else if (BirdnotesConstants.ValueType.SALES.equals(type)) {
			valueType = "SALES";
		}else if (BirdnotesConstants.ValueType.SAMPLE_NUMBER.equals(type)) {
			valueType = "SAMPLE_NUMBER";
		}else if (BirdnotesConstants.ValueType.PROSPECT_SATISFACTION.equals(type)) {
			valueType = "PROSPECT_SATISFACTION";
		}else if (BirdnotesConstants.ValueType.GROSS_SALES.equals(type)) {
			valueType = "GROSS_SALES";
		}else if (BirdnotesConstants.ValueType.NUMBER_OF_ORDERS.equals(type)){
			valueType = "NUMBER_OF_ORDERS";
		}else if (BirdnotesConstants.ValueType.PROSPECT_PRODUCT_SATISFACTION.equals(type)){
			valueType = "PROSPECT_PRODUCT_SATISFACTION";
		}else if (BirdnotesConstants.ValueType.PRODUCT_ORDER_PRESENTATION.equals(type)){
			valueType = "PRODUCT_ORDER_PRESENTATION";
		/*}else if (BirdnotesConstants.ValueType.VISITS_AVERAGE.equals(type)){
			valueType = BirdnotesConstants.ValueType.VISITS_AVERAGE;*/
		}else if (BirdnotesConstants.ValueType.SYNCHRONISATION_DELAY.equals(type)){
			valueType = "SYNCHRONISATION_DELAY";
		}
		return valueType;
	}
	
	public static List <ValueTypeDto> getAllValueType(MessageSource messageSource, Locale locale) {
		List <ValueTypeDto> valueTypes = new ArrayList<>(values().length);
		for(ValueType value:values()) {
			ValueTypeDto valueTypeDto = new ValueTypeDto();
			valueTypeDto.setId(value.getId());
			valueTypeDto.setName(messageSource.getMessage(value.toString(), null, locale));
			valueTypes.add(valueTypeDto);
		}
		return valueTypes;
	}
	
}
