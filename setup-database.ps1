# Script PowerShell pour configurer PostgreSQL avec Docker
# Usage: .\setup-database.ps1

Write-Host "🗄️  Configuration de la base de données PostgreSQL pour BirdNotes" -ForegroundColor Green

# Vérifier si Docker est installé
try {
    $dockerVersion = docker --version
    Write-Host "✅ Docker détecté: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker n'est pas installé ou n'est pas dans le PATH" -ForegroundColor Red
    Write-Host "Veuillez installer Docker Desktop depuis https://www.docker.com/products/docker-desktop" -ForegroundColor Yellow
    exit 1
}

# Vérifier si le conteneur existe déjà
$existingContainer = docker ps -a --filter "name=postgres-birdnotes" --format "{{.Names}}"

if ($existingContainer -eq "postgres-birdnotes") {
    Write-Host "📦 Conteneur PostgreSQL existant détecté" -ForegroundColor Yellow
    
    # Vérifier si le conteneur est en cours d'exécution
    $runningContainer = docker ps --filter "name=postgres-birdnotes" --format "{{.Names}}"
    
    if ($runningContainer -eq "postgres-birdnotes") {
        Write-Host "✅ Le conteneur PostgreSQL est déjà en cours d'exécution" -ForegroundColor Green
    } else {
        Write-Host "🔄 Démarrage du conteneur PostgreSQL existant..." -ForegroundColor Blue
        docker start postgres-birdnotes
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Conteneur PostgreSQL démarré avec succès" -ForegroundColor Green
        } else {
            Write-Host "❌ Erreur lors du démarrage du conteneur" -ForegroundColor Red
            exit 1
        }
    }
} else {
    Write-Host "🚀 Création et démarrage d'un nouveau conteneur PostgreSQL..." -ForegroundColor Blue
    
    docker run --name postgres-birdnotes `
        -e POSTGRES_DB=test `
        -e POSTGRES_USER=postgres `
        -e POSTGRES_PASSWORD=intellitech `
        -p 5432:5432 `
        -d postgres:13
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Conteneur PostgreSQL créé et démarré avec succès" -ForegroundColor Green
        Write-Host "⏳ Attente de l'initialisation de la base de données..." -ForegroundColor Yellow
        Start-Sleep -Seconds 10
    } else {
        Write-Host "❌ Erreur lors de la création du conteneur" -ForegroundColor Red
        exit 1
    }
}

# Vérifier la connexion à la base de données
Write-Host "🔍 Vérification de la connexion à la base de données..." -ForegroundColor Blue

$maxAttempts = 10
$attempt = 1

while ($attempt -le $maxAttempts) {
    try {
        $testConnection = docker exec postgres-birdnotes psql -U postgres -d test -c "SELECT 1;" 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Connexion à la base de données réussie!" -ForegroundColor Green
            break
        }
    } catch {
        # Continuer à essayer
    }
    
    Write-Host "⏳ Tentative $attempt/$maxAttempts - Attente de la base de données..." -ForegroundColor Yellow
    Start-Sleep -Seconds 3
    $attempt++
}

if ($attempt -gt $maxAttempts) {
    Write-Host "❌ Impossible de se connecter à la base de données après $maxAttempts tentatives" -ForegroundColor Red
    exit 1
}

# Afficher les informations de connexion
Write-Host ""
Write-Host "🎉 Configuration terminée avec succès!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Informations de connexion:" -ForegroundColor Cyan
Write-Host "   Host: localhost" -ForegroundColor White
Write-Host "   Port: 5432" -ForegroundColor White
Write-Host "   Database: test" -ForegroundColor White
Write-Host "   Username: postgres" -ForegroundColor White
Write-Host "   Password: intellitech" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Vous pouvez maintenant démarrer l'application Spring Boot:" -ForegroundColor Green
Write-Host "   mvn spring-boot:run" -ForegroundColor White
Write-Host ""
Write-Host "🛑 Pour arrêter la base de données:" -ForegroundColor Yellow
Write-Host "   docker stop postgres-birdnotes" -ForegroundColor White
Write-Host ""
Write-Host "🗑️  Pour supprimer complètement le conteneur:" -ForegroundColor Red
Write-Host "   docker stop postgres-birdnotes && docker rm postgres-birdnotes" -ForegroundColor White
