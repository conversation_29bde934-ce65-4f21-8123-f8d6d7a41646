package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ProspectActivity;
import com.intellitech.birdnotes.model.dto.ProspectActivityDto;

public interface ProspectActivityService {
	List<ProspectActivityDto> findAll() throws BirdnotesException;
	ProspectActivity saveprospectActivity(ProspectActivityDto prospectActivityDto) throws BirdnotesException;
	void delete(long id) throws BirdnotesException;

}