package com.intellitech.birdnotes.model.dto;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.model.request.ProspectListRequest;

public class ProspectDistributionRequest {
private Long selectedGroup;
private List<String> selectedActivities;
private List<Long> selectedUsers;
private List<Long> selectedSpecialities;
private List<Long> selectedPotentials;
private List<Long> selectedLocalities;
private List<Long> selectedSectors;
private boolean distribution;
private boolean cartography;
private ProspectListRequest prospectListRequest;
private String keywords;
private String affectationFilter;
private Integer limit;
private String prospectSource;
private Date date;



public ProspectDistributionRequest(Long selectedGroup, List<String> selectedActivities, List<Long> selectedUsers,
		List<Long> selectedSpecialities, List<Long> selectedPotentials, List<Long> selectedLocalities, 
		List<Long> selectedSectors, boolean distribution, String affectationFilter, String prospectSource, Date date, Integer limit) {
	super();
	this.selectedGroup = selectedGroup;
	this.selectedActivities = selectedActivities;
	this.selectedUsers = selectedUsers;
	this.selectedSpecialities = selectedSpecialities;
	this.selectedPotentials = selectedPotentials;
	this.selectedLocalities = selectedLocalities;
	this.selectedSectors = selectedSectors;
	this.affectationFilter = affectationFilter;
	this.distribution = distribution;
	this.affectationFilter = affectationFilter;
	this.prospectSource = prospectSource;
	this.date = date;
	this.limit = limit;
}

public boolean isDistribution() {
	return distribution;
}

public void setDistribution(boolean distribution) {
	this.distribution = distribution;
}

public ProspectDistributionRequest() {
	super();
	
}
public Long getSelectedGroup() {
	return selectedGroup;
}
public void setSelectedGroup(Long selectedGroup) {
	this.selectedGroup = selectedGroup;
}
public  List<String> getSelectedActivities() {
	return selectedActivities;
}
public void setSelectedActivities( List<String> selectedActivities) {
	this.selectedActivities = selectedActivities;
}
public List<Long> getSelectedUsers() {
	return selectedUsers;
}
public void setSelectedUsers(List<Long> selectedUsers) {
	this.selectedUsers = selectedUsers;
}
public List<Long> getSelectedSpecialities() {
	return selectedSpecialities;
}
public void setSelectedSpecialities(List<Long> selectedSpecialities) {
	this.selectedSpecialities = selectedSpecialities;
}
public List<Long> getSelectedPotentials() {
	return selectedPotentials;
}
public void setSelectedPotentials(List<Long> selectedPotentials) {
	this.selectedPotentials = selectedPotentials;
}
public List<Long> getSelectedLocalities() {
	return selectedLocalities;
}
public void setSelectedLocalities(List<Long> selectedLocalities) {
	this.selectedLocalities = selectedLocalities;
}
public List<Long> getSelectedSectors() {
	return selectedSectors;
}
public void setSelectedSectors(List<Long> selectedSectors) {
	this.selectedSectors = selectedSectors;
}

public String getProspectSource() {
	return prospectSource;
}

public void setProspectSource(String prospectSource) {
	this.prospectSource = prospectSource;
}

@Override
public String toString() {
	return "ProspectDistributionRequest [selectedGroup=" + selectedGroup + ", selectedActivities=" + selectedActivities
			+ ", selectedUsers=" + selectedUsers + ", selectedSpecialities=" + selectedSpecialities
			+ ", selectedPotentials=" + selectedPotentials + ", selectedLocalities=" + selectedLocalities
			+ ", selectedSectors=" + selectedSectors + "]";
}

public ProspectListRequest getProspectListRequest() {
	return prospectListRequest;
}

public void setProspectListRequest(ProspectListRequest prospectListRequest) {
	this.prospectListRequest = prospectListRequest;
}

public String getKeywords() {
	return keywords;
}

public void setKeywords(String keywords) {
	this.keywords = keywords;
}

public boolean isCartography() {
	return cartography;
}

public void setCartography(boolean cartography) {
	this.cartography = cartography;
}

public String getAffectationFilter() {
	return affectationFilter;
}

public void setAffectationFilter(String affectationFilter) {
	this.affectationFilter = affectationFilter;
}

public Integer getLimit() {
	return limit;
}

public void setLimit(Integer limit) {
	this.limit = limit;
}

public Date getDate() {
	return date;
}

public void setDate(Date date) {
	this.date = date;
}




}
