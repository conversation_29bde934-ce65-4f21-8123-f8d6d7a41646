package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Locality;


@Repository
public interface LocalityRepository extends JpaRepository<Locality, Long>{
	
	@Override
	@Query("SELECT l from Locality l ORDER BY l.name")
	List<Locality> findAll();

	Locality findByName(String name);
	
	Locality findFirstByNameAndSectorName(String localityName,String sectorName);
	
	Locality findFirstByNameIgnoreCaseAndSectorNameIgnoreCase(String localityName,String sectorName);
	
	Locality findFirstByNameIgnoreCaseAndSectorId(String localityName,Long sectorId);
	
	@Query("SELECT s from Locality s where upper(name) = :name ")
	Locality findByNameIgnoreCase(@Param("name") String name);
	
	
	@Query("SELECT l from Locality l WHERE l.sector.id=:id order by name")
	List<Locality> findBySectorId(@Param("id") Long id);
	
	@Query("SELECT l.name from Locality l order by name")
	List<String> findAllLocalityNames();

	@Modifying
	@Query("DELETE from Locality WHERE id=:id")
	void deleteByID(@Param("id") Long id);

	@Query("SELECT l.id from Locality l WHERE l.id in (:localities) order by l.name")
	List<Long> findWhereIdIn(@Param("localities") List<Long> localities);

	@Query("SELECT l.id from Locality l")
	List<Long> getAllLocalityIds();
		
}
