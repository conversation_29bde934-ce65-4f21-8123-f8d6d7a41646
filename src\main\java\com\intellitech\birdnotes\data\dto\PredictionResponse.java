package com.intellitech.birdnotes.data.dto;

import java.util.List;

public class PredictionResponse {
	

	private String labName;
	private String comment;
	private String model_description;
	private String score;
	private String status;
	
	private CommentClassification[] comments;
	
	private CommentClassification[] reuslts;
	
	private String [] specialityNotFound;
	
	private String [] productNotFound;
	
	private String [] localityNotFound;
	
	private List <ProspectOrderPredictionResponse> results;
	
	public PredictionResponse() {
		
	}
	
	public PredictionResponse(String labName, String comment) {
		super();
		this.labName = labName;
		this.comment = comment;
	}
	
	public String getLabName() {
		return labName;
	}
	public void setLabName(String labName) {
		this.labName = labName;
	}
	public String getComment() {
		return comment;
	}
	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getModel_description() {
		return model_description;
	}

	public void setModel_description(String model_description) {
		this.model_description = model_description;
	}

	public String getScore() {
		return score;
	}

	public void setScore(String score) {
		this.score = score;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public List<ProspectOrderPredictionResponse> getResults() {
		return results;
	}

	public void setResults(List<ProspectOrderPredictionResponse> results) {
		this.results = results;
	}

	public String[] getSpecialityNotFound() {
		return specialityNotFound;
	}

	public void setSpecialityNotFound(String[] specialityNotFound) {
		this.specialityNotFound = specialityNotFound;
	}

	public String[] getProductNotFound() {
		return productNotFound;
	}

	public void setProductNotFound(String[] productNotFound) {
		this.productNotFound = productNotFound;
	}

	public String[] getLocalityNotFound() {
		return localityNotFound;
	}

	public void setLocalityNotFound(String[] localityNotFound) {
		this.localityNotFound = localityNotFound;
	}

	public CommentClassification[] getComments() {
		return comments;
	}

	public void setComments(CommentClassification[] comments) {
		this.comments = comments;
	}

	public CommentClassification[] getReuslts() {
		return reuslts;
	}

	public void setReuslts(CommentClassification[] reuslts) {
		this.reuslts = reuslts;
	}	
	
}
