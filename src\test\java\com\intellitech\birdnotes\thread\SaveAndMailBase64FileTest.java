package com.intellitech.birdnotes.thread;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mail.javamail.JavaMailSender;

import java.util.Arrays;

@RunWith(MockitoJUnitRunner.class)
public class SaveAndMailBase64FileTest {

    @Mock
    private JavaMailSender mockJavaMailSender;

    private SaveAndMailBase64File saveAndMailBase64FileUnderTest;

    @Before
    public void setUp() throws Exception {
        saveAndMailBase64FileUnderTest = new SaveAndMailBase64File("imageString", "imageName", "pathUpload", false,
                mockJavaMailSender, Arrays.asList("value"), "from", new String[]{"to"}, "subject", "htmlBody");
    }

    @Test
    public void testRun() {
        // Setup
        // Run the test
        //saveAndMailBase64FileUnderTest.run();

        // Verify the results
    }
}
