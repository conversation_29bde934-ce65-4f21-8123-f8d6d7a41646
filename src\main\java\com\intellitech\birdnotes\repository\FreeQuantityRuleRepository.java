package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.FreeQuantityRule;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleDto;


@Repository
public interface FreeQuantityRuleRepository extends JpaRepository<FreeQuantityRule, Long> {

	void deleteById(long id);
	
	@Query("Select f from FreeQuantityRule f")
	List<FreeQuantityRule> findAll();

	


}
