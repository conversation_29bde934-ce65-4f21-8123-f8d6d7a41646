package com.intellitech.birdnotes.dao.impl;

import com.intellitech.birdnotes.data.dto.PlanningObjectiveCountDto;
import com.intellitech.birdnotes.model.*;
import com.intellitech.birdnotes.model.dto.*;
import com.intellitech.birdnotes.model.request.ProspectListRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DynamicQueriesImplTest {

    @Mock
    private EntityManager mockEntityManager;

    @InjectMocks
    private DynamicQueriesImpl dynamicQueriesImplUnderTest;

    @Test
    public void testFindGoalsSum() {
        // Setup
        // Configure EntityManager.createQuery(...).
        final TypedQuery<GoalSum> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("queryString", GoalSum.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<GoalSum> result = dynamicQueriesImplUnderTest.findGoalsSum("queryString",
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime(),
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
    }

    @Test
    public void testFindVisits() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<VisitsProducts> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", VisitsProducts.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<VisitsProducts> result = dynamicQueriesImplUnderTest.findVisits("query", parameters, 0, 0);

        // Verify the results
    }

    @Test
    public void testFindSample() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<SampleSupply> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", SampleSupply.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<SampleSupply> result = dynamicQueriesImplUnderTest.findSample("query", parameters);

        // Verify the results
    }

    @Test
    public void testFindPurchaseOrderTemplate() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<PurchaseOrderTemplate> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", PurchaseOrderTemplate.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<PurchaseOrderTemplate> result = dynamicQueriesImplUnderTest.findPurchaseOrderTemplate("query",
                parameters);

        // Verify the results
    }

    @Test
    public void testFindCommission() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Commission> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", Commission.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<Commission> result = dynamicQueriesImplUnderTest.findCommission("query", parameters);

        // Verify the results
    }

    @Test
    public void testFindPlanningOfWeeksToShow() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Long> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", Long.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<Long> result = dynamicQueriesImplUnderTest.findPlanningOfWeeksToShow("query", parameters);

        // Verify the results
        assertThat(result).isEqualTo(Arrays.asList());
    }

    @Test
    public void testFindGadget() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<GiftSupplyDto> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", GiftSupplyDto.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<GiftSupplyDto> result = dynamicQueriesImplUnderTest.findGadget("query", parameters);

        // Verify the results
    }

    @Test
    public void testFindGroupSearch() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<VisitHistoryGroupDto> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", VisitHistoryGroupDto.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<VisitHistoryGroupDto> result = dynamicQueriesImplUnderTest.findGroupSearch("query", parameters);

        // Verify the results
    }

    @Test
    public void testFindProspects() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Prospect> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", Prospect.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<Prospect> result = dynamicQueriesImplUnderTest.findProspects("query", parameters);

        // Verify the results
    }

    @Test
    public void testFindProspectsDistribution() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();
        final List<ProspectDistribution> expectedResult = Arrays.asList(
                new ProspectDistribution("groupOfSearch", 0L, "groupOfSearchValue"));

        // Configure EntityManager.createQuery(...).
        final TypedQuery<ProspectDistribution> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", ProspectDistribution.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<ProspectDistribution> result = dynamicQueriesImplUnderTest.findProspectsDistribution("query",
                parameters);

        // Verify the results
        //assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindProspectsList() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();
        final ProspectListRequest prospectListRequest = new ProspectListRequest();
        prospectListRequest.setFirst(0);
        prospectListRequest.setRows(0);
        prospectListRequest.setSortField("sortField");
        prospectListRequest.setSortOrder(0);
        prospectListRequest.setGlobalFilter("globalFilter");

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Prospect> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("queryString", Prospect.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<Prospect> result = dynamicQueriesImplUnderTest.findProspectsList("queryString", parameters,
                prospectListRequest, 0);

        // Verify the results
    }

    @Test
    public void testFindProspectsCount() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Long> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("queryString", Long.class)).thenReturn(mockTypedQuery);

        // Run the test
        final Long result = dynamicQueriesImplUnderTest.findProspectsCount("queryString", parameters);

        // Verify the results
        //assertThat(result).isEqualTo(0L);
    }

    @Test
    public void testFindVisitProductsCount() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Long> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("queryString", Long.class)).thenReturn(mockTypedQuery);

        // Run the test
        final Long result = dynamicQueriesImplUnderTest.findVisitProductsCount("queryString", parameters);

        // Verify the results
        //assertThat(result).isEqualTo();
    }

    @Test
    public void testFindProspectsListCartography() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();
        final ProspectDto prospectDto = new ProspectDto();
        prospectDto.setCreatorUser("creatorUser");
        prospectDto.setPassword("password");
        prospectDto.setRoleIds(Arrays.asList(0));
        prospectDto.setSupervisorIds(Arrays.asList(0L));
        prospectDto.setPotential("potential");
        final List<ProspectDto> expectedResult = Arrays.asList(prospectDto);

        // Configure EntityManager.createQuery(...).
        final TypedQuery<ProspectDto> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("queryString", ProspectDto.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<ProspectDto> result = dynamicQueriesImplUnderTest.findProspectsListCartography("queryString",
                parameters);

        // Verify the results
        //assertThat(result).isEqualTo(expectedResult);
    }

    @Test
    public void testFindPlanifiedProspects() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<PlanningObjectiveCountDto> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", PlanningObjectiveCountDto.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<PlanningObjectiveCountDto> result = dynamicQueriesImplUnderTest.findPlanifiedProspects("query",
                parameters);

        // Verify the results
    }

    @Test
    public void testFindPatients() {
        // Setup
        // Configure EntityManager.createQuery(...).
        final TypedQuery<Prospect> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", Prospect.class)).thenReturn(mockTypedQuery);

        // Run the test
        //final List<Prospect> result = dynamicQueriesImplUnderTest.findPatients("query");

        // Verify the results
    }
}
