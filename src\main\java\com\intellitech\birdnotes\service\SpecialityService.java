package com.intellitech.birdnotes.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;

public interface SpecialityService {
	
	List<SpecialityDto> findAll() throws BirdnotesException;
	List<SpecialityDto> saveAll(List<SpecialityDto> specialityDtos) throws BirdnotesException;
	Speciality add (SpecialityRequestDto specialityRequestDto, MultipartFile file) throws BirdnotesException;
	void delete (Long id) throws BirdnotesException ;
	SpecialityDto findSpecialityByName(String name)throws BirdnotesException;
	void addAll(List<SpecialityRequestDto> specialityRequestDtos)throws BirdnotesException;
	SpecialityDto findSpecialityDto(String specialityName, List<SpecialityDto> specialityDtos) ;
	Speciality saveSpeciality(SpecialityDto specialityDto, MultipartFile file) throws BirdnotesException;

}
