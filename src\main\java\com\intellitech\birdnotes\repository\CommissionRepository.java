package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;

@Repository
public interface CommissionRepository extends JpaRepository<Commission, Long>{
	@Query("SELECT c from Commission c where  LOWER(name) = LOWER(?1) AND id != ?2")
	Commission findByNameAndAnotherId(String name, Long id);
	
	

	
}
