package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class ProspectDtoForStatisticTable implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String firstName;
	private String lastName ;
	private String fullName;
	private String activity;
	private String potential;
	private String address;
	private String gsm;
	private String phone;
	private String email;	
	private String speciality;
	private String sector;
	private String locality;
	private List<String> delegateNames;
	private String userName;
	private Double latitude;
	private Double longitude;
	private String mapAddress;
	




	public String getPotential() {
		return potential;
	}

	public void setPotential(String potential) {
		this.potential = potential;
	}

	public String getSpeciality() {
		return speciality;
	}

	public void setSpeciality(String speciality) {
		this.speciality = speciality;
	}

	public String getSector() {
		return sector;
	}

	public void setSector(String sector) {
		this.sector = sector;
	}

	public String getLocality() {
		return locality;
	}

	public void setLocality(String locality) {
		this.locality = locality;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}


	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getGsm() {
		return gsm;
	}

	public void setGsm(String gsm) {
		this.gsm = gsm;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	
	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}


	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public String getMapAddress() {
		return mapAddress;
	}

	public void setMapAddress(String mapAddress) {
		this.mapAddress = mapAddress;
	}

	
	
	

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	@Override
	public String toString() {
		return "ProspectDto [id=" + id + ", firstName=" + firstName + ", lastName=" + lastName + ", activity="
				+ activity + ", potential=" + potential + ", address=" + address + ", gsm=" + gsm + ", phone="
				+ phone + ", email=" + email + ", speciality=" + speciality + ", sector="
				+ sector + ", locality=" + locality + ", delegateNames="
				+ delegateNames + ", userName=" + userName +", latitude=" + latitude
				+ ", longitude=" + longitude + ", mapAddress=" + mapAddress +  "]";
	}

	public ProspectDtoForStatisticTable(ProspectDto prospect) {
		super();
		this.id = prospect.getId();
		this.firstName = prospect.getFirstName();
		this.lastName = prospect.getLastName();
		this.fullName = prospect.getFullName();
		this.activity = prospect.getActivity();
		this.potential = prospect.getPotentialDto().getName();
		this.address = prospect.getAddress();
		this.gsm = prospect.getGsm();
		this.phone = prospect.getPhone();
		this.email = prospect.getEmail();
	
		this.speciality = prospect.getSpecialityDto().getName();
		this.sector = prospect.getSectorDto().getName();
		this.locality = prospect.getLocalityDto().getName();

		this.delegateNames = prospect.getDelegateNames();
		this.userName = prospect.getUserName();
		this.latitude = prospect.getLatitude();
		this.longitude = prospect.getLongitude();
		this.mapAddress = prospect.getMapAddress();
		
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((activity == null) ? 0 : activity.hashCode());
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((delegateNames == null) ? 0 : delegateNames.hashCode());
		result = prime * result + ((email == null) ? 0 : email.hashCode());
		result = prime * result + ((firstName == null) ? 0 : firstName.hashCode());
		
		result = prime * result + ((gsm == null) ? 0 : gsm.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());

		result = prime * result + ((lastName == null) ? 0 : lastName.hashCode());
		result = prime * result + ((latitude == null) ? 0 : latitude.hashCode());
		
		result = prime * result + ((longitude == null) ? 0 : longitude.hashCode());
		result = prime * result + ((mapAddress == null) ? 0 : mapAddress.hashCode());
		result = prime * result + ((phone == null) ? 0 : phone.hashCode());
		result = prime * result + ((userName == null) ? 0 : userName.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProspectDtoForStatisticTable other = (ProspectDtoForStatisticTable) obj;
		if (activity == null) {
			if (other.activity != null)
				return false;
		} else if (!activity.equals(other.activity))
			return false;
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;
		 
		
		if (delegateNames == null) {
			if (other.delegateNames != null)
				return false;
		} else if (!delegateNames.equals(other.delegateNames))
			return false;
		if (email == null) {
			if (other.email != null)
				return false;
		} else if (!email.equals(other.email))
			return false;
		if (firstName == null) {
			if (other.firstName != null)
				return false;
		} else if (!firstName.equals(other.firstName))
			return false;
		
		if (gsm == null) {
			if (other.gsm != null)
				return false;
		} else if (!gsm.equals(other.gsm))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (lastName == null) {
			if (other.lastName != null)
				return false;
		} else if (!lastName.equals(other.lastName))
			return false;
		if (latitude == null) {
			if (other.latitude != null)
				return false;
		} else if (!latitude.equals(other.latitude))
			return false;
		if (locality == null) {
			if (other.locality != null)
				return false;
		} else if (!locality.equals(other.locality))
			return false;
		if (longitude == null) {
			if (other.longitude != null)
				return false;
		} else if (!longitude.equals(other.longitude))
			return false;
		if (mapAddress == null) {
			if (other.mapAddress != null)
				return false;
		} else if (!mapAddress.equals(other.mapAddress))
			return false;
		
		if (phone == null) {
			if (other.phone != null)
				return false;
		} else if (!phone.equals(other.phone))
			return false;
		if (potential == null) {
			if (other.potential != null)
				return false;
		} else if (!potential.equals(other.potential))
			return false;
		
		if (sector == null) {
			if (other.sector != null)
				return false;
		} else if (!sector.equals(other.sector))
			return false;
		if (speciality== null) {
			if (other.speciality != null)
				return false;
		} else if (!speciality.equals(other.speciality))
			return false;
		
		if (userName == null) {
			if (other.userName != null)
				return false;
		} else if (!userName.equals(other.userName))
			return false;
		return true;
	}
}