package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.UserAffectationDetailsDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.DelegateProspectsDto;
import com.intellitech.birdnotes.model.dto.PreAffectationDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectsAffectationRequestDto;
import com.intellitech.birdnotes.model.dto.SelectedDataForSourceProspectsRequestDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.ProspectsAffectationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/prospectsAffectation")
public class ProspectsAffectationController {
	private static final Logger LOG = LoggerFactory.getLogger(ProspectsAffectationController.class);
	@Autowired
	private ProspectsAffectationService prospectsAffectationService;
	@Autowired
	UserService userService;
	@Autowired
	private DownloadDataService downloadDataService;
	@Autowired
	DelegateService delegateService;
	@RequestMapping(value = "saveProspectsAffectation", method = RequestMethod.POST)
	public ResponseEntity<String> saveProspectsAffectation(
			@RequestBody ProspectsAffectationRequestDto prospectsAffectationDto) {
		try {
			if (userService.checkHasPermission("AFFECTATION_EDIT")) {
				prospectsAffectationService.saveAffectation(prospectsAffectationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when adding affectation", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new affectation", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "/findSourceProspects/{id}", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectDto>> findSourceProspects(@PathVariable("id") Long id) {
		try {
			List<ProspectDto> prospectDtos = prospectsAffectationService.findSourceProspects(id);
			return new ResponseEntity<>(prospectDtos, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting source prospects", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/findPreAffectationByUser/{id}", method = RequestMethod.GET)
	public ResponseEntity<List<PreAffectationDto>> findPreAffectationByUser(@PathVariable("id") Long id) {
		try {
			List<PreAffectationDto> preAffectationDto = prospectsAffectationService.findPreAffectationByUser(id);
			return new ResponseEntity<>(preAffectationDto, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting pre-affectation", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "deleteAffectation", method = RequestMethod.POST)
	public ResponseEntity<String> deleteAffectation(
			@RequestBody ProspectsAffectationRequestDto prospectsAffectationDto) {
		try {
			if (userService.checkHasPermission("AFFECTATION_EDIT")) {
				prospectsAffectationService.deleteAffectation(prospectsAffectationDto);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("An exception occurred while deleting  a new affectation", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}
	
	@RequestMapping(value = "getUserAffectationDetails/{userId}", method = RequestMethod.GET)
	public ResponseEntity<UserAffectationDetailsDto> getUserAffectationDetails(@PathVariable("userId") Long userId) {
		try {
			UserAffectationDetailsDto userAffectationDetailsDto = prospectsAffectationService.getUserAffectationDetails(userId);
			return new ResponseEntity<>(userAffectationDetailsDto, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects by delegate", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	
	@RequestMapping(value = "/deletePreAffectation/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deletePreAffectation(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("PRE_AFFECTATION_DELETE")) {
	            prospectsAffectationService.deletePreAffectation(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting pre-affectation", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the pre-affectation with id =" + id, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "getProspectsByUser/{id}", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectDto>> findProspectsByUser(@PathVariable("id") Long id) {
		try {
			List<ProspectDto> prospectDtos = prospectsAffectationService.findprospectsByUser(id);
			return new ResponseEntity<>(prospectDtos, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects by delegate", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getProspectsByUserAndSectors/{id}/{sectorIds}", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectDto>> findProspectsByUserAndSectors(@PathVariable("id") Long id,
			@PathVariable("sectorIds") List<Long> sectorIds) {
		try {
			List<ProspectDto> prospectDtos = prospectsAffectationService.findprospectsByUserAndSectors(id, sectorIds);
			return new ResponseEntity<>(prospectDtos, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects by delegate", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findSourceProspectsWithSectors/{id}/{sectorIds}", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectDto>> findSourceProspectsWithSectors(@PathVariable("id") Long id,
			@PathVariable("sectorIds") List<Long> sectorIds) {
		try {
			List<ProspectDto> prospectDtos = prospectsAffectationService.findSourceProspectsWithSectors(id, sectorIds);
			return new ResponseEntity<>(prospectDtos, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting source prospects by sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findSourceProspectsByMultipleSelection", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> findSourceProspectsByMultipleSelection(
			@RequestBody SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) {
		try {
			if (userService.checkHasPermission("AFFECTATION_VIEW")) {
				List<ProspectDto> prospectDtos = prospectsAffectationService
						.findSourceProspectsByMultipleSelection(selectedDataForSourceProspectsRequest);
				return new ResponseEntity<>(prospectDtos, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting source prospects by multiple selection", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findDelegateProspectsByMultipleSelection", method = RequestMethod.POST)
	public ResponseEntity<DelegateProspectsDto> findDelegateProspectsByMultipleSelection(
			@RequestBody SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) {
		try {
			if (userService.checkHasPermission("AFFECTATION_VIEW")) {
				DelegateProspectsDto prospectDtos = prospectsAffectationService
						.findDelegateProspectsByMultipleSelection(selectedDataForSourceProspectsRequest);
				return new ResponseEntity<>(prospectDtos, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting prospects by delegate", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@Autowired
	private CurrentUser currentUser;

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("AFFECTATION_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "/getAllDataForNewProspect", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForNewProspect() {
		try {
			if (userService.checkHasPermission("AFFECTATION_VIEW")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForNewProspect(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for add new prospect", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
