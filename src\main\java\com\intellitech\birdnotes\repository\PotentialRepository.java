package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Sector;

@Repository
public interface PotentialRepository extends JpaRepository<Potential, Long> {

	Potential findByName(String name);
	
	Potential findFirstByNameIgnoreCase( String name);
	
	@Override
	@Query("Select p from Potential p ORDER BY p.name ASC")
	List<Potential> findAll();
	
	@Modifying
	@Query("DELETE FROM Potential where id=:id")
	void deleteById(@Param ("id") Long id);
    @Query("SELECT p.id from Potential p")
	List<Long> getAllPotentialIds();
    
	@Query("SELECT p FROM Potential p WHERE LOWER(p.name) = LOWER(?1) AND p.id != ?2")
	Potential findByNameAndAnotherId(String name, Long id);
	
	@Query("SELECT p.name FROM Potential p order by p.name")
	List<String>findAllPotentialNames();
}
