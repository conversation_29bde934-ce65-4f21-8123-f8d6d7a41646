package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.FavoriteMenuItem;
import com.intellitech.birdnotes.model.convertor.ConvertFavoriteToDto;
import com.intellitech.birdnotes.model.dto.FavoriteDto;
import com.intellitech.birdnotes.model.dto.FavoriteRequestDto;
import com.intellitech.birdnotes.repository.FavoriteRepository;
import com.intellitech.birdnotes.service.FavoriteService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("favoriteService")
@Transactional
public class FavoriteServiceImpl implements FavoriteService {

	private FavoriteRepository favoriteRepository;

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	public FavoriteServiceImpl(FavoriteRepository favoriteRepository, ConvertFavoriteToDto convertFavoriteToDto) {
		super();
		this.favoriteRepository = favoriteRepository;
	}

	@Override
	public List<FavoriteMenuItem> add(FavoriteRequestDto favoriteRequestDto) throws BirdnotesException {
		int rank = 1;
		List favoriteList = new ArrayList<>();
		for (FavoriteMenuItem favorite : favoriteRequestDto.getFavorites()) {
			if (favorite.getId() == null) {

				FavoriteMenuItem newFavorite = new FavoriteMenuItem();
				newFavorite.setLabel(favorite.getLabel());
				newFavorite.setRank(favorite.getRank());
				newFavorite.setRouterLink(favorite.getRouterLink());
				newFavorite.setIcon(favorite.getIcon());
				favoriteList.add(favoriteRepository.save(newFavorite));
				rank++;
			} else {
				FavoriteMenuItem existingFavorite = favoriteRepository.findOne(favorite.getId());
				if (existingFavorite != null) {
					existingFavorite.setLabel(favorite.getLabel());
					existingFavorite.setRank(rank);
					favoriteList.add(favoriteRepository.save(existingFavorite));
					rank++;
				}
			}

		}
		return favoriteList;
	}

	@Override
	public List<FavoriteDto> findAll() throws BirdnotesException {
		List<FavoriteDto> result = new ArrayList<>();
		List<FavoriteMenuItem> allFavorites = favoriteRepository.findAll();
		for (FavoriteMenuItem favorite : allFavorites) {
			FavoriteDto r = new FavoriteDto();
			r.setId(favorite.getId());
			r.setLabel(favorite.getLabel());
			r.setRouterLink(favorite.getRouterLink());
			r.setIcon(favorite.getIcon());
			result.add(r);
		}
		return result;
	}

	@Override
	public void delete(Long id) throws BirdnotesException{

			favoriteRepository.delete(id);



	}

}
