package com.intellitech.birdnotes.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;


@Entity
@Table(name = BirdnotesConstants.Tables.ProspectOrderPrediction, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ProspectOrderPrediction {
	
	
	private static final long serialVersionUID = 1L;
	private Long id;
	private String features;
	private Date predictionDate;
	private Prospect prospect;
	private Product product;
	private Long orderQuantityPrediction;

	
	
	@Id
	@SequenceGenerator(name = Sequences.PROSPECT_ORDER_PREDICTION__SEQUENCE, sequenceName = Sequences.PROSPECT_ORDER_PREDICTION__SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PROSPECT_ORDER_PREDICTION__SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	

	
	@ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID)
	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}
	
	@ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID)
	public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}
	
	@Temporal(TemporalType.DATE)
	@Column(name = BirdnotesConstants.Columns.PREDICTION_DATE)
	public Date getPredictionDate() {
		return predictionDate;
	}

	public void setPredictionDate(Date predictionDate) {
		this.predictionDate = predictionDate;
	}

	@Column(name = BirdnotesConstants.Columns.ORDER_QUNATITY_PREDICTION)
	public Long getOrderQuantityPrediction() {
		return orderQuantityPrediction;
	}

	public void setOrderQuantityPrediction(Long orderQuantityPrediction) {
		this.orderQuantityPrediction = orderQuantityPrediction;
	}

	@Column(name = Columns.FEATURES, length = BirdnotesConstants.Numbers.N_1024)
	public String getFeatures() {
		return features;
	}

	public void setFeatures(String features) {
		this.features = features;
	}


}
