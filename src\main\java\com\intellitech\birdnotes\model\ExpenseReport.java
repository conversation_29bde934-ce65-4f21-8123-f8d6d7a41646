package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.EXPENSE_REPORT, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ExpenseReport implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.EXPENSE_REPORT_SEQUENCE, sequenceName = Sequences.EXPENSE_REPORT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.EXPENSE_REPORT_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;
	
	private Long identifier;

	private Date date;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	private Delegate delegate;

	private Float montant;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.EXPENSE_TYPE_ID)
	private ExpenseType expenseType;

	@Column(name = BirdnotesConstants.Columns.DESCRIPTION_EXPENSE_REPORT, length = BirdnotesConstants.Numbers.N_255)
	private String description;

	@Column(name = BirdnotesConstants.Columns.ATTACHMENT_NAME, length = BirdnotesConstants.Numbers.N_255)
	private String pieceJointe;

	@Column(name = BirdnotesConstants.Columns.ATTACHEMENT_BASE64)
	@Type(type = "text")
	private String attachmentBase64;
	
	@OneToMany(mappedBy = "noteFrais")
	 private List<ValidationStatus> validationStatus;
	
	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	private UserValidationStatus status;
	
	@ManyToOne(optional=true)
	@JoinColumn(name = BirdnotesConstants.Columns.ACTIVITY_ID)
	private Activity activity;
	


	public ExpenseReport() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = Columns.IDENTIFIER)
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}


	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}

	public Float getMontant() {
		return montant;
	}

	public void setMontant(Float montant) {
		this.montant = montant;
	}

	public ExpenseType getExpenseType() {
		return expenseType;
	}

	public void setExpenseType(ExpenseType expenseType) {
		this.expenseType = expenseType;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public String getPieceJointe() {
		return pieceJointe;
	}

	public void setPieceJointe(String pieceJointe) {
		this.pieceJointe = pieceJointe;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public List<ValidationStatus> getValidationStatus() {
		return validationStatus;
	}

	public void setValidationStatus(List<ValidationStatus> validationStatus) {
		this.validationStatus = validationStatus;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public Activity getActivity() {
		return activity;
	}

	public void setActivity(Activity activity) {
		this.activity = activity;
	}


}
