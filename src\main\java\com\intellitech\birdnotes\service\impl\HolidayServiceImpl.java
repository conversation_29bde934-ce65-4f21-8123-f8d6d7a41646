package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Holiday;
import com.intellitech.birdnotes.model.dto.HolidayDto;
import com.intellitech.birdnotes.repository.HolidayRepository;
import com.intellitech.birdnotes.service.HolidayService;

@Service("holidayService")
@Transactional
public class HolidayServiceImpl implements HolidayService {

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	private HolidayRepository holidayRepository;

	@Override
	public List<HolidayDto> getAllHolidays() throws BirdnotesException {
		List<HolidayDto> result = new ArrayList<>();
		List<Holiday> holidays = holidayRepository.findAll();
		if (holidays != null && !holidays.isEmpty()) {
			for (Holiday holiday : holidays) {
				HolidayDto holidayDto = new HolidayDto();
				holidayDto.setId(holiday.getId());
				holidayDto.setHolidayType(holiday.getHolidayType());
				if (holiday.getDate() != null) {
					holidayDto.setDate(holiday.getDate());
				}
				if (holiday.getDay() != null) {
					holidayDto.setDay(holiday.getDay());
				}
				if (holiday.getMonth() != null) {
					holidayDto.setMonth(holiday.getMonth());
				}
				result.add(holidayDto);
			}
		}
		return result;

	}

	@Override
	public Holiday saveHoliday(HolidayDto holidayDto) throws BirdnotesException {
		Holiday holiday = null;
		if (holidayDto.getId() != null) {
			holiday = holidayRepository.findOne(holidayDto.getId());
		}
		if (holiday == null) {
			holiday = new Holiday();
		}
		holiday.setId(holidayDto.getId());
		holiday.setHolidayType(holidayDto.getHolidayType());
		if (holidayDto.getDay() != null) {
			holiday.setDay(holidayDto.getDay());
		}
		if (holidayDto.getMonth() != null) {
			holiday.setMonth(holidayDto.getMonth());
		}
		if (holidayDto.getDate() != null) {
			holiday.setDate(holidayDto.getDate());
		}
		return holidayRepository.save(holiday);
	}

	@Override
	public void deleteHoliday(Long holidayId) {
		holidayRepository.delete(holidayId);

	}

}
