
package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.OpportunityNoteDto;
import com.intellitech.birdnotes.model.request.ActionMarquetingRequest;
import com.intellitech.birdnotes.service.OpportunityNoteService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/opportunityNote")
public class OpportunityNoteController {
	private static final Logger LOG = LoggerFactory.getLogger(OpportunityNoteController.class);

	@Autowired
	private OpportunityNoteService opportunityNoteService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "findAllOpportunityNote", method = RequestMethod.GET)
	public ResponseEntity<List<OpportunityNoteDto>> findAllOpportunityNotes() {

		try {
			List<OpportunityNoteDto> opportunityNoteDto = opportunityNoteService.findAll();
			return new ResponseEntity<>(opportunityNoteDto, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all opportunities notes", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteOpportunityNote(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("OPPORTUNITY_NOTE_DELETE")) {
	            opportunityNoteService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting opportunity note", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the opportunity note with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}



	@RequestMapping(value = "/update", method = RequestMethod.PUT)
	public ResponseEntity<String> updateOpportunityNote(@RequestBody OpportunityNoteDto opportunityNoteDto) {

		try {
			opportunityNoteService.update(opportunityNoteDto);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

		} catch (BirdnotesException e) {
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while updating note d'opportunité ", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "changeStatus", method = RequestMethod.POST)
	public ResponseEntity<String> changeStatus(@RequestBody ActionMarquetingRequest opportunityNoteStatusRequest) {
		try {
			opportunityNoteService.changeStatus(opportunityNoteStatusRequest);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while status doesn't change", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
