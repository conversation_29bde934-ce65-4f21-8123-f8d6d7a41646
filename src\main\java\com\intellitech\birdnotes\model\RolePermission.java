package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.ROLE_PERMISSIONS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class RolePermission implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.ROLE_SEQUENCE, sequenceName = Sequences.ROLE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.ROLE_SEQUENCE)
	@Column(name = Columns.ID)
	private Integer id;

	public void setId(Integer id) {
		this.id = id;
	}

	public Integer getId() {
		return id;
	}

	@Column(name = BirdnotesConstants.Columns.PERMISSION_NAME, length = BirdnotesConstants.Numbers.N_85)
	private String permissionName;

	public String getPermissionName() {
		return permissionName;
	}

	public void setPermissionName(String permissionName) {
		this.permissionName = permissionName;
	}

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.ROLE_ID)
	private Role role;

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public RolePermission(String permissionName, Role role) {
		super();
		this.permissionName = permissionName;
		this.role = role;
	}

	public RolePermission() {
		super();
	}

}
