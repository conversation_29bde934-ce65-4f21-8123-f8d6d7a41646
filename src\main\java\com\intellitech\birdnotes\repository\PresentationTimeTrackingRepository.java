package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import com.intellitech.birdnotes.model.PresentationTimeTracking;

public interface PresentationTimeTrackingRepository extends JpaRepository<PresentationTimeTracking, Long> {
	
	@Query("SELECT p FROM PresentationTimeTracking p WHERE p.identifier = ?1 And p.visitProduct.visit.id = ?2")
	PresentationTimeTracking findByIdentifier(Long identifier, Long visitId);
	
	@Query("SELECT p FROM PresentationTimeTracking p WHERE p.visitProduct.id = ?1 And p.visitProduct.product.id = ?2")
	List<PresentationTimeTracking> findByProductAndVisitProduct(Long visitProductId, Long productId);
}
