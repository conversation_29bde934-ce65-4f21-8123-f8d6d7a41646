package com.intellitech.birdnotes.model.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.intellitech.birdnotes.data.dto.ProductRevenueByMonthDto;
import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.convertor.ConvertGiftToDto;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;

public class DelegateCommissionDto {
	private Long id;

	private String name;
	
	private Date firstDate;
	
	private Date lastDate;

	private List<Long> usersId;
	private List<Long> productsId;
	
	private List<UserDto> users;
	private List<ProductDto> products;
	
	private Long selectedUserId;
	private Long selectedProductId;
	
	private String type;
	
	private String month;
	
	private Float productRevenue;
	
	private Float delegateCommission;
	
	private List<ProductRevenueByMonthDto> productRevenueByMonth;
	 
	private String status;
	
	
	private List <ValidationStatusDto> validationStatusDto;
	
	public List<ValidationStatusDto> getValidationStatusDto() {
		return validationStatusDto;
	}

	public void setValidationStatusDto(List<ValidationStatusDto> validationStatusDto) {
		this.validationStatusDto = validationStatusDto;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	
	
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}






	public List<ProductRevenueByMonthDto> getProductRevenueByMonth() {
		return productRevenueByMonth;
	}

	public void setProductRevenueByMonth(List<ProductRevenueByMonthDto> productRevenueByMonth) {
		this.productRevenueByMonth = productRevenueByMonth;
	}






	public String getMonth() {
		return month;
	}

	public void setMonth(String month) {
		this.month = month;
	}

	public Float getProductRevenue() {
		return productRevenue;
	}

	public void setProductRevenue(Float productRevenue) {
		this.productRevenue = productRevenue;
	}






	private List<CommissionItemDto> commissionItem;

	
	public List<CommissionItemDto> getCommissionItem() {
		return commissionItem;
	}

	public void setCommissionItem(List<CommissionItemDto> commissionItem) {
		this.commissionItem = commissionItem;
	}

	public DelegateCommissionDto(CommissionDto commission) throws BirdnotesException {
		
			this.products = new ArrayList<>();
			this.users = new ArrayList<>();
			this.id = commission.getId();
			this.firstDate = commission.getFirstDate();
			this.lastDate = commission.getLastDate();
			this.name = commission.getName();
			this.products = commission.getProducts();
			this.users = commission.getUsers();
			this.delegateCommission = commission.getDelegateCommission();
			this.productRevenueByMonth = commission.getProductRevenueByMonth();
			this.commissionItem = commission.getCommissionItem();
			this.productsId = commission.getProductsId();
			this.usersId = commission.getUsersId();
			this.selectedUserId = commission.getSelectedUserId();
			this.type = commission.getType();
		
	}

	public DelegateCommissionDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getFirstDate() {
		return firstDate;
	}

	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}

	public Date getLastDate() {
		return lastDate;
	}

	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}

	public List<Long> getUsersId() {
		return usersId;
	}

	public void setUsersId(List<Long> usersId) {
		this.usersId = usersId;
	}

	public List<UserDto> getUsers() {
		return users;
	}

	public void setUsers(List<UserDto> users) {
		this.users = users;
	}


	public Long getSelectedUserId() {
		return selectedUserId;
	}

	public void setSelectedUserId(Long selectedUserId) {
		this.selectedUserId = selectedUserId;
	}

	public List<Long> getProductsId() {
		return productsId;
	}

	public void setProductsId(List<Long> productsId) {
		this.productsId = productsId;
	}

	public List<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(List<ProductDto> products) {
		this.products = products;
	}

	public Long getSelectedProductId() {
		return selectedProductId;
	}

	public void setSelectedProductId(Long selectedProductId) {
		this.selectedProductId = selectedProductId;
	}


	

	public Float getDelegateCommission() {
		return delegateCommission;
	}

	public void setDelegateCommission(Float delegateCommission) {
		this.delegateCommission = delegateCommission;
	}

	

	


	}
