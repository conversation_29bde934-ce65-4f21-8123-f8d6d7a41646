package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.VISIT, schema = Common.PUBLIC_SCHEMA)
public class Visit implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Long identifier;
	private Date visitDate;
	private Date synchronisationDate;
	private Prospect prospect;
	private Delegate delegate;
	
	private String generalNote;
	private Integer patientNumber;
	private Gift gadget;
	private Integer gadgetQuantity;
	private Location location;
	private Visit doubleVisit;


	public Visit(Long id, Date visitDate, Prospect prospect, Delegate delegate, String generalNote) {
		super();
		this.id = id;
		this.visitDate = visitDate;
		this.prospect = prospect;
		this.delegate = delegate;
		this.generalNote = generalNote;
		this.synchronisationDate = new Date();
	}

	public Visit() {
		super();
		this.synchronisationDate = new Date();
	}

	@Id
	@SequenceGenerator(name = Sequences.VISIT_SEQUENCE, sequenceName = Sequences.VISIT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.VISIT_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@JsonBackReference
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID)
	public Prospect getProspect() {
		return prospect;
	}

	@JsonProperty
	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

	@JsonIgnore
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}
	
	@JsonIgnore
	@OneToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.DOUBLE_VISIT_ID)
	public Visit getDoubleVisit() {
		return doubleVisit;
	}

	public void setDoubleVisit(Visit doubleVisit) {
		this.doubleVisit = doubleVisit;
	}


	@Temporal(TemporalType.DATE)
	@Column(name = Columns.VISIT_DATE)
	public Date getVisitDate() {
		return visitDate;
	}


	public void setVisitDate(Date visitDate) {
		this.visitDate = visitDate;
	}

	@Column(name = Columns.GENERAL_NOTE, length = BirdnotesConstants.Numbers.N_1024)
	public String getGeneralNote() {
		return generalNote;
	}

	@Column(name = Columns.PATIENT_NUMBER)
	public Integer getPatientNumber() {
		return patientNumber;
	}

	public void setGeneralNote(String generalNote) {
		this.generalNote = generalNote;
	}

	public Date getSynchronisationDate() {
		return synchronisationDate;
	}

	public void setSynchronisationDate(Date synchronisationDate) {
		this.synchronisationDate = synchronisationDate;
	}

	public void setPatientNumber(Integer patientNumber) {

		this.patientNumber = patientNumber;
	}
	
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.GIFT_ID)
	public Gift getGadget() {
		return gadget;
	}

	public void setGadget(Gift gadget) {
		this.gadget = gadget;
	}
	@Column(name = Columns.GADGET_QUANTITY)
	public Integer getGadgetQuantity() {
		return gadgetQuantity;
	}

	public void setGadgetQuantity(Integer gadgetQuantity) {
		this.gadgetQuantity = gadgetQuantity;
	}

	@Column(name = Columns.IDENTIFIER)
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}
	
	@OneToOne(optional = true)
	@JoinColumn(name = Columns.POSITION_ID)
		public Location getLocation() {
		return location;
	}

	public void setLocation(Location location) {
		this.location = location;
	}


	

}