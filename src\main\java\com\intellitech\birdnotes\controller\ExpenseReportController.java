package com.intellitech.birdnotes.controller;

import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.ExpenseReportService;
import com.intellitech.birdnotes.service.ExpenseTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/expenseReport")
public class ExpenseReportController {
	private static final Logger LOG = LoggerFactory.getLogger(ExpenseReportController.class);

	@Autowired
	private ExpenseReportService noteFraisService;
	@Autowired
	UserService userService;
	@Autowired
	DelegateService delegateService;
	@Autowired
	private ValidationStepService validationStepService;
	@Autowired
	private ExpenseTypeService typeNoteFraisService;
	@Autowired
	private DownloadDataService downloadDataService;

	@RequestMapping(value = "/acceptValidationStep/{id}", method = RequestMethod.PUT)
	public ResponseEntity<String> acceptValidationStep(@PathVariable("id") long id) {

		try {
			if (userService.checkHasPermission("EXPENSE_VALIDATION")) {
				noteFraisService.acceptValidationStep(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while updating validation Status", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "saveExpenseReport", method = RequestMethod.POST)
	public ResponseEntity<Long> saveExpenseReport(@RequestBody ExpenseReportDto expenseReportDto) {

		try {
			if (userService.checkHasPermission("TYPE_EXPENSE_EDIT")) {
				ExpenseReport savedReport = noteFraisService.saveExpenseReport(expenseReportDto);
				if (savedReport != null) {
					return new ResponseEntity<>(savedReport.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving expense Report", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/refuseValidationStep/{id}", method = RequestMethod.PUT)
	public ResponseEntity<String> refuseValidationStep(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("EXPENSE_VALIDATION")) {
				noteFraisService.refuseValidationStep(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while updating validation Status", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getExpenseReportByDate/{startDate}/{endDate}/{userId}", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getDataDyDateForNoteFrais(@PathVariable("startDate") Date startDate,
			@PathVariable("endDate") Date endDate, @PathVariable("userId") Long userId) {
		try {
			if (userService.checkHasPermission("EXPENSE_VIEW")) {
				return new ResponseEntity<>(downloadDataService.getNoteFraisDyDate(startDate, endDate, userId),
						HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the note frais lists", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getExpenseReportById/{id}", method = RequestMethod.GET)
	public ResponseEntity<ExpenseReportDto> findNoteFraisById(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("EXPENSE_VIEW")) {

				ExpenseReportDto noteFraisDto = noteFraisService.findNoteFraisById(id);
				return new ResponseEntity<>(noteFraisDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting note frais", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addNoteFrais(@RequestBody ExpenseReportDto noteFraisDto) {
		try {
			if (userService.checkHasPermission("EXPENSE_ADD")) {
				noteFraisService.add(noteFraisDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add note frais", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new note de frais", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteNotefrais(@PathVariable("id") Long idNoteFrais) {
		try {
			if (userService.checkHasPermission("EXPENSE_DELETE")) {
				noteFraisService.delete(idNoteFrais);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("An exception occurred while deleting the noteFrais with id =" + idNoteFrais, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "updateNoteFrais", method = RequestMethod.PUT)
	public ResponseEntity<String> updateNoteFrais(@RequestBody ExpenseReportDto noteFraisDto) {

		try {
			if (userService.checkHasPermission("EXPENSE_EDIT")) {
				noteFraisService.updateNoteFrais(noteFraisDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to update note frais", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while updating note de frais", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

	@RequestMapping(value = "findAllExpenseType", method = RequestMethod.GET)
	public ResponseEntity<List<ExpenseTypeDto>> findAllTypeNoteFrais() {
		try {
			if (userService.checkHasPermission("TYPE_EXPENSE_VIEW")) {
				List<ExpenseTypeDto> expenseTypeDto = typeNoteFraisService.findAll();
				return new ResponseEntity<>(expenseTypeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all type note frais", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("EXPENSE_VIEW")) {
				List<DelegateDto> userDtos = delegateService.findAllDelegates();

				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "generateExpensePDF/{startDate}/{endDate}/{userId}", method = RequestMethod.GET)
	public ResponseEntity<InputStreamResource> generateExpensePDF(@PathVariable("startDate") Date startDate,
			@PathVariable("endDate") Date endDate, @PathVariable("userId") Long userId, HttpServletResponse response) {

		try {
			String filePath = noteFraisService.generateExpensePDF(startDate, endDate, userId);
			response.setHeader("Content-Disposition", "attachment;" + filePath);
			response.setHeader("filename", "generatedExpenseReport"+((new Date()).getTime()));
			response.setHeader("Access-Control-Expose-Headers", "filename");
			InputStream inputStream = new FileInputStream(filePath + "/generatedExpenseReport.pdf");
			InputStreamResource inputStreamResource = new InputStreamResource(inputStream, "generatedExpenseReport");
			return ResponseEntity.ok().contentType(MediaType.APPLICATION_PDF).body(inputStreamResource);

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to generate pdf", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while generating pdf", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}

}
