package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.Locale;



import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.InvestigationDto;
import com.intellitech.birdnotes.enumeration.Affectation;
import com.intellitech.birdnotes.enumeration.GroupType;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.ContactType;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.PotentielProduit;
import com.intellitech.birdnotes.model.Preference;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.ProspectsAffectation;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToProspect;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ContactTypeDto;
import com.intellitech.birdnotes.model.dto.DuplicateProspectDto;
import com.intellitech.birdnotes.model.dto.ExportProspects;
import com.intellitech.birdnotes.model.dto.InterestDto;
import com.intellitech.birdnotes.model.dto.KeyValueDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.PatientDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectChangedDto;
import com.intellitech.birdnotes.model.dto.ProspectDistribution;
import com.intellitech.birdnotes.model.dto.ProspectDistributionList;
import com.intellitech.birdnotes.model.dto.ProspectDistributionRequest;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectsToMoveRequestDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.model.request.ProspectListRequest;
import com.intellitech.birdnotes.repository.ActionMarketingRepository;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.ContactTypeRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.EstablishmentRepository;
import com.intellitech.birdnotes.repository.InterestRepository;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.MessageTagRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProductsPotentialRepository;
import com.intellitech.birdnotes.repository.ProspectOrderPredictionRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.ProspectsAffectationRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ContactTypeService;
import com.intellitech.birdnotes.service.InterestService;
import com.intellitech.birdnotes.service.LocalityService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.PlanningService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.PreferenceService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.ProspectsAffectationService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.thread.ThreadSendEmail;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

@Service("prospectService")

@Transactional

public class ProspectServiceImpl implements ProspectService {
	private Marker logSyncMarker = MarkerFactory.getMarker("SYNC");

	private static final Logger LOG = LoggerFactory.getLogger(ProspectServiceImpl.class);

	private ProspectsAffectationRepository prospectsAffectationRepository;

	private RangeRepository gammeRepository;

	private ProspectRepository prospectRepository;

	private VisitRepository visitRepository;

	private VisitsProductsRepository visitsProductsRepository;

	private MessageTagRepository messageTagRepository;

	private SectorRepository sectorRepository;

	private LocalityRepository localityRepository;

	private SpecialityRepository specialityRepository;

	private ProspectTypeRepository prospectTypeRepository;
	
	private InterestRepository interestRepository;
	
	private ContactTypeRepository contactTypeRepository;


	private EstablishmentRepository establishmentRepository;

	private PurchaseOrderRepository purchaseOrderRepository;

	private ConvertProspectToDto convertProspectToDto;

	private ConvertDtoToProspect convertDtoToProspect;

	private UserRepository userRepository;

	private DelegateRepository delegateRepository;

	private ProspectsAffectationService prospectsAffectationService;

	private ActionMarketingRepository actionMarketingRepository;

	private CurrentUser currentUser;

	private UserService userService;

	private JavaMailSender javaMailSender;

	private DateFormat df = new SimpleDateFormat("yyyy-MM-dd");

	private PlanningRepository planningRepository;

	private ConfigurationService configurationService;

	private ProductsPotentialRepository productsPotentialRepository;

	private PotentialRepository potentialRepository;

	private ProductRepository productRepository;

	private NotificationService notificationService;

	private NotificationMessageBuilder notificationMessageBuilder;

	private PotentialService potentialService;

	private PlanningService planningService;

	private SpecialityService specialityService;

	private LocalityService localityService;

	private SectorService sectorService;

	private DynamicQueries dynamicQueries;

	private ValidationStatusRepository validationStatusRepository;

	private ValidationStepService validationStepService;

	private UserToDtoConvertor userToDtoConvertor;

	private ConfigurationRepository configureRepository;
	
	private ProspectOrderPredictionRepository prospectOrderPredictionRepository;

	@Value("${uploadUrl}")

	private String uploadUrl;

	@Value("${specialityPath}")

	private String specialityPath;

	@Value("${sendInvestigationSubject}")

	private String sendInvestigationSubject;

	@Value("${sendInvestigationHtmlBody}")

	private String sendInvestigationHtmlBody;

	@Autowired
	public void setValidationStepService(ValidationStepService validationStepService) {
		this.validationStepService = validationStepService;
	}
	
	@Autowired
	private MessageSource messageSource;
	
    @Autowired
    private InterestService interestService;
    
    @Autowired
    private PreferenceService preferenceService;
    
    
    @Autowired
    private ContactTypeService contactTypeService;

	@Autowired

	public void setDynamicQueries(DynamicQueries dynamicQueries) {

		this.dynamicQueries = dynamicQueries;

	}

	@Autowired
	public void setUserService(UserService userService) {
		this.userService = userService;
	}

	@Autowired
	public void setPlanningService(PlanningService planningService) {

		this.planningService = planningService;

	}

	@Autowired

	public void setGammeRepository(RangeRepository gammeRepository) {

		this.gammeRepository = gammeRepository;

	}

	@Autowired

	public void setProductsPotentialRepository(ProductsPotentialRepository productsPotentialRepository) {

		this.productsPotentialRepository = productsPotentialRepository;

	}

	@Autowired

	public void setPotentialRepository(PotentialRepository potentialRepository) {

		this.potentialRepository = potentialRepository;

	}

	@Autowired

	public void setProductRepository(ProductRepository productRepository) {

		this.productRepository = productRepository;

	}

	@Autowired

	public void setNotificationService(NotificationService notificationService) {

		this.notificationService = notificationService;

	}

	@Autowired

	public void setProspectsAffectationService(ProspectsAffectationService prospectsAffectationService) {

		this.prospectsAffectationService = prospectsAffectationService;

	}

	@Autowired

	public void setNotificationMessageBuilder(NotificationMessageBuilder notificationMessageBuilder) {

		this.notificationMessageBuilder = notificationMessageBuilder;

	}

	@Autowired

	public void setActionMarketingRepository(ActionMarketingRepository actionMarketingRepository) {

		this.actionMarketingRepository = actionMarketingRepository;

	}

	@Autowired

	public void setProspectRepository(ProspectRepository prospectRepository) {

		this.prospectRepository = prospectRepository;

	}

	@Autowired

	public void setProspectsAffectationRepository(ProspectsAffectationRepository prospectsAffectationRepository) {

		this.prospectsAffectationRepository = prospectsAffectationRepository;

	}

	@Autowired

	public void setVisitRepository(VisitRepository visitRepository) {

		this.visitRepository = visitRepository;

	}

	@Autowired

	public void setSectorRepository(SectorRepository sectorRepository) {

		this.sectorRepository = sectorRepository;

	}

	@Autowired

	public void setLocalityRepository(LocalityRepository localityRepository) {

		this.localityRepository = localityRepository;

	}

	@Autowired

	public void setSpecialityRepository(SpecialityRepository specialityRepository) {

		this.specialityRepository = specialityRepository;

	}

	@Autowired

	public void setProspectTypeRepository(ProspectTypeRepository prospectTypeRepository) {

		this.prospectTypeRepository = prospectTypeRepository;

	}
	
	@Autowired

	public void setInterestRepository(InterestRepository interestRepository) {

		this.interestRepository = interestRepository;

	}
	
	@Autowired

	public void setContactypeRepository(ContactTypeRepository contactTypeRepository) {

		this.contactTypeRepository = contactTypeRepository;

	}

	@Autowired

	public void setEstablishmentRepository(EstablishmentRepository establishmentRepository) {

		this.establishmentRepository = establishmentRepository;

	}

	@Autowired

	public void setPurchaseOrderRepository(PurchaseOrderRepository purchaseOrderRepository) {

		this.purchaseOrderRepository = purchaseOrderRepository;

	}

	@Autowired

	public void setConvertProspectToDto(ConvertProspectToDto convertProspectToDto) {

		this.convertProspectToDto = convertProspectToDto;

	}

	@Autowired

	public void setConvertDtoToProspect(ConvertDtoToProspect convertDtoToProspect) {

		this.convertDtoToProspect = convertDtoToProspect;

	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {

		this.userRepository = userRepository;

	}

	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}

	@Autowired

	public void setCurrentUser(CurrentUser currentUser) {

		this.currentUser = currentUser;

	}

	@Autowired

	public void setJavaMailSender(JavaMailSender javaMailSender) {

		this.javaMailSender = javaMailSender;

	}

	@Autowired

	public void setConfigurationService(ConfigurationService configurationService) {

		this.configurationService = configurationService;

	}

	@Autowired

	public void setVisitsProductsRepository(VisitsProductsRepository visitsProductsRepository) {

		this.visitsProductsRepository = visitsProductsRepository;

	}

	@Autowired
	public void setMessageTagRepository(MessageTagRepository messageTagRepository) {
		this.messageTagRepository = messageTagRepository;
	}

	@Autowired

	public void setValidationStatusRepository(ValidationStatusRepository validationStatusRepository) {

		this.validationStatusRepository = validationStatusRepository;

	}

	@Autowired

	public void setPotentialService(PotentialService potentialService) {

		this.potentialService = potentialService;

	}

	@Autowired

	public void setSpecialityService(SpecialityService specialityService) {

		this.specialityService = specialityService;

	}

	@Autowired

	public void setLocalityService(LocalityService localityService) {

		this.localityService = localityService;

	}

	@Autowired

	public void setSectorService(SectorService sectorService) {

		this.sectorService = sectorService;

	}

	@Autowired

	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {

		this.userToDtoConvertor = userToDtoConvertor;

	}

	@Autowired

	public void setPlanningRepository(PlanningRepository planningRepository) {

		this.planningRepository = planningRepository;

	}

	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}
	
	@Autowired
	public void setProspectOrderPredictionRepository(ProspectOrderPredictionRepository prospectOrderPredictionRepository) {
		this.prospectOrderPredictionRepository = prospectOrderPredictionRepository;
	}


	public void setSendInvestigationSubject(String sendInvestigationSubject) {

		this.sendInvestigationSubject = sendInvestigationSubject;

	}

	public void setSendInvestigationHtmlBody(String sendInvestigationHtmlBody) {

		this.sendInvestigationHtmlBody = sendInvestigationHtmlBody;

	}

	private Marker logProspectMarker = MarkerFactory.getMarker("PROSPECT");
	

	@Override
	public Map<String, Object> getProspectUpdateAndNewRequests() throws BirdnotesException {
		HashMap<String, Object> prospectData = new HashMap<>();
		List<ProspectDto> prospectChangedDtos;
		prospectChangedDtos = findProspectWithStatus();
		List<ProspectDto> existingProspects;

		for (ProspectDto prospectDto : prospectChangedDtos) {
			if (prospectDto.getStatus().equals(UserValidationStatus.NEW)) {
				existingProspects = findSimilarProspects(prospectDto);
				if (existingProspects.size() == 0) {
					acceptAddingNewProspectRequest(prospectDto);
				}
			} else if (prospectDto.getStatus().equals(UserValidationStatus.UPDATE)) {
				acceptProspectUpdateRequest(prospectDto);
			}

		}

		return prospectData;
	}

	@Override

	public List<ProspectDto> findAll(ProspectListRequest prospectListRequest) throws BirdnotesException {

		Pageable pageable = new PageRequest(prospectListRequest.getFirst() / prospectListRequest.getRows(),
				prospectListRequest.getRows());

		List<ProspectDto> back = new ArrayList<>();

		List<Prospect> listOfProspects = prospectRepository
				.findAllWithPagination(prospectListRequest.getGlobalFilter().toUpperCase(), pageable);

		if (listOfProspects != null && !listOfProspects.isEmpty()) {

			for (Prospect prospect : listOfProspects) {

				back.add(convertProspectToDto.convert(prospect));

			}

		}

		return back;

	}

	@Override

	public void addAllImportedProspects(List<ProspectDto> prospectDtos) throws BirdnotesException {

		for (ProspectDto prospectDto : prospectDtos) {

			addImportedProspect(prospectDto);

		}

	}

	@Override

	public void addImportedProspect(ProspectDto prospectDto) throws BirdnotesException {

		Prospect prospect = convertDtoToProspect.convert(prospectDto, null);

		prospect.setCreationDate(new Date());
		prospect.setStatus(UserValidationStatus.VALID);

		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();

		// Delegate delegate =
		// delegateRepository.findDelegateByUserId(birdnotesUser.getUserDto().getId());

		User user = userRepository.findById(birdnotesUser.getUserDto().getId());

		prospect.setUser(user);

		prospect.setIdentifier(new Date().getTime());

		Prospect result = prospectRepository.save(prospect);

		if (prospectDto.getDelegateIds() != null && !prospectDto.getDelegateIds().isEmpty()) {

			prospectsAffectationService.saveAffectation(prospectDto.getDelegateIds(), result);

		}

	}

	@Override

	public Prospect add(ProspectDto prospectDto) throws BirdnotesException {



			Prospect prospect = null;
			if (prospectDto.getId() != null) {
				prospect = prospectRepository.findOne(prospectDto.getId());
			}

			if (prospect == null) {
				Prospect result = prospectRepository.findByNameWithDiffId(prospectDto.getFirstName(),

						prospectDto.getLastName(), prospectDto.getId());

				if (result != null) {

					throw new BirdnotesException(Exceptions.ALREADY_EXIST);

				} else {
				      prospect = new Prospect();
			      }
				}

			validateAndConvertProspect(prospectDto, prospect);

			// Prospect prospect = convertDtoToProspect.convert(prospectDto);

			// validateProspect(prospect, prospectDto);

			BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();

			/*
			 * if (birdnotesUser == null || birdnotesUser.getUserDto() == null
			 * 
			 * || birdnotesUser.getUserDto().getId() == null) {
			 * 
			 * throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
			 * 
			 * }
			 */
			// Delegate delegate =
			// delegateRepository.findDelegateByUserId(birdnotesUser.getUserDto().getId());
			User user = userRepository.findById(birdnotesUser.getUserDto().getId());
			prospect.setUser(user);
			prospect.setIdentifier(new Date().getTime());
			prospect.setCreationDate(new Date());
			prospect.setStatus(UserValidationStatus.VALID);
			prospect.setCode(prospectDto.getCode());
			if (prospectDto.isUser() == true) {
				UserDto userDto = new UserDto();
				User appUser = prospectRepository.findUserByProspectAppUserId(prospectDto.getAppUserId());
				userDto.setId(0L);
				if (appUser != null) {
					userDto.setId(appUser.getId());
				}
				userDto.setPhone(prospectDto.getPhone());
				userDto.setEmail(prospectDto.getEmail());
				userDto.setUsername(prospectDto.getUserName());
				userDto.setPassword(prospectDto.getPassword());
				userDto.setRoleIds(prospectDto.getRoleIds());
				userDto.setSuperiors(prospectDto.getSupervisorIds());
				userDto.setRangeIds(prospectDto.getRangeIds());
				appUser = userService.saveUser(userDto);
				prospect.setAppUser(appUser);
				// merge from change prospect
			}
			prospectRepository.save(prospect);

			/********* Save potential product ********/
			// !!!!!!!!comment savoir la correspondence entre potentiel et produit ??
			if (prospectDto.getPotentialIds() != null && prospectDto.getProductIds() != null) {

				for (Long potentialId : prospectDto.getPotentialIds()) {

					if (potentialId != null) {

						Potential potential = potentialRepository.findOne(potentialId);

						PotentielProduit potentielProduit = new PotentielProduit();

						for (Long productId : prospectDto.getProductIds()) {

							Product product = productRepository.findOne(productId);

							potentielProduit.setProduct(product);

							potentielProduit.setPotential(potential);

							potentielProduit.setProspect(prospect);

							productsPotentialRepository.save(potentielProduit);
						}

					}

				}

			}
			return prospect;
		

	}

	@Override
	public List<WholesalerDto> getWholesalersByStatus(Boolean forMobile) {
		List<WholesalerDto> wholesalerDtos = new ArrayList<>();
		List<Prospect> prospects = prospectRepository.getWholesalersByStatus();
		int i = 0;
		for (Prospect prospect : prospects) {
			WholesalerDto wholesalerDto = new WholesalerDto();
			if(prospect.getIdentifier() == null) {				
				prospect.setIdentifier(new Date().getTime()+i);
				i++;
				prospectRepository.save(prospect);
			}
			if(forMobile == true) {
				
				wholesalerDto.setId(prospect.getIdentifier());
			}else {
				wholesalerDto.setId(prospect.getId());
			}			
			wholesalerDto.setName(prospect.getFirstName() + " " + prospect.getLastName());
			wholesalerDto.setAddress(prospect.getAddress());
			wholesalerDto.setEmail(prospect.getEmail());
			wholesalerDto.setSectorId(prospect.getSector().getId());
			wholesalerDtos.add(wholesalerDto);
		}
		return wholesalerDtos;
	}

	@Override
	public Prospect getProspect(Long prospectId) {
		LOG.info(" getProspect function, prospect identifier : " + prospectId);
		Prospect prospect = prospectRepository.findByIdentifierAndStatus(prospectId);
		if (prospect == null) {
			LOG.error(logSyncMarker, "Null error from getProspect function, prospect identifier = " + prospectId);
		} else {
			if (prospect.getStatus().equals(UserValidationStatus.REFUSED)) {
				prospect = null;
			} else if (prospect.getStatus().equals(UserValidationStatus.MERGED)) {
				prospect = prospectRepository.findByIdentifier(prospect.getIdprospect());
			}
		}

		return prospect;

	}

	@SuppressWarnings("unchecked")

	public List<ProspectDto> findSimilarProspects(ProspectDto prospectDto) throws BirdnotesException {

		StringBuilder prospectQuery;

		List<ProspectDto> similarProspectDtos = new ArrayList<>();

		Map<String, Object> prospectParameters;

		prospectQuery = (StringBuilder) prospectQueryBuild(prospectDto).get(BirdnotesConstants.VisitHistory.QUERY);

		prospectParameters = (Map<String, Object>) prospectQueryBuild(prospectDto)

				.get(BirdnotesConstants.VisitHistory.PARAMETERS);

		List<Prospect> similarProspects = dynamicQueries.findProspects(prospectQuery.toString(), prospectParameters);

		if (similarProspects != null && !similarProspects.isEmpty()) {

			for (Prospect prospect : similarProspects) {

				similarProspectDtos.add(convertProspectToDto.convert(prospect));

			}

		}

		return similarProspectDtos;

	}

	private Map<String, Object> prospectQueryBuild(ProspectDto prospectDto) {

		StringBuilder prospectQuery = new StringBuilder();

		Map<String, Object> prospectParameters = new HashMap<>();

		Map<String, Object> prospectQueryParameters = new HashMap<>();

		prospectQuery.append("SELECT p from Prospect p where p.status= 'VALID' ");

		findProspectByFirstName(prospectDto, prospectQuery, prospectParameters);

		findProspectByLastName(prospectDto, prospectQuery, prospectParameters);

		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.QUERY, prospectQuery);

		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.PARAMETERS, prospectParameters);

		return prospectQueryParameters;

	}

	public void findProspectByFirstName(ProspectDto prospectDto, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDto.getFirstName() != null && !prospectDto.getFirstName().isEmpty()) {

			String firstName = '%' + prospectDto.getFirstName().toLowerCase().replaceAll("[aeoiyu]", "_") + '%';

			prospectQuery.append(
					" and (upper(p.firstName) like upper(:firstName) OR upper(p.lastName) like upper(:firstName)) ");

			prospectParameters.put("firstName", firstName);

		}

	}

	public void findProspectByLastName(ProspectDto prospectDto, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDto.getLastName() != null && !prospectDto.getLastName().isEmpty()) {

			String lastName = '%' + prospectDto.getLastName().toLowerCase().replaceAll("[aeoiyu]", "_") + '%';

			prospectQuery.append(
					" and (upper(p.lastName) like upper(:lastName) OR upper(p.firstName) like upper(:lastName) )");

			prospectParameters.put("lastName", lastName);

		}

	}

	/*
	 * @Override
	 * 
	 * public List<Prospect> saveAll(List<ProspectDto> prospectDtos) throws
	 * BirdnotesException {
	 * 
	 * List<Prospect> prospectsSaved = new ArrayList<>();
	 * 
	 * if (prospectDtos != null && !prospectDtos.isEmpty()) {
	 * 
	 * for (ProspectDto prospectDto : prospectDtos) {
	 * 
	 * prospectsSaved.add(saveProspectByAdmin(prospectDto));
	 * 
	 * }
	 * 
	 * }
	 * 
	 * return prospectsSaved;
	 * 
	 * }
	 */

	@Override

	public void mergeProspect(ProspectDto newProspectDto, ProspectDto prospectToMergeDto) throws BirdnotesException {

		LOG.info(logProspectMarker, "Start merging prospect, id new prospect = " + newProspectDto.getId()
				+ " id prospect to merge = " + prospectToMergeDto.getId());

		mergeProspectForImport(prospectToMergeDto, newProspectDto);

		Prospect oldProspect = prospectRepository.findOne(prospectToMergeDto.getId());

		Prospect newProspect = prospectRepository.findOne(newProspectDto.getId());

		newProspect.setStatus(UserValidationStatus.MERGED);

		newProspect.setIdprospect(oldProspect.getIdentifier());

		prospectRepository.save(newProspect);

		mergeVisits(oldProspect, newProspect);

		mergeMarketingActions(oldProspect, newProspect);

		mergePlannings(oldProspect, newProspect);

		mergeAffectations(oldProspect, newProspect);
		
		mergePurchaseOrder(oldProspect, newProspect);

		// prospectRepository.delete(newProspectDto.getId());

		LOG.info(logProspectMarker, "End merging prospect");

	}

	public void mergeVisits(Prospect oldProspect, Prospect newProspect) {

		if (newProspect != null && newProspect.getVisits() != null && newProspect.getVisits().size() > 0) {

			for (Visit visit : newProspect.getVisits()) {

				visit.setProspect(oldProspect);

				List<VisitsProducts> visitsProducts = visitsProductsRepository.findAllByVisitId(visit.getId());

				if (visitsProducts.size() > 0) {

					/*
					 * for (VisitsProducts visitProduct : visitsProducts) {
					 * 
					 * if (visitProduct.getPurchaseOrder() != null) {
					 * 
					 * // visitProduct.getPurchaseOrder().setProspect(prospectToUpdate);
					 * 
					 * }
					 * 
					 * }
					 */

					visitsProductsRepository.save(visitsProducts);

				}

			}

			oldProspect.setVisits(newProspect.getVisits());

			newProspect.setVisits(null);

		}

	}

	public void mergePlannings(Prospect oldProspect, Prospect newProspect) {

		planningRepository.updateProspect(oldProspect, newProspect);

	}
	
	public void mergePurchaseOrder(Prospect oldProspect, Prospect newProspect) {

		purchaseOrderRepository.updateProspect(oldProspect, newProspect);

	}

	public void mergeMarketingActions(Prospect oldProspect, Prospect newProspect) {

		List<ActionMarketing> marketingAction = actionMarketingRepository.findByProspects_Id(newProspect.getId());

		for (ActionMarketing actionMarketing : marketingAction) {

			Set<Prospect> prospects = actionMarketing.getProspects().stream()

					.filter(item -> !item.getId().equals(newProspect.getId())).collect(Collectors.toSet());

			prospects.add(oldProspect);

			actionMarketing.setProspects(prospects);

			actionMarketingRepository.save(actionMarketing);

		}
	}

	public void mergeAffectations(Prospect oldProspect, Prospect newProspect) {

		List<ProspectsAffectation> propectAffectations = prospectsAffectationRepository.findByProspect(newProspect);

		if (propectAffectations != null && propectAffectations.size() > 0) {

			for (ProspectsAffectation pa : propectAffectations) {
				ProspectsAffectation prospectsAffectation = prospectsAffectationRepository
						.findByDelegateAndProspect(pa.getDelegate().getUser().getId(), oldProspect.getId());
				if (prospectsAffectation == null) {
					pa.setProspect(oldProspect);
					prospectsAffectationRepository.save(pa);
				}
			}

		} else {
			ProspectsAffectation prospectsAffectation = prospectsAffectationRepository
					.findByDelegateAndProspect(newProspect.getUser().getId(), oldProspect.getId());
			if (prospectsAffectation == null) {
				ProspectsAffectation pa = new ProspectsAffectation();
				pa.setDelegate(newProspect.getUser().getDelegate());
				pa.setProspect(oldProspect);
				prospectsAffectationRepository.save(pa);
			}
		}
	}

	@Override
	public void mergeProspectForImport(ProspectDto prospectToMergeDto, ProspectDto newProspectDto)
			throws BirdnotesException {

		Prospect existingProspectToUpdate = prospectRepository.findById(prospectToMergeDto.getId());

		Prospect prospectToSave = validateAndConvertProspect(prospectToMergeDto, existingProspectToUpdate);

		prospectRepository.save(prospectToSave);

	}

	/*
	 * private Prospect saveProspectByAdmin(ProspectDto prospectDto) throws
	 * BirdnotesException {
	 * 
	 * Prospect prospect = convertDtoToProspect.convert(prospectDto);
	 * 
	 * validateProspect(prospect);
	 * 
	 * BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
	 * 
	 * prospect.setUser(userRepository.findOne(birdnotesUser.getUserDto().getId()));
	 * 
	 * return prospectRepository.save(prospect);
	 * 
	 * }
	 */

	/*
	 * @Override
	 * 
	 * public void validateProspect(Prospect prospect) throws BirdnotesException {
	 * 
	 * LOG.info(logProspectMarker, "Start validating prospect, id prospect = " +
	 * prospect.getFirstName()+' '+prospect.getLastName() );
	 * 
	 * Sector sector = sectorRepository.findOne(prospect.getSector().getId());
	 * 
	 * if (sector == null) {
	 * 
	 * throw new BirdnotesException("Secteur est obligatoire");
	 * 
	 * }
	 * 
	 * prospect.setSector(sector);
	 * 
	 * Locality locality =
	 * localityRepository.findOne(prospect.getLocality().getId());
	 * 
	 * if (locality == null) {
	 * 
	 * throw new BirdnotesException("Localité est obligatoire");
	 * 
	 * }
	 * 
	 * prospect.setLocality(locality);
	 * 
	 * Speciality speciality =
	 * specialityRepository.findOne(prospect.getSpeciality().getId());
	 * 
	 * if (speciality == null) {
	 * 
	 * throw new BirdnotesException("Specialité est obligatoire");
	 * 
	 * } prospect.setSpeciality(speciality);
	 * 
	 * ProspectType prospectType =
	 * prospectTypeRepository.findOne(prospect.getProspectType().getId());
	 * 
	 * if (prospectType == null) {
	 * 
	 * throw new BirdnotesException("Type est obligatoire");
	 * 
	 * } prospect.setProspectType(prospectType);
	 * 
	 * if (prospect.getEstablishment() != null &&
	 * prospect.getEstablishment().getId() !=null) { Establishment establishment =
	 * establishmentRepository.findOne(prospect.getEstablishment().getId());
	 * prospect.setEstablishment(establishment);
	 * 
	 * 
	 * }else { prospect.setEstablishment(null); }
	 * 
	 * 
	 * Potential potential =
	 * potentialRepository.findOne(prospect.getPotential().getId());
	 * 
	 * if (potential == null) {
	 * 
	 * throw new BirdnotesException("Potential est obligatoire");
	 * 
	 * }
	 * 
	 * prospect.setPotential(potential);
	 * 
	 * prospect.setCreationDate(new Date());
	 * 
	 * LOG.info(logProspectMarker, "End validating prospect"); }
	 */

	@Override

	public boolean delete(Long prospectId) throws BirdnotesException {

		/*
		 * List<Visit> visits = visitRepository.findByProspectId(idProspect);
		 * 
		 * if (visits != null && !visits.isEmpty()) {
		 * 
		 * for (Visit visit : visits) {
		 * 
		 * visitRepository.delete(visit);
		 * 
		 * visitsProductsRepository.deleteByVisitId(visit.getId());
		 * 
		 * }
		 * 
		 * }
		 */

		LOG.info(logProspectMarker, "delete prospect -> prospect status set to deleted, id prospect = " + prospectId);

		prospectsAffectationRepository.deleteByProspect(prospectId);
		Prospect prospect = prospectRepository.findOne(prospectId);
		prospect.setStatus(UserValidationStatus.DELETED);

		prospectRepository.save(prospect);

		return true;

	}

	@Override

	public boolean refuseProspect(Long prospectId) throws BirdnotesException {

		LOG.info(logProspectMarker, "Start refusing prospect, id prospect = " + prospectId);

		Prospect prospect = prospectRepository.findOne(prospectId);
		prospect.setStatus(UserValidationStatus.REFUSED);

		List<Visit> visits = visitRepository.findByProspectId(prospectId);

		if (visits != null && !visits.isEmpty()) {

			for (Visit visit : visits) {
				messageTagRepository.deleteByVisitId(visit.getId());
				visitsProductsRepository.deleteByVisitId(visit.getId());
				purchaseOrderRepository.deleteByVisitId(visit.getId());
				if(visit.getDoubleVisit() != null) {
					Visit doubleVisit = visitRepository.findById(visit.getDoubleVisit().getId());
					visit.setDoubleVisit(null);
					visitRepository.delete(doubleVisit);
				}
				visitRepository.delete(visit);

			}

		}
		// planningRepository.deleteByProspect(prospect);

		planningRepository.deleteByProspectId(prospectId);

		// validationStatusRepository.deleteByProspectId(idProspect);

		/*
		 * List<PurchaseOrder> purchaseOrders = null;
		 * //purchaseOrderRepository.findByProspect(prospect.getId());
		 * 
		 * if (purchaseOrders.size() > 0) {
		 * 
		 * for (PurchaseOrder purchaseOrder : purchaseOrders) {
		 * 
		 * purchaseOrderRepository.delete(purchaseOrder.getId());
		 * 
		 * }
		 * 
		 * }
		 */

		List<ActionMarketing> marketingAction = actionMarketingRepository.findByProspects_Id(prospect.getId());

		for (ActionMarketing actionMarketing : marketingAction) {

			Set<Prospect> prospects = actionMarketing.getProspects().stream()

					.filter(item -> !item.getId().equals(prospect.getId())).collect(Collectors.toSet());

			actionMarketing.setProspects(prospects);

			actionMarketingRepository.save(actionMarketing);

		}

		prospectRepository.save(prospect);

		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		
		notificationMessageBuilder.setMessageType("prospectsRefuseNotificationMessage");

		notificationMessageBuilder.setUser(sourceUser);

		notificationMessageBuilder.setTagetUser(prospect.getUser());

		notificationMessageBuilder.setProspect(prospect);

		Notification notification = notificationMessageBuilder.Build();

		notificationService.generateUsingAllNotificationMethods(notification);

		LOG.info(logProspectMarker, "End refusing prospect");

		return true;

	}

	@Override

	public Prospect updateProspect(ProspectDto prospectDto) throws BirdnotesException {

		LOG.info(logProspectMarker, "Start updating prospect, id prospect = " + prospectDto.getId());

		if (prospectDto == null || prospectDto.getId() == null) {

			throw new BirdnotesException("prospectDto is null");

		}

		Prospect prospectToUpdate = prospectRepository.findOne(prospectDto.getId());

		validateAndConvertProspect(prospectDto, prospectToUpdate);

		List<Range> gammes = gammeRepository.findAll();

		if (gammes != null && !gammes.isEmpty()) {

			fillGammes(gammes, prospectDto, prospectToUpdate);

		}

		prospectRepository.save(prospectToUpdate);

		LOG.info(logProspectMarker, "End updating prospect");

		return prospectToUpdate;

	}

	@Override

	public void acceptAddingNewProspectRequest(ProspectDto prospectDto) throws BirdnotesException {

		LOG.info(logProspectMarker, "Start accepting new prospect, id prospect = " + prospectDto.getId());

		prospectDto.setStatus(UserValidationStatus.VALID);

		Prospect prospectToAccept = this.updateProspect(prospectDto);

		ProspectsAffectation prospectsAffectation = new ProspectsAffectation();

		prospectsAffectation.setProspect(prospectToAccept);

		prospectsAffectation.setDelegate(prospectToAccept.getUser().getDelegate());

		prospectsAffectationRepository.save(prospectsAffectation);

		try {
			User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());

			notificationMessageBuilder.setMessageType("prospectsAcceptationNotificationMessage");

			notificationMessageBuilder.setUser(sourceUser);

			notificationMessageBuilder.setTagetUser(prospectToAccept.getUser());

			notificationMessageBuilder.setProspect(prospectToAccept);

			Notification notification = notificationMessageBuilder.Build();

			notificationService.generateUsingAllNotificationMethods(notification);

		} catch (Exception e) {

			LOG.error(logProspectMarker, "Error occured when adding notification for new prospect affectation", e);

		}
		LOG.info(logProspectMarker, "End accepting new prospect");

	}

	private void fillGammes(List<Range> gammes, ProspectDto prospectDto, Prospect prospect) {

		Set<Range> gammeProspect = new HashSet<>();

		if (prospectDto.getRangeIds() != null) {

			for (Integer gammeId : prospectDto.getRangeIds()) {

				for (Range gamme : gammes) {

					if (gamme.getId() == gammeId) {

						gammeProspect.add(gamme);

					}

				}

			}

		}

		prospect.setRanges(gammeProspect);

	}

	/*
	 * private void setterSectorAndLocality(Prospect prospectToUpdate, Locality
	 * locality, Sector sector)
	 * 
	 * throws BirdnotesException {
	 * 
	 * if (locality == null) {
	 * 
	 * throw new BirdnotesException("Veuillez choisir une localité de prospect");
	 * 
	 * }
	 * 
	 * prospectToUpdate.setLocality(locality);
	 * 
	 * 
	 * 
	 * 
	 * }
	 */

	@Override

	public void updateLocality(ProspectsToMoveRequestDto prospectsToMoveRequestDto) throws BirdnotesException {

		if (prospectsToMoveRequestDto.getProspects() == null)

			throw new BirdnotesException("List of prospects to move is empty");

		if (prospectsToMoveRequestDto.getLocalityId() == null)

			throw new BirdnotesException("Locality id is empty");

		Locality locality = localityRepository.findOne(prospectsToMoveRequestDto.getLocalityId());

		if (locality == null)

			throw new BirdnotesException("Locality is null");

		for (ProspectDto prospectDto : prospectsToMoveRequestDto.getProspects()) {

			Prospect prospectToMove = prospectRepository.findOne(prospectDto.getId());

			if (prospectToMove == null)

				throw new BirdnotesException("Prospect is null");

			prospectToMove.setSector(locality.getSector());

			prospectToMove.setLocality(locality);

			prospectRepository.save(prospectToMove);

		}

	}

	@Override

	public Prospect validateAndConvertProspect(ProspectDto prospectDto, Prospect prospectToUpdate)
			throws BirdnotesException {

		LOG.info(logProspectMarker, "Start validateAndConvertProspect prospect, id prospect = "
				+ prospectDto.getFirstName() + ' ' + prospectDto.getLastName());

		if (prospectDto == null) {

			throw new BirdnotesException("prospectDto is null");

		}

		if (prospectToUpdate == null) {

			throw new BirdnotesException(BirdnotesConstants.Exceptions.PROSPECT_TO_UPDATE_ALREADY_DELETED);

		}

		if (prospectDto.getSpecialityDto() == null) {

			throw new BirdnotesException("Speciality is null or empty");

		}

		if (prospectDto.getPotentialDto() == null) {

			throw new BirdnotesException("Potential is null or empty");

		}

		if (prospectDto.getSectorDto() == null) {

			throw new BirdnotesException("Sector is null or empty");

		}

		if (prospectDto.getLocalityDto() == null) {

			throw new BirdnotesException("Locality is null or empty");

		}

		fillProspect(prospectToUpdate, prospectDto);

		fillSectorById(prospectToUpdate, prospectDto);

		fillLocalityById(prospectToUpdate, prospectDto);

		fillSpecialityById(prospectToUpdate, prospectDto);

		fillPotentialById(prospectToUpdate, prospectDto);

		fillProspectTypeById(prospectToUpdate, prospectDto);
		

		
		
		

		


		fillAppUserById(prospectToUpdate, prospectDto);

		fillEstablishmentById(prospectToUpdate, prospectDto);

		LOG.info(logProspectMarker, "End validateAndConvertProspect ");

		return prospectToUpdate;

	}

	private void fillProspect(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		prospectToUpdate.setFirstName(prospectDto.getFirstName());

		prospectToUpdate.setLastName(prospectDto.getLastName());

		prospectToUpdate.setStatus(prospectDto.getStatus());

		prospectToUpdate.setIdprospect(null);

		prospectToUpdate.setActivity(prospectDto.getActivity());

		prospectToUpdate.setAddress(prospectDto.getAddress());

		prospectToUpdate.setGsm(prospectDto.getGsm());

		prospectToUpdate.setPhone(prospectDto.getPhone());

		prospectToUpdate.setEmail(prospectDto.getEmail());

		prospectToUpdate.setNote(prospectDto.getNote());
		
		prospectToUpdate.setSocialMedia(prospectDto.getSocialMedia());
		
		prospectToUpdate.setTaxIdNumber(prospectDto.getTaxIdNumber());


		prospectToUpdate.setSecretary(prospectDto.getSecretary());
		
		prospectToUpdate.setGrade(prospectDto.getGrade());

		prospectToUpdate.setActive(prospectDto.getActive());

		prospectToUpdate.setActivity(prospectDto.getActivity());

		prospectToUpdate.setLatitude(prospectDto.getLatitude());

		prospectToUpdate.setLongitude(prospectDto.getLongitude());

		prospectToUpdate.setMapAddress(prospectDto.getMapAddress());

		if (prospectToUpdate.getStatus() != null) {
			prospectToUpdate.setUpdateDate(prospectDto.getCreationDate());
		} else {
			prospectToUpdate.setUpdateDate(new Date());
		}

		if (prospectDto.getUpdatePositionDate() != null) {

			prospectToUpdate.setUpdatePositionDate(prospectDto.getUpdatePositionDate());
		}
		
	    if (prospectDto.getInterestIds() != null) {
	        Set<Interest> interests = interestService.findInterestsByIds(prospectDto.getInterestIds());
	        prospectToUpdate.setInterests(interests);
	    }
	    if (prospectDto.getContactTypeIds() != null) {
	        Set<ContactType> contactTypes = contactTypeService.findContactTypesByIds(prospectDto.getContactTypeIds());
	        prospectToUpdate.setContactTypes(contactTypes);
	    }

		
		
		if (prospectDto.getPreferenceIds() != null) {
			
			Set<Preference> preferences = preferenceService.findPreferencesByIds(prospectDto.getPreferenceIds());

			prospectToUpdate.setPreferences(preferences);
		}
		
	}

	private void fillSectorById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		Sector sector = sectorRepository.findOne(prospectDto.getSectorDto().getId());

		if (sector == null) {

			throw new BirdnotesException("Veuillez choisir un secteur");

		}

		prospectToUpdate.setSector(sector);

	}

	private void fillLocalityById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		Locality locality = localityRepository.findOne(prospectDto.getLocalityDto().getId());

		if (locality == null) {

			throw new BirdnotesException("Veuillez choisir une locality");

		}

		prospectToUpdate.setLocality(locality);

	}

	private void fillSpecialityById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		Speciality speciality = specialityRepository.findOne(prospectDto.getSpecialityDto().getId());

		if (speciality == null) {

			throw new BirdnotesException("Veuillez choisir une specialité");

		}

		prospectToUpdate.setSpeciality(speciality);

	}

	private void fillProspectTypeById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		ProspectType prospectType = prospectTypeRepository.findOne(prospectDto.getProspectTypeDto().getId());

		if (prospectType == null) {

			throw new BirdnotesException("Veuillez choisir un type");

		}

		prospectToUpdate.setProspectType(prospectType);

	}
	

	private void fillEstablishmentById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		if (prospectDto.getEstablishmentDto() != null && prospectDto.getEstablishmentDto().getId() != null) {
			Establishment establishment = establishmentRepository.findOne(prospectDto.getEstablishmentDto().getId());
			prospectToUpdate.setEstablishment(establishment);

		} else {
			prospectToUpdate.setEstablishment(null);
		}

	}

	private void fillPotentialById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		Potential potential = potentialRepository.findOne(prospectDto.getPotentialDto().getId());

		if (potential == null) {

			throw new BirdnotesException("Veuillez choisir un potentiel");

		}

		prospectToUpdate.setPotential(potential);

	}

	private void fillAppUserById(Prospect prospectToUpdate, ProspectDto prospectDto) throws BirdnotesException {

		User user = null;

		if (prospectDto.getAppUserId() != null) {
			user = userRepository.findOne(prospectDto.getAppUserId());
		}

		prospectToUpdate.setAppUser(user);

	}

	/*
	 * private void fillLocalisation( Prospect prospectToUpdate , ProspectDto
	 * prospectDto) {
	 * 
	 * if (prospectDto.getLatitude() != null) {
	 * 
	 * prospectToUpdate.setLatitude(prospectDto.getLatitude());
	 * 
	 * }
	 * 
	 * if (prospectDto.getLongitude() != null) {
	 * 
	 * prospectToUpdate.setLongitude(prospectDto.getLongitude());
	 * 
	 * }
	 * 
	 * if (prospectDto.getMapAddress() != null) {
	 * 
	 * prospectToUpdate.setMapAddress(prospectDto.getMapAddress());
	 * 
	 * }
	 * 
	 * }
	 */

	public List<Delegate> findProspectsAffectation(Long prospectId) throws BirdnotesException {

		List<Delegate> delegates = new ArrayList<>();

		delegates = prospectRepository.findProspectsAffectation(prospectId);

		return delegates;

	}

	@Override

	public void acceptProspectUpdateRequest(ProspectDto prospectDto) throws BirdnotesException {

		LOG.info(logProspectMarker, "Start accepting prospect update request, id prospect = " + prospectDto.getId());

		if (prospectDto == null || prospectDto.getId() == null) {

			throw new BirdnotesException("prospectDto is null");

		}

		Prospect prospectToUpdate = prospectRepository.findByIdentifierAndStatus(prospectDto.getIdprospect(),
				UserValidationStatus.VALID);

		validateAndConvertProspect(prospectDto, prospectToUpdate);

		prospectToUpdate.setStatus(UserValidationStatus.VALID);

		prospectRepository.save(prospectToUpdate);

		Prospect oldProspect = prospectRepository.findOne(prospectDto.getId());

		oldProspect.setStatus(UserValidationStatus.ACCEPTED);

		prospectRepository.save(oldProspect);

		List<Delegate> users = findProspectsAffectation(prospectToUpdate.getId());

		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());

		for (Delegate delegate : users) {
			notificationMessageBuilder.setMessageType("prospectsModificationAcceptationNotificationMessage");

			notificationMessageBuilder.setUser(sourceUser);

			notificationMessageBuilder.setTagetUser(delegate.getUser());

			notificationMessageBuilder.setProspect(prospectToUpdate);

			Notification notification = notificationMessageBuilder.Build();

			notificationService.generateUsingAllNotificationMethods(notification);

		}
		LOG.info(logProspectMarker, "End accepting prospect update request");

	}

	/*
	 * @Override
	 * 
	 * public List<ProspectDto> findProspectBySectorId(Long sectorId) throws
	 * BirdnotesException {
	 * 
	 * List<ProspectDto> back = new ArrayList<>();
	 * 
	 * List<Prospect> listOfProspects = prospectRepository.findBySectorId(sectorId);
	 * 
	 * if (listOfProspects != null && !listOfProspects.isEmpty()) {
	 * 
	 * for (Prospect prospect : listOfProspects) {
	 * 
	 * back.add(convertProspectToDto.convert(prospect));
	 * 
	 * }
	 * 
	 * }
	 * 
	 * return back;
	 * 
	 * }
	 */

	@Override

	public boolean sendInvestigation(InvestigationDto investigationDto) throws BirdnotesException {

		Prospect prospect = null;

		boolean result = false;

		if (investigationDto.getProspectId() != null) {

			prospect = prospectRepository.findOne(investigationDto.getProspectId());

		}

		if (prospect != null) {
			
            String sendInvestigationHtmlBody = userService.getTranslatedLabel("sendInvestigationHtmlBody");
            String sendInvestigationSubject = userService.getTranslatedLabel("sendInvestigationSubject");

			MessageFormat messageFormat = new MessageFormat(sendInvestigationHtmlBody);

			String[] messageSplits = investigationDto.getMessage().split(",");

			String title = messageSplits[0];

			String body = "<br/>";

			for (int i = 1; i < messageSplits.length; i++) {

				body += messageSplits[i];

			}

			String[] args = { title, body, investigationDto.getLink(), "" };

			String htmlBody = messageFormat.format(args);

			String[] to = { prospect.getEmail() };

			ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to,

					sendInvestigationSubject, htmlBody);

			Thread thread = new Thread(threadSendEmail);

			thread.start();

			result = true;

		}

		return result;

	}

	/*
	 * @Override
	 * 
	 * public ProspectDto findById(Long id) throws BirdnotesException {
	 * 
	 * if (id != null && id > 0) {
	 * 
	 * return convertProspectToDto.convert(prospectRepository.findOne(id));
	 * 
	 * }
	 * 
	 * return null;
	 * 
	 * }
	 */

	@Override

	public List<ProspectChangedDto> getUpdateRequestDifference(Long prospectId) throws BirdnotesException {

		List<ProspectChangedDto> listChangement = new ArrayList<>();

		Prospect prospectChanged = prospectRepository.findProspectChanged(prospectId);

		Prospect oldProspect = prospectRepository.findOldProspect(prospectChanged.getIdprospect());

		if (oldProspect != null && prospectChanged != null) {

			checkFirstName(oldProspect, prospectChanged, listChangement);

			checkLastName(oldProspect, prospectChanged, listChangement);

			checkAddress(oldProspect, prospectChanged, listChangement);

			checkActivity(oldProspect, prospectChanged, listChangement);

			checkEmail(oldProspect, prospectChanged, listChangement);

			checkGsm(oldProspect, prospectChanged, listChangement);

			checkLocality(oldProspect, prospectChanged, listChangement);

			checkNote(oldProspect, prospectChanged, listChangement);
			
			checkSocialMedia(oldProspect, prospectChanged, listChangement);
			
			checkTaxIdNumber(oldProspect, prospectChanged, listChangement);


			checkSecretary(oldProspect, prospectChanged, listChangement);

			checkProspectType(oldProspect, prospectChanged, listChangement);
			
			checkInterest(oldProspect, prospectChanged, listChangement);
			
			checkContactType(oldProspect, prospectChanged, listChangement);

			checkEstablishment(oldProspect, prospectChanged, listChangement);

			checkGrade(oldProspect, prospectChanged, listChangement);

			checkPhone(oldProspect, prospectChanged, listChangement);

			checkPotential(oldProspect, prospectChanged, listChangement);

			checkSector(oldProspect, prospectChanged, listChangement);

			checkSpeciality(oldProspect, prospectChanged, listChangement);

			checkLatitude(oldProspect, prospectChanged, listChangement);

			checkLongitude(oldProspect, prospectChanged, listChangement);

			checkMapAdress(oldProspect, prospectChanged, listChangement);

		}

		return listChangement;

	}

	private void checkMapAdress(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getMapAddress() != null && !prospectChanged.getMapAddress().equals("")) {

			if (oldProspect.getMapAddress() == null) {

				oldProspect.setMapAddress("");

			}

			if (!oldProspect.getMapAddress().equals(prospectChanged.getMapAddress())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Map adresse");

				prospectChangeDto.setNewValue(prospectChanged.getMapAddress());

				prospectChangeDto.setOldValue(oldProspect.getMapAddress());

				listChangement.add(prospectChangeDto);

			}

		}

	}

	private void checkLongitude(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getLongitude() != null) {

			if (oldProspect.getLongitude() == null) {

				oldProspect.setLongitude(0.0);

			}

			if (!Double.toString(prospectChanged.getLongitude()).equals(Double.toString(oldProspect.getLongitude()))) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Longitude");

				prospectChangeDto.setNewValue(Double.toString(prospectChanged.getLongitude()));

				prospectChangeDto.setOldValue(Double.toString(oldProspect.getLongitude()));

				listChangement.add(prospectChangeDto);

			}

		}

	}

	private void checkLatitude(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getLatitude() != null) {

			if (oldProspect.getLatitude() == null) {

				oldProspect.setLatitude(0.0);

			}

			if (!Double.toString(prospectChanged.getLatitude()).equals(Double.toString(oldProspect.getLatitude()))) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Latitude");

				prospectChangeDto.setNewValue(Double.toString(prospectChanged.getLatitude()));

				prospectChangeDto.setOldValue(Double.toString(oldProspect.getLatitude()));

				listChangement.add(prospectChangeDto);

			}

		}

	}

	private void checkSpeciality(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getSpeciality().equals(prospectChanged.getSpeciality())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Spécialité");

			prospectChangeDto.setNewValue(prospectChanged.getSpeciality().getName());

			prospectChangeDto.setOldValue(oldProspect.getSpeciality().getName());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkSector(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getSector().equals(prospectChanged.getSector())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Secteur");

			prospectChangeDto.setNewValue(prospectChanged.getSector().getName());

			prospectChangeDto.setOldValue(oldProspect.getSector().getName());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkPotential(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getPotential().getName().equals(prospectChanged.getPotential().getName())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Potentiel");

			prospectChangeDto.setNewValue(prospectChanged.getPotential().getName());

			prospectChangeDto.setOldValue(oldProspect.getPotential().getName());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkPhone(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (oldProspect.getPhone() != null && prospectChanged.getPhone() != null

				&& !oldProspect.getPhone().equals(prospectChanged.getPhone())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Téléphone");

			prospectChangeDto.setNewValue(prospectChanged.getPhone());

			prospectChangeDto.setOldValue(oldProspect.getPhone());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkNote(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getNote() != null) {

			if (oldProspect.getNote() == null) {

				oldProspect.setNote("");

			}

			if (!oldProspect.getNote().equals(prospectChanged.getNote())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Note");

				prospectChangeDto.setNewValue(prospectChanged.getNote());

				prospectChangeDto.setOldValue(oldProspect.getNote());

				listChangement.add(prospectChangeDto);

			}

		}

	}
	
	private void checkSocialMedia(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getSocialMedia() != null) {

			if (oldProspect.getSocialMedia() == null) {

				oldProspect.setSocialMedia("");

			}

			if (!oldProspect.getSocialMedia().equals(prospectChanged.getSocialMedia())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("SocialMedia");

				prospectChangeDto.setNewValue(prospectChanged.getSocialMedia());

				prospectChangeDto.setOldValue(oldProspect.getSocialMedia());

				listChangement.add(prospectChangeDto);

			}

		}

	}
	
	private void checkTaxIdNumber(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getTaxIdNumber() != null) {

			if (oldProspect.getTaxIdNumber() == null) {

				oldProspect.setTaxIdNumber("");

			}

			if (!oldProspect.getTaxIdNumber().equals(prospectChanged.getTaxIdNumber())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("TaxIdNumber");

				prospectChangeDto.setNewValue(prospectChanged.getTaxIdNumber());

				prospectChangeDto.setOldValue(oldProspect.getTaxIdNumber());

				listChangement.add(prospectChangeDto);

			}

		}

	}

	private void checkSecretary(Prospect oldProspect, Prospect prospectChanged,
			List<ProspectChangedDto> listChangement) {

		if ((prospectChanged.getSecretary() != null && oldProspect.getSecretary() == null)
				|| (prospectChanged.getSecretary() != null
						&& !oldProspect.getSecretary().equals(prospectChanged.getSecretary()))) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Secrétaire");

			prospectChangeDto.setNewValue(prospectChanged.getSecretary());

			if (oldProspect.getSecretary() == null) {

				prospectChangeDto.setOldValue("");

			}

			else {

				prospectChangeDto.setOldValue(oldProspect.getSecretary());

			}

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkGrade(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if ((prospectChanged.getGrade() != null && oldProspect.getGrade() == null)
				|| (prospectChanged.getGrade() != null && !oldProspect.getGrade().equals(prospectChanged.getGrade()))) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Grade");

			prospectChangeDto.setNewValue(prospectChanged.getGrade());

			if (oldProspect.getGrade() == null) {

				prospectChangeDto.setOldValue("");

			}

			else {

				prospectChangeDto.setOldValue(oldProspect.getGrade());

			}

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkLocality(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getLocality().equals(prospectChanged.getLocality())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Localité");

			prospectChangeDto.setNewValue(prospectChanged.getLocality().getName());

			prospectChangeDto.setOldValue(oldProspect.getLocality().getName());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkProspectType(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getProspectType().equals(prospectChanged.getProspectType())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Type");

			prospectChangeDto.setNewValue(prospectChanged.getProspectType().getName());

			prospectChangeDto.setOldValue(oldProspect.getProspectType().getName());

			listChangement.add(prospectChangeDto);

		}

	}
	
	private void checkInterest(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {
	    if (oldProspect.getInterest() != null && prospectChanged.getInterest() != null) {
	        if (!oldProspect.getInterest().equals(prospectChanged.getInterest())) {
	            ProspectChangedDto prospectChangeDto = new ProspectChangedDto();
	            prospectChangeDto.setLabel("Interest");
	            prospectChangeDto.setNewValue(prospectChanged.getInterest().getName());
	            prospectChangeDto.setOldValue(oldProspect.getInterest().getName());
	            listChangement.add(prospectChangeDto);
	        }
	    }
	}
	
	private void checkContactType(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {
	    if (oldProspect.getContactType() != null && prospectChanged.getContactType() != null) {
	        if (!oldProspect.getContactType().equals(prospectChanged.getContactType())) {
	            ProspectChangedDto prospectChangeDto = new ProspectChangedDto();
	            prospectChangeDto.setLabel("ContactType");
	            prospectChangeDto.setNewValue(prospectChanged.getContactType().getName());
	            prospectChangeDto.setOldValue(oldProspect.getContactType().getName());
	            listChangement.add(prospectChangeDto);
	        }
	    }
	}


	private void checkEstablishment(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {
		if (prospectChanged.getEstablishment() != null) {

			if (oldProspect.getEstablishment() == null
					|| !(oldProspect.getEstablishment().getName() == prospectChanged.getEstablishment().getName())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Etablissement");

				prospectChangeDto.setNewValue(prospectChanged.getEstablishment().getName());

				prospectChangeDto.setOldValue("");

				listChangement.add(prospectChangeDto);

			}
		} else {
			if (oldProspect.getEstablishment() != null) {
				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Etablissement");

				prospectChangeDto.setNewValue("");

				prospectChangeDto.setOldValue(oldProspect.getEstablishment().getName());

				listChangement.add(prospectChangeDto);
			}
		}
	}

	private void checkGsm(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (oldProspect.getGsm() != null && prospectChanged.getGsm() != null) {

			if (oldProspect.getGsm() == null) {

				oldProspect.setGsm("");

			}

			if (!oldProspect.getGsm().equals(prospectChanged.getGsm())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Gsm");

				prospectChangeDto.setNewValue(prospectChanged.getGsm());

				prospectChangeDto.setOldValue(oldProspect.getGsm());

				listChangement.add(prospectChangeDto);

			}

		}

	}

	private void checkEmail(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (prospectChanged.getEmail() != null) {

			if (oldProspect.getEmail() == null) {

				oldProspect.setEmail("");

			}

			if (!oldProspect.getEmail().equals(prospectChanged.getEmail())) {

				ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

				prospectChangeDto.setLabel("Email");

				prospectChangeDto.setNewValue(prospectChanged.getEmail());

				prospectChangeDto.setOldValue(oldProspect.getEmail());

				listChangement.add(prospectChangeDto);

			}

		}

	}

	private void checkActivity(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getActivity().equals(prospectChanged.getActivity())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Activité");

			prospectChangeDto.setNewValue(prospectChanged.getActivity());

			prospectChangeDto.setOldValue(oldProspect.getActivity());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkAddress(Prospect oldProspect, Prospect prospectChanged, List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getAddress().equals(prospectChanged.getAddress())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Adresse");

			prospectChangeDto.setNewValue(prospectChanged.getAddress());

			prospectChangeDto.setOldValue(oldProspect.getAddress());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkFirstName(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getFirstName().equals(prospectChanged.getFirstName())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Nom");

			prospectChangeDto.setNewValue(prospectChanged.getFirstName());

			prospectChangeDto.setOldValue(oldProspect.getFirstName());

			listChangement.add(prospectChangeDto);

		}

	}

	private void checkLastName(Prospect oldProspect, Prospect prospectChanged,

			List<ProspectChangedDto> listChangement) {

		if (!oldProspect.getLastName().equals(prospectChanged.getLastName())) {

			ProspectChangedDto prospectChangeDto = new ProspectChangedDto();

			prospectChangeDto.setLabel("Prénom");

			prospectChangeDto.setNewValue(prospectChanged.getLastName());

			prospectChangeDto.setOldValue(oldProspect.getLastName());

			listChangement.add(prospectChangeDto);

		}

	}

	@Override

	public List<ProspectDto> findProspectWithStatus() throws BirdnotesException {
		LOG.info(logProspectMarker, "Start getting prospects of update requests list");
		List<ProspectDto> back = new ArrayList<>();
		List<Long> userIds = userService.getSubUsersIds();

		List<Prospect> listOfProspects = prospectRepository.findProspectWithStatus(userIds);

		if (listOfProspects != null && !listOfProspects.isEmpty()) {

			for (Prospect prospect : listOfProspects) {

				back.add(convertProspectToDto.convert(prospect));

			}

		}
		LOG.info(logProspectMarker, "End getting prospects of update requests List");
		return back;

	}

	@Override

	@SuppressWarnings("unchecked")

	public ProspectDistributionList getProspectsDistribution(ProspectDistributionRequest prospectDistributionRequest)
			throws BirdnotesException {

		StringBuilder prospectQuery;

		String prospectListQuery;

		String prospectCountQuery;

		Map<String, Object> prospectParameters;

		ProspectDistributionList prospectDistributionList = new ProspectDistributionList();

		if (prospectDistributionRequest.getSelectedGroup() == null) {

			prospectDistributionRequest.setSelectedGroup(GroupType.DELEGUE.getId().longValue());

		}

		Map<String, Object> query = prospectQueryBuild(prospectDistributionRequest);

		prospectQuery = (StringBuilder) query.get(BirdnotesConstants.VisitHistory.QUERY);

		prospectParameters = (Map<String, Object>) query.get(BirdnotesConstants.VisitHistory.PARAMETERS);

		String whereClause = (prospectQuery.substring(prospectQuery.indexOf("where"), prospectQuery.indexOf("GROUP")))
				.toString();

		if (prospectDistributionRequest.isDistribution()) {
			List<ProspectDistribution> prospectDistributions = dynamicQueries
					.findProspectsDistribution(prospectQuery.toString(), prospectParameters);

			prospectDistributionList.setProspectCountBySector(prospectDistributions);

		}

		if (prospectDistributionRequest.getProspectListRequest() != null) {

			String order = orderBy(prospectDistributionRequest);

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectListQuery = "SELECT DISTINCT pa.prospect from ProspectsAffectation pa " + whereClause;

				prospectCountQuery = "SELECT count (DISTINCT pa.prospect) from ProspectsAffectation pa " + whereClause;

				if (!order.equals("")) {
					prospectListQuery += order;
				} else {
					prospectListQuery += " ORDER BY pa.prospect.firstName ";
				}

			} else {

				prospectListQuery = "SELECT DISTINCT p from Prospect p " + whereClause;

				prospectCountQuery = "SELECT count(DISTINCT p) from Prospect p " + whereClause;
				if (!order.equals("")) {
					prospectListQuery += order;
				} else {
					prospectListQuery += " ORDER BY p.firstName ";
				}

			}

			List<Prospect> prospects = dynamicQueries.findProspectsList(prospectListQuery.toString(),
					prospectParameters, prospectDistributionRequest.getProspectListRequest(),
					prospectDistributionRequest.getLimit());

			Collections.sort(prospects, new Comparator<Prospect>() {
				public int compare(Prospect p1, Prospect p2) {
					return p1.getLocality().getName().compareTo(p2.getLocality().getName());
				}
			});

			Long count = dynamicQueries.findProspectsCount(prospectCountQuery, prospectParameters);
			if (prospectDistributionRequest.getLimit() != null && prospectDistributionRequest.getLimit() < count) {
				count = (long) prospectDistributionRequest.getLimit();
			}
			
			

			List<ProspectDto> prospectDtoList = new ArrayList<ProspectDto>();
			
			List<Long> prospectIds = prospects.stream().map(Prospect::getId).collect(Collectors.toList());
			
			Map<Long, Float> orderQuantityMap = new HashMap<>(); 

			
			if (prospectDistributionRequest.getDate() != null) {
			    LocalDate localDate = prospectDistributionRequest.getDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
			    
			    List<KeyValueDto> orderQuantityList = prospectOrderPredictionRepository.getOrdersPredictionsByProspectIds(localDate.getMonthValue(), localDate.getYear(), prospectIds);
			    
			    orderQuantityMap = orderQuantityList.stream()
			        .collect(Collectors.toMap(KeyValueDto::getId, KeyValueDto::getValue));
			}
	

			if (prospects != null && !prospects.isEmpty()) {

				for (Prospect prospect : prospects) {

					ProspectDto prospectDto = convertProspectToDto.convert(prospect);
					prospectDtoList.add(prospectDto);
					if(prospectDistributionRequest.getDate() != null) {
						prospectDto.setOrderPrediction(orderQuantityMap.get(prospectDto.getId()));
					}
					

				}

			}
			
			
			prospectDistributionList.setProspectCount(count);

			prospectDistributionList.setProspectList(prospectDtoList);

		}

		if (prospectDistributionRequest.isCartography()) {

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectListQuery = "SELECT new com.intellitech.birdnotes.model.dto.ProspectDto(pa.prospect) from ProspectsAffectation pa "
						+ whereClause;

			} else {

				prospectListQuery = "SELECT new com.intellitech.birdnotes.model.dto.ProspectDto (p) from Prospect p "
						+ whereClause;

			}

			List<ProspectDto> prospectListCartography = dynamicQueries
					.findProspectsListCartography(prospectListQuery.toString(), prospectParameters);

			prospectDistributionList.setProspectList(prospectListCartography);

			Configuration config = configureRepository.findById(1);

			prospectDistributionList.setSpecialityIconsRootPath(config.getBackendUrl() + uploadUrl + specialityPath);

		}

		return prospectDistributionList;

	}
	


	private Map<String, Object> prospectQueryBuild(ProspectDistributionRequest prospectDistributionRequest) {

		StringBuilder prospectQuery = new StringBuilder();

		Map<String, Object> prospectParameters = new HashMap<>();

		Map<String, Object> prospectQueryParameters = new HashMap<>();

		findProspectDistributionBySelectedGroup(prospectDistributionRequest, prospectQuery, prospectParameters);
		
		findProspectDistributionByUser(prospectDistributionRequest, prospectQuery, prospectParameters);

		findProspectDistributionBySector(prospectDistributionRequest, prospectQuery, prospectParameters);

		findProspectDistributionByLocality(prospectDistributionRequest, prospectQuery, prospectParameters);

		findProspectDistributionByActivity(prospectDistributionRequest, prospectQuery, prospectParameters);

		findProspectDistributionByPotential(prospectDistributionRequest, prospectQuery, prospectParameters);

		findProspectDistributionBySpeciality(prospectDistributionRequest, prospectQuery, prospectParameters);

		findProspectByName(prospectDistributionRequest, prospectQuery, prospectParameters);
		
		

		groupProspectDistributionBy(prospectDistributionRequest, prospectQuery, prospectParameters);

		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.QUERY, prospectQuery);

		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.PARAMETERS, prospectParameters);

		return prospectQueryParameters;

	}

	public void findProspectDistributionBySelectedGroup(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()) {

			prospectQuery.append(
					"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(pa.id), pa.delegate.firstName||' '||pa.delegate.lastName ) from ProspectsAffectation pa");

		} else

		if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.SECTOR.getId()) {

			if (prospectDistributionRequest.getSelectedUsers() != null
					&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(pa.prospect.id), pa.prospect.sector.name) from ProspectsAffectation pa");

			} else {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(p.id), p.sector.name) from Prospect p");

			}

		} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.ACTIVITY.getId()) {

			if (prospectDistributionRequest.getSelectedUsers() != null
					&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(pa.prospect.id), pa.prospect.activity ) from ProspectsAffectation pa");

			} else {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(p.id), p.activity ) from Prospect p");

			}

		} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.POTENTIAL.getId()) {

			if (prospectDistributionRequest.getSelectedUsers() != null
					&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(pa.prospect.id), pa.prospect.potential.name ) from ProspectsAffectation pa");

			} else {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(p.id), p.potential.name ) from Prospect p");

			}

		} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.SPECIALITY.getId()) {

			if (prospectDistributionRequest.getSelectedUsers() != null
					&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(pa.prospect.id), pa.prospect.speciality.name ) from ProspectsAffectation pa");

			} else {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(p.id), p.speciality.name ) from Prospect p");

			}

		} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.LOCALITY.getId()) {

			if (prospectDistributionRequest.getSelectedUsers() != null
					&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(pa.prospect.id), pa.prospect.locality.name ) from ProspectsAffectation pa");

			} else {

				prospectQuery.append(
						"SELECT new com.intellitech.birdnotes.model.dto.ProspectDistribution(count(p.id), p.locality.name ) from Prospect p");

			}

		}

		if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
				|| prospectDistributionRequest.getSelectedUsers() != null
						&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

			prospectQuery.append(" where pa.prospect.status = 'VALID' ");
			if(prospectDistributionRequest.getProspectSource()!= null && prospectDistributionRequest.getProspectSource().equals("recommanded")) {
				prospectQuery.append(" and pa.prospect.id in (select pros.prospect.id from ProspectOrderPrediction pros where (pros.predictionDate) >= date(:planningDate))");
				
				Date planningDate = new Date (prospectDistributionRequest.getDate().getTime());
				planningDate.setDate(1);
				prospectParameters.put("planningDate", planningDate);
			}

		} else {

			prospectQuery.append(" where p.status= 'VALID' ");
			if (prospectDistributionRequest.getAffectationFilter().equals(Affectation.NOT_AFFECTED.toString())) {
				prospectQuery.append(" and p.id not in (select pa.prospect.id from ProspectsAffectation pa)");
			} else if (prospectDistributionRequest.getAffectationFilter().equals(Affectation.AFFECTED.toString())) {
				prospectQuery.append(" and p.id in (select pa.prospect.id from ProspectsAffectation pa)");
			}

		}
		

	}

	public void findProspectDistributionByUser(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedUsers() != null
				&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

			if (prospectDistributionRequest.getSelectedGroup().equals(GroupType.DELEGUE.getId().longValue())
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(" AND pa.delegate.id in :usersIds ");

			} else {

				prospectQuery.append(
						" AND p.id in (SELECT pa.prospect.id from ProspectsAffectation pa WHERE pa.delegate.id in :usersIds)");

			}

			prospectParameters.put("usersIds", prospectDistributionRequest.getSelectedUsers());

		}

	}

	public void findProspectDistributionBySector(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedSectors() != null
				&& !prospectDistributionRequest.getSelectedSectors().isEmpty()) {

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(" AND pa.prospect.sector.id IN (:sectorIds)");

			} else {

				prospectQuery.append(" AND p.sector.id IN (:sectorIds)");

			}

			prospectParameters.put("sectorIds", prospectDistributionRequest.getSelectedSectors());

		}

	}

	public void findProspectDistributionByLocality(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedLocalities() != null
				&& !prospectDistributionRequest.getSelectedLocalities().isEmpty()) {

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(" AND pa.prospect.locality.id IN (:localityIds)");

			} else {

				prospectQuery.append(" AND p.locality.id IN (:localityIds)");

			}

			prospectParameters.put("localityIds", prospectDistributionRequest.getSelectedLocalities());

		}

	}

	public void findProspectDistributionByActivity(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedActivities() != null
				&& !prospectDistributionRequest.getSelectedActivities().isEmpty()) {

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(" AND pa.prospect.activity IN (:activityNames)");

			} else {

				prospectQuery.append(" AND p.activity IN (:activityNames)");

			}

			prospectParameters.put("activityNames", prospectDistributionRequest.getSelectedActivities());

		}

	}

	public void findProspectDistributionByPotential(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedPotentials() != null
				&& !prospectDistributionRequest.getSelectedPotentials().isEmpty()) {

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(" AND pa.prospect.potential.id IN (:potentialIds)");

			} else {

				prospectQuery.append(" AND p.potential.id IN (:potentialIds)");

			}

			prospectParameters.put("potentialIds", prospectDistributionRequest.getSelectedPotentials());

		}

	}

	public void findProspectDistributionBySpeciality(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getSelectedSpecialities() != null
				&& !prospectDistributionRequest.getSelectedSpecialities().isEmpty()) {

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(" AND pa.prospect.speciality.id IN (:specialityIds)");

			} else {

				prospectQuery.append(" AND p.speciality.id IN (:specialityIds)");

			}

			prospectParameters.put("specialityIds", prospectDistributionRequest.getSelectedSpecialities());

		}

	}

	private void findProspectByName(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery, Map<String, Object> prospectParameters) {

		if (prospectDistributionRequest.getKeywords() != null && !prospectDistributionRequest.getKeywords().isEmpty()) {
			String name = '%' + prospectDistributionRequest.getKeywords().toLowerCase().replaceAll("[aeoiyu ]", "_")
					+ '%';
			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append(
						" AND (UPPER(CONCAT(pa.prospect.firstName,' ',pa.prospect.lastName)) like :name OR UPPER(pa.prospect.firstName) like :name OR UPPER(CONCAT(pa.prospect.lastName,' ',pa.prospect.firstName)) like :name OR UPPER(pa.prospect.firstName) like :name OR UPPER(pa.prospect.lastName) like :name)");

			} else {

				prospectQuery.append(
						" AND (UPPER(CONCAT(p.firstName,' ',p.lastName)) like :name OR UPPER(CONCAT(p.lastName,' ',p.firstName)) like :name OR UPPER(p.firstName) like :name OR UPPER(p.lastName) like :name)");

			}

			prospectParameters.put("name", name.toUpperCase());

		}

	}
	


	public void groupProspectDistributionBy(ProspectDistributionRequest prospectDistributionRequest,
			StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		StringBuilder subQuery = new StringBuilder();

		if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()) {

			subQuery.append("pa.delegate.firstName||' '||pa.delegate.lastName");

		} else {

			if (prospectDistributionRequest.getSelectedUsers() != null
					&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				subQuery.append("pa.prospect.");

			} else {

				subQuery.append("p.");

			}

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.SECTOR.getId()) {

				subQuery.append("sector.name");

			} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.ACTIVITY.getId()) {

				subQuery.append("activity");

			} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.POTENTIAL.getId()) {

				subQuery.append("potential.name");

			} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.SPECIALITY.getId()) {

				subQuery.append("speciality.name");

			} else if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.LOCALITY.getId()) {

				subQuery.append("locality.name");

			}

		}

		prospectQuery.append(" GROUP BY ").append(subQuery);

	}

	private String orderBy(ProspectDistributionRequest prospectDistributionRequest) {

		StringBuilder prospectQuery = new StringBuilder();

		String sortField = prospectDistributionRequest.getProspectListRequest().getSortField();

		Integer sortOrder = prospectDistributionRequest.getProspectListRequest().getSortOrder();

		if (sortField != null && !sortField.isEmpty()) {

			prospectQuery.append(" ORDER BY ");

			if (prospectDistributionRequest.getSelectedGroup().intValue() == GroupType.DELEGUE.getId()
					|| prospectDistributionRequest.getSelectedUsers() != null
							&& !prospectDistributionRequest.getSelectedUsers().isEmpty()) {

				prospectQuery.append("pa.prospect.");

			} else {

				prospectQuery.append("p.");

			}

			if (sortField.equals("fullName")) {

				prospectQuery.append("firstName");

			}

			else if (sortField.equals("activity")) {

				prospectQuery.append("activity");

			}

			else if (sortField.equals("specialityDto.name")) {

				prospectQuery.append("speciality.name");

			}

			else if (sortField.equals("potentialDto.name")) {

				prospectQuery.append("potential.name");

			}

			else if (sortField.equals("sectorDto.name")) {

				prospectQuery.append("sector.name");

			}

			else if (sortField.equals("localityDto.name")) {

				prospectQuery.append("locality.name");

			}

			if (sortOrder > 0) {

				prospectQuery.append(" ASC");

			} else {

				prospectQuery.append(" DESC");

			}

		}

		return prospectQuery.toString();

	}

	@Override

	public boolean validatePhone(String gsm) {

		if (!gsm.equals("")) {

			return gsm.matches("^((\\d){8})$");

		} else {

			return true;

		}

	}

	@Override

	public boolean validateEmail(String email) {

		if (!email.equals("")) {

			return email.matches("^[a-zA-Z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$");

		}

		return true;

	}

	@Override

	public boolean validateAddress(String address) {

		return ((!address.equals("")) && (address != null));

	}

	public static boolean validateSequence(String value) {

		return !Pattern.compile("([a-zA-ZÀ-ú])\\1{8,}").matcher(value).find();

	}

	@Override

	public boolean validateName(String name) {

		if ((!name.equals("")) || name != null) {

			return ((name.matches("^([a-zA-ZÀ-ú\\-\\']{2,}(\\s)*)+$")) && validateSequence(name));

		} else {

			return false;

		}

	}

	@Override

	public HashMap<String, Object> saveProspectsWhichBecameValid(List<ProspectDto> invalidProspects)

			throws BirdnotesException {

		Long savedProspects = 0L;

		List<ProspectDto> prospectDtosToAdd = new ArrayList<>();

		HashMap<String, Object> invalidData = new HashMap<>();

		List<DuplicateProspectDto> duplicateProspectDtosList = new ArrayList<>();

		List<PotentialDto> potentialDtos = potentialService.findAll();

		List<LocalityDto> localityDtos = localityService.findAll();

		List<SpecialityDto> specialityDtos = specialityService.findAll();

		List<SectorDto> sectorDtos = sectorService.findAll();

		for (ProspectDto prospectDto : invalidProspects) {

			if (prospectValidation(prospectDto, sectorDtos, localityDtos, specialityDtos, potentialDtos)) {

				List<ProspectDto> result = findSimilarProspects(prospectDto);

				if (result != null && !result.isEmpty()) {

					DuplicateProspectDto duplicateProspectDto = new DuplicateProspectDto();

					duplicateProspectDto.setProspect(prospectDto);

					duplicateProspectDto.setSimilarProspectDtos(result);

					duplicateProspectDtosList.add(duplicateProspectDto);

				} else {

					List<Long> productIds = new ArrayList<>();

					prospectDto.setPotentialIds(productIds);

					prospectDtosToAdd.add(prospectDto);

					savedProspects++;

				}

			}

		}

		for (int i = 0; i < duplicateProspectDtosList.size(); i++) {

			invalidProspects.remove(duplicateProspectDtosList.get(i).getProspectDto());

		}

		if (!prospectDtosToAdd.isEmpty()) {

			addAllImportedProspects(prospectDtosToAdd);

			invalidProspects.removeAll(prospectDtosToAdd);

		}

		invalidData.put("duplicateProspects", duplicateProspectDtosList);

		invalidData.put("invalidProspects", invalidProspects);

		invalidData.put("savedProspects", savedProspects);

		return invalidData;

	}

	public boolean prospectValidation(ProspectDto prospectDto, List<SectorDto> sectorDtos,
			List<LocalityDto> localityDtos, List<SpecialityDto> specialityDtos, List<PotentialDto> potentialDtos)
			throws BirdnotesException {

		boolean isValid = false;

		boolean potentialIsValid = false;

		boolean specialityIsValid = false;

		boolean localityIsValid = false;

		if (prospectDto.getPotentialDto().getId() == 0 && !prospectDto.getPotentialDto().getName().equals("")) {

			PotentialDto potentialDto = potentialService.findPotentialDto(prospectDto.getPotentialDto().getName(),
					potentialDtos);

			if (potentialDto != null) {

				prospectDto.setPotentialDto(potentialDto);

				potentialIsValid = true;

			}

		} else {

			potentialIsValid = true;

		}

		if (prospectDto.getSpecialityDto().getId() == 0 && !prospectDto.getSpecialityDto().getName().equals("")) {

			SpecialityDto specialityDto = specialityService.findSpecialityDto(prospectDto.getSpecialityDto().getName(),
					specialityDtos);

			if (specialityDto != null) {

				prospectDto.setSpecialityDto(specialityDto);

				specialityIsValid = true;

			}

		} else {

			specialityIsValid = true;

		}

		if (prospectDto.getLocalityDto() != null && prospectDto.getLocalityDto().getId() == 0

				&& !prospectDto.getLocalityDto().getName().equals("")

				&& prospectDto.getLocalityDto().getName() != null) {

			LocalityDto localityDto = localityService.findLocalityDto(prospectDto.getLocalityDto().getName(),
					prospectDto.getSectorDto().getName(), localityDtos);

			SectorDto sectorDto = sectorService.findSectorDto(prospectDto.getSectorDto().getName(), sectorDtos);

			if (localityDto != null && sectorDto != null) {

				prospectDto.setLocalityDto(localityDto);

				prospectDto.setSectorDto(sectorDto);

				localityIsValid = true;

			}

		} else {

			localityIsValid = true;

		}

		if (validateName(prospectDto.getFirstName()) && validateName(prospectDto.getLastName())

				&& validateAddress(prospectDto.getAddress()) && validateEmail(prospectDto.getEmail())

				&& validatePhone(prospectDto.getGsm()) && validatePhone(prospectDto.getPhone()) && potentialIsValid

				&& specialityIsValid && localityIsValid)

			isValid = true;

		return isValid;

	}

	@Override

	@SuppressWarnings("unchecked")

	public File exportProspects(ExportProspects exportProspects) throws IOException {

		StringBuilder prospectQuery;

		Map<String, Object> prospectParameters;

		prospectQuery = (StringBuilder) prospectQueryBuild(exportProspects)

				.get(BirdnotesConstants.VisitHistory.QUERY);

		prospectParameters = (Map<String, Object>) prospectQueryBuild(exportProspects)

				.get(BirdnotesConstants.VisitHistory.PARAMETERS);

		List<Prospect> prospectsToExport = dynamicQueries.findProspects(prospectQuery.toString(), prospectParameters);

		return writeDataInCsvFile(prospectsToExport, exportProspects.getDataToExport());

	}

	private Map<String, Object> prospectQueryBuild(ExportProspects exportProspects) {

		StringBuilder prospectQuery = new StringBuilder();

		Map<String, Object> prospectParameters = new HashMap<>();

		Map<String, Object> prospectQueryParameters = new HashMap<>();

		prospectQuery.append("SELECT p from Prospect p WHERE p.status= 'VALID' ");

		findProspectByUser(exportProspects, prospectQuery, prospectParameters);

		findProspectBySector(exportProspects, prospectQuery, prospectParameters);

		findProspectByLocality(exportProspects, prospectQuery, prospectParameters);

		findProspectByActivity(exportProspects, prospectQuery, prospectParameters);

		findProspectByPotential(exportProspects, prospectQuery, prospectParameters);

		findProspectBySpeciality(exportProspects, prospectQuery, prospectParameters);

		prospectQuery.append(" order by p.firstName ASC");

		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.QUERY, prospectQuery);

		prospectQueryParameters.put(BirdnotesConstants.VisitHistory.PARAMETERS, prospectParameters);

		return prospectQueryParameters;

	}

	public void findProspectByUser(ExportProspects exportProspects, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (exportProspects.getSelectedUsers() != null && !exportProspects.getSelectedUsers().isEmpty()) {

			prospectQuery.append(
					" and p.id in ( SELECT pa.prospect.id from ProspectsAffectation pa WHERE pa.delegate.id in (:selectedUsers))");

			prospectParameters.put("selectedUsers", exportProspects.getSelectedUsers());

		}

	}

	public void findProspectBySector(ExportProspects exportProspects, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (exportProspects.getSelectedSectors() != null && !exportProspects.getSelectedSectors().isEmpty()) {

			prospectQuery.append(" and p.sector.id in (:selectedSectors)");

			prospectParameters.put("selectedSectors", exportProspects.getSelectedSectors());

		}

	}

	public void findProspectByLocality(ExportProspects exportProspects, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (exportProspects.getSelectedLocalities() != null && !exportProspects.getSelectedLocalities().isEmpty()) {

			prospectQuery.append(" and p.locality.id in (:selectedLocalities)");

			prospectParameters.put("selectedLocalities", exportProspects.getSelectedLocalities());

		}

	}

	public void findProspectByActivity(ExportProspects exportProspects, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (exportProspects.getSelectedActivities() != null && !exportProspects.getSelectedActivities().isEmpty()) {

			prospectQuery.append(" and p.activity in (:selectedActivities)");

			prospectParameters.put("selectedActivities", exportProspects.getSelectedActivities());

		}

	}

	public void findProspectByPotential(ExportProspects exportProspects, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (exportProspects.getSelectedPotentials() != null && !exportProspects.getSelectedPotentials().isEmpty()) {

			prospectQuery.append(" and p.potential.id in (:selectedPotentials)");

			prospectParameters.put("selectedPotentials", exportProspects.getSelectedPotentials());

		}

	}

	public void findProspectBySpeciality(ExportProspects exportProspects, StringBuilder prospectQuery,

			Map<String, Object> prospectParameters) {

		if (exportProspects.getSelectedSpecialities() != null && !exportProspects.getSelectedSpecialities().isEmpty()) {

			prospectQuery.append(" and p.speciality.id in (:selectedSpecialities)");

			prospectParameters.put("selectedSpecialities", exportProspects.getSelectedSpecialities());

		}

	}

	public File writeDataInCsvFile(List<Prospect> prospectsToExport, List<String> dataToExport) throws IOException {

		File file = File.createTempFile("prospects-", ".csv");

		StringBuilder header = new StringBuilder();

		header.append("id prospect;");

		if (dataToExport.contains("firstName")) {

			header.append("Prénom;");

		}

		if (dataToExport.contains("lastName")) {

			header.append("Nom;");

		}

		if (dataToExport.contains("speciality")) {

			header.append("Spécialité;");

		}

		if (dataToExport.contains("phone")) {

			header.append("Téléphone;");

		}

		if (dataToExport.contains("gsm")) {

			header.append("GSM;");

		}

		if (dataToExport.contains("address")) {

			header.append("Adresse;");

		}

		if (dataToExport.contains("sector")) {

			header.append("Secteur;");

		}

		if (dataToExport.contains("locality")) {

			header.append("Localité;");

		}

		if (dataToExport.contains("activity")) {

			header.append("Activité;");

		}

		if (dataToExport.contains("potential")) {

			header.append("Potentiel;");

		}

		if (dataToExport.contains("email")) {

			header.append("Email;");

		}

		if (dataToExport.contains("secretary")) {

			header.append("Secretaire;");

		}

		if (dataToExport.contains("grade")) {

			header.append("Grade;");

		}
		if (dataToExport.contains("prospectType")) {

			header.append("Type;");

		}
		
		if (dataToExport.contains("interest")) {

			header.append("Interest;");

		}
		
		if (dataToExport.contains("contactType")) {

			header.append("ContactType;");

		}
		
		if (dataToExport.contains("establishment")) {

			header.append("Establishment;");

		}
		if (dataToExport.contains("latitude")) {

			header.append("Latitude;");

		}

		if (dataToExport.contains("longitude")) {

			header.append("Longitude;");

		}

		if (dataToExport.contains("delegates")) {

			header.append("Délégués;");

		}

		if (dataToExport.contains("note")) {

			header.append("Notes;");

		}
		
		if (dataToExport.contains("socialMedia")) {

			header.append("réseaux sociaux;");

		}
		
		if (dataToExport.contains("taxIdNumber")) {

			header.append("matricule fiscale;");

		}

		StringBuilder content = new StringBuilder();

		for (Prospect prospect : prospectsToExport) {

			content.append(prospect.getId());

			content.append(";");

			if (dataToExport.contains("firstName")) {

				content.append(prospect.getFirstName());

				content.append(";");

			}

			if (dataToExport.contains("lastName")) {

				content.append(prospect.getLastName());

				content.append(";");

			}

			if (dataToExport.contains("speciality")) {

				content.append(prospect.getSpeciality().getName());

				content.append(";");

			}

			if (dataToExport.contains("phone")) {

				content.append(prospect.getPhone());

				content.append(";");

			}

			if (dataToExport.contains("gsm")) {

				content.append(prospect.getGsm());

				content.append(";");

			}

			if (dataToExport.contains("address")) {

				if (prospect.getAddress() != null) {
					content.append(prospect.getAddress().replace("\n", " "));
				}

				content.append(";");

			}

			if (dataToExport.contains("sector")) {

				content.append(prospect.getSector().getName());

				content.append(";");

			}

			if (dataToExport.contains("locality")) {

				content.append(prospect.getLocality().getName());

				content.append(";");

			}

			if (dataToExport.contains("activity")) {

				content.append(prospect.getActivity());

				content.append(";");

			}

			if (dataToExport.contains("potential")) {

				content.append(prospect.getPotential().getName());

				content.append(";");

			}

			if (dataToExport.contains("email")) {

				content.append(prospect.getEmail());

				content.append(";");

			}

			if (dataToExport.contains("secretary")) {

				content.append(prospect.getSecretary());

				content.append(";");

			}

			if (dataToExport.contains("prospectType")) {

				content.append(prospect.getProspectType().getName());

				content.append(";");

			}
			
			if (dataToExport.contains("interest")) {

				content.append(prospect.getInterest().getName());

				content.append(";");

			}
			
			if (dataToExport.contains("contactType")) {

				content.append(prospect.getContactType().getName());

				content.append(";");

			}

			if (dataToExport.contains("establishment")) {

				content.append(prospect.getEstablishment().getName());

				content.append(";");

			}

			if (dataToExport.contains("grade")) {

				content.append(prospect.getGrade());

				content.append(";");

			}

			if (dataToExport.contains("latitude")) {

				if (prospect.getLatitude() != null) {

					content.append(prospect.getLatitude().toString());

				} else {

					content.append("");

				}

				content.append(";");

			}

			if (dataToExport.contains("longitude")) {

				if (prospect.getLongitude() != null) {

					content.append(prospect.getLongitude().toString());

				} else {

					content.append("");

				}

				content.append(";");

			}

			if (dataToExport.contains("delegates")) {

				List<String> delegatesNames = new ArrayList<>();

				for (ProspectsAffectation prospectAffectation : prospect.getProspectsAffectation()) {

					delegatesNames.add(prospectAffectation.getDelegate().getFirstName() + " "

							+ prospectAffectation.getDelegate().getLastName());

				}

				String delegates = StringUtils.join(delegatesNames, ",");

				content.append(delegates);

				content.append(";");

			}

			if (dataToExport.contains("note")) {

				if (prospect.getNote() != null) {
					content.append(prospect.getNote().replace("\n", " "));
				}

			}
			if (dataToExport.contains("socialMedia")) {

				if (prospect.getSocialMedia() != null) {
					content.append(prospect.getSocialMedia().replace("\n", " "));
				}

			}
			if (dataToExport.contains("taxIdNumber")) {

				if (prospect.getTaxIdNumber() != null) {
					content.append(prospect.getTaxIdNumber().replace("\n", " "));
				}

			}

			content.append("\n");

		}

		FileWriter csvWriter = new FileWriter(file);

		csvWriter.append(header);

		csvWriter.append("\n");

		csvWriter.append(content);

		csvWriter.flush();

		csvWriter.close();

		return file;

	}

	@Override

	public List<ProspectDto> getNotVisitedProspect(Long userId, Date startDate, Date endDate)

			throws BirdnotesException {

		List<ProspectDto> listNotVisitedProspectDtos = new ArrayList<>();

		List<Prospect> listNotVisitedProspects = prospectRepository.getNotVisitedProspect(userId, startDate, endDate);

		if (listNotVisitedProspects != null && !listNotVisitedProspects.isEmpty()) {

			for (Prospect prospect : listNotVisitedProspects) {

				listNotVisitedProspectDtos.add(convertProspectToDto.convert(prospect));

			}

		}

		return listNotVisitedProspectDtos;

	}

	@Override

	public List<ProspectDto> getVisitedProspect(Long userId, Date startDate, Date endDate)

			throws BirdnotesException {

		List<ProspectDto> listVisitedProspectDtos = new ArrayList<>();

		List<Prospect> listVisitedProspects = prospectRepository.getVisitedProspect(userId, startDate, endDate);

		if (listVisitedProspects != null && !listVisitedProspects.isEmpty()) {

			for (Prospect prospect : listVisitedProspects) {

				listVisitedProspectDtos.add(convertProspectToDto.convert(prospect));

			}

		}

		return listVisitedProspectDtos;

	}

	@Override

	public List<LabelValueDto> findByName(String name, Long prospectId) {
		return prospectRepository.findByName(name.toUpperCase(), prospectId);
	}

	public void sendNotification(List<ValidationStatus> allValidationStatus, int rank, Prospect prospect)
			throws BirdnotesException {
		for (ValidationStatus validationStatus : allValidationStatus) {
			if (validationStatus.getStatus().equals(UserValidationStatus.NEW)
					&& validationStatus.getRank().equals(rank)) {
				User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
				validationStatus.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
				validationStatusRepository.save(validationStatus);
				notificationMessageBuilder.setMessageType("prospectValidationRequest");
				notificationMessageBuilder.setUser(sourceUser);
				notificationMessageBuilder.setTagetUser(validationStatus.getUser());
				notificationMessageBuilder.setEntityId(prospect.getId());
				Notification notification = notificationMessageBuilder.Build();
				notificationService.generateUsingAllNotificationMethods(notification);
			}
		}
	}

	public List<Integer> setValidationStatus(Prospect prospect, List<ValidationStatus> allValidationStatus)
			throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();

		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {

			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);

		}

		int rank = 0;

		int minNumberOfValidators = 0;

		for (ValidationStatus validationStatus : allValidationStatus) {

			if (validationStatus.getStatus().equals(UserValidationStatus.WAITING_FOR_VALIDATION)

					&& validationStatus.getUser().getId().equals(birdnotesUser.getUserDto().getId()))

			{

				validationStatus.setStatus(UserValidationStatus.ACCEPTED);

				validationStatusRepository.save(validationStatus);

				rank = validationStatus.getRank();

				minNumberOfValidators = validationStatus.getValidationStep().getMinValidatorsNumber();

			}

		}
		int maxRank = 0;
		int numberOfValidators = 0;

		for (ValidationStatus validationStatus : allValidationStatus) {

			if (validationStatus.getStatus().equals(UserValidationStatus.ACCEPTED)

					&& validationStatus.getRank().equals(rank))

			{

				numberOfValidators++;

			}
			if (validationStatus.getRank() > maxRank) {
				maxRank = validationStatus.getRank();

			}

		}

		return Arrays.asList(rank, minNumberOfValidators, maxRank, numberOfValidators);
	}

	@Override
	public void acceptAddingNewProspectRequestStep(Long validationStatusId, ProspectDto prospectDto)
			throws BirdnotesException {

		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		List<ValidationStatus> allValidationStatus = validationStatusRepository
				.findByProspectOrderByRankAsc(userValidationStatus.getProspect());

		boolean isWorkflowFinished = this.validationStepService.accept(allValidationStatus, validationStatusId,
				userValidationStatus.getProspect().getId());

		if (isWorkflowFinished) {

			acceptAddingNewProspectRequest(prospectDto);

		}
	}

	@Override
	public void acceptProspectChangeRequestStep(Long validationStatusId, ProspectDto prospectDto)
			throws BirdnotesException {

		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		List<ValidationStatus> allValidationStatus = validationStatusRepository
				.findByProspectOrderByRankAsc(userValidationStatus.getProspect());

		boolean isWorkflowFinished = this.validationStepService.accept(allValidationStatus, validationStatusId,
				userValidationStatus.getProspect().getId());

		if (isWorkflowFinished) {

			acceptProspectUpdateRequest(prospectDto);

		}

	}

	@Override
	public void refuseValidationStep(long validationStatusId) throws BirdnotesException {

		this.validationStepService.refuse(validationStatusId);
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		refuseProspect(userValidationStatus.getProspect().getId());

	}

	@Override
	public ProspectDto findProspectById(Long id) throws BirdnotesException {
		if (id != null && id > 0) {
			Prospect prospect = prospectRepository.findOne(id);
			ProspectDto prospectDto = convertProspectToDto.convert(prospect);
			return prospectDto;
		}
		return null;
	}

	@Override
	public List<ProspectDto> getNewAndUpdated(Long userId) throws BirdnotesException {

		List<ProspectDto> prospects = new ArrayList<>();
		List<Prospect> newAndUpdatedProspecs = prospectRepository.getNewAndUpdated(userId);
		for (Prospect prospect : newAndUpdatedProspecs) {
			if (prospect.getStatus().equals(UserValidationStatus.UPDATE)) {
				prospect.setIdentifier(prospect.getIdprospect());
			}
			prospects.add(convertProspectToDto.convert(prospect));

		}
		return prospects;
	}

	@Override
	public List<ProspectDto> findAllPatients(ProspectDistributionRequest patientDistributionRequest)
			throws BirdnotesException {
		List<ProspectDto> prospectsDto = new ArrayList<>();
		Pageable pageable = new PageRequest(patientDistributionRequest.getProspectListRequest().getFirst(),
				patientDistributionRequest.getProspectListRequest().getRows());
		List<Prospect> patients = prospectRepository.findAllPatient(pageable);
		if (patients != null && !patients.isEmpty()) {
			for (Prospect patient : patients) {
				prospectsDto.add(convertProspectToDto.convertPatient(patient));

			}
		}
		return prospectsDto;

	}

	@Override
	public void deletePatient(Long idPatient) {
		prospectRepository.delete(idPatient);

	}

	@Override
	public Prospect savePatient(PatientDto patientDto) throws BirdnotesException {
		Prospect prospect = null;
		if (patientDto.getId() != null) {
			prospect = prospectRepository.findOne(patientDto.getId());
		}
		if (prospect == null) {
			prospect = new Prospect();
		}
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		User user = userRepository.findById(birdnotesUser.getUserDto().getId());
		prospect.setFirstName(patientDto.getFirstName());
		prospect.setLastName(patientDto.getLastName());
		prospect.setAddress(patientDto.getAddress());
		prospect.setGsm(patientDto.getGsm());
		prospect.setPhone(patientDto.getPhone());
		prospect.setEmail(patientDto.getEmail());
		prospect.setNote(patientDto.getNote());
		prospect.setSocialMedia(patientDto.getSocialMedia());
		prospect.setStatus(UserValidationStatus.VALID);
		prospect.setCreationDate(new Date());
		prospect.setUser(user);
		prospect.setActive(true);
		SectorDto sectorDto = patientDto.getSectorDto();
		Sector sector = new Sector();
		if (sectorDto != null && sectorDto.getId() != null) {
			sector.setId(sectorDto.getId());
			sector.setName(sectorDto.getName());
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_SECTOR);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_SECTOR);
		}
		prospect.setSector(sector);

		LocalityDto localityDto = patientDto.getLocalityDto();
		Locality locality = new Locality();
		if (localityDto != null && localityDto.getId() != null) {
			locality.setId(localityDto.getId());
			locality.setName(localityDto.getName());
			locality.setSector(sector);
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_LOCALITY);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_LOCALITY);
		}
		prospect.setLocality(locality);

		PotentialDto potentialDto = patientDto.getPotentialDto();
		Potential potential = new Potential();
		if (potentialDto != null && potentialDto.getId() != null) {
			potential.setId(potentialDto.getId());
			potential.setName(potentialDto.getName());
		} else {
			LOG.error(BirdnotesConstants.Exceptions.NULL_POTENTIAL);
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_POTENTIAL);
		}
		prospect.setPotential(potential);
		prospect.setIdprospect(patientDto.getIdprospect());
		ProspectType prospectType = prospectTypeRepository.findByName("Médecin");
		if (patientDto.getDoctorId() != null) {
			Prospect doctor = prospectRepository.findOne(patientDto.getDoctorId());
			if (doctor != null) {
				prospect.setDoctor(doctor);

			}
		}
		return prospectRepository.save(prospect);
	}
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> getAllProspectsIdAndFullName() {
	    List<Prospect> prospects = prospectRepository.findAll(); 
	    return prospects.stream()
	        .map(prospect -> {
	            Map<String, Object> map = new HashMap<>();
	            map.put("id", prospect.getId());
	            map.put("fullName", prospect.getFirstName() + " " + prospect.getLastName()); 
	            return map;
	        })
	        .collect(Collectors.toList());
	}



}