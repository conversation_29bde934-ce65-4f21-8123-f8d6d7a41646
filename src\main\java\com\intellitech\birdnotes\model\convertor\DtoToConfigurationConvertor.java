package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;

@Component("dtoToConfigurationConvertor")
public class DtoToConfigurationConvertor {

	private static final Logger LOG = LoggerFactory.getLogger(DtoToConfigurationConvertor.class);

	public Configuration convert(ConfigurationDto configurationDto) throws BirdnotesException {

		if (configurationDto == null) {
			LOG.error("Configuration is null");
			throw new BirdnotesException("configuration is null");
		}
		Configuration configuration = new Configuration();
		ValidAndSetId(configurationDto,configuration);
		ValidAndSetName(configurationDto,configuration);
	    ValidAndSetLogo(configurationDto,configuration);
        ValidAnsSetSyncro(configurationDto,configuration);
		ValidAndSetCycle(configurationDto,configuration);
		return configuration;
	}

	private void ValidAndSetCycle(ConfigurationDto configurationDto, Configuration configuration) throws BirdnotesException {
		if (configurationDto.getCycle() == null ) {
			LOG.error("cycle is null ");
			throw new BirdnotesException("cycle is null ");
		}
		configuration.setCycle(configurationDto.getCycle());
	}

	private void ValidAnsSetSyncro(ConfigurationDto configurationDto, Configuration configuration) throws BirdnotesException {
		if (configurationDto.getSyncro() == null ) {
			LOG.error("syncro is null ");
			throw new BirdnotesException("syncro is null ");
		}
		configuration.setSyncro(configurationDto.getSyncro());
	}

	private void ValidAndSetLogo(ConfigurationDto configurationDto, Configuration configuration) throws BirdnotesException {
		if (configurationDto.getLogo() == null ) {
			LOG.error("Logo is null or empty");
			throw new BirdnotesException("Logo is null ");
		}
		configuration.setLogo(configurationDto.getLogo());
	}

	private void ValidAndSetName(ConfigurationDto configurationDto, Configuration configuration) throws BirdnotesException {
		if (configurationDto.getName() == null && configurationDto.getName().isEmpty()) {
			LOG.error("Name is null or empty");
			throw new BirdnotesException("Name is null or empty");
		}
		configuration.setName(configurationDto.getName());
	}

	private void ValidAndSetId(ConfigurationDto configurationDto, Configuration configuration) throws BirdnotesException {
		if (configurationDto.getId() == null ) {
			LOG.error("id is null or empty");
			throw new BirdnotesException("id is null or empty");
		}
		configuration.setId(configurationDto.getId());
		
	}
}
