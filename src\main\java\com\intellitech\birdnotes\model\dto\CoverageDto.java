package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class CoverageDto implements Serializable, Comparable<CoverageDto> {

	private static final long serialVersionUID = 1L;
	private String name;

	private long totalBusinessDays;
	private float totalConnexeActivities;
	private short holidays;
	private float daysWorked;
	
	private float prospectsTotalCount;
	private float cycle;
	private float workingDaysPerWeek;
	private float dailyTarget;
	
	private float visitObjectif;
	private float visitExecuted;
	private float coverage;
	private String count;
	private float visitAverage;

	public CoverageDto() {
		super();
	}

	public CoverageDto(String name, String count, float visitObjectif, float visitExecuted) {
		super();
		this.name = name;
		this.count = count;
		this.visitObjectif = visitObjectif;
		this.visitExecuted = visitExecuted;
	}
	
	
	

	public CoverageDto(String name, long totalBusinessDays, float totalConnexeActivities, short holidays,
			long prospectsTotalCount, short cycle, short workingDaysPerWeek, float visitExecuted) {
		
		super();
		this.name = name;
		this.totalBusinessDays = totalBusinessDays;
		this.totalConnexeActivities = totalConnexeActivities;
		this.holidays = holidays;
		this.daysWorked  = this.totalBusinessDays  - (this.totalConnexeActivities  + this.holidays);
		this.prospectsTotalCount = prospectsTotalCount;
		this.cycle = cycle;
		this.workingDaysPerWeek = workingDaysPerWeek;
		this.dailyTarget = this.prospectsTotalCount  / this.cycle / this.workingDaysPerWeek;
		this.visitExecuted = visitExecuted;
		this.visitObjectif = this.dailyTarget * this.daysWorked;
		this.coverage = this.visitExecuted  / this.visitObjectif;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCount() {
		return count;
	}

	public void setCount(String count) {
		this.count = count;
	}

	public float getVisitObjectif() {
		return visitObjectif;
	}

	public void setVisitObjectif(float visitObjectif) {
		this.visitObjectif = visitObjectif;
	}

	public float getVisitExecuted() {
		return visitExecuted;
	}

	public void setVisitExecuted(float visitExecuted) {
		this.visitExecuted = visitExecuted;
	}

	public float getCoverage() {
		return coverage;
	}

	public void setCoverage(float coverage) {
		this.coverage = coverage;
	}

	@Override
	public int compareTo(CoverageDto o) {
		return Double.compare(this.coverage , o.getCoverage());
	}

	public long getTotalBusinessDays() {
		return totalBusinessDays;
	}

	public void setTotalBusinessDays(long totalBusinessDays) {
		this.totalBusinessDays = totalBusinessDays;
	}

	public float getTotalConnexeActivities() {
		return totalConnexeActivities;
	}

	public void setTotalConnexeActivities(float totalConnexeActivities) {
		this.totalConnexeActivities = totalConnexeActivities;
	}

	public short getHolidays() {
		return holidays;
	}

	public void setHolidays(short holidays) {
		this.holidays = holidays;
	}

	public float getDaysWorked() {
		return daysWorked;
	}

	public void setDaysWorked(float daysWorked) {
		this.daysWorked = daysWorked;
	}

	public float getProspectsTotalCount() {
		return prospectsTotalCount;
	}

	public void setProspectsTotalCount(float prospectsTotalCount) {
		this.prospectsTotalCount = prospectsTotalCount;
	}

	public float getCycle() {
		return cycle;
	}

	public void setCycle(float cycle) {
		this.cycle = cycle;
	}

	public float getWorkingDaysPerWeek() {
		return workingDaysPerWeek;
	}

	public void setWorkingDaysPerWeek(float workingDaysPerWeek) {
		this.workingDaysPerWeek = workingDaysPerWeek;
	}

	public float getDailyTarget() {
		return dailyTarget;
	}

	public void setDailyTarget(float dailyTarget) {
		this.dailyTarget = dailyTarget;
	}

	public float getVisitAverage() {
		return visitAverage;
	}

	public void setVisitAverage(float visitAverage) {
		this.visitAverage = visitAverage;
	}




}
