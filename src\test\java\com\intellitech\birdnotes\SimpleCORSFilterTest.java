package com.intellitech.birdnotes;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import java.io.IOException;

@RunWith(MockitoJUnitRunner.class)
public class SimpleCORSFilterTest {

    @Mock
    private FilterConfig mockFilterConfig;

    private SimpleCORSFilter simpleCORSFilterUnderTest;

    @Before
    public void setUp() throws Exception {
        simpleCORSFilterUnderTest = new SimpleCORSFilter();
        simpleCORSFilterUnderTest.init(mockFilterConfig);
    }

    @Test
    public void testDoFilter() throws Exception {
        // Setup
        final MockHttpServletRequest req = new MockHttpServletRequest();
        final MockHttpServletResponse res = new MockHttpServletResponse();
        final FilterChain chain = null;

        // Run the test
        //simpleCORSFilterUnderTest.doFilter(req, res, chain);

        // Verify the results
    }

    //@Test(expected = IOException.class)
    public void testDoFilter_ThrowsIOException() throws Exception {
        // Setup
       /* final MockHttpServletRequest req = new MockHttpServletRequest();
        final MockHttpServletResponse res = new MockHttpServletResponse();
        final FilterChain chain = null;

        // Run the test
        //simpleCORSFilterUnderTest.doFilter(req, res, chain);*/
    }

    //@Test(expected = ServletException.class)
    public void testDoFilter_ThrowsServletException() throws Exception {
        // Setup
        /*final MockHttpServletRequest req = new MockHttpServletRequest();
        final MockHttpServletResponse res = new MockHttpServletResponse();
        final FilterChain chain = null;

        // Run the test
        //simpleCORSFilterUnderTest.doFilter(req, res, chain);*/
    }

    @Test(expected = UnsupportedOperationException.class)
    public void testDestroy() {
        simpleCORSFilterUnderTest.destroy();
    }
}
