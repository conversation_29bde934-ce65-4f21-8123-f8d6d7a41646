package com.intellitech.birdnotes.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.MarketingActionType;
import com.intellitech.birdnotes.model.dto.InterestDto;
import com.intellitech.birdnotes.model.dto.MarketingActionTypeDto;


public interface MarketingActionTypeService {
	
	List<MarketingActionTypeDto> findAll() throws BirdnotesException;
	
	void saveAllMarketingActionTypes(List<MarketingActionTypeDto> marketingActionTypeDtos) throws BirdnotesException;

	MarketingActionTypeDto findMarketingActionTypeDto(String marketingActionTypeName, List<MarketingActionTypeDto> marketingActionTypeDtos);
	
	MarketingActionType saveMarketingActionType(MarketingActionTypeDto marketingActionTypeDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;




	

}