package com.intellitech.birdnotes.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;

@Entity
@Table(name = BirdnotesConstants.Tables.FAVORITE_MENU_ITEM, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class FavoriteMenuItem {
	private Long id;
	private String label;
	private Integer rank;
	private String routerLink;
	private String icon;

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.FAVORITE_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.FAVORITE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.FAVORITE_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = Columns.LABEL)
	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	@Column(name = Columns.RANK)
	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	@Column(name = Columns.ROUTER_LINK)
	public String getRouterLink() {
		return routerLink;
	}

	public void setRouterLink(String routerLink) {
		this.routerLink = routerLink;
	}

	@Column(name = Columns.ICON)
	public String getIcon() {
		return icon;
	}

	public void setIcon(String icon) {
		this.icon = icon;
	}

}
