package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.dto.LocalityRequestDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SectorRequestDto;
import org.springframework.dao.DataIntegrityViolationException;

public interface SectorService {

	List<SectorDto> findAll() throws BirdnotesException;

	void add(SectorRequestDto sectorDto) throws BirdnotesException;

	List<SectorRequestDto> saveAll(List<SectorRequestDto> sectorRequestDtos) throws BirdnotesException;

	void delete(Long idSector) throws BirdnotesException;

	List<SectorDto> findSectorByUserId(Long idSector) throws BirdnotesException;

	SectorDto findSectorByName(String name) throws BirdnotesException;

	void addAll(List<SectorRequestDto> sectorDtos) throws BirdnotesException;

	Sector addSector(LocalityRequestDto localityRequestDto) throws BirdnotesException;

	Sector saveSector(SectorDto sectorDto) throws BirdnotesException;

	SectorDto findSectorDto(String sectorName, List<SectorDto> sectorDtos);


}