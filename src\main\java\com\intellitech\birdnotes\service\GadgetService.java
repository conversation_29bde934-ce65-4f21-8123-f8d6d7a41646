package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.request.GadgetRequest;



public interface GadgetService {
	
	Gift add (GadgetRequest gadgetRequest) throws BirdnotesException;
	List<GiftDto> findAll() throws BirdnotesException;
	//Gadget saveGadget(GadgetRequest gadgetRequest);
	void delete(Long id) throws BirdnotesException;
	List<GiftDto> findAllPurchaseOrderTemplateGifts() throws BirdnotesException;
	Gift saveGadget(GiftDto gadgetDto) throws BirdnotesException;
}
