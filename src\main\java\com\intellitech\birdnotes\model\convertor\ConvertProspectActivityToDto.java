package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ProspectActivity;
import com.intellitech.birdnotes.model.dto.ProspectActivityDto;

@Component("convertprospectActivityToDto")
public class ConvertProspectActivityToDto {

	private static final Logger LOG = LoggerFactory.getLogger(ConvertProspectActivityToDto.class);

	public ProspectActivityDto convert(ProspectActivity prospectActivity) throws BirdnotesException {

		if (prospectActivity == null) {
			LOG.error("prospect activity is null");
			throw new BirdnotesException("prospect activity is null");
		}
		ProspectActivityDto prospectActivityDto = new ProspectActivityDto();
		prospectActivityDto.setName(prospectActivity.getName());
		prospectActivityDto.setId(prospectActivity.getId());

		return prospectActivityDto;
	}

}
