package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Speciality;

@Repository
public interface SpecialityRepository extends JpaRepository<Speciality, Long> {

	Speciality findByName(String name);

	Speciality findFirstByNameIgnoreCase(String specialityName);

	@Override
	@Query("SELECT s from Speciality s order by s.name ASC")
	List<Speciality> findAll();

	@Query("SELECT s.id from Speciality s WHERE s.id in (:specialities) order by s.name")
	List<Long> findWhereIdIn(@Param("specialities") List<Long> specialities);

	@Modifying
	@Query("DELETE FROM Speciality where id=:id")
	void deleteById(@Param("id") Long id);

	@Query("SELECT s.id FROM Speciality s")
	List<Long> getSpecialityIds();

	@Query("SELECT s from Speciality s WHERE LOWER(name) = LOWER(?1) AND id != ?2 ")
	Speciality findByNameWithDiffId(String name, Long id);
	
	@Query("SELECT s.name FROM Speciality s order by s.name")
	List<String>findAllSpecialityNames();
}