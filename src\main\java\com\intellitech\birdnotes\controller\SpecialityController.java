package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/specialities")
public class SpecialityController {
	private static final Logger LOG = LoggerFactory.getLogger(SpecialityController.class);

	@Autowired
	private SpecialityService specialityService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/findallspecialities", method = RequestMethod.GET)
	public ResponseEntity<List<SpecialityDto>> findAllSpecialities() {
		try {
			if (userService.checkHasPermission("SPECIALITY_VIEW")) {
				List<SpecialityDto> specialityDtos = specialityService.findAll();
				return new ResponseEntity<>(specialityDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all specialities", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllSpecialities(@RequestBody List<SpecialityRequestDto> specialityRequestDtos) {
		try {
			specialityService.addAll(specialityRequestDtos);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add specialities ", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding specialities ", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addSpeciality(
			@RequestPart("specialityRequest") SpecialityRequestDto specialityRequestDto,
			@RequestPart(name = "file", required = false) MultipartFile file) {
		try {
			if (userService.checkHasPermission("SPECIALITY_ADD")) {
				Speciality specialitySaved = specialityService.add(specialityRequestDto, file);
				if (specialitySaved != null) {
					return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
				}
				return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<String>("", HttpStatus.CONFLICT);
			}

		} catch (BirdnotesException fe) {
			return new ResponseEntity<String>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in saveSpeciality", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteSpeciality(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("SPECIALITY_DELETE")) {
	            specialityService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.SPECIALITY_TO_DELETE_ALREADY_DELETED, 
	                    HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting speciality", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the speciality with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}



	@RequestMapping(value = "/saveSpeciality", method = RequestMethod.POST)
	public ResponseEntity<String> saveSpeciality(@RequestPart("specialityRequest") SpecialityDto specialityDto,
	        @RequestPart(name = "file", required = false) MultipartFile file) {

	    try {
	        if (userService.checkHasPermission("SPECIALITY_EDIT")) {
	            Speciality savedSpeciality = specialityService.saveSpeciality(specialityDto, file);
	            if (savedSpeciality != null) {
	                return new ResponseEntity<>(savedSpeciality.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving saveSpeciality", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving saveSpeciality", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

}
