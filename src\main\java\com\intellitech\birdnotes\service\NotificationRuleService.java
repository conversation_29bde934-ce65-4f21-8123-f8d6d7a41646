package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.NotificationRuleDto;
import com.intellitech.birdnotes.model.request.NotificationRuleRequest;




public interface NotificationRuleService {
	boolean saveNotification (NotificationRuleRequest notificationRuleRequest) throws BirdnotesException;
	List<Event> getEventXml() throws IOException;
	List<NotificationRuleDto> findNotificationByEvent( String eventType);
	void deleteByEventType(String eventType);
	Set<User> getSuperiorsByRole(Long id, String roleName);
	

}
