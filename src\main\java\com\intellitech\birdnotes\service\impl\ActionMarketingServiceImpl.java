package com.intellitech.birdnotes.service.impl;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.logging.Logger;

import javax.transaction.Transactional;

import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.intellitech.birdnotes.data.dto.OrdersPredictionRequest;
import com.intellitech.birdnotes.data.dto.PredictionResponse;
import com.intellitech.birdnotes.data.dto.ProspectFeaturesDto;
import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.MarketingActionType;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.convertor.ConvertActivityToDto;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ActionMarketingDto;
import com.intellitech.birdnotes.model.dto.ActionMarketingFormDto;
import com.intellitech.birdnotes.model.dto.ActionMarketingResponseDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.MarketingActionOrderPredictionDto;
import com.intellitech.birdnotes.model.dto.MarketingActionTypeDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.model.request.ActionMarketingRequest;
import com.intellitech.birdnotes.repository.ActionMarketingRepository;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.MarketingActionTypeRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ActionMarketingService;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.MarketingActionTypeService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

@Service("actionMarketingService")
@Transactional
public class ActionMarketingServiceImpl implements ActionMarketingService {
	
	private static final org.slf4j.Logger log = LoggerFactory.getLogger(ActionMarketingServiceImpl.class);


	@Autowired
	private ActionMarketingRepository actionMarketingRepository;

	private NotificationService notificationService;

	private CurrentUser currentUser;

	private NotificationMessageBuilder notificationMessageBuilder;

	private ValidationStepService validationStepService;

	private ValidationStatusRepository validationStatusRepository;

	private UserToDtoConvertor userToDtoConvertor;
	
	private UserService userService;
	 
	private DelegateRepository delegateRepository;
	
	
	@Autowired
	private ConfigurationRepository configureRepository;
	
	@Autowired
	private RestTemplate restTemplate;

	
	@Value("${ml.serverToken}")
	private String mlServerToken;

	
	@Autowired
	private ProspectRepository prospectRepository;
	@Autowired
	private ProductRepository productRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private MarketingActionTypeRepository marketingActionTypeRepository;
	
    @Autowired
    private MarketingActionTypeService marketingActionTypeService;
	

	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;
		
	}
	
	

	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}



	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	@Autowired
	public void setNotificationService(NotificationService notificationService) {
		this.notificationService = notificationService;
	}

	@Autowired
	public void setNotificationMessageBuilder(NotificationMessageBuilder notificationMessageBuilder) {
		this.notificationMessageBuilder = notificationMessageBuilder;
	}

	@Autowired
	public void setValidationStepService(ValidationStepService validationStepService) {
		this.validationStepService = validationStepService;
	}

	@Autowired
	public void setValidationStatusRepository(ValidationStatusRepository validationStatusRepository) {
		this.validationStatusRepository = validationStatusRepository;
	}
	
	@Autowired
	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}
	@Autowired
	private MessageSource messageSource;
	
	
	@Override
	public ActionMarketing add(ActionMarketingRequest actionMarketingRequest) throws BirdnotesException {

		if (actionMarketingRequest == null) {
			throw new RuntimeException();
		}

		ActionMarketing actionMarketing = new ActionMarketing();

		ActionMarketing result = actionMarketingRepository.findByName(actionMarketingRequest.getName());
		if (result != null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}

		actionMarketing.setName(actionMarketingRequest.getName());
		actionMarketing.setBudget(actionMarketingRequest.getBudget());

		ActionMarketing actionMarketingSaved = actionMarketingRepository.save(actionMarketing);
		return actionMarketingSaved;
	}

	@Override
	public List<ActionMarketingDto> findActionMarketingByDate(Date firstDate, Date lastDate) throws BirdnotesException {
		List<ActionMarketingDto> result = new ArrayList<>();
		List<Long> subUsers = userService.getSubUsersIds();
		List<ActionMarketing> allActionMarketings = actionMarketingRepository.findActionMarketingByDate(firstDate,
				lastDate, subUsers);
		for (ActionMarketing actionMarketing : allActionMarketings) {
			ActionMarketingDto actionMarketingDto = new ActionMarketingDto();
			actionMarketingDto.setId(actionMarketing.getId());
			actionMarketingDto.setName(actionMarketing.getName());
			actionMarketingDto.setBudget(actionMarketing.getBudget());
			actionMarketingDto.setDate(actionMarketing.getDate());
			actionMarketingDto.setStatus(actionMarketing.getStatus().toString());
			actionMarketingDto.setDescription(actionMarketing.getDescription());
			actionMarketingDto.setType(actionMarketing.getMarketingActionType().getName());
			actionMarketingDto.setMarketingActionTypeId(actionMarketing.getMarketingActionType().getId());		
			
			String userName = actionMarketing.getUser().getUsername();
			actionMarketingDto.setUserName(userName);
			actionMarketingDto.setUserId(actionMarketing.getUser().getId());
			List<ValidationStatusDto> listValidationStatusDto = validationStepService
					.findByActionMarketing(actionMarketing);
			actionMarketingDto.setValidationStatusDto(listValidationStatusDto);
			Set<ProspectDto> prospects = new HashSet<>();

			if (actionMarketing.getProspects() != null && !(actionMarketing.getProspects().isEmpty())) {
				for (Prospect prospect : actionMarketing.getProspects()) {
					ProspectDto prospectDto = new ProspectDto();
					prospectDto.setId(prospect.getId());
					prospectDto.setFirstName(prospect.getFirstName());
					prospectDto.setLastName(prospect.getLastName());
					prospects.add(prospectDto);
				}
			}
			actionMarketingDto.setProspects(prospects);

			Set<ProductDto> products = new HashSet<>();
			if (actionMarketing.getProducts() != null && !(actionMarketing.getProducts().isEmpty())) {
				for (Product product : actionMarketing.getProducts()) {
					ProductDto productDto = new ProductDto();
					productDto.setId(product.getId());
					productDto.setName(product.getName());
					products.add(productDto);
				}
			}
			actionMarketingDto.setProducts(products);

			result.add(actionMarketingDto);
		}
		return result;
	}

	@Override
	public List<ActionMarketingResponseDto> findActionMarketingByUser(Long userId) {

		List<ActionMarketingResponseDto> actionMarketingListDtos = new ArrayList<>();

		List<ActionMarketing> actionMarketings = actionMarketingRepository.findActionMarketingByUser(userId);

		if (actionMarketings != null && !actionMarketings.isEmpty()) {
			for (ActionMarketing actionMarketing : actionMarketings) {
				ActionMarketingResponseDto actionMarketingDto = new ActionMarketingResponseDto();
				actionMarketingDto.setStatus(actionMarketing.getStatus().name());
				actionMarketingDto.setName(actionMarketing.getName());
				actionMarketingDto.setId(actionMarketing.getId());
				actionMarketingDto.setDescription(actionMarketing.getDescription());
				actionMarketingDto.setBudget(actionMarketing.getBudget());
				actionMarketingDto.setDate(actionMarketing.getDate());
				actionMarketingDto.setIdentifier(actionMarketing.getIdentifier());
				Set<ProspectDto> prospects = new HashSet<>();

				if (actionMarketing.getProspects() != null && !(actionMarketing.getProspects().isEmpty())) {
					for (Prospect prospect : actionMarketing.getProspects()) {
						ProspectDto prospectDto = new ProspectDto();
						prospectDto.setId(prospect.getId());
						prospectDto.setFirstName(prospect.getFirstName());
						prospectDto.setLastName(prospect.getLastName());
						prospects.add(prospectDto);
					}
				}
				actionMarketingDto.setProspects(prospects);

				Set<ProductDto> products = new HashSet<>();
				if (actionMarketing.getProducts() != null && !(actionMarketing.getProducts().isEmpty())) {
					for (Product product : actionMarketing.getProducts()) {
						ProductDto productDto = new ProductDto();
						productDto.setId(product.getId());
						productDto.setName(product.getName());
						products.add(productDto);
					}
				}
				actionMarketingDto.setProducts(products);
				actionMarketingListDtos.add(actionMarketingDto);
			}
		}
		return actionMarketingListDtos;

	}

	@Override
	public void delete(Long id) throws BirdnotesException{
		validationStatusRepository.deleteByActionMarketingId(id);
		actionMarketingRepository.deleteById(id);

	}

	@Override
	public ActionMarketing saveMarketingAction(ActionMarketingDto actionMarketingDto) throws BirdnotesException {

		if (actionMarketingDto.getId() == null || actionMarketingDto == null) {
			throw new RuntimeException("actionMarketingDto est null");
		}

		if (actionMarketingDto.getName() == null || actionMarketingDto.getName().isEmpty()) {
			throw new BirdnotesException("nom du action marketing est vide");
		}

		if (actionMarketingDto.getBudget() == null) {
			throw new BirdnotesException("Budget d'action marketing est vide");
		}

		ActionMarketing actionMarketing = null;
		if(actionMarketingDto.getId() != null) {
			actionMarketing = actionMarketingRepository.findOne(actionMarketingDto.getId());
				
		}
		if (actionMarketing == null) {
			actionMarketing = new ActionMarketing();			
		}
		actionMarketing.setName(actionMarketingDto.getName());
		actionMarketing.setStatus(UserValidationStatus.ACCEPTED);
		actionMarketing.setDate(actionMarketingDto.getDate());
		actionMarketing.setBudget(actionMarketingDto.getBudget());
		actionMarketing.setDescription(actionMarketingDto.getDescription());
		Set<Prospect> prospects = prospectRepository.findProspectsByIds(actionMarketingDto.getProspectIds());
		actionMarketing.setProspects(prospects);
		Set<Product> products = productRepository.findProducts(actionMarketingDto.getProductIds());
		actionMarketing.setProducts(products);
	    User user = userRepository.findOne(actionMarketingDto.getUserId());
	    actionMarketing.setUser(user);
	    MarketingActionType marketingActionType = marketingActionTypeRepository.findOne(actionMarketingDto.getMarketingActionTypeId());
	    actionMarketing.setMarketingActionType(marketingActionType);
	    


		ActionMarketing savedActionMarketing = actionMarketingRepository.save(actionMarketing);
		return savedActionMarketing;
	}

	public void acceptValidationStep(Long validationStatusId) throws BirdnotesException {
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		List<ValidationStatus> allValidationStatus =validationStatusRepository.findByActionMarketingOrderByRankAsc(userValidationStatus.getActionMarketing());	
		
		
		boolean isWorkflowFinished = this.validationStepService.accept(allValidationStatus, validationStatusId, userValidationStatus.getActionMarketing().getId());
		
		if(isWorkflowFinished) {
			
			 userValidationStatus.getActionMarketing().setStatus(UserValidationStatus.ACCEPTED);
			 actionMarketingRepository.save( userValidationStatus.getActionMarketing());
			 notificationService.sendSingleNotification( userValidationStatus.getActionMarketing().getUser(), null, "actionMarketingValidationNotificationMessage");
			
		}else {
			
			 userValidationStatus.getActionMarketing().setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			 actionMarketingRepository.save( userValidationStatus.getActionMarketing());
		}	
				
	}

	@Override
	public void refuseValidationStep(Long validationStatusId) throws BirdnotesException {
		this.validationStepService.refuse(validationStatusId);
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		userValidationStatus.getActionMarketing().setStatus(UserValidationStatus.REFUSED);
		actionMarketingRepository.save(userValidationStatus.getActionMarketing());
		notificationService.sendSingleNotification(userValidationStatus.getActionMarketing().getUser(), null, "actionMarketingRefusNotificationMessage");
	}

	@Override
	public List<LabelValueDto> getMarketingExpensesByDelegate(Date startDate, Date endDate) {
		return actionMarketingRepository.getMarketingExpensesByDelegate(startDate, endDate);
	}

	@Override
	public ActionMarketingDto getActionMarketingById(long id)  throws BirdnotesException{
		ActionMarketing actionMarketing = actionMarketingRepository.findById(id);
		ActionMarketingDto actionMarketingDto = new ActionMarketingDto();
		actionMarketingDto.setId(actionMarketing.getId());
		actionMarketingDto.setName(actionMarketing.getName());
		actionMarketingDto.setBudget(actionMarketing.getBudget());
		actionMarketingDto.setDate(actionMarketing.getDate());
		actionMarketingDto.setStatus(actionMarketing.getStatus().toString());
		actionMarketingDto.setDescription(actionMarketing.getDescription());
		Delegate delegate = delegateRepository.findDelegateByUserId(actionMarketing.getUser().getId());
		String firstName = delegate.getFirstName();
		String lastName = delegate.getLastName();
		String userName = firstName + " " + lastName;
		actionMarketingDto.setUserName(userName);
		List<ValidationStatusDto> listValidationStatusDto = validationStepService.findByActionMarketing(actionMarketing);
		actionMarketingDto.setValidationStatusDto(listValidationStatusDto);
		Set<ProspectDto> prospects = new HashSet<>();

		if (actionMarketing.getProspects() != null && !(actionMarketing.getProspects().isEmpty())) {
			for (Prospect prospect : actionMarketing.getProspects()) {
				ProspectDto prospectDto = new ProspectDto();
				prospectDto.setId(prospect.getId());
				prospectDto.setFirstName(prospect.getFirstName());
				prospectDto.setLastName(prospect.getLastName());
				prospects.add(prospectDto);
			}
		}
		actionMarketingDto.setProspects(prospects);

		Set<ProductDto> products = new HashSet<>();
		if (actionMarketing.getProducts() != null && !(actionMarketing.getProducts().isEmpty())) {
			for (Product product : actionMarketing.getProducts()) {
				ProductDto productDto = new ProductDto();
				productDto.setId(product.getId());
				productDto.setName(product.getName());
				products.add(productDto);
			}
		}
		actionMarketingDto.setProducts(products);
		return actionMarketingDto;

	}
	
    public ActionMarketingFormDto loadFormData(int page, int size) throws BirdnotesException {
        ActionMarketingFormDto marketingActionDto = new ActionMarketingFormDto();

        List<LabelValueDto> products = productRepository.getAllProductIdAndName();
        marketingActionDto.setProducts(products);

        Pageable pageable = new PageRequest(page, size);
        List<LabelValueDto> prospects = prospectRepository.getAllProspectIdAndName(pageable);
        marketingActionDto.setProspects(prospects);

        List<MarketingActionTypeDto> marketingActionTypes = marketingActionTypeService.findAll();
        marketingActionDto.setMarketingActionTypes(marketingActionTypes);

        List<UserDto> users = userService.getSubUsers();
        marketingActionDto.setUsers(users);

        return marketingActionDto;
    }



    @Override
    public Long predictMarketingActionOrder(MarketingActionOrderPredictionDto predictionDto) throws BirdnotesException {
        Configuration config = configureRepository.findById(1);
        if (config.getMlServerUrl() != null && !config.getMlServerUrl().isEmpty()) {
            try {
                URI uri = new URI(config.getMlServerUrl() + "/marketing_action_prediction/");
                HttpHeaders headers = new HttpHeaders();
                headers.set("Authorization", "Bearer " + mlServerToken);
                headers.set("Content-Type", "application/json");

                OrdersPredictionRequest ordersPredictionDto = new OrdersPredictionRequest();
                ordersPredictionDto.setLabName(config.getName().toLowerCase());
                ordersPredictionDto.setModel("best");

                ProspectFeaturesDto prospectFeaturesDto = new ProspectFeaturesDto();

                Prospect prospect = prospectRepository.findById(predictionDto.getProspectId());
                Product product = productRepository.findById(predictionDto.getProductId());
                MarketingActionType marketingActionType = marketingActionTypeRepository.findOne(predictionDto.getMarketingActionTypeId());

                Date visitDate = predictionDto.getDate();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(visitDate);
                int month = calendar.get(Calendar.MONTH) + 1;
                Double visitMonth = (double) month;
                
                prospectFeaturesDto.setVisitMonth(visitMonth);
                prospectFeaturesDto.setBudget(predictionDto.getBudget());
                prospectFeaturesDto.setActivity(prospect.getActivity());
                prospectFeaturesDto.setPotential(prospect.getPotential().getWeight());
                prospectFeaturesDto.setProduct(product.getName());
                prospectFeaturesDto.setSpeciality(prospect.getSpeciality().getName());
                prospectFeaturesDto.setLocality(prospect.getLocality().getName());
                prospectFeaturesDto.setMarketingActionType(marketingActionType.getName());
                prospectFeaturesDto.setVisitCount(predictionDto.getVisitCount());

                List<ProspectFeaturesDto> featuresList = Collections.singletonList(prospectFeaturesDto);
                ordersPredictionDto.setFeatures(featuresList);

                HttpEntity<OrdersPredictionRequest> request = new HttpEntity<>(ordersPredictionDto, headers);

                ResponseEntity<PredictionResponse> response = restTemplate.postForEntity(uri, request, PredictionResponse.class);
                ProspectOrderPredictionResponse prediction = response.getBody().getResults().get(0);

                return prediction.getOrderQuantity();

            } catch (URISyntaxException e) {
                log.error("Failed to create URI for ML server prediction: {}", config.getMlServerUrl(), e);
                throw new BirdnotesException("Failed to create URI for ML server prediction");
            } catch (RestClientException e) {
                log.error("Failed to make prediction request to ML server: {}", config.getMlServerUrl(), e);
                throw new BirdnotesException("Failed to make prediction request to ML server");
            }
        } else {
            throw new BirdnotesException("ML Server URL is undefined");
        }
    }
}
    
    
    
    
    




	
	



