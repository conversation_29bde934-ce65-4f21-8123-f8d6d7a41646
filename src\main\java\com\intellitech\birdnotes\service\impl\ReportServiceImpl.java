package com.intellitech.birdnotes.service.impl;


import java.util.Date;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.intellitech.birdnotes.dao.impl.ReportDynamicQueriesImpl;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.data.dto.ProspectFilterDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.convertor.ConvertLocalityToDto;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.service.ReportService;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.UserRepository;

@Service("reportService")
@Transactional
public class ReportServiceImpl implements ReportService{

	
	private VisitsProductsRepository visitsProductsRepository;
	private LocalityRepository localityRepository;
	private ConvertLocalityToDto convertLocalityToDto;
	private ConvertProspectToDto convertProspectToDto;
	private ReportDynamicQueriesImpl reportDynamicQueriesImpl;
	private ProspectRepository prospectRepository;
	private UserRepository userRepository;
	private VisitRepository visitRepository;
	private CurrentUser currentUser;
	private ProductToDtoConvertor productToDtoConvertor;
	
	
		
	@Autowired
	public ReportServiceImpl(VisitsProductsRepository visitsProductsRepository, LocalityRepository localityRepository,
			ConvertLocalityToDto convertLocalityToDto, ConvertProspectToDto convertProspectToDto,
			ReportDynamicQueriesImpl reportDynamicQueriesImpl, ProspectRepository prospectRepository,
			UserRepository userRepository, VisitRepository visitRepository, CurrentUser currentUser,
			ProductToDtoConvertor productToDtoConvertor) {
		super();
		this.visitsProductsRepository = visitsProductsRepository;
		this.localityRepository = localityRepository;
		this.convertLocalityToDto = convertLocalityToDto;
		this.convertProspectToDto = convertProspectToDto;
		this.reportDynamicQueriesImpl = reportDynamicQueriesImpl;
		this.prospectRepository = prospectRepository;
		this.userRepository = userRepository;
		this.visitRepository = visitRepository;
		this.currentUser = currentUser;
		this.productToDtoConvertor = productToDtoConvertor;
	}
	


	@Override
	public List<LocalityDto> findLocalityBySectorId(Long sectorId) throws BirdnotesException {
		List<LocalityDto> localityDto = new ArrayList<>();
		List<Locality> allLocalities = localityRepository.findBySectorId(sectorId);
		for (Locality locality : allLocalities) {
			localityDto.add(convertLocalityToDto.convert(locality));
		}
		return localityDto;
	}




	@Override
	public List<ProspectDto> findProspectBySectorIdAndPotentialIdAndActivityId(ProspectFilterDto prospectFilterDto) throws BirdnotesException {
		List<Prospect> prospects;
		List<ProspectDto> prospectsDto = new ArrayList<>();
		StringBuilder query = new StringBuilder();
		StringBuilder where = new StringBuilder();
		Map<String, Object> parameters = new HashMap<>();
		query.append(
				"SELECT pp from Prospect pp ");
		
		if(prospectFilterDto.getSpecialityId() !=0 ) {
			where.append("  pp.speciality.id=:specialtyId ");
			parameters.put("specialtyId", prospectFilterDto.getSpecialityId());
			
		}
		if(prospectFilterDto.getPotentialId()!=0) {
			if(!where.toString().isEmpty()) {
				where.append(" AND " );	
			}
			where.append("  pp.potential.id=:potentialId ");
			parameters.put("potentialId", prospectFilterDto.getPotentialId());
		}
		if(prospectFilterDto.getSectorId()!= 0) {
			if(!where.toString().isEmpty()) {
				where.append(" AND " );	
			}
			where.append(" pp.sector.id=:sectorId ");
			parameters.put("sectorId", prospectFilterDto.getSectorId());
			
		}
		if(prospectFilterDto.getLocalityId()!= 0) {
			if(!where.toString().isEmpty()) {
				where.append(" AND " );	
			}
			where.append(" pp.locality.id=:localityId ");
			parameters.put("localityId", prospectFilterDto.getLocalityId());
		}
		if(prospectFilterDto.getActivity()!= null) {
			if(!where.toString().isEmpty()) {
				where.append(" AND " );	
			}
			where.append(" pp.activity=:activity ");
			parameters.put("activity", prospectFilterDto.getActivity());
		}
		if(!where.toString().isEmpty()) {
			query.append(" WHERE ").append(where);
		}
		prospects = reportDynamicQueriesImpl.findProspect(query.toString(), parameters);
		for(Prospect prospect:prospects) {
			prospectsDto.add(convertProspectToDto.convert(prospect));
		}			

		return prospectsDto;
	}

	
	@Override
	public List<ProductDto> getProductsHistoryForProspect(Long prospectId,Date date) throws BirdnotesException {
		List<ProductDto> productsDto = new ArrayList<>();
		List<Visit> visits = visitRepository.findByVisitDateAndProspectId(date, prospectId);
		
		if(visits != null && !visits.isEmpty()) {
			for(Visit visit : visits) {
				List<VisitsProducts> visitsProducts = visitsProductsRepository.findAllByVisitId(visit.getId());
				for(VisitsProducts visitProducts:visitsProducts) {
					Product product = visitProducts.getProduct();
					productsDto.add(productToDtoConvertor.convert(product));
				}
			}
		}
		
		return productsDto;
	}


	@Override
	public Visit addVisit(Visit visit) throws BirdnotesException {
		
		return visitRepository.save(visit);
	}

	@Override
	public VisitsProducts addVisitsProducts(VisitsProducts visitsProducts) throws BirdnotesException {
		
		return visitsProductsRepository.save(visitsProducts);
	}

		

	@Override
	public List<ProspectDto> getVisit(Date date) throws BirdnotesException {

		List<Visit> visits = visitRepository.findByVisitDate(date);
		List<ProspectDto> prospectsDto = new ArrayList<>();
		
		for(Visit visit:visits) {
			Prospect prospect = visit.getProspect();
			prospectsDto.add(convertProspectToDto.convert(prospect));
		}
		
		
		return prospectsDto;
		
			
	}

	
	@Override
	public List<ProspectDto> findAllProspect() throws BirdnotesException {
		
		List<ProspectDto> back = new ArrayList<>();

		
		List<Prospect> listOfProspects = prospectRepository.findByUser(24L);
		
		if (listOfProspects != null && !listOfProspects.isEmpty()) {

			for (Prospect prospect : listOfProspects) {
				
				back.add(convertProspectToDto.convert(prospect));
			}
		}
		return back;
	}

}
