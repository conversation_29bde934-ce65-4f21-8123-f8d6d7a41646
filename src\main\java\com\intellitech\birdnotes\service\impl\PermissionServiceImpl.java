package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.transaction.Transactional;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Module;
import com.intellitech.birdnotes.model.ModulePackage;
import com.intellitech.birdnotes.model.ModulePermissions;
import com.intellitech.birdnotes.model.Packages;
import com.intellitech.birdnotes.model.Permission;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.RolePermission;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.RolePermissionRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.service.PermissionService;

@Service("permissionService")
@Transactional
public class PermissionServiceImpl implements PermissionService {
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	/*
	 * @Autowired ResourceLoader resourceLoader;
	 * 
	 * @Autowired RoleRepository roleRepository;
	 * 
	 * @Autowired RolePermissionRepository rolePermissionRepository;
	 */

	// Ajouté pour le test
	private final ResourceLoader resourceLoader;
	private final RoleRepository roleRepository;
	private final RolePermissionRepository rolePermissionRepository;
	private final ConfigurationRepository configurationRepository;

	// Ajouté pour le test
	public PermissionServiceImpl(ResourceLoader resourceLoader, RoleRepository roleRepository,
			RolePermissionRepository rolePermissionRepository, ConfigurationRepository configurationRepository) {
		super();
		this.resourceLoader = resourceLoader;
		this.roleRepository = roleRepository;
		this.rolePermissionRepository = rolePermissionRepository;
		this.configurationRepository = configurationRepository;
	}

	@Override
	public List<Module> getXml() throws IOException {
		try {
			Resource resource = resourceLoader.getResource("classpath:permissions.xml");

			InputStream input = resource.getInputStream();

			File file = resource.getFile();
			JAXBContext jaxbContext = JAXBContext.newInstance(ModulePermissions.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			ModulePermissions modulePermissions = (ModulePermissions) jaxbUnmarshaller.unmarshal(file);

			List<Role> roles = roleRepository.findAll();
			Configuration configuration = configurationRepository.findById(1);
			List<Module> activeModules = getActiveModules(configuration.getPackageName());
			for (Module modulePermission : modulePermissions.getModule()) {
				for (Module activeModule : activeModules) {
					if(modulePermission.getLabel().equals(activeModule.getLabel())){
						List<Permission> permissions = modulePermission.getPermissions();
						for (Permission perms : permissions) {
							List<RoleDto> rolesDto = new ArrayList<>();
							List<RolePermission> rolePermissions = rolePermissionRepository.findByPermissionName(perms.getName());
							for (Role role : roles) {
								RoleDto roleDto = new RoleDto();
								roleDto.setId(role.getId());
								roleDto.setName(role.getName());
								for (RolePermission rolePerm : rolePermissions) {
	
									if ((role.getId()).equals(rolePerm.getRole().getId())) {
										roleDto.setChecked(true);
										break;
									}
								}
								rolesDto.add(roleDto);
							}
							perms.setRolesDto(rolesDto);
						}
						activeModule.setPermissions(permissions);
					}
				}
			}
			return activeModules;

		} catch (Exception e) {
			log.error("error in getXml " , e);
			return new ArrayList<>();
		}

	}

	private List<Module> getActiveModules(String PackageName) {

		try {
			Resource resource = resourceLoader.getResource("classpath:packages.xml");

			InputStream input = resource.getInputStream();

			File file = resource.getFile();
			JAXBContext jaxbContext = JAXBContext.newInstance(Packages.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			Packages packages = (Packages) jaxbUnmarshaller.unmarshal(file);
			for (ModulePackage modulePackage : packages.getModulePackages()) {
				if (modulePackage.getName().equals(PackageName)) {
					return modulePackage.getModule();
				}
			}
			// à voir
			return Collections.emptyList();
		} catch (Exception e) {

			log.error("error in getXml " + e);
			return new ArrayList<>();
		}

	}

	@Override
	public void saveAllRolesPermissions(List<Module> modules) throws BirdnotesException {
		rolePermissionRepository.deleteAll();
		for (Module modls : modules) {
			List<Permission> permissions = modls.getPermissions();
			for (Permission perms : permissions) {
				List<RoleDto> rolesDto = perms.getRolesDto();
				for (RoleDto roledto : rolesDto) {
					Role role = new Role();
					role.setId(roledto.getId());
					role.setName(roledto.getName());
					if (roledto.isChecked() == true) {
						RolePermission rolePerm = new RolePermission(perms.getName(), role);
						rolePermissionRepository.save(rolePerm);
					}

				}

			}
		}

	}

}
