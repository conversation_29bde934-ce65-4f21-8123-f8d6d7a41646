package com.intellitech.birdnotes.controller;

import java.util.Collections;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.AutomationRuleFormData;
import com.intellitech.birdnotes.data.dto.GoalFormData;
import com.intellitech.birdnotes.data.dto.GoalsRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.RangeDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.service.GoalManagementService;
import com.intellitech.birdnotes.service.GoalsService;
import com.intellitech.birdnotes.service.PotentialService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.RangeService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@RestController
@RequestMapping("/goal")
public class GoalsController {

	private static final Logger LOG = LoggerFactory.getLogger(GoalsController.class);
	@Autowired
	private RangeService rangeService;
	@Autowired
	private GoalsService goalService;
	@Autowired
	private ProductService productService;
	@Autowired
	private PotentialService potentialService;
	@Autowired
	UserService userService;
	@Autowired
	private SpecialityService specialityService;
	@Autowired
	private SectorService sectorService;
	@Autowired
	private ProspectTypeService prospectTypeService;
	@Autowired
	private GoalManagementService goalManagementService;

	/*
	 * @RequestMapping(value = "saveGoal", method = RequestMethod.POST) public
	 * ResponseEntity<String> saveGoal(@RequestBody GoalsRequestDto goalsRequestDto)
	 * {
	 * 
	 * try { boolean productSaved =
	 * goalService.saveGoalsByPotentials(goalsRequestDto); if (productSaved) {
	 * return new ResponseEntity<>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
	 * } return new ResponseEntity<>("", HttpStatus.CONFLICT);
	 * 
	 * } catch (BirdnotesException fe) { LOG.
	 * error("An exception occurred :Non-Authoritative Information when save product"
	 * , fe); return new ResponseEntity<>(fe.getMessage(),
	 * HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	 * 
	 * } catch (Exception e) { LOG.error("Error in saveProduct", e); return new
	 * ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED); } }
	 */

	@RequestMapping(value = "/update", method = RequestMethod.PUT)
	public ResponseEntity<String> updateGaol(@RequestBody GoalDto goalManagementDto) {
		try {
			if (userService.checkHasPermission("GOAL_EDIT")) {
				goalManagementService.update(goalManagementDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when update goal", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while updating goal", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteGoal(@PathVariable("id") Long id) {

		try {
			if (userService.checkHasPermission("GOAL_DELETE")) {
				goalManagementService.delete(id);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.LOCALITY_TO_DELETE_ALREADY_DELETED,
						HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		
		} catch (Exception e) {
			LOG.error("An exception occurred while deleting the goal with id =" + id, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getGoal/{id}", method = RequestMethod.GET)
	public ResponseEntity<GoalDto> getGoal(@PathVariable("id") Long id) {

		try {
			GoalDto goalManagementDto = goalManagementService.getGoal(id);
			return new ResponseEntity<>(goalManagementDto, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting goal", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findAll", method = RequestMethod.GET)
	public ResponseEntity<List<GoalDto>> findAllGoal() {

		try {
			if (userService.checkHasPermission("GOAL_VIEW")) {
				List<GoalDto> goalsManagementDto = goalManagementService.findAll();
				return new ResponseEntity<>(goalsManagementDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all goals", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveGoal", method = RequestMethod.POST)
	public ResponseEntity<String> saveGoal(@RequestBody GoalRequest goalRequest) {

	    try {
	        if (userService.checkHasPermission("GOAL_ADD")) {
	            Goal savedGoal = goalManagementService.saveGoal(goalRequest);
	            if (savedGoal != null) {
	                return new ResponseEntity<>(savedGoal.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving saveGoal", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving saveGoal", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "/getAllDataGoalForm", method = RequestMethod.GET)
	public ResponseEntity<GoalFormData> getAllDataFromAutomationsRule() {
		try {
			GoalFormData result = goalManagementService.getAllDataGoalForm();
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	@RequestMapping(value = "findAllRanges", method = RequestMethod.GET)
	public ResponseEntity<List<RangeDto>> findAllGamme() {

		try {
			if (userService.checkHasPermission("GOAL_VIEW")) {
				List<RangeDto> rangeDto = rangeService.findAll();
				return new ResponseEntity<>(rangeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all gamme", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/findProductsByRange/{rangeIds}", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> findProductsByGamme(@PathVariable("rangeIds") List<Integer> rangeIds) {
		try {
			if (userService.checkHasPermission("GOAL_VIEW")) {
				List<ProductDto> productDtos = productService.findProductsByGamme(rangeIds);
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting products by gamme", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "findAllPotentials", method = RequestMethod.GET)
	public ResponseEntity<List<PotentialDto>> findAllPotentials() {

		try {
			if (userService.checkHasPermission("GOAL_VIEW")) {
				List<PotentialDto> potentialsDto = potentialService.findAll();
				return new ResponseEntity<>(potentialsDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all potentials", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllUsers", method = RequestMethod.GET)
	public ResponseEntity<List<UserDto>> getAllUsersWithRoles() {
		try {

			if (userService.checkHasPermission("GOAL_VIEW") || userService.checkHasPermission("GOAL_ADD")
					|| userService.checkHasPermission("GOAL_EDIT")) {
				List<UserDto> userDtos = userService.getAllUsers();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	

	@RequestMapping(value = "getAllDelegatesWithoutGoal", method = RequestMethod.POST)
	public ResponseEntity<List<UserDto>> findAllDelegatesWithoutGoal(@RequestBody GoalRequest goalsRequestDto) {
		try {
			if (userService.checkHasPermission("GOAL_VIEW")) {
				List<UserDto> userDtos = userService.findAllDelegatesWithoutGoal(goalsRequestDto);
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegatesWithoutGoal", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/findAllProspectTypes", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectTypeDto>> findAllProspectTypes() {
		try {
			if (userService.checkHasPermission("GOAL_VIEW") || userService.checkHasPermission("GOAL_ADD")
					|| userService.checkHasPermission("GOAL_EDIT")) {
				List<ProspectTypeDto> prospectTypeDto = prospectTypeService.findAll();
				return new ResponseEntity<>(prospectTypeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all prospect types", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("GOAL_VIEW") || userService.checkHasPermission("GOAL_ADD")
					|| userService.checkHasPermission("GOAL_EDIT")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/findAllspecialities", method = RequestMethod.GET)
	public ResponseEntity<List<SpecialityDto>> findAllSpecialities() {
		try {
			if (userService.checkHasPermission("GOAL_VIEW") || userService.checkHasPermission("GOAL_ADD")
					|| userService.checkHasPermission("GOAL_EDIT")) {
				List<SpecialityDto> specialityDtos = specialityService.findAll();
				return new ResponseEntity<>(specialityDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all specialities", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findAllsectors", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> findallSectors() {
		try {
			if (userService.checkHasPermission("GOAL_VIEW") || userService.checkHasPermission("GOAL_ADD")
					|| userService.checkHasPermission("GOAL_EDIT")) {
				List<SectorDto> sectorDtos = sectorService.findAll();
				return new ResponseEntity<>(sectorDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
