package com.intellitech.birdnotes.model.dto;

public class VisitKey {
	
	private String visitDate;
	private Long delegateId ;
	private Long prospectId;
	private Long sectorId;
	private Long localityId;
	private Long potentialId;
	private String activity;
	private Long productId;
	private Long specialityId;
	public VisitKey(String visitDate, Long delegateId, Long prospectId) {
		super();
		this.visitDate = visitDate;
		this.delegateId = delegateId;
		this.prospectId = prospectId;
	}
	
	public VisitKey(String visitDate, Long delegateId, Long prospectId, Long sectorId, Long localityId,
			Long potentialId, String activity, Long productId, Long specialityId) {
		super();
		this.visitDate = visitDate;
		this.delegateId = delegateId;
		this.prospectId = prospectId;
		this.sectorId = sectorId;
		this.localityId = localityId;
		this.potentialId = potentialId;
		this.activity = activity;
		this.productId = productId;
		this.specialityId = specialityId;
	}

	public VisitKey() {
		super();
	}
	public String getVisitDate() {
		return visitDate;
	}
	public void setVisitDate(String visitDate) {
		this.visitDate = visitDate;
	}
	public Long getDelegateId() {
		return delegateId;
	}
	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}
	public Long getProspectId() {
		return prospectId;
	}
	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}
	
	public Long getSectorId() {
		return sectorId;
	}
	public void setSectorId(Long sectorId) {
		this.sectorId = sectorId;
	}
	
	public Long getLocalityId() {
		return localityId;
	}
	public void setLocalityId(Long localityId) {
		this.localityId = localityId;
	}
	public Long getPotentialId() {
		return potentialId;
	}
	public void setPotentialId(Long potentialId) {
		this.potentialId = potentialId;
	}

	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}

	public Long getProductId() {
		return productId;
	}
	public void setProductId(Long productId) {
		this.productId = productId;
	}
	public Long getSpecialityId() {
		return specialityId;
	}
	public void setSpecialityId(Long specialityId) {
		this.specialityId = specialityId;
	}
	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((delegateId == null) ? 0 : delegateId.hashCode());
		result = prime * result + ((prospectId == null) ? 0 : prospectId.hashCode());
		result = prime * result + ((visitDate == null) ? 0 : visitDate.hashCode());
		return result;
	}
	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		VisitKey other = (VisitKey) obj;
		if (delegateId == null) {
			if (other.delegateId != null)
				return false;
		} else if (!delegateId.equals(other.delegateId))
			return false;
		if (prospectId == null) {
			if (other.prospectId != null)
				return false;
		} else if (!prospectId.equals(other.prospectId))
			return false;
		if (visitDate == null) {
			if (other.visitDate != null)
				return false;
		} else if (!visitDate.equals(other.visitDate))
			return false;
		return true;
	}
	
	
	

}
