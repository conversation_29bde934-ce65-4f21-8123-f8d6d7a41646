package com.intellitech.birdnotes.data.dto;

import java.util.List;

import com.intellitech.birdnotes.model.Criteria;
import com.intellitech.birdnotes.model.dto.ProductDto;

public class SurveyFormData {
	private List<ProductDto> products;
	private List<Criteria> criterias;
	private String criteriaFileName;

	public List<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(List<ProductDto> products) {
		this.products = products;
	}

	public List<Criteria> getCriterias() {
		return criterias;
	}

	public void setCriterias(List<Criteria> criterias) {
		this.criterias = criterias;
	}

	public String getCriteriaFileName() {
		return criteriaFileName;
	}

	public void setCriteriaFileName(String criteriaFileName) {
		this.criteriaFileName = criteriaFileName;
	}

}
