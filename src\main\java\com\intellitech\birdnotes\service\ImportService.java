package com.intellitech.birdnotes.service;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.poi.ss.usermodel.Sheet;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.UserDto;

public interface ImportService {
	void importProspect(String filePath) throws BirdnotesException;

	
	Map<String, List<PlanningDto>> getPlanningData(String filePath, Long userId) throws ParseException, IOException;


	Map<String, List<ProductDto>> getProductData(String filePath) throws IOException, ParseException;


	Map<String, List<UserDto>> getUserData(String filePath) throws ParseException, IOException;


	Map<String, List<DelegateDto>> getDelegateData(String filePath) throws ParseException, IOException;



	
	}
