package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.PROSPECT_ACTIVITY , schema = Common.PUBLIC_SCHEMA)
public class ProspectActivity implements Serializable {
	
	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.PROSPECT_ACTIVITY_SEQUENCE, sequenceName = Sequences.PROSPECT_ACTIVITY_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PROSPECT_ACTIVITY_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.NAME)
	private String name;
	
	
	public ProspectActivity(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
		
	}
	

	public ProspectActivity() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	

}
