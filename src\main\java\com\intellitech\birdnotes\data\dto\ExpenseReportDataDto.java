package com.intellitech.birdnotes.data.dto;

import java.text.SimpleDateFormat;

public class ExpenseReportDataDto {
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");

	private Long id;
	private String startDate;
	private String endDate;
	private String delegateName;

	public ExpenseReportDataDto(Long id, String startDate, String endDate, String delegateName) {
		super();
		this.id = id;
		this.startDate = format.format(startDate);
		this.endDate = format.format(endDate);
		this.delegateName = delegateName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;
	}

	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

}
