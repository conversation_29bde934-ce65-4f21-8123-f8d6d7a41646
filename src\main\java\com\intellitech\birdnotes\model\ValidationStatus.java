package com.intellitech.birdnotes.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.VALIDATION_STATUS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class ValidationStatus {
	@Id
	@SequenceGenerator(name = Sequences.VALIDATION_STATUS_SEQUENCE, sequenceName = Sequences.VALIDATION_STATUS_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.VALIDATION_STATUS_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;
	
	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	private UserValidationStatus status;
	
	@Column(name = Columns.VALIDATION_STATUS_DATE)
	private Date creationDate;
	
	@Column(name = Columns.RANK)
	private Integer rank ;
	
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.VALIDATION_STEP_ID)
	private ValidationStep validationStep;
	
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;
    
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.ACTION_MARKETING_ID)
	private ActionMarketing actionMarketing;

    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID)
	private Prospect prospect;
    
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.NOTEFRAIS_ID)
	private ExpenseReport noteFrais;
    
    @ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_COMMISSION_ID)
	private DelegateCommission delegateCommission;
    
    @ManyToOne
 	@JoinColumn(name = BirdnotesConstants.Columns.PLANNING_VALIDATION_ID)
 	private PlanningValidation planningValidation;
    
	public ValidationStatus() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Integer getRank() {
		return rank;
	}

	public void setRank(Integer rank) {
		this.rank = rank;
	}

	public ValidationStep getValidationStep() {
		return validationStep;
	}

	public void setValidationStep(ValidationStep validationStep) {
		this.validationStep = validationStep;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public ActionMarketing getActionMarketing() {
		return actionMarketing;
	}

	public void setActionMarketing(ActionMarketing actionMarketing) {
		this.actionMarketing = actionMarketing;
	}

	public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

	public ExpenseReport getNoteFrais() {
		return noteFrais;
	}

	public void setNoteFrais(ExpenseReport noteFrais) {
		this.noteFrais = noteFrais;
	}

	public PlanningValidation getPlanningValidation() {
		return planningValidation;
	}

	public void setPlanningValidation(PlanningValidation planningValidation) {
		this.planningValidation = planningValidation;
	}

	public DelegateCommission getDelegateCommission() {
		return delegateCommission;
	}

	public void setDelegateCommission(DelegateCommission delegateCommission) {
		this.delegateCommission = delegateCommission;
	}   
    
}
