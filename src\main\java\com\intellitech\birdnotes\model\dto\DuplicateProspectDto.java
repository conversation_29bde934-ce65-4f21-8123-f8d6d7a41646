package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class DuplicateProspectDto {
private ProspectDto prospectDto;
private List<ProspectDto> similarProspectDtos;
public DuplicateProspectDto(ProspectDto prospectDto, List<ProspectDto> similarProspectDtos) {
	super();
	this.prospectDto = prospectDto;
	this.similarProspectDtos = similarProspectDtos;
}
public DuplicateProspectDto() {
	super();
}

public ProspectDto getProspectDto() {
	return prospectDto;
}
public void setProspect(ProspectDto prospectDto) {
	this.prospectDto = prospectDto;
}
public List<ProspectDto> getSimilarProspectDtos() {
	return similarProspectDtos;
}
public void setSimilarProspectDtos(List<ProspectDto> similarProspectDtos) {
	this.similarProspectDtos = similarProspectDtos;
}
@Override
public int hashCode() {
	final int prime = 31;
	int result = 1;
	result = prime * result + ((prospectDto == null) ? 0 : prospectDto.hashCode());
	result = prime * result + ((similarProspectDtos == null) ? 0 : similarProspectDtos.hashCode());
	return result;
}
@Override
public boolean equals(Object obj) {
	if (this == obj)
		return true;
	if (obj == null)
		return false;
	if (getClass() != obj.getClass())
		return false;
	DuplicateProspectDto other = (DuplicateProspectDto) obj;
	if (prospectDto == null) {
		if (other.prospectDto != null)
			return false;
	} else if (!prospectDto.equals(other.prospectDto))
		return false;
	if (similarProspectDtos == null) {
		if (other.similarProspectDtos != null)
			return false;
	} else if (!similarProspectDtos.equals(other.similarProspectDtos))
		return false;
	return true;
}



}
