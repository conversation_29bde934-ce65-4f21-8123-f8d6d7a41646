package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.PLANNING_VALIDATION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class PlanningValidation implements Serializable {
	
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long identifier;
	private Delegate delegate;
	private Date date;
    private String notes;
    private UserValidationStatus status;
    private List<ValidationStatus> validationStatus;
    private boolean specificWeek;
    private Integer weekNumber;
    
    @Id
	@SequenceGenerator(name = Sequences.PLANNIG_VALIDATION_SEQUENCE, sequenceName = Sequences.PLANNIG_VALIDATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PLANNIG_VALIDATION_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	@Column(name = Columns.IDENTIFIER)
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}
	
	@Column(name = Columns.WEEK_NUMBER)
	public Integer getWeekNumber() {
		return weekNumber;
	}

	public void setWeekNumber(Integer weekNumber) {
		this.weekNumber = weekNumber;
	}
	
	@ManyToOne(fetch = FetchType.EAGER, optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}
	
	@Column(name = Columns.NOTES)
	public String getNotes() {
		return notes;
	}


	public void setNotes(String notes) {
		this.notes = notes;
	}
	
	
	@Column(name = Columns.SPECIFIC_WEEK)
	public boolean isSpecificWeek() {
		return specificWeek;
	}

	public void setSpecificWeek(boolean specificWeek) {
		this.specificWeek = specificWeek;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = BirdnotesConstants.Columns.PLANNING_DATE)
	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}
	
	@Enumerated(EnumType.STRING)	
	@Column(name = Columns.STATUS)
	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}
   @OneToMany(mappedBy = "planningValidation")
	public List<ValidationStatus> getValidationStatus() {
		return validationStatus;
	}

	public void setValidationStatus(List<ValidationStatus> validationStatus) {
		this.validationStatus = validationStatus;
	}
	
	
	public PlanningValidation() { super();}
    
	
}
