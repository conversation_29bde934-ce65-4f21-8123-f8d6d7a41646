# DATA SOURCE
# DATA SOURCE
spring.datasource.url=******************************************
spring.datasource.username=postgres
spring.datasource.password=intellitech
spring.datasource.driverClassName=org.postgresql.Driver


#upload
uploadUrl=/attachments
uploadPath=/opt/apache-tomcat-9.0.41/webapps/attachments
mobileAppLogsPath=/mobile_logs
logoPath=/logo
userImagePath = /userImagePath
expenseReportPath=/expense_report
missionReportPath=/mission_report
purchaseOrderPath=/purchase_order
generatedDocumentPath = /generated_document
generatedDoPath = /delivery_order
opportunityNotePath=/opportunityNotePath
commentsRatesFilePath=/ml_data/comments_rates.txt



productPath=/product
specialityPath=/speciality
userPath=/user
recoveryPath=/recovery

ml.serverToken=sdfghjkloerdtfyguhiopfghjkl;fghjkl

erp.serverUrl=http://***************:8069
erp.login=<EMAIL>
erp.password=5p9m-qbeb-wvhb
erp.updateFrequency=0 0 1 * * *
