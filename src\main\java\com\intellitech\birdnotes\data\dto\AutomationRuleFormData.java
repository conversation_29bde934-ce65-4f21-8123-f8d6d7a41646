package com.intellitech.birdnotes.data.dto;

import java.util.List;

import com.intellitech.birdnotes.model.Action;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;

public class AutomationRuleFormData {

	private List<Event> events;
	private List<Action> actions;
	private List<ActivityTypeDto> activitiesType;

	public List<Event> getEvents() {
		return events;
	}

	public void setEvents(List<Event> events) {
		this.events = events;
	}

	public List<Action> getActions() {
		return actions;
	}

	public void setActions(List<Action> actions) {
		this.actions = actions;
	}

	public List<ActivityTypeDto> getActivitiesType() {
		return activitiesType;
	}

	public void setActivitiesType(List<ActivityTypeDto> activitiesType) {
		this.activitiesType = activitiesType;
	}

}
