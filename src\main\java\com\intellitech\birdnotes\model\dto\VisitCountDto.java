package com.intellitech.birdnotes.model.dto;

public class VisitCountDto {
	
	private Long userId;
	private String firstName;
	private String lastName;
	private Long countVisit;
	private Long secteurId;
	
	public VisitCountDto() {
		super();
	}

	public VisitCountDto(Long userId, String firstName, String lastName, Long countVisit, Long secteurId) {
		super();
		this.userId = userId;
		this.firstName = firstName;
		this.lastName = lastName;
		this.countVisit = countVisit;
		this.secteurId = secteurId;
	}

	public VisitCountDto(Long userId, String firstName, String lastName, Long countVisit) {
		super();
		this.userId = userId;
		this.firstName = firstName;
		this.lastName = lastName;
		this.countVisit = countVisit;
	}
	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public Long getCountVisit() {
		return countVisit;
	}

	public void setCountVisit(Long countVisit) {
		this.countVisit = countVisit;
	}

	public Long getSecteurId() {
		return secteurId;
	}

	public void setSecteurId(Long secteurId) {
		this.secteurId = secteurId;
	}
	
}
