package com.intellitech.birdnotes.controller;

import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.OrderProductDto;
import com.intellitech.birdnotes.service.VisitsProductsService;

@RestController
@RequestMapping("/productCharts")
public class ProductChartsController {

	private static final Logger LOG = LoggerFactory.getLogger(ProductChartsController.class);
	@Autowired
	private VisitsProductsService visitsProductsService;

	@RequestMapping(value = "averageOrderProduct/{mondayDate}/{productId}", method = RequestMethod.GET)
	public ResponseEntity<List<OrderProductDto>> averageOrderProduct(@PathVariable("mondayDate") Date mondayDate,
			@PathVariable("productId") Long productId) {
		try {
			List<OrderProductDto> result = visitsProductsService.averageOrderProduct(productId, mondayDate);
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("Error in averageOrderProduct", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}

	}
}
