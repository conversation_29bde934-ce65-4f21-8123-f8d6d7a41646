package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;

@Repository
public interface RoleRepository extends JpaRepository<Role, Integer> {

	Role findByName(String name);

	Role findFirstByNameIgnoreCase(String name);

	@Query("Select min(r.rank) from Role r where r.id IN (:roleIds) ")
	 Integer getMinRole(@Param("roleIds") List<Integer> roleIds);
	
	@Query("SELECT r from Role r order by r.rank asc")
	List<Role> findAll();

	@Modifying
	@Query("DELETE FROM Role where id=:id")
	void deleteById(@Param("id") Integer id);
	
	@Query("select min(r.rank) FROM User u join u.roles r where u.id=:id ")
	Integer findRoleByUserId(@Param("id") Long id);
}
