package com.intellitech.birdnotes.service.impl;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.net.URI;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.Locale;

import javax.annotation.PostConstruct;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.CheckedValueDto;
import com.intellitech.birdnotes.data.dto.CommentClassification;
import com.intellitech.birdnotes.data.dto.OrdersPredictionRequest;
import com.intellitech.birdnotes.data.dto.PredictionResponse;
import com.intellitech.birdnotes.data.dto.ProspectFeaturesDto;
import com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.convertor.ConvertPlanifiedProspectToDto;
import com.intellitech.birdnotes.model.convertor.DelegateToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.OrdersPredictionResponse;
import com.intellitech.birdnotes.model.dto.PlanifiedProspectDto;
import com.intellitech.birdnotes.model.dto.PlannedActivityRequest;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.PlanningValidationDto;
import com.intellitech.birdnotes.model.dto.PostponePlanningRequest;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.repository.ActivityRepository;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.PlanningValidationRepository;
import com.intellitech.birdnotes.repository.ProspectOrderPredictionRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ImportService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.PlanningService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.thread.ThreadSendEmail;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

@Service("planningService")
@Transactional
public class PlanningServiceImpl implements PlanningService {

	private PlanningRepository planningRepository;
	private ConvertPlanifiedProspectToDto convertPlanifiedProspectToDto;
	private PlanningValidationRepository planningValidationRepository;
	private UserRepository userRepository;
	private UserToDtoConvertor userToDtoConvertor;
	private DelegateToDtoConvertor delegateToDtoConvertor;
	private CurrentUser currentUser;
	private NotificationService notificationService;
	private NotificationMessageBuilder notificationMessageBuilder;
	private ValidationStepService validationStepService;
	private ValidationStatusRepository validationStatusRepository;
	private VisitRepository visitRepository;
	private UserService userService;
	private ProspectRepository prospectRepository;
	private RestTemplate restTemplate;
	private ProspectOrderPredictionRepository prospectOrderPredictionRepository;
	private ConfigurationRepository configureRepository;
	private static final Logger LOG = LoggerFactory.getLogger(PlanningServiceImpl.class);
	private DelegateRepository delegateRepository;
	private ActivityRepository activityRepository;
	private ConfigurationService configurationService;
	@Autowired
	private JavaMailSender javaMailSender;
	private static final String IMGBALISE = " <br/> <img src=";
	private String pathLogo;
	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${logoPath}")
	private String logoPath;
	

	
	
	
	@PostConstruct
	private void init() {
		ConfigurationDto config = configurationService.findConfiguration();
		if (config != null) {
			pathLogo = config.getBackendUrl() + uploadUrl + logoPath + "/" + config.getLogo();
		}

	}
	
	@Autowired
	public void setConfigurationService(ConfigurationService configurationService) {
		this.configurationService = configurationService;
	}
	

	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}

	@Autowired
	public void setActivityRepository(ActivityRepository activityRepository) {
		this.activityRepository = activityRepository;
	}

	@Value("${ml.serverToken}")
	private String mlServerToken;

	@PersistenceContext
	private EntityManager entityManager;

	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}
	@Autowired
	private MessageSource messageSource;

	@Autowired
	public void setProspectOrderPredictionRepository(
			ProspectOrderPredictionRepository prospectOrderPredictionRepository) {
		this.prospectOrderPredictionRepository = prospectOrderPredictionRepository;
	}

	@Autowired
	public void setProspectRepository(ProspectRepository prospectRepository) {
		this.prospectRepository = prospectRepository;
	}

	@Autowired
	public void setRestTemplate(RestTemplate restTemplate) {
		this.restTemplate = restTemplate;
	}

	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;

	}

	@Autowired
	public void setDelegateToDtoConvertor(DelegateToDtoConvertor delegateToDtoConvertor) {
		this.delegateToDtoConvertor = delegateToDtoConvertor;
	}

	@Autowired
	private ImportService importService;

//	   PRIVATE FINAL IMPORTSERVICE IMPORTSERVICE;
//
//	    @AUTOWIRED
//	    PUBLIC PLANNINGSERVICEIMPL(@LAZY IMPORTSERVICE IMPORTSERVICE) {
//	        THIS.IMPORTSERVICE = IMPORTSERVICE;
//	    }

	@Autowired
	public void setVisitRepository(VisitRepository visitRepository) {
		this.visitRepository = visitRepository;
	}

	@Autowired
	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}

	@Autowired
	public void setUserRepository(UserRepository userRepository) {
		this.userRepository = userRepository;
	}

	@Autowired
	public void setPlanningRepository(PlanningRepository planningRepository) {
		this.planningRepository = planningRepository;
	}

	@Autowired
	private DynamicQueries dynamicQueries;

	@Autowired
	public void setConvertPlanifiedProspectToDto(ConvertPlanifiedProspectToDto convertPlanifiedProspectToDto) {
		this.convertPlanifiedProspectToDto = convertPlanifiedProspectToDto;
	}

	@Autowired
	public void setCurrentUser(CurrentUser currentUser) {
		this.currentUser = currentUser;
	}

	@Autowired
	public void setPlanningValidationRepository(PlanningValidationRepository planningValidationRepository) {
		this.planningValidationRepository = planningValidationRepository;
	}

	@Autowired
	public void setNotificationService(NotificationService notificationService) {
		this.notificationService = notificationService;
	}

	@Autowired
	public void setNotificationMessageBuilder(NotificationMessageBuilder notificationMessageBuilder) {
		this.notificationMessageBuilder = notificationMessageBuilder;
	}

	@Autowired
	public void setValidationStepService(ValidationStepService validationStepService) {
		this.validationStepService = validationStepService;
	}

	@Autowired
	public void setValidationStatusRepository(ValidationStatusRepository validationStatusRepository) {
		this.validationStatusRepository = validationStatusRepository;
	}
	

	

	@Override
	public List<PlanifiedProspectDto> findPlanificationByUserAndDate(Long userId, Date firstDay, Date lastDay)
			throws BirdnotesException {

		if (lastDay == null) {
			lastDay = BirdnotesUtils.addDaysToDate(firstDay, 5);
		}
		List<PlanifiedProspectDto> back = new ArrayList<>();
		List<Planning> planningOfWeek = planningRepository.findPlanificationByUserAndDate(userId, firstDay, lastDay);
		List<Long> visitedProspects = visitRepository.findVisitedProspectByDateAndUser(firstDay, lastDay, userId);
		for (Planning planning : planningOfWeek) {
			PlanifiedProspectDto planningDto = convertPlanifiedProspectToDto.convert(planning);
			if (visitedProspects.contains(planningDto.getId())) {
				planningDto.setProspectVisited(true);
			} else {
				planningDto.setProspectVisited(false);
			}
			back.add(planningDto);
		}
		return back;
	}

	@Override
	public List<Long> findPlanningOfWeeksToShow(Long userId, Date firstDay, List<CheckedValueDto> weekToShow)
			throws BirdnotesException {
		StringBuilder query = new StringBuilder();
		Map<String, Object> parameters = new HashMap<>();
		List<Long> planningIds = new ArrayList<Long>();
		List<CheckedValueDto> checkedWeeks = new ArrayList<>();
		if (weekToShow != null) {
			for (CheckedValueDto week : weekToShow) {
				if (week.getChecked() == true) {
					checkedWeeks.add(week);
				}
			}
		}
		if (checkedWeeks.size() > 0) {
			query.append("SELECT p.prospect.id from Planning p where ");
			for (CheckedValueDto week : checkedWeeks) {
				if (checkedWeeks.indexOf(week) != 0) {
					query.append(" OR ");
				}
				Date firstDayOfWeek = BirdnotesUtils.minusDaysToDate(firstDay, 7 * week.getValue());
				Date lastDayOfWeek = BirdnotesUtils.addDaysToDate(firstDayOfWeek, 5);
				String firstDayOfWeekParam = "firstDayOfWeek" + week.getValue();
				String lastDayOfWeekParam = "lastDayOfWeek" + week.getValue();
				query.append(" (date(p.date) BETWEEN date(:" + firstDayOfWeekParam + ") and date(:" + lastDayOfWeekParam
						+ "))");

				parameters.put(firstDayOfWeekParam, firstDayOfWeek);
				parameters.put(lastDayOfWeekParam, lastDayOfWeek);

			}
			query.append(" and p.delegate.user.id = :userId ");
			parameters.put("userId", userId);

			planningIds = dynamicQueries.findPlanningOfWeeksToShow(query.toString(), parameters);
		}

		return planningIds;
	}

	@Override
	public PlanningValidationDto findPlanningValidationByUserAndDate(Long userId, Date date) throws BirdnotesException {

		PlanningValidationDto planningValidationDto = null;
		PlanningValidation planningValidation = planningValidationRepository.findPlanningValidationByUserAndDate(userId,
				date);
		if (planningValidation != null) {
			planningValidationDto = new PlanningValidationDto();
			planningValidationDto.setStatus(planningValidation.getStatus().toString());
			planningValidationDto.setNotes(planningValidation.getNotes());
			planningValidationDto.setUser(planningValidation.getDelegate().getId());
			planningValidationDto.setDate(date);
			planningValidationDto.setId(planningValidation.getId());
			planningValidationDto.setSpecificWeek(planningValidation.isSpecificWeek());
			planningValidationDto.setWeekNumber(planningValidation.getWeekNumber());
			List<ValidationStatusDto> listValidationStatusDto = validationStepService
					.findByPlanningValidation(planningValidation);
			planningValidationDto.setValidationStatusDto(listValidationStatusDto);
		}

		return planningValidationDto;
	}

	@Override
	public List<PlanningValidationDto> findPlanningValidationByUser(List<Long> planningWaitingValidation, Long UserId)
			throws BirdnotesException {

		LocalDateTime now =  LocalDateTime.now();     //Current Date and Time
		LocalDateTime sameDayNextMonth = now.minusMonths(3);
		Date date = Date.from(sameDayNextMonth.atZone(ZoneId.systemDefault()).toInstant());
		List<PlanningValidationDto> planningValidationDtos = new ArrayList<>();
		if (planningWaitingValidation.size() > 0) {
			List<PlanningValidation> planningValidations = planningValidationRepository
					.findPlanningValidationByUser(date, UserId);
			if (planningValidations != null && !planningValidations.isEmpty()) {
				for (PlanningValidation planningValidation : planningValidations) {
					PlanningValidationDto planningValidationDto = new PlanningValidationDto();
					planningValidationDto.setStatus(planningValidation.getStatus().toString());
					planningValidationDto.setNotes(planningValidation.getNotes());
					planningValidationDto.setUser(planningValidation.getDelegate().getId());
					planningValidationDto.setDate(planningValidation.getDate());
					planningValidationDto.setId(planningValidation.getId());
					planningValidationDto.setIdentifier(planningValidation.getIdentifier());
					planningValidationDtos.add(planningValidationDto);
				}
			}
		}

		return planningValidationDtos;
	}

	@Override
	public boolean delete(Long id) {
		planningValidationRepository.deleteById(id);
		return true;
	}

	@Override
	public boolean deletePlanningByDateAndUser(Long userId, Date firstDay) throws BirdnotesException {

		Date lastDay = BirdnotesUtils.addDaysToDate(firstDay, 5);

		List<Planning> planningOfWeek = planningRepository.findPlanificationByUserAndDate(userId, firstDay, lastDay);
		boolean activityExist = false ;
		for (Planning planning : planningOfWeek) {
			if(planning.getActivity() == null) {
				planningRepository.delete(planning.getId());
			}else {
				activityExist = true;
			}
			
		}
		if(activityExist == false) {
			PlanningValidation planningValidation = planningValidationRepository.findPlanningValidationByUserAndDate(userId,
					firstDay);
			validationStatusRepository.deleteByPlanningValidation(planningValidation.getId());
			planningValidationRepository.deleteById(planningValidation.getId());
		}

		return true;
	}

	@Override
	public List<DelegateDto> findDelegatesWaitingForValidationByWeek(Date date) throws BirdnotesException {

		List<String> roles = currentUser.getUserRoles();
		List<DelegateDto> back = new ArrayList<>();
		List<Delegate> delegates = new ArrayList<>();
		List<Long> subUsers = userService.getSubUsersIds();
		delegates = planningValidationRepository.findDelegatesWaitingForValidationByWeek(date, subUsers);

		for (Delegate delegate : delegates) {
			back.add(delegateToDtoConvertor.convert(delegate));
		}
		return back;

	}

	//

	@Override
	public List<PlanningDto> findPlanningByUser(Long userId) throws BirdnotesException {
		List<PlanningDto> back = new ArrayList<>();
		List<Planning> plannings = planningRepository.findPlanningByUser(userId);
		for (Planning planning : plannings) {
			back.add(convertPlanifiedProspectToDto.convertToPlanningDto(planning));
		}
		return back;

	}

	@Override
	public List<PlanningValidationDto> findAllPlanningValidationByUser(Long userId) throws BirdnotesException {
		List<PlanningValidationDto> back = new ArrayList<>();
		List<PlanningValidation> planningValidations = planningValidationRepository
				.findAllPlanningValidationByUser(userId);
		for (PlanningValidation planningValidation : planningValidations) {
			PlanningValidationDto planningValidationDto = new PlanningValidationDto();
			planningValidationDto.setDate(planningValidation.getDate());
			planningValidationDto.setIdentifier(planningValidation.getIdentifier());
			planningValidationDto.setNotes(planningValidation.getNotes());
			planningValidationDto.setStatus(planningValidation.getStatus().toString());
			back.add(planningValidationDto);
		}
		return back;

	}

	@Override
	public void savePlanningValidation(PlanningValidationDto planningValidationDto) {
		PlanningValidation planningValidation = planningValidationRepository
				.findPlanningValidationByUserAndDate(planningValidationDto.getUser(), planningValidationDto.getDate());
		if (planningValidation == null) {
			planningValidation = new PlanningValidation();
			planningValidation.setDate(planningValidationDto.getDate());
			planningValidation.setIdentifier((new Date()).getTime());
			Delegate delegate = delegateRepository.findOne(planningValidationDto.getUser());

			planningValidation.setDelegate(delegate);
		}

		planningValidation.setStatus(UserValidationStatus.ACCEPTED);
		planningValidation.setSpecificWeek(planningValidationDto.isSpecificWeek());
		planningValidation.setWeekNumber(planningValidationDto.getWeekNumber());
		planningValidationRepository.save(planningValidation);
	}


	@Override
	public void acceptValidationStep(PlanningValidationDto planningValidationDto) throws BirdnotesException {

		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		User sourceUser = userRepository.findById(currentUser.getBirdnotesUser().getUserDto().getId());
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}

		PlanningValidation planningValidation = planningValidationRepository
				.findPlanningValidationByUserAndDate(planningValidationDto.getUser(), planningValidationDto.getDate());
		List<ValidationStatus> allValidationStatus = validationStatusRepository
				.findByPlanningValidationOrderByRankAsc(planningValidation);
		planningValidation.setNotes(planningValidationDto.getNotes());
		boolean isWorkflowFinished = this.validationStepService.accept(allValidationStatus,
				planningValidationDto.getId(), planningValidation.getId());

		if(planningValidationDto.getStatus().equals(UserValidationStatus.WAITING_FOR_VALIDATION.toString())) {
			planningValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			this.validationStepService.cancelAccept(allValidationStatus, planningValidationDto.getId(), planningValidation.getId());
		}
		
		else if (isWorkflowFinished) {
			String planificationValidationNotificationMessage = userService.getTranslatedLabel(
				    "planificationValidationNotificationMessage");


			planningValidation.setStatus(UserValidationStatus.ACCEPTED);
			planningValidationRepository.save(planningValidation);
	        notificationService.sendSingleNotification(
	        		sourceUser, null, planificationValidationNotificationMessage);

		} else {

			planningValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			planningValidationRepository.save(planningValidation);
		}

	}


	@Override
	public void refuseValidationStep(PlanningValidationDto planningValidationDto) throws BirdnotesException {
	    ConfigurationDto config = configurationService.findConfiguration();
	    Locale locale = new Locale(config.getLanguage());

		this.validationStepService.refuse(planningValidationDto.getId());

		PlanningValidation planningValidation = planningValidationRepository
				.findPlanningValidationByUserAndDate(planningValidationDto.getUser(), planningValidationDto.getDate());
		planningValidation.setStatus(UserValidationStatus.REFUSED);
		planningValidation.setNotes(planningValidationDto.getNotes());
		planningValidationRepository.save(planningValidation);
	    String planificationRefusNotificationMessage = messageSource.getMessage(
	            "planificationRefusNotificationMessage", null, locale);
	    notificationService.sendSingleNotification(
	            planningValidation.getDelegate().getUser(), null, planificationRefusNotificationMessage);

	}

	@Override
	public void reviseValidationStep(PlanningValidationDto planningValidationDto) throws BirdnotesException {
	    ConfigurationDto config = configurationService.findConfiguration();
	    Locale locale = new Locale(config.getLanguage());

		this.validationStepService.review(planningValidationDto.getId());
		PlanningValidation planningValidation = planningValidationRepository
				.findPlanningValidationByUserAndDate(planningValidationDto.getUser(), planningValidationDto.getDate());
		planningValidation.setStatus(UserValidationStatus.TO_BE_REVIEWED);
		planningValidationRepository.save(planningValidation);
		planningValidation.setNotes(planningValidationDto.getNotes());
		planningValidationRepository.save(planningValidation);
	    String planificationReviseNotificationMessage = messageSource.getMessage(
	            "planificationReviseNotificationMessage", null, locale);
	    notificationService.sendSingleNotification(
	            planningValidation.getDelegate().getUser(), null, planificationReviseNotificationMessage);

	}

	@Override
	public List<PlanifiedProspectDto> findPlanningById(Long id) throws BirdnotesException {
		PlanningValidation planningValidation = planningValidationRepository.findOne(id);
		if (planningValidation != null) {
			Date firstDate = new Date(planningValidation.getDate().getTime());
			Long userId = planningValidation.getDelegate().getId();
			List<PlanifiedProspectDto> PlanifiedProspects = findPlanificationByUserAndDate(userId, firstDate, null);

			return PlanifiedProspects;
		} else {
			return null;
		}
	}

	@Override
	public void save(PlanningValidationDto planningValidationDto) {
		this.savePlanningValidation(planningValidationDto);

		Delegate delegate = delegateRepository.findById(planningValidationDto.getUser());
		for (PlanifiedProspectDto plannifiedProspect : planningValidationDto.getPlanifiedProspectDto()) {

			Planning planning = new Planning();
			Prospect prospect = prospectRepository.findByIdentifier(plannifiedProspect.getIdentifier());
			planning.setProspect(prospect);
			planning.setIdentifier((new Date()).getTime());
			planning.setDate(plannifiedProspect.getPlanningDate());
			planning.setDelegate(delegate);
			planningRepository.save(planning);

		}

	}

	@Override
	public void deletePlanning(Long id) {
		planningRepository.delete(id);

	}

	@Override
	public List<PlanningDto> findNewPlanning(Delegate delegate, Date currentWeekDate) {
		List<Planning> plannings = planningRepository.findByDelegateAndDateGreaterThanEqual(delegate, currentWeekDate);
		List<PlanningDto> back = new ArrayList<>();
		for (Planning planning : plannings) {
			back.add(convertPlanifiedProspectToDto.convertToPlanningDto(planning));
		}
		return back;

	}

	@Override
	public List<PlanningValidationDto> findNewPlanningValidation(Delegate delegate, Date currentWeekDate) {

		List<PlanningValidation> planningValidations = planningValidationRepository
				.findByDelegateAndDateGreaterThanEqual(delegate, currentWeekDate);
		List<PlanningValidationDto> back = new ArrayList<>();
		for (PlanningValidation planningValidation : planningValidations) {
			PlanningValidationDto planningValidationDto = new PlanningValidationDto();
			planningValidationDto.setDate(planningValidation.getDate());
			planningValidationDto.setIdentifier(planningValidation.getIdentifier());
			planningValidationDto.setNotes(planningValidation.getNotes());
			planningValidationDto.setStatus(planningValidation.getStatus().toString());
			back.add(planningValidationDto);
		}
		return back;
	}

	@Override
	public OrdersPredictionResponse getOrdersPredictions(Date date, Long prospectId, Long productId, int first, int row) {

	   LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
	   // return
	   // prospectOrderPredictionRepository.getOrdersPredictions(localDate.getMonthValue(),
	   // localDate.getYear());

	   StringBuilder selectQueryString = new StringBuilder("SELECT new com.intellitech.birdnotes.data.dto.ProspectOrderPredictionResponse(pop)");
	   StringBuilder countQueryString = new StringBuilder("SELECT count(pop)");
	   String queryString =  " From ProspectOrderPrediction pop where MONTH(pop.predictionDate) =:month AND YEAR(pop.predictionDate) =:year " ;
 

	   selectQueryString.append(queryString);
	   countQueryString.append(queryString);
	   
	   
	   if (prospectId != null && prospectId != 0) {
		   selectQueryString.append(" AND pop.prospect.id=:prospectId");
		   countQueryString.append(" AND pop.prospect.id=:prospectId");
	   }

	   if (productId != null && productId != 0) {
		   selectQueryString.append(" AND pop.product.id=:productId");
		   countQueryString.append(" AND pop.product.id=:productId");
	   }

	   selectQueryString.append(" Order by orderQuantityPrediction Desc ");

	   Query selectQuery = entityManager.createQuery(selectQueryString.toString(), ProspectOrderPredictionResponse.class);
	   selectQuery.setParameter("month", localDate.getMonthValue());
	   selectQuery.setParameter("year", localDate.getYear());

	   Query countQuery = entityManager.createQuery(countQueryString.toString());
	   countQuery.setParameter("month", localDate.getMonthValue());
	   countQuery.setParameter("year", localDate.getYear());

	   if (prospectId != null && prospectId != 0) {
	       selectQuery.setParameter("prospectId", prospectId);
	       countQuery.setParameter("prospectId", prospectId);
	   }

	   if (productId != null && productId != 0) {
	       selectQuery.setParameter("productId", productId);
	       countQuery.setParameter("productId", productId);
	   }

	   selectQuery.setFirstResult(first);
	   selectQuery.setMaxResults(row);

	   List<ProspectOrderPredictionResponse> records = selectQuery.getResultList();
	   Long totalCount = (Long) countQuery.getSingleResult();

	   return new OrdersPredictionResponse(records, totalCount);
	}
	@Override
	public void deleteOrdersPredictions(Date date, Long prospectId, Long productId) {

		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

		StringBuilder queryString = new StringBuilder(
				"Delete from ProspectOrderPrediction where MONTH(predictionDate)  =:month AND YEAR(predictionDate) =:year  ");

		String whereString = "";
		if (prospectId != null && prospectId != 0) {
			whereString += " AND prospect.id=:prospectId";
		}

		if (productId != null && productId != 0) {
			whereString += " AND product.id=:productId";
		}

		queryString.append(whereString);

		Query query = entityManager.createQuery(queryString.toString());

		query.setParameter("month", localDate.getMonthValue());
		query.setParameter("year", localDate.getYear());

		if (prospectId != null && prospectId != 0) {
			query.setParameter("prospectId", prospectId);
		}

		if (productId != null && productId != 0) {
			query.setParameter("productId", productId);
		}

		query.executeUpdate();
	}

	@Override
	public void processProspectsOrdersPrediction(Date date, Long prospectId, Long productId) throws BirdnotesException {

		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        sendOrdersPredictionDoneEmail(localDate.getMonthValue());

		ObjectMapper mapper = new ObjectMapper();

		try {

			Configuration config = configureRepository.findById(1);
			if (config.getMlServerUrl() != null && !config.getMlServerUrl().isEmpty()) {
				URI uri = new URI(config.getMlServerUrl() + "/predict");
				HttpHeaders headers = new HttpHeaders();
				headers.set("Authorization", "Bearer " + mlServerToken);
				headers.set("Content-Type", "application/json");

				List<ProspectFeaturesDto> features = new ArrayList<ProspectFeaturesDto>();
				// List<Object[]> result = prospectRepository.getProspectsForPrediction();

				StringBuilder queryString = new StringBuilder(
						"  select  p.id as prospectId, prd.id as productId , pt.weight as potential ,l.name as locality, sp.name as speciality, prd.name as product, "
								+ "count(distinct(v.id)) as cumulVisit, sum (vp.order_quantity)  as cumulCmd ,sum (vp.sample_quantity) as cumulEch, sum (vp.sale_quantity) as cumulStock,  "
								+ "avg (vp.smily) as moySatisf, min (v.visit_date) as fistVisitDate,max (v.visit_date  ) as lastVisitDate  "
								+ "from visits_products vp " + "inner join visit v on (vp.visit_id = v.id) "
								+ "inner join product prd on (vp.product_id = prd.id )  "
								+ "inner join prospect p on (v.prospect_id  = p.id)  "
								+ "inner join user_prospects up on (p.id = up.prospect_id)  "
								+ "inner join potential pt on (pt.id = p.potential_id)  "
								+ "inner join speciality sp   on (sp.id = p.speciality_id)  "
								+ "inner join locality l on (p.locality_id = l.id) ");

				String whereString = " WHERE v.visit_date < :visit_date";
				if (prospectId != null && prospectId != 0) {
					whereString += " AND p.id=:prospectId ";
				}

				if (productId != null && productId != 0) {
					whereString += " AND prd.id=:productId ";
				}
				queryString.append(whereString);
				queryString.append(" group by p.id, prd.id , pt.weight, l.name, sp.name, prd.name ");

				Query query = entityManager.createNativeQuery(queryString.toString());

				query.setParameter("visit_date", new java.sql.Date(date.getTime()));
				if (prospectId != null && prospectId != 0) {
					query.setParameter("prospectId", prospectId);
				}

				if (productId != null && productId != 0) {
					query.setParameter("productId", productId);
				}
				List<Object[]> result = query.getResultList();

				OrdersPredictionRequest ordersPredictionDto = new OrdersPredictionRequest();
				ordersPredictionDto.setLabName(config.getName().toLowerCase());
				ordersPredictionDto.setModel("best");
				int recordCount = 0;

				for (Object[] record : result) {
					try {

						ProspectFeaturesDto prospectFeaturesDto = new ProspectFeaturesDto();
						prospectFeaturesDto.setProspectId(
								(BigInteger) record[0] != null ? (BigInteger) record[0] : BigInteger.valueOf(0));
						prospectFeaturesDto.setProductId(
								(BigInteger) record[1] != null ? (BigInteger) record[1] : BigInteger.valueOf(0));
						prospectFeaturesDto
								.setPotential((Float) record[2] != null ? (Float) record[2] : Float.valueOf(0));
						prospectFeaturesDto.setLocality(((String) record[3]).toLowerCase());
						prospectFeaturesDto.setSpeciality((String) record[4]);
						prospectFeaturesDto.setProduct((String) record[5]);
						prospectFeaturesDto.setCumulVisit(
								(BigInteger) record[6] != null ? (BigInteger) record[6] : BigInteger.valueOf(0));
						prospectFeaturesDto.setCumulCmd(
								(BigInteger) record[7] != null ? (BigInteger) record[7] : BigInteger.valueOf(0));
						prospectFeaturesDto.setCumulEch(
								(BigInteger) record[8] != null ? (BigInteger) record[8] : BigInteger.valueOf(0));

						prospectFeaturesDto.setCumulStock(
								(BigInteger) record[9] != null ? (BigInteger) record[9] : BigInteger.valueOf(0));
						prospectFeaturesDto.setMoySatisf(
								(BigDecimal) record[10] != null ? (BigDecimal) record[10] : BigDecimal.valueOf(0));
						Date fistVisitDate = (Date) record[11];
						Date lastVisitDate = (Date) record[12];
						long recency = 0;
						prospectFeaturesDto.setVisitMonth(new Double((localDate.getMonthValue())));
						float frequency = 0;
						if (lastVisitDate != null) {
							long recencyMillis = date.getTime() - lastVisitDate.getTime();
							if (recencyMillis > 0) {

								long periodMillis = lastVisitDate.getTime() - fistVisitDate.getTime();
								recency = TimeUnit.DAYS.convert(recencyMillis, TimeUnit.MILLISECONDS);
								frequency = TimeUnit.MILLISECONDS.toDays(periodMillis)
										/ prospectFeaturesDto.getCumulVisit().intValue();

							}

						}
						prospectFeaturesDto.setFistVisitDate(fistVisitDate);
						prospectFeaturesDto.setLastVisitDate(lastVisitDate);
						prospectFeaturesDto.setFrequency(frequency);
						prospectFeaturesDto.setRecency(recency);
						features.add(prospectFeaturesDto);
						recordCount++;

						if (recordCount > 0 && recordCount % 1000 == 0 || recordCount == result.size()) {
							ordersPredictionDto.setFeatures(features);
							HttpEntity<OrdersPredictionRequest> request = new HttpEntity<>(ordersPredictionDto,
									headers);

							LOG.info("recordCount = " + recordCount);

							ResponseEntity<PredictionResponse> response = restTemplate.postForEntity(uri, request,
									PredictionResponse.class);
							ProspectOrderPredictionResponse prediction;

							LOG.info("response size = " + response.getBody().getResults().size());

							for (int i = 0; i < response.getBody().getResults().size(); i++) {
								prediction = response.getBody().getResults().get(i);
								deleteOrdersPredictions(date, prediction.getProspectId(), prediction.getProductId());
								prospectOrderPredictionRepository.insert(date,
										mapper.writeValueAsString(features.get(i)), prediction.getProspectId(),
										prediction.getProductId(), prediction.getOrderQuantity());

							}
							features.clear();
						}

					} catch (Exception e) {

						LOG.error("Error in preparing and sending orders predictions ", e);

					}
				}
			} else {
				throw new BirdnotesException("Labo Url is undefined");
			}

		} catch (Exception e) {

			LOG.error("Error in processProspectsOrdersPrediction ", e);
			throw new BirdnotesException(e.getMessage());
		}

	}


	@Override
	public void postponePlanning(PostponePlanningRequest postponePlanningRequest) {

		planningValidationRepository.postponePlanningValidation(postponePlanningRequest.getUserId(),
				postponePlanningRequest.getCurrentWeekDate());
		planningRepository.postponePlanning(postponePlanningRequest.getUserId(),
				postponePlanningRequest.getCurrentWeekDate());

	}

	@Override
	public String getCommentClassification(String comment) {
		try {

			Configuration config = configureRepository.findById(1);
			if (config.getMlServerUrl() != null && !config.getMlServerUrl().isEmpty()) {
				URI uri = new URI(config.getMlServerUrl() + "/comment");
				HttpHeaders headers = new HttpHeaders();
				headers.set("Authorization", "Bearer " + mlServerToken);
				headers.set("Content-Type", "application/json");

				CommentClassification[] commentsClassificationiList = new CommentClassification[1];
				commentsClassificationiList[0] = new CommentClassification(comment, 0L);
				PredictionResponse predictionRequest = new PredictionResponse();
				predictionRequest.setComments(commentsClassificationiList);

				HttpEntity<PredictionResponse> request = new HttpEntity<>(predictionRequest, headers);
				ResponseEntity<PredictionResponse> response = restTemplate.postForEntity(uri, request,
						PredictionResponse.class);

				return response.getBody().getReuslts()[0].getScore();

			} else {
				return "Config missing";
			}

		} catch (Exception e) {
			LOG.error("Error rating comment ", e);
			return "Error";
		}

	}

	@Override
	public Map<String, List<PlanningDto>> importPlanning(String filePath, Long userId) throws BirdnotesException {
		try {
			User user = userRepository.findById(userId);
			Delegate delegate = delegateRepository.findDelegateByUserId(userId);

			Map<String, List<PlanningDto>> planningDtos = importService.getPlanningData(filePath, userId);

			if (planningDtos.containsKey("validData")) {
				Map<Date, List<PlanningDto>> planningMap = new HashMap<>();
				for (PlanningDto planningDto : planningDtos.get("checkedData")) {
					List<PlanningDto> planningByDateList;
					if (!planningMap.containsKey(planningDto.getDate())) {
						planningByDateList = new ArrayList<>();
						planningMap.put(planningDto.getDate(), planningByDateList);
					}
					planningMap.get(planningDto.getDate()).add(planningDto);
				}
				for (Map.Entry mapEntry : planningMap.entrySet()) {
					Date planningDate = (Date) mapEntry.getKey();
					Date mondayOfWeek = BirdnotesUtils.getMondayOfWeek(planningDate);
					PlanningValidation planningValidation = planningValidationRepository
							.findPlanningValidationByUserAndDate(userId, mondayOfWeek);

					if (planningValidation == null) {
						planningValidation = new PlanningValidation();
						planningValidation.setDate(mondayOfWeek);
						planningValidation.setIdentifier(new Date().getTime());
						planningValidation.setSpecificWeek(false);
						planningValidation.setStatus(UserValidationStatus.ACCEPTED);
						planningValidation.setDelegate(delegate);
						planningValidationRepository.save(planningValidation);

					}

					for (PlanningDto planningDto : planningMap.get(planningDate)) {
						Prospect prospect = prospectRepository.findById(planningDto.getProspect());
						Planning planning = new Planning();
						planning.setDate(planningDto.getDate());
						planning.setDelegate(delegate);
						planning.setProspect(prospect);
						planning.setIdentifier(new Date().getTime());
						planningRepository.save(planning);
					}
				}

			}
			return planningDtos;
		} catch (Exception e) {
			LOG.error("Error in importing planning ", e);
		}
		return null;

	}

	@Override
	public void updatePlannedActivity(PlannedActivityRequest plannedActivityRequest) {
		Planning planning = planningRepository.findOne(plannedActivityRequest.getId());
		if (planning != null) {
			planning.setStatus(plannedActivityRequest.getStatus());
			Activity activity = planning.getActivity();
			if (activity != null) {
				activity.setComment(plannedActivityRequest.getComment());
				activity.setActivityDate(new Date());
			}
			planningRepository.save(planning);

		}

	}
	@Override
	public void sendOrdersPredictionDoneEmail(Integer month) {
	    String sendPredictionEmailSubject = userService.getTranslatedLabel("sendPredictionEmailSubject");
	    String sendPredictionEmailHtmlBody = userService.getTranslatedLabel("sendPredictionEmailHtmlBody");

	    Configuration config = configureRepository.findById(1);
	    String serverPath = config.getServerPath(); 
	    String staticPath = "/#/order-prediction-list"; 
	    String fullLink = serverPath + staticPath; 


	    MessageFormat messageFormat = new MessageFormat(sendPredictionEmailHtmlBody);
	    String[] args = { month.toString(), fullLink };
	    String emailBody = messageFormat.format(args);


	    List<User> topUsers = userRepository.getTopUsers();
	    String[] to = topUsers.stream()
	                          .map(User::getEmail)
	                          .toArray(String[]::new);


	    ThreadSendEmail threadSendEmail = new ThreadSendEmail(javaMailSender, to, sendPredictionEmailSubject, emailBody);
	    Thread thread = new Thread(threadSendEmail);
	    thread.start();
	}


	



}
