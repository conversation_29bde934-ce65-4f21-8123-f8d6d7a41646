package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.GiftSupply;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.GiftSupplyDto;
import com.intellitech.birdnotes.model.dto.GiftSupplyRequestDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.repository.GiftSupplyRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.GadgetSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Service("gadgetSupplyService")
@Transactional
public class GiftSupplyServiceImp implements GadgetSupplyService {
	@Autowired
	private GiftRepository gadgetRepository;
	@Autowired
	private DelegateRepository delegateRepository;
	@Autowired
	private GiftSupplyRepository gadgetSupplyRepository;
	
	private DynamicQueries dynamicQueries;
	
	private UserService userService;

	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;
		
	}
	@Autowired
	public void setDynamicQueries(DynamicQueries dynamicQueries) {
		this.dynamicQueries = dynamicQueries;
	}
	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}
	@Override
	public GiftSupply saveGadgetSupply(GiftSupplyDto gadgetSupplyDto) throws BirdnotesException {
		GiftSupply gadgetSupply = null;

		if(gadgetSupplyDto.getId() != null) {
			gadgetSupply = gadgetSupplyRepository.findOne(gadgetSupplyDto.getId());
		}
		if(gadgetSupply==null) {
			gadgetSupply = new GiftSupply();
		}
		
		if (gadgetSupplyDto.getGiftId()!= null) {
			Gift gadget = gadgetRepository.findOne(gadgetSupplyDto.getGiftId());
			gadgetSupply.setGift(gadget);
		}
		if (gadgetSupplyDto.getDelegateId() != null) {
			Delegate delegate = delegateRepository.findOne(gadgetSupplyDto.getDelegateId());
			gadgetSupply.setDelegate(delegate);
		}
		gadgetSupply.setQuantity(gadgetSupplyDto.getQuantity());
		Date deliveryDate = new Date(gadgetSupplyDto.getDeliveryDate().getTime());
		gadgetSupply.setDeliveryDate(deliveryDate);
		return gadgetSupplyRepository.save(gadgetSupply);
	}

	@Override
	public List<GiftSupplyDto> getAllGadgetsSupply() throws BirdnotesException {
		
		List<GiftSupply> gadgetsSupply = gadgetSupplyRepository.findAll();
		List<GiftSupplyDto> gadgetsSupplyDto = new ArrayList<>();

		if (gadgetsSupply != null && !gadgetsSupply.isEmpty()) {
			for (GiftSupply gadgetSupply : gadgetsSupply) {
				GiftSupplyDto gadgetSupplyDto = new GiftSupplyDto();
			
				gadgetSupplyDto.setGiftName(gadgetSupply.getGift().getName());
				gadgetSupplyDto.setDelegateName(gadgetSupply.getDelegate().getFirstName()+ " " + gadgetSupply.getDelegate().getLastName());
				gadgetSupplyDto.setQuantity(gadgetSupply.getQuantity());
				gadgetSupplyDto.setDeliveryDate(gadgetSupply.getDeliveryDate());
				gadgetsSupplyDto.add(gadgetSupplyDto);

			}
		}

		return gadgetsSupplyDto;
	}

	@Override
	public List<GiftSupplyDto> findGadgetSupplyByUserAndDateAndGadget(Date firstDate, Date lastDate, Long userId,
			Integer gadgetId) throws BirdnotesException {
		List<Long> subUsers = userService.getSubUsersIds();
		List<GiftSupplyDto> result = new ArrayList<>();
		List<GiftSupply> gadgetsSupply = gadgetSupplyRepository.findGadgetsSupplyByUserDateAndGadget(firstDate,
				lastDate, userId, gadgetId, subUsers);
		for (GiftSupply gadgetSupply : gadgetsSupply) {
			GiftSupplyDto gadgetSupplyDto = new GiftSupplyDto();
			gadgetSupplyDto.setGiftName(gadgetSupply.getGift().getName());
			gadgetSupplyDto.setDelegateName(gadgetSupply.getDelegate().getFirstName()+ " " + gadgetSupply.getDelegate().getLastName());
			gadgetSupplyDto.setQuantity(gadgetSupply.getQuantity());
			gadgetSupplyDto.setDeliveryDate(gadgetSupply.getDeliveryDate());
			result.add(gadgetSupplyDto);
		}
		return result;
	}

	
	public List<GiftSupplyDto> getGadgetSupplyByUserGadgetAndDate(GiftSupplyRequestDto gadgetSupplyRequestDto) throws BirdnotesException {

		StringBuilder query = new StringBuilder();
		List<GiftSupplyDto> gadgetSupplyDto;
		Map<String, Object> parameters = new HashMap<>();
		query.append(
				"SELECT new com.intellitech.birdnotes.model.dto.GiftSupplyDto(gs)"
				+ " from GiftSupply gs ");
		
		
		query.append(" where (date(gs.deliveryDate) between date(:firstDate) and date(:lastDate))");
		parameters.put(BirdnotesConstants.QueryBuild.FIRST_DATE, gadgetSupplyRequestDto.getFirstDate());
		parameters.put(BirdnotesConstants.QueryBuild.LAST_DATE, gadgetSupplyRequestDto.getLastDate());
		
		if(gadgetSupplyRequestDto.getSelectedUser()!= null && gadgetSupplyRequestDto.getSelectedUser()!= 0) {
			query.append(" AND gs.delegate.id=:userId  ");
			parameters.put("userId", gadgetSupplyRequestDto.getSelectedUser());
			
		}
		if(gadgetSupplyRequestDto.getSelectedGift()!= null && gadgetSupplyRequestDto.getSelectedGift()!= 0) {
			query.append(" AND gs.gift.id=:gadgetId  ");
			parameters.put("gadgetId", gadgetSupplyRequestDto.getSelectedGift());
		}
		List<Long> subUsersIds = userService.getSubUsersIds();
		query.append(" AND gs.delegate.id IN (:subUsersIds) ");
		parameters.put("subUsersIds", subUsersIds);
		gadgetSupplyDto = dynamicQueries.findGadget(query.toString(), parameters);
					
		query.append(" order by gs.deliveryDate ");
		return gadgetSupplyDto;
	}
	
	@Override
	public void deleteGadgetSupply(Integer gadgetSupplyId) {
		
		gadgetSupplyRepository.delete(gadgetSupplyId);
	}

}
