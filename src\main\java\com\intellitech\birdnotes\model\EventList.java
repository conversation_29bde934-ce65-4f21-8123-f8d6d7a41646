package com.intellitech.birdnotes.model;

import java.util.List;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class EventList {
	private List<Event> event;

	public EventList() {

	}

	public EventList(List<Event> event) {
		super();
		this.event = event;
	}

	@XmlElement
	public List<Event> getEvent() {
		return event;
	}

	public void setEvent(List<Event> event) {
		this.event = event;
	}

}
