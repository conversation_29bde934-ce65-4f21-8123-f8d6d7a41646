package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.DiscountFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.DiscountDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.service.DiscountService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/discount")
public class DiscountController {
	private static final Logger LOG = LoggerFactory.getLogger(DiscountController.class);
	@Autowired
	private DiscountService discountService;

	@Autowired
	private UserService userService;

	@Autowired
	private ProductService productService;

	@Autowired
	private ProspectTypeService prospectTypeService;

	@RequestMapping(value = "/saveDiscount", method = RequestMethod.POST)
	public ResponseEntity<List<Long>> saveDiscount(@RequestBody DiscountDto discountDto) {
		try {
			if (userService.checkHasPermission("DISCOUNT_ADD") || userService.checkHasPermission("DISCOUNT_EDIT")) {
				List<Long> savedDiscount = discountService.saveDiscount(discountDto);

				if (savedDiscount != null) {
					return new ResponseEntity<>(savedDiscount, HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information when saving discount", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving discount", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteDiscount(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("DISCOUNT_DELETE")) {
	            discountService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting discount", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the discount with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "getDiscount", method = RequestMethod.GET)
	public ResponseEntity<List<DiscountDto>> getDiscount() {

		try {
			// if (userService.checkHasPermission("DISCOUNT_VIEW")) {
			List<DiscountDto> discountDto = discountService.getDiscount();
			return new ResponseEntity<>(discountDto, HttpStatus.OK);
			/*
			 * } else { return new ResponseEntity<>(null,
			 * HttpStatus.NON_AUTHORITATIVE_INFORMATION); }
			 */
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all discountDto", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findAllProspectTypes", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectTypeDto>> findAllProspectTypes() {
		try {
			if (userService.checkHasPermission("DISCOUNT_VIEW") || userService.checkHasPermission("DISCOUNT_ADD")
					|| userService.checkHasPermission("DISCOUNT_EDIT")) {
				List<ProspectTypeDto> prospectTypeDto = prospectTypeService.findAll();
				return new ResponseEntity<>(prospectTypeDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all prospect types", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("DISCOUNT_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "getProductWithoutDiscount", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getProductWithoutRule() {
		try {
			if (userService.checkHasPermission("DISCOUNT_VIEW")) {
				List<ProductDto> productDtos = discountService.getProductWithoutDiscount();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getProductWithoutDiscount", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/loadDiscountFormData", method = RequestMethod.GET)
	public ResponseEntity<DiscountFormData> loadDiscountFormData() {
		try {
			DiscountFormData result = discountService.loadDiscountFormData();
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
