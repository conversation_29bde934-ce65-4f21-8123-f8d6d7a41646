package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.EXPENSE_TYPE, schema = Common.PUBLIC_SCHEMA)
public class ExpenseType implements Serializable {
	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.PROSPECT_TYPE_SEQUENCE, sequenceName = Sequences.PROSPECT_TYPE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PROSPECT_TYPE_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.NAME)
	private String name;
	
	@Column(name = Columns.PRICE)
	private float price;
	
	@Column(name = Columns.MILEAGE)
	private float mileage;

	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.EXPENSE_TYPE_USERS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.EXPENSE_TYPE_ID, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID,  updatable = false) })
	private List<Delegate> delegates;
	
	
	private Boolean requiredAttachment;
	
	public ExpenseType(Long id, String name, float price, float mileage) {
		super();
		this.id = id;
		this.name = name;
		this.price = price;
		this.mileage = mileage;
	}
	
	
	
	public List<Delegate> getDelegates() {
		return delegates;
	}



	public void setDelegates(List<Delegate> delegates) {
		this.delegates = delegates;
	}



	public ExpenseType() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}


	public float getMileage() {
		return mileage;
	}


	public void setMileage(float mileage) {
		this.mileage = mileage;
	}


	public Boolean getRequiredAttachment() {
		return requiredAttachment;
	}


	public void setRequiredAttachment(Boolean requiredAttachment) {
		this.requiredAttachment = requiredAttachment;
	}


	
	
	
}
