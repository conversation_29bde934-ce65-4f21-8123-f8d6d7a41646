package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.LoadPlan;
import com.intellitech.birdnotes.model.dto.LoadPlanDto;

public interface LoadPlanService {
	public List<LoadPlanDto> getAllLoadPlans() throws BirdnotesException;

	public LoadPlan saveLoadPlan(LoadPlanDto loadPlanDto) throws BirdnotesException;

	public void deleteLoadPlan(Long loadPlanId);
}
