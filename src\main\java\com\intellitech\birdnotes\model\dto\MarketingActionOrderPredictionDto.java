package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;



public class MarketingActionOrderPredictionDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Date date;
    private Long prospectId;
    private Long productId;
    private Long marketingActionTypeId;
    private Integer budget;
    private Integer visitCount;
	
	
	public MarketingActionOrderPredictionDto(Date date, Long prospectId, Long productId, Long marketingActionType ,
			Integer budget , Integer visitCount ) {
		super();
		this.date = date;
		this.prospectId = prospectId;
		this.productId = productId;
		this.marketingActionTypeId = marketingActionType;
		this.budget = budget;
		this.visitCount= visitCount;

	}
	
	public MarketingActionOrderPredictionDto() {
		super();
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Long getProspectId() {
		return prospectId;
	}

	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}



	public Integer getBudget() {
		return budget;
	}

	public void setBudget(Integer budget) {
		this.budget = budget;
	}

	public Long getMarketingActionTypeId() {
		return marketingActionTypeId;
	}

	public void setMarketingActionTypeId(Long marketingActionTypeId) {
		this.marketingActionTypeId = marketingActionTypeId;
	}

	public Integer getVisitCount() {
		return visitCount;
	}

	public void setVisitCount(Integer visitCount) {
		this.visitCount = visitCount;
	}


	

	

}
