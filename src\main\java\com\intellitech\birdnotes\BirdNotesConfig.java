package com.intellitech.birdnotes;

import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.LocaleChangeInterceptor;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.service.ConfigurationService;


@Configuration
@EnableWebSecurity
@EnableScheduling
@EnableGlobalMethodSecurity(securedEnabled = true)
public class BirdNotesConfig extends WebSecurityConfigurerAdapter {

	
	

	@Autowired
	private ConfigurationService configurationService;



	private static final String[] ALLOWED_FOR_ADMIN = { "/loadPlan/**", "/issues/**", "/network/**", "/gammes/**",
			"/localities/**", "/noteFrais/**", "/roles/**", "/typeNoteFrais/**", "/upload/**", "/users/saveUser",
			"/users/saveDelegate", "/users/deleteUser/", "/users/updateUser", "users/updateDelegate" };

	private static final String[] ALLOWED_FOR_ADMIN_AND_SUPERVISER = { "/downloadData/getAllDataForNoteFrais",
			"/detailsHistorique/**", "/actionMarketing/**", "/opportunityNote/**", "/charts/**", "/planning/**",
			"/productCharts/**", "/visits/**", "/visits/coverage/**", "/visits/activity/**",
			"/products/compareProductsPerMonth/**", "/productCharts/**", "/prospect/getProspectChangesNumber"

	};
	private static final String[] ALLOWED_FOR_SUPERVISER = {};
	private static final String[] ALLOWED_FOR_DELEGATE = { "/synchronise/**" };

	private static final String[] PERMIT_ALL = { "/authentication/authenticate/**",
			"/authentication/checkTokenIsExpired/**", "/downloadData/getDataToVisitHistory", "/visits/**",
			"/users/findHistoryParams/**", "/authentication/sendIssue/**" };

	@Autowired
	private UserDetailsService birdnotesUserDetailsService;

	@Autowired
	private AuthTokenConfig authTokenConfig;

	private static final Logger LOG = LoggerFactory.getLogger(BirdNotesConfig.class);

	protected void configure(HttpSecurity http) throws Exception {
			
			String [] methodSecured={"/**"};
			
		     http.csrf().disable().authorizeRequests().antMatchers("/*","/authentication/**", "/testNetwork", "/assets/**", "/swagger-ui.html", "/webjars/**", "/v2/api-docs", "/swagger-resources/**").permitAll()
			.antMatchers(methodSecured).authenticated();
		     
		//http.csrf().disable().authorizeRequests().antMatchers(HttpMethod.OPTIONS, "/**").permitAll();
		        // .antMatchers(ALLOWED_FOR_ADMIN_AND_SUPERVISER)
		        // .hasAnyAuthority(RoleEnum.ROLE_ADMIN.toString(), RoleEnum.ROLE_SUPERVISER.toString()) 
		        // .antMatchers(ALLOWED_FOR_ADMIN).hasAuthority(RoleEnum.ROLE_ADMIN.toString())
		        // .antMatchers(ALLOWED_FOR_DELEGATE).hasAuthority(RoleEnum.ROLE_DELEGATE.toString())
		        // .antMatchers(ALLOWED_FOR_SUPERVISER).hasAuthority(RoleEnum.ROLE_SUPERVISER.toString())
				// .antMatchers(PERMIT_ALL).permitAll();
		         //.antMatchers("/**").denyAll();
			        http.apply(authTokenConfig);
			        
	
		
	  }


	@Override
	protected void configure(AuthenticationManagerBuilder authManagerBuilder) throws Exception {
		authManagerBuilder.userDetailsService(birdnotesUserDetailsService).passwordEncoder(bCryptPasswordEncoder());
	}

	@Bean
	public BCryptPasswordEncoder bCryptPasswordEncoder() {
		return new BCryptPasswordEncoder();
	}

	@Bean
	@Override
	public AuthenticationManager authenticationManagerBean() throws Exception {
		return super.authenticationManagerBean();
	}

	
	
	 
	 
	@Bean
	public LocaleResolver localeResolver(){
	       SessionLocaleResolver localeResolver = new SessionLocaleResolver();
	       localeResolver.setDefaultLocale(Locale.US);
	       return  localeResolver;
	   }
	
	@Bean
	public LocaleChangeInterceptor localeChangeInterceptor() {
	  LocaleChangeInterceptor localeChangeInterceptor = new LocaleChangeInterceptor();
	  localeChangeInterceptor.setParamName("lang");
	  return localeChangeInterceptor;
	}
	
	
	
	@Bean
	public ThreadPoolTaskScheduler threadPoolTaskScheduler() {

		ThreadPoolTaskScheduler threadPoolTaskScheduler = new ThreadPoolTaskScheduler();
		threadPoolTaskScheduler.setPoolSize(5);
		threadPoolTaskScheduler.setThreadNamePrefix("ThreadPoolTaskScheduler");
		return threadPoolTaskScheduler;
	}

	@Bean
	public RestTemplate restTemplate() {
		return new RestTemplate();
	}

	/*
	 * @Bean public ErrorPageFilter errorPageFilter() { return new
	 * ErrorPageFilter(); }
	 * 
	 * @Bean public FilterRegistrationBean
	 * disableSpringBootErrorFilter(ErrorPageFilter filter) { FilterRegistrationBean
	 * filterRegistrationBean = new FilterRegistrationBean();
	 * filterRegistrationBean.setFilter(filter);
	 * filterRegistrationBean.setEnabled(false);
	 * 
	 * return filterRegistrationBean; }
	 */

	@EventListener(ApplicationReadyEvent.class)
	public void executeAfterStartup() throws BirdnotesException {
		this.configurationService.setUpCrons();

	}
	


}
