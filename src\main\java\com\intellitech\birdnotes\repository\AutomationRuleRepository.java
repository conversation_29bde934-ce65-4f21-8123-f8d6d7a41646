package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.AutomationRule;

public interface AutomationRuleRepository extends JpaRepository<AutomationRule, Long> {

	@Query("SELECT a  from AutomationRule a  where a.activityTypeEvent.id=:activityTypeEventId")
	List<AutomationRule> findByActivityTypeEvent(@Param("activityTypeEventId") Long activityTypeEventId);
}
