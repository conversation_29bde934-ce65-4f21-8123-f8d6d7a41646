package com.intellitech.birdnotes.dao.impl;

import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.repository.ProspectRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;

import javax.persistence.EntityManager;
import javax.persistence.TypedQuery;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ReportDynamicQueriesImplTest {

    @Mock
    private EntityManager mockEntityManager;
    @Mock
    private ProspectRepository mockProspectRepository;

    @InjectMocks
    private ReportDynamicQueriesImpl reportDynamicQueriesImplUnderTest;

    @Before
    public void setUp() throws Exception {
        reportDynamicQueriesImplUnderTest.prospectRepository = mockProspectRepository;
    }

    @Test
    public void testFindProspect() {
        // Setup
        final Map<String, Object> parameters = new HashMap<>();

        // Configure EntityManager.createQuery(...).
        final TypedQuery<Prospect> mockTypedQuery = mock(TypedQuery.class);
        when(mockEntityManager.createQuery("query", Prospect.class)).thenReturn(mockTypedQuery);

        // Run the test
        final List<Prospect> result = reportDynamicQueriesImplUnderTest.findProspect("query", parameters);

        // Verify the results
    }
}
