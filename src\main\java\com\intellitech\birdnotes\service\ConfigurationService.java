package com.intellitech.birdnotes.service;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;

public interface ConfigurationService {
	
	ConfigurationDto findByName(String name);
	
	
	ConfigurationDto save(ConfigurationDto configurationDto) throws BirdnotesException;
	
	ConfigurationDto findConfiguration();

	void updateConfiguration(ConfigurationDto configurationDto) throws BirdnotesException;

	Configuration add(ConfigurationDto configurationDto)throws BirdnotesException;

	public void executeDeletionScript(String scriptType);
	
	public void setUpCrons() throws BirdnotesException;
	
}
