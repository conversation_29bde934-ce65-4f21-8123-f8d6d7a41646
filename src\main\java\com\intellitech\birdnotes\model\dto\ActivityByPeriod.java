package com.intellitech.birdnotes.model.dto;

import java.util.Date;

public class ActivityByPeriod {
	
	private String label;
	
	private long totalVisits;
	
	private long totalOrders;
	
	private long totalSamples;
	
	
	

	public ActivityByPeriod(Date period, long totalVisits, long totalOrders, long totalSamples) {
		super();
		this.label = period.toString();
		this.totalVisits = totalVisits;
		this.totalOrders = totalOrders;
		this.totalSamples = totalSamples;
	}
	
	
	public ActivityByPeriod(String label, long totalVisits, long totalOrders, long totalSamples) {
		super();
		this.label = label;
		this.totalVisits = totalVisits;
		this.totalOrders = totalOrders;
		this.totalSamples = totalSamples;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String period) {
		this.label = period;
	}

	public long getTotalVisits() {
		return totalVisits;
	}

	public void setTotalVisits(long totalVisits) {
		this.totalVisits = totalVisits;
	}

	public long getTotalOrders() {
		return totalOrders;
	}

	public void setTotalOrders(long totalOrders) {
		this.totalOrders = totalOrders;
	}

	public long getTotalSamples() {
		return totalSamples;
	}

	public void setTotalSamples(long totalSamples) {
		this.totalSamples = totalSamples;
	}
	
	

}
