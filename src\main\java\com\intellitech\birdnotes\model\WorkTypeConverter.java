package com.intellitech.birdnotes.model;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

import com.intellitech.birdnotes.enumeration.WorkType;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Converter(autoApply = true)
public class WorkTypeConverter implements AttributeConverter<WorkType, String> {

	@Override
	public String convertToDatabaseColumn(WorkType workType) {
		if (WorkType.MEDICAL.equals(workType)) {
			return BirdnotesConstants.WorkType.MEDICAL;
		}
		if (WorkType.PHARMACEUTICAL.equals(workType)) {
			return BirdnotesConstants.WorkType.PHARMACEUTICAL;
		}
		return BirdnotesConstants.WorkType.ADMIN;
	}

	@Override
	public WorkType convertToEntityAttribute(String workType) {
		if (BirdnotesConstants.WorkType.MEDICAL.equals(workType)) {
			return WorkType.MEDICAL;
		}

		if (BirdnotesConstants.WorkType.PHARMACEUTICAL.equals(workType)) {
			return WorkType.PHARMACEUTICAL;
		}
		return WorkType.ADMIN;
	}

}
