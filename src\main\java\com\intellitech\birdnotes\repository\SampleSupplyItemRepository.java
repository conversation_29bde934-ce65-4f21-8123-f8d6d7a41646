package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.SampleSupplyItem;

public interface SampleSupplyItemRepository extends JpaRepository<SampleSupplyItem, Long> {
	
	@Modifying
	@Query("DELETE FROM SampleSupplyItem where sampleSupply.id = :id")
	void deleteBySampleSupplyId(@Param("id") Long id);

}
