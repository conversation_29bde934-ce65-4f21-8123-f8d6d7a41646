#Generated by Eclipse Messages Editor (Eclipse Babel)
# DATA SOURCE
spring.datasource.driverClassName = org.postgresql.Driver
#spring.datasource.password        = intellitech
spring.datasource.password        = ${DB_PASSWORD}
#spring.datasource.url             = ******************************************************
spring.datasource.url             = jdbc:postgresql://${DB_SVC}:${DB_PORT}/${DB_NAME}
#spring.datasource.username        = postgres
spring.datasource.username        = ${DB_USER}

#serveurPath = http://birdnotes-backend:8080
#serveurPath = http://vmi750991.contaboserver.net:30001/api
#serveurPath = ${BACKEND_URL}
serveurPath = http://${BIRDNOTES_SERVER}:${BIRDNOTES_PORT}/api


#laboName=LABORATOIRE DEVOPS
laboName=LABORATOIRE ${CLIENT_NAME}

#upload
uploadUrl = /attachments
uploadPath = /usr/local/tomcat/webapps/attachments
mobileAppLogsPath = /mobile_logs
logoPath = /logo
expenseReportPath = /expense_report
missionReportPath=/mission_report
generatedDoPath = /delivery_order
purchaseOrderPath=/purchase_order
opportunityNotePath=/opportunityNotePath
commentsRatesFilePath = /ml_data/comments_rates.txt
userImagePath = /userImagePath
generatedDocumentPath = /generated_document
userPath=/user


productPath=/product
specialityPath=/speciality
recoveryPath=/recovery
ml.serverUrl=http://194.163.132.114:9980/
ml.serverToken=sdfghjkloerdtfyguhiopfghjkl;fghjkl
ml.laboName=demo
ml.enabled=true

erp.serverUrl=http://194.163.132.114:8069
erp.login=<EMAIL>
erp.password=5p9m-qbeb-wvhb
erp.updateFrequency=0 0 1 * * *
