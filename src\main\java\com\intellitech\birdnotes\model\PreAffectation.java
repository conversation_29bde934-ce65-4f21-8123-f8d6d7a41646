package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;

@Entity
@Table(name = BirdnotesConstants.Tables.PREAFFECTATION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class PreAffectation implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private Integer delegateNumber;
	private String name;

	public PreAffectation() {
		super();
	}
	
	public PreAffectation(Long id, Integer delegateNumber) {
		super();
		this.id = id;
		this.delegateNumber = delegateNumber;
	}

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.PREAFFECTATION_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.PREAFFECTATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.PREAFFECTATION_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}


	@Column(name = BirdnotesConstants.Columns.DELEGATE_NUMBER)
	public Integer getDelegateNumber() {
		return delegateNumber;
	}

	public void setDelegateNumber(Integer delegateNumber) {
		this.delegateNumber = delegateNumber;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
	
	
}
