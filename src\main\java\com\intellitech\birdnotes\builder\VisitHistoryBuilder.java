package com.intellitech.birdnotes.builder;

import java.util.List;

import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryDto;
import com.intellitech.birdnotes.model.dto.VisitHistorySummaryDto;

public class VisitHistoryBuilder {
	
	
	VisitHistorySummaryDto visitHistorySummaryDto = new VisitHistorySummaryDto();

	public VisitHistorySummaryDto buildPeriodeDto(int numberOfDay, int numberOfMonth, int numberOfYear) {
		visitHistorySummaryDto.setNumberOfDay(numberOfDay);
		visitHistorySummaryDto.setNumberOfMonth(numberOfMonth);
		visitHistorySummaryDto.setNumberOfYear(numberOfYear);
		return visitHistorySummaryDto;
	}
	
	public VisitHistorySummaryDto buildCommentRatingAverage(List<LabelValueDto>commentRatingAverage) {
		visitHistorySummaryDto.setCommentRatingAverage(commentRatingAverage);
		return visitHistorySummaryDto;
	}

	public VisitHistorySummaryDto buildNumberSearchAndGroup (Object numberResultSearch,  Object groupOfSearch) {
		visitHistorySummaryDto.setNumberResultSearch(numberResultSearch);
		visitHistorySummaryDto.setGroupOfSearch(groupOfSearch);
		return visitHistorySummaryDto;
	}
	public VisitHistorySummaryDto buildResultat (List<VisitHistoryDto> visits) {
	
		visitHistorySummaryDto.setVisitHistoryList(visits);
		return visitHistorySummaryDto;
	}
	public VisitHistorySummaryDto getVisitHistorySummaryDto() {
		return visitHistorySummaryDto;
	}
}
