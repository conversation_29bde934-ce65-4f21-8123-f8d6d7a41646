package com.intellitech.birdnotes.repository;

import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.FreeQuantityRule;
import com.intellitech.birdnotes.model.FreeQuantityRuleItem;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleDto;
import com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto;


@Repository
public interface FreeQuantityRuleItemRepository extends JpaRepository<FreeQuantityRuleItem, Long> {

	void deleteById(long id);
	@Query("Select f from FreeQuantityRuleItem f ")
	List<FreeQuantityRuleItem> findAll();

	Set<FreeQuantityRuleItem> findByFreeQuantityRule(FreeQuantityRule freeQuantityRule);
	
	@Query("Select new com.intellitech.birdnotes.model.dto.FreeQuantityRuleItemDto(q.id, q.orderQuantity, q.freeQuantity, q.labGratuity, pro.id, pot.id, pt.id)"+
			" from FreeQuantityRuleItem q join q.freeQuantityRule.products pro join q.freeQuantityRule.potentials pot "
			+ "join q.freeQuantityRule.prospectTypes pt order by pro.id, pot.id")
			List<FreeQuantityRuleItemDto> findFreeQuantityRulesAndItems();
}
