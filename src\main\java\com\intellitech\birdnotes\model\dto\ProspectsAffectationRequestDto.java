package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class ProspectsAffectationRequestDto implements Serializable{
	
	private static final long serialVersionUID = 1L;


	private Long userId;
	private Long prospectId;
	private String userFirstName;
	private String userLastName;
	private List<Long> prospect;
	private String prospectName;
	private Short cycle;
	private Integer delegateNumber;
	
	
	private SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest;
	
	
	public Long getUserId() {
		return userId;
	}
	public void setUserId(Long userId) {
		this.userId = userId;
	}
	public Long getProspectId() {
		return prospectId;
	}
	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}

	public String getUserFirstName() {
		return userFirstName;
	}
	public void setUserFirstName(String userFirstName) {
		this.userFirstName = userFirstName;
	}
	public String getUserLastName() {
		return userLastName;
	}
	public void setUserLastName(String userLastName) {
		this.userLastName = userLastName;
	}
	public List<Long> getProspect() {
		return prospect;
	}
	public void setProspect(List<Long> prospect) {
		this.prospect = prospect;
	}
	public String getProspectName() {
		return prospectName;
	}
	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	public Integer getDelegateNumber() {
		return delegateNumber;
	}
	public void setDelegateNumber(Integer delegateNumber) {
		this.delegateNumber = delegateNumber;
	}
	public ProspectsAffectationRequestDto() { super(); }
	public ProspectsAffectationRequestDto(Long userId, String userFirstName, String userLastName, List<Long> prospect,
			String prospectName) {
		super();
		this.userId = userId;
		this.userFirstName = userFirstName;
		this.userLastName = userLastName;
		this.prospect = prospect;
		this.prospectName = prospectName;
	}
	public SelectedDataForSourceProspectsRequestDto getSelectedDataForSourceProspectsRequest() {
		return selectedDataForSourceProspectsRequest;
	}
	public void setSelectedDataForSourceProspectsRequest(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) {
		this.selectedDataForSourceProspectsRequest = selectedDataForSourceProspectsRequest;
	}
	public Short getCycle() {
		return cycle;
	}
	public void setCycle(Short cycle) {
		this.cycle = cycle;
	}
	

}
