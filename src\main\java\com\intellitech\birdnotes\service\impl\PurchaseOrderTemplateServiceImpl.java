package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.PurchaseOrderTemplateItem;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.convertor.ConvertGiftToDto;
import com.intellitech.birdnotes.model.convertor.DelegateToDtoConvertor;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderTemplateItemDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderTemplateItemRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderTemplateRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.service.PurchaseOrderTemplateService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("PurchaseOrderTemplateService")
@Transactional
public class PurchaseOrderTemplateServiceImpl implements PurchaseOrderTemplateService {
	@Autowired
	private PurchaseOrderTemplateRepository purchaseOrderTemplateRepository;
	@Autowired
	private GiftRepository gadgetRepository;
	@Autowired
	private UserRepository userRepository;
	@Autowired
	private DelegateRepository delegateRepository;
	@Autowired
	private PurchaseOrderTemplateItemRepository purchaseOrderTemplateItemRepository;
	@Autowired
	private	ProductRepository productRepository;
	@Autowired
	private DynamicQueries dynamicQueries;
	
	private UserService userService;

	
	private DelegateToDtoConvertor delegateToDtoConvertor ;
	
	private ConvertGiftToDto convertGadgetToDto ;
	
	Logger LOG = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;
		
	}
	
	@Autowired
	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}


	@Autowired
	public void setDelegateToDtoConvertor(DelegateToDtoConvertor delegateToDtoConvertor) {
		this.delegateToDtoConvertor = delegateToDtoConvertor;
	}
	@Autowired
	public void setConvertGadgetToDto(ConvertGiftToDto convertGadgetToDto) {
		this.convertGadgetToDto = convertGadgetToDto;
	}

	@Override
	public PurchaseOrderTemplate savePurchaseOrderTemplate(PurchaseOrderTemplateDto purchaseOrderTemplateDto)throws BirdnotesException {
	    PurchaseOrderTemplate existingTemplate = purchaseOrderTemplateRepository.findByNameAndAnotherId(purchaseOrderTemplateDto.getName(), purchaseOrderTemplateDto.getId());
	    if (existingTemplate != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }
		PurchaseOrderTemplate purchaseOrderTemplate = null;
		if(purchaseOrderTemplateDto.getId() != null) {
			purchaseOrderTemplate = purchaseOrderTemplateRepository.findOne(purchaseOrderTemplateDto.getId());
		}
		if(purchaseOrderTemplate==null) {
			purchaseOrderTemplate = new PurchaseOrderTemplate();
		}
		purchaseOrderTemplate.setFirstDate(purchaseOrderTemplateDto.getFirstDate());
		purchaseOrderTemplate.setLastDate(purchaseOrderTemplateDto.getLastDate());
		purchaseOrderTemplate.setName(purchaseOrderTemplateDto.getName());
		List<Gift> gifts = gadgetRepository.findAll(purchaseOrderTemplateDto.getGiftsId());
		if(gifts!=null) {
			purchaseOrderTemplate.setGifts(gifts);
		}
		List<Delegate> users = delegateRepository.findAll(purchaseOrderTemplateDto.getDelegatesId());
		if(users != null) {
			purchaseOrderTemplate.setDelegates(users);
		}
		PurchaseOrderTemplate savedPurchaseOrderTemplate = purchaseOrderTemplateRepository.save(purchaseOrderTemplate);
		for(PurchaseOrderTemplateItemDto purchaseOrderTemplateItemDto :  purchaseOrderTemplateDto.getPurchaseOrderTemplateItem()) {
			PurchaseOrderTemplateItem purchaseOrderTemplateItem = null;
			if(purchaseOrderTemplateItemDto.getId() != null) {
				purchaseOrderTemplateItem = purchaseOrderTemplateItemRepository.findOne(purchaseOrderTemplateItemDto.getId());
			}
			if(purchaseOrderTemplateItemDto.getId() == null) {
				purchaseOrderTemplateItem = new PurchaseOrderTemplateItem();
			}
			Product product = productRepository.findById(purchaseOrderTemplateItemDto.getProductId());
			if(product != null) {
				purchaseOrderTemplateItem.setProduct(product);
			}else {
				throw new BirdnotesException(Exceptions.PRODUCT_PURCHASE_ORDER_TEMPLATE_ITEM_IS_EMPTY);
			}
			purchaseOrderTemplateItem.setPurchaseOrderTemplate(savedPurchaseOrderTemplate);
			purchaseOrderTemplateItem.setQuantity(purchaseOrderTemplateItemDto.getQuantity());
			purchaseOrderTemplateItem.setFreeOrder(purchaseOrderTemplateItemDto.getFreeOrder());
			purchaseOrderTemplateItem.setLabGratuity(purchaseOrderTemplateItemDto.getLabGratuity());
			purchaseOrderTemplateItemRepository.save(purchaseOrderTemplateItem);
			purchaseOrderTemplateRepository.save(purchaseOrderTemplate);
		}
		
		return savedPurchaseOrderTemplate;
		
	}
	
	
	@Override
	public List<PurchaseOrderTemplateDto> getPurchaseOrderTemplateByUserProductAndDate(
			PurchaseOrderTemplateDto purchaseOrderTemplateRequestDto) throws BirdnotesException{
		StringBuilder query = new StringBuilder();
		List<PurchaseOrderTemplateDto> purchaseOrderTemplateDtoList = new ArrayList<>();
		List<PurchaseOrderTemplate> purchaseOrderTemplateList;
		Map<String, Object> parameters = new HashMap<>();
		query.append(
				"SELECT pot from PurchaseOrderTemplate pot ");
		if(purchaseOrderTemplateRequestDto.getSelectedDelegateId()!= null && purchaseOrderTemplateRequestDto.getSelectedDelegateId()!= 0) {
		    query.append(" join pot.users u ");		
		}
		
		if(purchaseOrderTemplateRequestDto.getSelectedGiftId()!= null && purchaseOrderTemplateRequestDto.getSelectedGiftId()!= 0) {
			query.append(" join pot.gifts g  ");
		}
		query.append(" where (date(pot.firstDate) between date(:firstDate) and date(:lastDate)) and (date(pot.lastDate) between date(:firstDate) and date(:lastDate))");
		parameters.put(BirdnotesConstants.QueryBuild.FIRST_DATE, purchaseOrderTemplateRequestDto.getFirstDate());
		parameters.put(BirdnotesConstants.QueryBuild.LAST_DATE, purchaseOrderTemplateRequestDto.getLastDate());
		if(purchaseOrderTemplateRequestDto.getSelectedDelegateId()!= null && purchaseOrderTemplateRequestDto.getSelectedDelegateId()!= 0) {
			query.append(" And u.id =:userId ");
			parameters.put("userId", purchaseOrderTemplateRequestDto.getSelectedDelegateId());	
		}
		if(purchaseOrderTemplateRequestDto.getSelectedGiftId()!= null && purchaseOrderTemplateRequestDto.getSelectedGiftId()!= 0) {
			query.append(" And g.id =:giftId  ");
			parameters.put("giftId", purchaseOrderTemplateRequestDto.getSelectedGiftId());
		}
		/*List<Long> subUsersIds = userService.getSubUsersIds();
		query.append(" And u.id IN (:subUsersIds) ");
		parameters.put("subUsersIds", subUsersIds);*/

		
		purchaseOrderTemplateList = dynamicQueries.findPurchaseOrderTemplate(query.toString(), parameters);
		
		for(PurchaseOrderTemplate purchaseOrderTemplate : purchaseOrderTemplateList) {
			List<GiftDto> gifts = new ArrayList<>();
			List<DelegateDto>users = new ArrayList<>();
			List<Long> giftsId = new ArrayList<>();
			List<Long>usersId = new ArrayList<>();

			PurchaseOrderTemplateDto purchaseOrderTemplateDto = new PurchaseOrderTemplateDto();
			purchaseOrderTemplateDto.setId(purchaseOrderTemplate.getId());
			purchaseOrderTemplateDto.setFirstDate(purchaseOrderTemplate.getFirstDate());
			purchaseOrderTemplateDto.setLastDate(purchaseOrderTemplate.getLastDate());
			purchaseOrderTemplateDto.setName(purchaseOrderTemplate.getName());
			for(Gift gadget :purchaseOrderTemplate.getGifts()) {
				gifts.add(convertGadgetToDto.convert(gadget));
				giftsId.add(gadget.getId());
			}
			for(Delegate user : purchaseOrderTemplate.getDelegates()) {
				users.add(delegateToDtoConvertor.convert(user));
				usersId.add(user.getId());
			}
			purchaseOrderTemplateDto.setGifts(gifts);
			purchaseOrderTemplateDto.setDelegates(users);
			purchaseOrderTemplateDto.setGiftsId(giftsId);
			purchaseOrderTemplateDto.setDelegatesId(usersId);

			purchaseOrderTemplateDtoList.add(purchaseOrderTemplateDto);
		}
		

				return purchaseOrderTemplateDtoList;
		
	}
	
	
	@Override
	public void delete (Long id)throws BirdnotesException {
			PurchaseOrderTemplate purchaseOrderTemplate = purchaseOrderTemplateRepository.findOne(id);
			
			for (PurchaseOrderTemplateItem purchaseOrderTemplateItem : purchaseOrderTemplateItemRepository.findByPurchaseOrderTemplate(purchaseOrderTemplate) )
			{
				purchaseOrderTemplateItemRepository.delete(purchaseOrderTemplateItem);
			}
			
			for ( Delegate delegate : delegateRepository.findDelegatesOfPot(purchaseOrderTemplate.getId()) ) 
			{
				if(delegate.getPurchaseOrderTemplates()!=null) {
					delegate.getPurchaseOrderTemplates().remove(purchaseOrderTemplate);
			}	
				
				delegateRepository.save(delegate);
			}
			
			for ( Gift gift : gadgetRepository.findGiftsOfPot(purchaseOrderTemplate.getId()) ) 
			{
				if(gift.getPurchaseOrderTemplates()!=null) {
					gift.getPurchaseOrderTemplates().remove(purchaseOrderTemplate);
			}	
				
				gadgetRepository.save(gift);
			}
			purchaseOrderTemplateRepository.delete(id);



	}
		@Override
		public boolean deletePurchaseOrderTemplateItem (Long id) throws BirdnotesException {
			try{

				purchaseOrderTemplateItemRepository.delete(id);
				
			return true;
		} catch (Exception e) {
			LOG.error("error when delete PurchaseOrderTemplateItem ",e);
			throw new BirdnotesException("Could not delete PurchaseOrderTemplateItem!");
		}
		
	}
		
	@Override
	public List<PurchaseOrderTemplateItemDto> getPurchaseOrderTemplateItem(
			PurchaseOrderTemplateDto purchaseOrderTemplateRequestDto) {
		
		List<PurchaseOrderTemplateItemDto> purchaseOrderTemplateItemDtoList = new ArrayList<>();
		
		PurchaseOrderTemplate purchaseOrderTemplate = purchaseOrderTemplateRepository.findOne(purchaseOrderTemplateRequestDto.getId());
		
		Set<PurchaseOrderTemplateItem> purchaseOrderTemplateItems = purchaseOrderTemplateItemRepository.findByPurchaseOrderTemplate(purchaseOrderTemplate);
		
		for(PurchaseOrderTemplateItem purchaseOrderTemplateItem : purchaseOrderTemplateItems) {
			PurchaseOrderTemplateItemDto purchaseOrderTemplateItemDto = new PurchaseOrderTemplateItemDto();
			purchaseOrderTemplateItemDto.setId(purchaseOrderTemplateItem.getId());
			purchaseOrderTemplateItemDto.setProductId(purchaseOrderTemplateItem.getProduct().getId());
			purchaseOrderTemplateItemDto.setProductName(purchaseOrderTemplateItem.getProduct().getName());
			purchaseOrderTemplateItemDto.setQuantity(purchaseOrderTemplateItem.getQuantity());
			purchaseOrderTemplateItemDto.setFreeOrder(purchaseOrderTemplateItem.getFreeOrder());
			purchaseOrderTemplateItemDto.setLabGratuity(purchaseOrderTemplateItem.getLabGratuity());
			purchaseOrderTemplateItemDtoList.add(purchaseOrderTemplateItemDto);
		}
		return purchaseOrderTemplateItemDtoList;
	}
	
	@Override
	public List<PurchaseOrderTemplateDto> getPurchaseOrderTemplateByUser(Long userId) throws BirdnotesException {
		List<PurchaseOrderTemplateDto> purchaseOrderTemplateDtos = new ArrayList<>();
			//Date currentDate = Calendar.getInstance().getTime();
			
			
		Set<PurchaseOrderTemplate> purchaseOrderTemplates = purchaseOrderTemplateRepository.findPurchaseOrderTemplatesOfUser(userId);
		if(purchaseOrderTemplates!=null && purchaseOrderTemplates.size() > 0) {
			for(PurchaseOrderTemplate purchaseOrderTemplate : purchaseOrderTemplates) {
				
				PurchaseOrderTemplateDto purchaseOrderTemplateDto = new PurchaseOrderTemplateDto();				
				purchaseOrderTemplateDto.setId(purchaseOrderTemplate.getId());
				purchaseOrderTemplateDto.setFirstDate(purchaseOrderTemplate.getFirstDate());
				purchaseOrderTemplateDto.setLastDate(purchaseOrderTemplate.getLastDate());
				purchaseOrderTemplateDto.setName(purchaseOrderTemplate.getName());
				List<GiftDto> giftsDto = new ArrayList<>();
				for(Gift gift : purchaseOrderTemplate.getGifts()) {
					giftsDto.add(convertGadgetToDto.convert(gift));
				}
				purchaseOrderTemplateDto.setGifts(giftsDto);
				
				Set<PurchaseOrderTemplateItem> purchaseOrderTemplateItemList = purchaseOrderTemplateItemRepository.findByPurchaseOrderTemplate(purchaseOrderTemplate);
				List<PurchaseOrderTemplateItemDto> purchaseOrderTemplateItemsDto = new ArrayList<>();
				for(PurchaseOrderTemplateItem purchaseOrderTemplateItem :purchaseOrderTemplateItemList)		{
					PurchaseOrderTemplateItemDto purchaseOrderTemplateItemDto = new PurchaseOrderTemplateItemDto();
					purchaseOrderTemplateItemDto.setId(purchaseOrderTemplateItem.getId());
					purchaseOrderTemplateItemDto.setProductId(purchaseOrderTemplateItem.getProduct().getId());
					purchaseOrderTemplateItemDto.setProductName(purchaseOrderTemplateItem.getProduct().getName());
					purchaseOrderTemplateItemDto.setQuantity(purchaseOrderTemplateItem.getQuantity());
					purchaseOrderTemplateItemDto.setFreeOrder(purchaseOrderTemplateItem.getFreeOrder());
					purchaseOrderTemplateItemDto.setLabGratuity(purchaseOrderTemplateItem.getLabGratuity());
					purchaseOrderTemplateItemDto.setPurchaseOrderTemplateId(purchaseOrderTemplateDto.getId());
					purchaseOrderTemplateItemsDto.add(purchaseOrderTemplateItemDto);
				}
				purchaseOrderTemplateDto.setPurchaseOrderTemplateItem(purchaseOrderTemplateItemsDto);
				purchaseOrderTemplateDtos.add(purchaseOrderTemplateDto);
			}
		}
		return purchaseOrderTemplateDtos;
		
	}
	
	
	

	
}
