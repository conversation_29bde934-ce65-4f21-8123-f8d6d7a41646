package com.intellitech.birdnotes.model.request;

import java.util.Date;

public class IssueRequest {
	private Date firstDate;
	private Date lastDate;
	
	public IssueRequest(Date firstDate, Date lastDate) {
		super();
		this.firstDate = firstDate;
		this.lastDate = lastDate;
	}
	
	public IssueRequest() {
		super();
	}

	public Date getFirstDate() {
		return firstDate;
	}
	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}
	public Date getLastDate() {
		return lastDate;
	}
	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}
	
}
