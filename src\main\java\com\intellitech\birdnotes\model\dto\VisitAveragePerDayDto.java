package com.intellitech.birdnotes.model.dto;

public class VisitAveragePerDayDto {

	private String delegateName;
	private Double averageValue;
	private Long sum;
	private String average;

	public VisitAveragePerDayDto() {
		super();
	}

	public VisitAveragePerDayDto(String delegateName, Double averageValue, Long sum, String average) {
		this.delegateName = delegateName;
		this.averageValue = averageValue;
		this.sum = sum;
		this.average = average;
	}

	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

	public Double getAverageValue() {
		return averageValue;
	}

	public void setAverageValue(Double averageValue) {
		this.averageValue = averageValue;
	}

	public Long getSum() {
		return sum;
	}

	public void setSum(Long sum) {
		this.sum = sum;
	}

	public String getAverage() {
		return average;
	}

	public void setAverage(String average) {
		this.average = average;
	}
}
