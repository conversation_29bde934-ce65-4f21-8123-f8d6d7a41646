package com.intellitech.birdnotes.model.request;

import java.io.Serializable;
import java.util.Date;

public class ReportValidationRequest implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long selectedUser;
	private Date visitDate;

	public Long getSelectedUser() {
		return selectedUser;
	}

	public void setSelectedUser(Long selectedUser) {
		this.selectedUser = selectedUser;
	}

	public Date getVisitDate() {
		return visitDate;
	}

	public void setVisitDate(Date visitDate) {
		this.visitDate = visitDate;
	}

}
