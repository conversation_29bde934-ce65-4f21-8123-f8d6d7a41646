package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.dto.ActivityByPeriod;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.VisitCountDto;
import com.intellitech.birdnotes.model.dto.VisitHistoryDto;

@Repository
public interface VisitRepository extends JpaRepository<Visit, Long>{
	
	@Query("SELECT v from Visit v where v.delegate=:user and DATE(v.visitDate) = DATE(:visitDate) and v.prospect = :prospect")
	List<Visit>  findByVisitDateAndProspectAndUser(@Param("visitDate")Date visitDate,@Param("prospect") Prospect prospect,@Param("user") User user);
 	
	List<Visit> findByVisitDate(Date visitDate);
	
	List<Visit> findByVisitDateAndProspectId(Date visitDate,Long idProspect);
	
	@Override
	@Query("SELECT v from Visit v order by v.id")
	List<Visit> findAll();
	
	@Query("SELECT v.id from Visit v where v.delegate.id=:userId and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerUser(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("userId") Long userId);
	
	@Query("SELECT v.id from Visit v where v.prospect.speciality.id=:specialityId and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerSpeciality(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("specialityId") Long specialityId);
	
	@Query("SELECT v.id from Visit v where v.delegate.id=:userId and v.prospect.speciality.id=:specialityId and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerSpeciality(@Param("userId")Long userId,@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("specialityId") Long specialityId);
	
	@Query("SELECT v.id from Visit v where v.prospect.potential=:potential and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerPotential(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("potential") String potential);
	
	@Query("SELECT v.id from Visit v where v.delegate.id=:userId and v.prospect.potential=:potential and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerPotential(@Param("userId")Long userId,@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("potential") String potential);
	
	@Query("SELECT v.id from Visit v where v.prospect.activity=:activity and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerActivity(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("activity") String activity);
	
	@Query("SELECT v.id from Visit v where v.delegate.id=:userId and v.prospect.activity=:activity and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerActivity(@Param("userId")Long userId,@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("activity") String activity);
	
	@Query("SELECT count(v) from Visit v where v.prospect.sector.id=:sectorId and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	Long countVisitPerSector(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("sectorId") Long sectorId);
	
	@Query("SELECT v.id from Visit v where v.delegate.id=:userId and v.prospect.sector.id=:sectorId and (DATE(v.visitDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Visit> countVisitPerSector(@Param("userId")Long userId,@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("sectorId") Long sectorId);
	
	
	@Query("SELECT distinct vp.visit from VisitsProducts vp  where (date(vp.visit.visitDate) between date(?1) and date(?2)) and vp.visit.delegate.id = ?3 order by vp.visit.visitDate")
	List<Visit> findVisitReport(Date firstDate, Date lastDate, Long userId);
	
	
	@Query("SELECT v.prospect.id  from VisitsProducts vp join vp.visit v where (date(v.visitDate) between date(?1) and date(?2)) and v.delegate.id = ?3")
	List<Long> findVisitedProspectByDateAndUser(Date firstDate, Date lastDate, Long userId);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.VisitCountDto(v.delegate.id, v.delegate.firstName, v.delegate.lastName, count(v))  FROM Visit v WHERE DATE(v.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:fridayDate)"
			+ " GROUP BY v.delegate.id, v.delegate.firstName, v.delegate.lastName")
	List<VisitCountDto> visitAveragePerWeek(@Param("mondayDate") Date mondayDate, @Param("fridayDate") Date fridayDate);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.VisitCountDto(v.delegate.id, v.delegate.firstName, v.delegate.lastName, count(v))  "
			+ "FROM Visit v WHERE (DATE(v.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:fridayDate)) "
			+ "AND v.prospect.speciality.id=:specialityId"
			+ " GROUP BY v.delegate.id, v.delegate.firstName, v.delegate.lastName")
	List<VisitCountDto> visitAveragePerWeek(@Param("mondayDate") Date mondayDate, @Param("fridayDate") Date fridayDate,
			@Param("specialityId") Long specialityId);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.VisitCountDto(v.delegate.id, v.delegate.firstName, v.delegate.lastName, count(v),  v.prospect.sector.id)  FROM Visit v WHERE DATE(v.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:fridayDate)"
			+ " GROUP BY v.delegate.id, v.delegate.firstName, v.delegate.lastName, v.prospect.sector.id")
	List<VisitCountDto> visitAveragePerWeekWithSector(@Param("mondayDate") Date mondayDate, @Param("fridayDate") Date fridayDate);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.VisitCountDto(v.delegate.id, v.delegate.firstName, v.delegate.lastName, count(v), v.prospect.sector.id)  "
			+ "FROM Visit v WHERE (DATE(v.visitDate) BETWEEN DATE(:mondayDate) AND DATE(:fridayDate)) "
			+ "AND v.prospect.speciality.id=:specialityId"
			+ " GROUP BY v.delegate.id, v.delegate.firstName, v.delegate.lastName, v.prospect.sector.id")
	List<VisitCountDto> visitAveragePerWeekWithSector(@Param("mondayDate") Date mondayDate, @Param("fridayDate") Date fridayDate,
			@Param("specialityId") Long specialityId);

	@Query("SELECT v FROM Visit v WHERE v.prospect.id = ?1")
	List<Visit> findByProspectId(Long idProspect);
	
	@Query("SELECT count(v) FROM Visit v WHERE v.delegate.id = :userId")
	Long findByUsertId(@Param("userId") Long userId);

	@Query("UPDATE Visit v set v.prospect = 0 WHERE v.prospect.id = ?1")
	List<Visit> detachVisitFromProspect(Long idProspect);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.VisitCountDto"
			+ "(v.delegate.id, v.delegate.firstName, v.delegate.lastName, count(v)) "
			+ "FROM Visit v WHERE MONTH(v.visitDate)=:month AND YEAR(v.visitDate)=:year"
			+ " GROUP BY v.delegate.id, v.delegate.firstName, v.delegate.lastName")
	List<VisitCountDto> visitAveragePerMonth(@Param("month") Integer month, @Param("year") Integer year);
	
	@Query("SELECT v FROM Visit v where v.prospect.id =:id And v.visitDate >=  DATE(:date) ORDER BY v.id ASC")
	List<Visit> findProspectVisits(@Param("id") Long id,@Param("date") Date date);


	@Query("SELECT new com.intellitech.birdnotes.model.dto.ActivityByPeriod(vp.visit.visitDate, count(distinct vp.visit), sum(vp.orderQuantity) , sum (vp.sampleQuantity))  "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate)  GROUP BY vp.visit.visitDate")
	List<ActivityByPeriod> activityByPeriod(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);


	@Query("SELECT new com.intellitech.birdnotes.model.dto.ActivityByPeriod(vp.visit.prospect.potential.name, count(distinct vp.visit), sum(vp.orderQuantity) , sum (vp.sampleQuantity))  "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate)  GROUP BY vp.visit.prospect.potential.name")
	List<ActivityByPeriod> activityByPotential(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);


	@Query("SELECT new com.intellitech.birdnotes.model.dto.ActivityByPeriod(vp.visit.delegate.lastName, count(distinct vp.visit), sum(vp.orderQuantity) , sum (vp.sampleQuantity))  "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate)   GROUP BY vp.visit.delegate.lastName")
	List<ActivityByPeriod> activityByDelegate(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);
	
	
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.ActivityByPeriod(vp.visit.prospect.speciality.name, count(distinct vp.visit), sum(vp.orderQuantity) , sum (vp.sampleQuantity))  "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate)  GROUP BY vp.visit.prospect.speciality.name")
	List<ActivityByPeriod> activityBySpeciality(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(vp.smily, count(vp)) "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate)  GROUP BY vp.smily")
	List<LabelValueDto> getProspectsSatisfaction(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);
	
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.VisitHistoryDto(vp) from VisitsProducts vp where DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate)   and vp.urgent = true")
	List<VisitHistoryDto> findUrgentMessages(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);

	@Query("SELECT  sum(vp.orderQuantity * vp.product.price )  "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate) ")
	Float getSalesRevenuee(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate);


	@Query("SELECT  sum(vp.orderQuantity * vp.product.buyingPrice )  "
			+ "FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:stardDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate) "
			+ "and vp.product.id in (:productIds) and vp.visit.delegate.id =:userId")
	Float getProductCommissionRevenue(@Param("stardDate")  Date stardDate, @Param("endDate")  Date endDate , @Param("productIds")  List<Long> productIds, @Param("userId")  Long userId);


	
	@Query("SELECT v FROM Visit v WHERE v.id = ?1 ")
	Visit findById(Long id);

	
	@Query("SELECT v FROM Visit v WHERE v.identifier = ?1 And v.delegate.id = ?2")
	Visit findByIdentifier(Long identifier, Long userId);
	
	@Modifying
	@Query("Delete FROM Visit v WHERE v.identifier = ?1 And v.delegate.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);

	@Query("SELECT v FROM Visit v where v.doubleVisit.id =:visitDoubleId")
	Visit findByDoubleVisit(@Param("visitDoubleId") Long  visitDoubleId);
	
	@Query("SELECT v FROM Visit v where v.prospect.id in (SELECT p.prospect.id FROM  ProspectsAffectation p WHERE p.delegate.id=:userId) ORDER BY v.id ASC")
	List<Visit> getAffectedProspectVisits(@Param("userId") Long  userId);
	
	@Query("SELECT v FROM Visit v where v.delegate.id=:userId and v.prospect.id in (SELECT p.prospect.id FROM  ProspectsAffectation p WHERE p.delegate.id=:userId) ORDER BY v.id ASC")
	List<Visit> getDelegateVisits(@Param("userId") Long  userId);

	@Query("SELECT v FROM Visit v where v.delegate.id =:userId and v.doubleVisit is not null and DATE(v.visitDate) >=  DATE(:lastReceiveDate)")
	List<Visit> getDoubleVisits(@Param("userId") Long  userId, @Param("lastReceiveDate")  Date lastReceiveDate);

		
	@Query("SELECT v FROM Visit v where v.prospect.id in (SELECT p.prospect.id FROM  ProspectsAffectation p "
			+ "WHERE p.delegate.id=:userId) AND v.delegate.id != :userId ORDER BY v.id ASC")
	List<Visit> getCollegueSameAffectedProspectVisits(@Param("userId") Long  userId);
	
	@Query("SELECT v FROM Visit v where v.prospect.user.id = :userId AND v.prospect.status = 'NEW' ")
	List<Visit> getNewProspectWaitingForValidationVisits(@Param("userId") Long  userId);

	@Query("SELECT v.location FROM Visit v where v.id = :visitId  ")
	Location getVisitLocation(@Param("visitId") Long  visitId);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(vp.commentRating, count(vp.commentRating))"
			+ " FROM VisitsProducts vp WHERE DATE(vp.visit.visitDate) >=  DATE(:startDate) AND DATE(vp.visit.visitDate) <=  DATE(:endDate) "
			+ " and vp.commentRating <>'' and vp.commentRating is not null GROUP By vp.commentRating ")
	List<LabelValueDto> getCommentRatingAverage(@Param("startDate")  Date startDate, @Param("endDate")  Date endDate);
	
	@Query("SELECT v from Visit v where v.delegate=:delegate and DATE(v.visitDate) = DATE(:visitDate)")
	List<Visit> findByDateAndUser(@Param("visitDate") Date visitDate, @Param("delegate") Delegate delegate);

	
}
