package com.intellitech.birdnotes.service.impl;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.controller.UploadController;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.Planning;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.PlanningDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.NetworkRepository;
import com.intellitech.birdnotes.repository.PlanningRepository;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.ImportService;
import com.intellitech.birdnotes.model.dto.DelegateDto;


@Service("ImportService")
@Transactional
public class ImportServiceImpl implements ImportService {

	@Autowired
	private LocalityRepository localityRepository;

	@Autowired
	private RoleRepository roleRepository;

	@Autowired
	private NetworkRepository networkRepository;

	@Autowired
	private SectorRepository sectorRepository;

	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private DelegateRepository delegateRepository;

	@Autowired
	private SpecialityRepository specialityRepository;

	@Autowired
	private PotentialRepository potentialRepository;

	private DateFormat formatter = new SimpleDateFormat("dd/MM/yyyy");

	@Autowired
	private ProspectRepository prospectRepository;

	@Autowired
	private PlanningRepository planningRepository;

	@Autowired
	private ProductRepository productRepository;

	@Autowired
	private RangeRepository gammeRepository;
	private static final Logger LOG = LoggerFactory.getLogger(UploadController.class);

	private static final String PRODUCT_NAME_LABEL = "nom";
	private static final String PRODUCT_PRICE_LABEL = "prix";
	private static final String PRODUCT_RANGE_LABEL = "gamme";
	private static final String PRODUCT_NUMBER_OF_UNITY_LABEL = "unite";
	private static final String PRODUCT_CODE_LABEL = "code";
	private static final String PRODUCT_STOCK_LABEL = "stock";
	private static final String PRODUCT_DESCRIPTION_LABEL = "description";
	private static final String PRODUCT_WHOLESALE_PRICE_LABEL = "prix grossiste";
	private static final String PRODUCT_QUANTITY_LABEL = "quantite";

	@Autowired
	public ImportServiceImpl(SectorRepository sectorRepository, LocalityRepository localityRepository,
			UserRepository userRepository, SpecialityRepository specialityRepository,
			PotentialRepository potentialRepository, DelegateRepository delegateRepository) {
		super();
		this.sectorRepository = sectorRepository;
		this.localityRepository = localityRepository;
		this.userRepository = userRepository;
		this.specialityRepository = specialityRepository;
		this.potentialRepository = potentialRepository;
		this.delegateRepository = delegateRepository;
	}

	public Sheet readFromExcelFile(String filePath) throws IOException {

		Workbook workbook = null;
		try (FileInputStream fis = new FileInputStream(filePath)) {
			if (filePath.toLowerCase().endsWith("xlsx")) {
				workbook = new XSSFWorkbook(fis);
			} else if (filePath.toLowerCase().endsWith("xls")) {
				workbook = new HSSFWorkbook(fis);
			} else if (filePath.toLowerCase().endsWith("csv")) {
				workbook = new HSSFWorkbook(fis);
			}
		}
		if (workbook == null) {
			throw new NullPointerException("Workbook is null");
		}
		Sheet sheet = workbook.getSheetAt(0);
		return sheet;

	}

	private Map<String, Integer> getCellNames(Sheet sheet) {
		Map<String, Integer> cellNames = new HashMap<>();
		Row row = sheet.getRow(0); // Get first row

		short minColIndex = row.getFirstCellNum();
		short maxColIndex = row.getLastCellNum();

		for (short colIx = minColIndex; colIx < maxColIndex; colIx++) {
			Cell cell = row.getCell(colIx);
			if (cell != null) {
				cellNames.put(cell.getStringCellValue(), cell.getColumnIndex());
			}
		}
		return cellNames;
	}

	@Override
	public Map<String, List<PlanningDto>> getPlanningData(String filePath, Long userId)
			throws ParseException, IOException {
		Sheet sheet = readFromExcelFile(filePath);
		Map<String, Integer> cellNames = getCellNames(sheet);
		// Verify file header is what we expect
		Map<String, List<PlanningDto>> planningList = new HashMap<>();
		if (cellNames.get("id prospect") == null || cellNames.get("Date") == null) {
			throw new IllegalArgumentException(String.format("Unexpected header"));
		}
		Set<PlanningDto> emptyPlanningData = new HashSet<>();
		Set<PlanningDto> invalidPlanningProspects = new HashSet<>();
		Set<PlanningDto> duplicatePlanningProspects = new HashSet<>();
		emptyPlanningData = checkEmptyPlanningData(sheet);
		if (emptyPlanningData.size() != 0) {
			planningList.put("emptyData", new ArrayList<>(emptyPlanningData));
			return planningList;
		} else {
			invalidPlanningProspects = checkExistingPlanningData(sheet);
		}
		if (invalidPlanningProspects.size() != 0) {
			planningList.put("invalidProspects", new ArrayList<>(invalidPlanningProspects));
			return planningList;
		} else {
			duplicatePlanningProspects = checkDuplicatePlanning(sheet, userId);
		}
		if (duplicatePlanningProspects.size() != 0) {
			planningList.put("duplicateProspects", new ArrayList<>(duplicatePlanningProspects));
			return planningList;
		} else {
			Set<PlanningDto> listPlanning = new HashSet<>();
			for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
				PlanningDto planning = new PlanningDto();
				Row dataRow = sheet.getRow(i);
				Long prospectId = (long) dataRow.getCell(cellNames.get("id prospect")).getNumericCellValue();
				planning.setProspect(prospectId);
				Date date = dataRow.getCell(cellNames.get("Date")).getDateCellValue();
				planning.setDate(date);
				listPlanning.add(planning);
			}

			planningList.put("validData", new ArrayList<>(listPlanning));
			return planningList;
		}
	}

	private Set<PlanningDto> checkEmptyPlanningData(Sheet sheet) throws ParseException {
		Set<PlanningDto> emptyPlanningData = new HashSet<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			Long prospectId = (long) dataRow.getCell(cellNames.get("id prospect")).getNumericCellValue();
			Date planningDate = dataRow.getCell(cellNames.get("Date")).getDateCellValue();
			if (prospectId == null) {
				emptyPlanningData
						.add(new PlanningDto(null, null, null, planningDate, prospectId, "Prospect is empty", i));
			}
			if (planningDate == null) {
				emptyPlanningData.add(new PlanningDto(null, null, null, planningDate, prospectId, "Date is empty", i));
			}
		}
		return emptyPlanningData;
	}

	private Set<PlanningDto> checkExistingPlanningData(Sheet sheet) throws ParseException {
		Set<PlanningDto> invalidPlanningProspects = new HashSet<>();
		Map<String, Integer> cellNames = getCellNames(sheet);

		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			Long prospectId = (long) dataRow.getCell(cellNames.get("id prospect")).getNumericCellValue();
			Date planningDate = dataRow.getCell(cellNames.get("Date")).getDateCellValue();
			checkIfProspectExit(prospectId, planningDate, invalidPlanningProspects, i);
		}
		return invalidPlanningProspects;

	}

	private void checkIfProspectExit(Long prospectId, Date date, Set<PlanningDto> invalidPlanningProspects,
			Integer line) {
		Prospect prospect = prospectRepository.findById(prospectId);
		if (prospect == null) {
			invalidPlanningProspects
					.add(new PlanningDto(null, null, null, date, prospectId, "Invalid prospect ID", line));
		}
	}

	private Set<PlanningDto> checkDuplicatePlanning(Sheet sheet, Long userId) throws ParseException {
		Set<PlanningDto> duplicatePlanningProspects = new HashSet<>();
		Map<String, Integer> cellNames = getCellNames(sheet);
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			Long prospectId = (long) dataRow.getCell(cellNames.get("id prospect")).getNumericCellValue();
			Date planningDate = dataRow.getCell(cellNames.get("Date")).getDateCellValue();
			checkIfPlanningIsDupplicateFromDB(prospectId, planningDate, userId, duplicatePlanningProspects, i);
			for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
				if (i != j && rowsAreEqual(sheet.getRow(i), sheet.getRow(j))) {
					duplicatePlanningProspects.add(new PlanningDto(null, null, null, planningDate, prospectId,
							"Duplicate planning in the file", i));

				}
			}
		}

		return duplicatePlanningProspects;

	}

	private void checkIfPlanningIsDupplicateFromDB(Long prospectId, Date date, Long userId,
			Set<PlanningDto> duplicatePlanningProspects, Integer line) {
		Planning planning = planningRepository.findByProspectDateAndUser(prospectId, date, userId);
		if (planning != null) {
			duplicatePlanningProspects
					.add(new PlanningDto(null, null, null, date, prospectId, "Duplicate planning in the DB", line));
		}
	}

	private boolean rowsAreEqual(Row row1, Row row2) {
		int equalCount = 0;
		for (int i = 0; i < row1.getLastCellNum(); i++) {
			Cell c1 = row1.getCell(i);
			Cell c2 = row2.getCell(i);

			String s1 = getCellContentAsString(c1);
			String s2 = getCellContentAsString(c2);

			if (s1 != null && s1.equals(s2)) {
				equalCount++;
			} else if (s1 == null && s2 == null) {
				equalCount++;
			}
		}

		if (equalCount == row1.getLastCellNum()) {
			return true;
		}
		return false;
	}

	private String getCellContentAsString(Cell cell) {
		String data = null;
		if (cell != null) { // Check if cell is not null
			if (cell.getCellType() == Cell.CELL_TYPE_STRING) {
				data = cell.getStringCellValue();
			} else if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
				data = String.valueOf(cell.getNumericCellValue());
			}
		}
		return data;
	}

	@Override
	public Map<String, List<ProductDto>> getProductData(String filePath) throws ParseException, IOException {
		Sheet sheet = readFromExcelFile(filePath);
		Map<String, Integer> cellNames = getCellNames(sheet);
		Map<String, List<ProductDto>> productList = new HashMap<>();

		if (cellNames.get(PRODUCT_NAME_LABEL) == null || cellNames.get(PRODUCT_PRICE_LABEL) == null
				|| cellNames.get(PRODUCT_RANGE_LABEL) == null || cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL) == null
				|| cellNames.get(PRODUCT_CODE_LABEL) == null || cellNames.get(PRODUCT_STOCK_LABEL) == null
				|| cellNames.get(PRODUCT_DESCRIPTION_LABEL) == null
				|| cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL) == null
				|| cellNames.get(PRODUCT_QUANTITY_LABEL) == null) {
			throw new IllegalArgumentException(String.format("Unexpected header"));
		}
		Set<ProductDto> emptyProductData = new HashSet<>();
		Set<ProductDto> invalidProduct = new HashSet<>();
		Set<ProductDto> duplicateProduct = new HashSet<>();
		emptyProductData = checkEmptyProductData(sheet);
		if (emptyProductData.size() != 0) {
			productList.put("emptyData", new ArrayList<>(emptyProductData));
			return productList;
		} else {
			invalidProduct = checkExistingProductData(sheet);
		}
		if (invalidProduct.size() != 0) {
			productList.put("invalidData", new ArrayList<>(invalidProduct));
			return productList;
		} else {
			duplicateProduct = checkDuplicateProduct(sheet);
		}
		if (duplicateProduct.size() != 0) {
			productList.put("duplicateData", new ArrayList<>(duplicateProduct));

			return productList;
		}

		else {
			Set<ProductDto> listProduct = new HashSet<>();

			for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
				Row dataRow = sheet.getRow(i);
				ProductDto product = new ProductDto();
				if (dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)) != null) {
					product.setName(dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)) != null) {
					product.setPrice((float) dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)).getNumericCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)) != null) {
					product.setBuyingPrice((float) dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)).getNumericCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)) != null) {
					product.setNumberOfCapsules((int) dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)).getNumericCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)) != null) {
					product.setQuantityUnit(dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)) != null) {
					product.setStock((int) dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)).getNumericCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)) != null) {
					product.setCode(dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)) != null) {
					product.setRangesString(dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)) != null) {
					product.setDescription(dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)).getStringCellValue());

				}

				listProduct.add(product);

			}

			productList.put("validData", new ArrayList<>(listProduct));
			return productList;
		}
	}

	private Set<ProductDto> checkEmptyProductData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<ProductDto> emptyProductData = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			ProductDto product = new ProductDto();
			product.setLine(i);
				if (dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)) == null) {
				product.setMessage(product.getMessage() + " name is empty /");
				} else {
					product.setName(dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)).getStringCellValue());
				}	
				if (dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)) == null) {
					product.setMessage(product.getMessage() + " price is empty /");
				}else {
					product.setPrice((float) dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)).getNumericCellValue());
				}
				
				if (dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)) == null) {
					product.setMessage(product.getMessage() + " ranges is empty /");
				}else {
					product.setRangesString(dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)).getStringCellValue());
				}
				
				if (dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)) == null) {
					product.setMessage(product.getMessage() + " quantity is empty /");
				}else {
					product.setNumberOfCapsules((int) dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)).getNumericCellValue());
				}
				
				if (dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)) == null) {
					product.setMessage(product.getMessage() + " unity is empty /");
				}else {
					product.setQuantityUnit(dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)).getStringCellValue());
				}
				
				if (dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)) != null) {
					product.setBuyingPrice((float) dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)).getNumericCellValue());
				}
								
				if (dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)) != null) {
					product.setStock((int) dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)).getNumericCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)) != null) {
					product.setCode(dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)) != null) {
					product.setDescription(dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)).getStringCellValue());
				}

				if (product.getMessage() != null) {
					emptyProductData.add(product);

				}
			}


		return emptyProductData;
	}

	private Set<ProductDto> checkExistingProductData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<ProductDto> invalidProduct = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);

			ProductDto product = new ProductDto();
			product.setName(dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)).getStringCellValue());
			product.setPrice((float) dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)).getNumericCellValue());
			product.setNumberOfCapsules((int) dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)).getNumericCellValue());
			product.setQuantityUnit(dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)).getStringCellValue());
			product.setMessage("");
			if (dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)) != null) {
				product.setBuyingPrice((float) dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)).getNumericCellValue());
			}

			if (dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)) != null) {
				product.setStock((int) dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)).getNumericCellValue());
			}
			if (dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)) != null) {
				product.setCode(dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)) != null) {
				product.setDescription(dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)).getStringCellValue());
			}

			Map<String,List<String>> rangesExist = getRanges(dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)).getStringCellValue());
			if (rangesExist.get("notExistingRanges") != null ) {
				product.setLine(i);
				if(!"".equals(product.getMessage())){
					product.setMessage(product.getMessage() + " / ");
				}
				product.setMessage(product.getMessage() + "These ranges do not exist : "+ rangesExist.get("notExistingRanges"));
				invalidProduct.add(product);
			} else if (rangesExist.get("notExistingRanges") == null) {
				product.setRangesString(dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)).getStringCellValue());

			}
		}
		return invalidProduct;
	}


	private Set<ProductDto> checkDuplicateProduct(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<ProductDto> duplicateProduct = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			ProductDto product = new ProductDto();

			product.setRangesString(dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)).getStringCellValue());
			product.setName(dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)).getStringCellValue());
			product.setPrice((float) dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)).getNumericCellValue());
			product.setNumberOfCapsules((int) dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)).getNumericCellValue());
			product.setQuantityUnit(dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)).getStringCellValue());
			if (dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)) != null) {
				product.setBuyingPrice((float) dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)).getNumericCellValue());
			}

			if (dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)) != null) {
				product.setStock((int) dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)).getNumericCellValue());
			}
			if (dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)) != null) {
				product.setCode(dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)) != null) {
				product.setDescription(dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)).getStringCellValue());
			}

			Product productFromBD = checkIfProductIsDupplicate(
					dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)).getStringCellValue());
			if (productFromBD != null) {
				product.setLine(i);
				product.setMessage("dupplicate product from BD");
				duplicateProduct.add(product);
			}
			for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
				if (i != j && rowsAreEqual(sheet.getRow(i), sheet.getRow(j))) {
					ProductDto productFromFile = new ProductDto();
					productFromFile.setRangesString(dataRow.getCell(cellNames.get(PRODUCT_RANGE_LABEL)).getStringCellValue());
					productFromFile.setLine(i);
					productFromFile.setMessage("duplicate product from file");
					productFromFile.setName(dataRow.getCell(cellNames.get(PRODUCT_NAME_LABEL)).getStringCellValue());
					productFromFile.setPrice((float) dataRow.getCell(cellNames.get(PRODUCT_PRICE_LABEL)).getNumericCellValue());
					productFromFile.setNumberOfCapsules((int) dataRow.getCell(cellNames.get(PRODUCT_QUANTITY_LABEL)).getNumericCellValue());
					productFromFile.setQuantityUnit(dataRow.getCell(cellNames.get(PRODUCT_NUMBER_OF_UNITY_LABEL)).getStringCellValue());

					if (dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)) != null) {
						productFromFile.setBuyingPrice((float) dataRow.getCell(cellNames.get(PRODUCT_WHOLESALE_PRICE_LABEL)).getNumericCellValue());
					}
					if (dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)) != null) {
						productFromFile.setStock((int) dataRow.getCell(cellNames.get(PRODUCT_STOCK_LABEL)).getNumericCellValue());
					}
					if (dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)) != null) {
						productFromFile.setCode(dataRow.getCell(cellNames.get(PRODUCT_CODE_LABEL)).getStringCellValue());
					}
					if (dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)) != null) {
						productFromFile.setDescription(dataRow.getCell(cellNames.get(PRODUCT_DESCRIPTION_LABEL)).getStringCellValue());
					}
					duplicateProduct.add(productFromFile);
				}
			}
		}
		return duplicateProduct;

	}

	private Product checkIfProductIsDupplicate(String name) {
		return productRepository.findByName(name);
	}

	private static final String DELEGATE_FIRST_NAME = "prenom";
	private static final String DELEGATE_LAST_NAME = "nom";
	private static final String DELEGATE_RANGES = "gammes";
	private static final String DELEGATE_NETWORK = "reseaux";
	private static final String DELEGATE_TYPE = "type";
	private static final String DELEGATE_GENDER = "sexe";
	private static final String DELEGATE_CONTRACT_TYPE = "type_contrat";
	private static final String DELEGATE_SUPERVISEURS = "superviseurs";
	private static final String DELEGATE_ROLE = "role";
	private static final String DELEGATE_PHONE = "tel";
	private static final String DELEGATE_EMAIL = "email";
	private static final String DELEGATE_NAME = "username";
	private static final String DELEGATE_PASSWORD = "password";
	private static final String DELEGATE_CIN = "cin";
	private static final String DELEGATE_WORKING_DAYS = "jour_travail";
	private static final String DELEGATE_CAR_TYPE = "type_voiture";
	private static final String DELEGATE_BIRTHDAY_DATE = "birthday_date";
	private static final String DELEGATE_HIRING_DATE = "date_embauche";
	private static final String DELEGATE_CYCLE = "cycle";

	@Override
	public Map<String, List<DelegateDto>> getDelegateData(String filePath) throws ParseException, IOException {
		Sheet sheet = readFromExcelFile(filePath);
		Map<String, Integer> cellNames = getCellNames(sheet);
		Map<String, List<DelegateDto>> userList = new HashMap<>();
		if (cellNames.get(DELEGATE_LAST_NAME) == null || cellNames.get(DELEGATE_FIRST_NAME) == null
				|| cellNames.get(DELEGATE_RANGES) == null || cellNames.get(DELEGATE_NETWORK) == null
				|| cellNames.get(DELEGATE_TYPE) == null || cellNames.get(DELEGATE_GENDER) == null
				|| cellNames.get(DELEGATE_CONTRACT_TYPE) == null || cellNames.get(DELEGATE_SUPERVISEURS) == null
				|| cellNames.get(DELEGATE_ROLE) == null || cellNames.get(DELEGATE_PHONE) == null
				|| cellNames.get(DELEGATE_EMAIL) == null || cellNames.get(DELEGATE_NAME) == null
				|| cellNames.get(DELEGATE_PASSWORD) == null || cellNames.get(DELEGATE_CIN) == null
				|| cellNames.get(DELEGATE_WORKING_DAYS) == null || cellNames.get(DELEGATE_CAR_TYPE) == null
				|| cellNames.get(DELEGATE_BIRTHDAY_DATE) == null || cellNames.get(DELEGATE_HIRING_DATE) == null
				|| cellNames.get(DELEGATE_CYCLE) == null) {
			throw new IllegalArgumentException(String.format("Unexpected header"));
		}
		Set<DelegateDto> emptyDelegateData = new HashSet<>();
		Set<DelegateDto> invalidDelegate = new HashSet<>();
		Set<DelegateDto> duplicateDelegate = new HashSet<>();
		emptyDelegateData = checkEmptyDelegateData(sheet);
		if (emptyDelegateData.size() != 0) {
			userList.put("emptyData", new ArrayList<>(emptyDelegateData));
			return userList;
		} else {
			invalidDelegate = checkExistingDelegateData(sheet);
		}
		if (invalidDelegate.size() != 0) {
			userList.put("invalidData", new ArrayList<>(invalidDelegate));
			return userList;
		} else {
			duplicateDelegate = checkDuplicateDelegate(sheet);
		}
		if (duplicateDelegate.size() != 0) {
			userList.put("duplicateData", new ArrayList<>(duplicateDelegate));

			return userList;
		} else {
			Set<DelegateDto> listDelegate = new HashSet<>();

			for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
				Row dataRow = sheet.getRow(i);
				DelegateDto user = new DelegateDto();
				user.setFirstName(dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)).getStringCellValue());
				user.setLastName(dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME)).getStringCellValue());
				user.setGammesString(dataRow.getCell(cellNames.get(DELEGATE_RANGES)).getStringCellValue());
				user.setNetworkString(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());
				user.setWorkType(dataRow.getCell(cellNames.get(DELEGATE_TYPE)).getStringCellValue());
				user.setRolesString(dataRow.getCell(cellNames.get(DELEGATE_ROLE)).getStringCellValue());
				user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_PHONE)).getNumericCellValue()));
				user.setCycle((short) dataRow.getCell(cellNames.get(DELEGATE_CYCLE)).getNumericCellValue());
				user.setUsername(dataRow.getCell(cellNames.get(DELEGATE_NAME)).getStringCellValue());
				user.setPassword(dataRow.getCell(cellNames.get(DELEGATE_PASSWORD)).getStringCellValue());
				user.setWorkingDaysPerWeek(
						(int) dataRow.getCell(cellNames.get(DELEGATE_WORKING_DAYS)).getNumericCellValue());
				user.setSupervisorsString(dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)).getStringCellValue());
				if (dataRow.getCell(cellNames.get(DELEGATE_GENDER)) != null) {
					user.setGender(dataRow.getCell(cellNames.get(DELEGATE_GENDER)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)) != null) {
					user.setContractType(dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)).getStringCellValue());
				}

				if (dataRow.getCell(cellNames.get(DELEGATE_EMAIL)) != null) {
					user.setEmail(dataRow.getCell(cellNames.get(DELEGATE_EMAIL)).getStringCellValue());
				}

				if (dataRow.getCell(cellNames.get(DELEGATE_CIN)) != null) {
					user.setCin(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_CIN)).getNumericCellValue()));
				}

				if (dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)) != null) {
					user.setCarType(dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)).getStringCellValue());
				}
				if (dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)) != null) {
					user.setBirthdayDate(dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)).getDateCellValue());
				}
				if (dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)) != null) {
					user.setHiringDate(dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)).getDateCellValue());
				}

				listDelegate.add(user);

			}

			userList.put("validData", new ArrayList<>(listDelegate));
			return userList;
		}
	}

	private Set<DelegateDto> checkEmptyDelegateData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<DelegateDto> emptyDelegateData = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			DelegateDto user = new DelegateDto();
			user.setLine(i);
			user.setMessage("");
						
			if (dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)) != null) {
				user.setFirstName(dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)).getStringCellValue());	

			}else {
				user.setMessage(user.getMessage()+" First name is empty /");
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME))!= null) {
				user.setLastName(dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME)).getStringCellValue());
			}else {
				user.setMessage(user.getMessage()+" Last name is empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_RANGES)) != null) {
				user.setGammesString(dataRow.getCell(cellNames.get(DELEGATE_RANGES)).getStringCellValue());
			}else {
				user.setMessage(user.getMessage()+" Ranges are empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)) != null) {
				user.setNetworkString(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());				
			}else {
				user.setMessage(user.getMessage()+" Network is empty ");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_TYPE)) != null){
				user.setWorkType(dataRow.getCell(cellNames.get(DELEGATE_TYPE)).getStringCellValue());
			}else {
				user.setMessage(user.getMessage()+" Type is empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)) != null) {
				user.setSupervisorsString(dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)).getStringCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Supervisors are empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_ROLE)) !=  null) {
				user.setRolesString(dataRow.getCell(cellNames.get(DELEGATE_ROLE)).getStringCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Roles are empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_PHONE)) != null){
				user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_PHONE)).getNumericCellValue()));
				
			}else {
				user.setMessage(user.getMessage()+" Delegate phone is empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_NAME)) != null) {
				user.setUsername(dataRow.getCell(cellNames.get(DELEGATE_NAME)).getStringCellValue());
					
			}else {
				user.setMessage(user.getMessage()+" Username is empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_PASSWORD)) != null) {
				user.setPassword(dataRow.getCell(cellNames.get(DELEGATE_PASSWORD)).getStringCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Password is empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_WORKING_DAYS))!= null) {
				user.setWorkingDaysPerWeek((int) dataRow.getCell(cellNames.get(DELEGATE_WORKING_DAYS)).getNumericCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Number of working days is empty /");
			}
			if(dataRow.getCell(cellNames.get(DELEGATE_CYCLE)) != null) {
				user.setCycle((short) dataRow.getCell(cellNames.get(DELEGATE_CYCLE)).getNumericCellValue());

			}else {
				user.setMessage(user.getMessage()+" Cycle is empty /");
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_GENDER)) != null) {
				user.setGender(dataRow.getCell(cellNames.get(DELEGATE_GENDER)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)) != null) {
				user.setContractType(dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)).getStringCellValue());
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_EMAIL)) != null) {
				user.setEmail(dataRow.getCell(cellNames.get(DELEGATE_EMAIL)).getStringCellValue());
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_CIN)) != null) {
				user.setCin(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_CIN)).getNumericCellValue()));
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)) != null) {
				user.setCarType(dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)) != null) {
				user.setBirthdayDate(dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)).getDateCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)) != null) {
				user.setHiringDate(dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)).getDateCellValue());
			}
			
			if (!"".equals(user.getMessage())) {
				emptyDelegateData.add(user);
			}
			
		}
		return emptyDelegateData;
	}

	private Set<DelegateDto> checkExistingDelegateData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<DelegateDto> invalidDelegate = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);

			DelegateDto user = new DelegateDto();
			user.setMessage("");
			user.setFirstName(dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)).getStringCellValue());
			user.setLastName(dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME)).getStringCellValue());
			user.setWorkType(dataRow.getCell(cellNames.get(DELEGATE_TYPE)).getStringCellValue());
			user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_PHONE)).getNumericCellValue()));
			user.setCycle((short) dataRow.getCell(cellNames.get(DELEGATE_CYCLE)).getNumericCellValue());
			user.setUsername(dataRow.getCell(cellNames.get(DELEGATE_NAME)).getStringCellValue());
			user.setPassword(dataRow.getCell(cellNames.get(DELEGATE_PASSWORD)).getStringCellValue());
			user.setWorkingDaysPerWeek((int) dataRow.getCell(cellNames.get(DELEGATE_WORKING_DAYS)).getNumericCellValue());

			if (dataRow.getCell(cellNames.get(DELEGATE_GENDER)) != null) {
				user.setGender(dataRow.getCell(cellNames.get(DELEGATE_GENDER)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)) != null) {
				user.setContractType(dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)).getStringCellValue());
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_EMAIL)) != null) {
				user.setEmail(dataRow.getCell(cellNames.get(DELEGATE_EMAIL)).getStringCellValue());
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_CIN)) != null) {
				user.setCin(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_CIN)).getNumericCellValue()));
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)) != null) {
				user.setCarType(dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)) != null) {
				user.setBirthdayDate(dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)).getDateCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)) != null) {
				user.setHiringDate(dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)).getDateCellValue());
			}

			Map<String, List<String>> rangesExist = getRanges(dataRow.getCell(cellNames.get(DELEGATE_RANGES)).getStringCellValue());
			if (rangesExist.get("notExistingRanges") != null ) {
				user.setLine(i);
				if(!"".equals(user.getMessage())){
					user.setMessage(user.getMessage() + " / ");
				}
				user.setMessage(user.getMessage() + "These ranges do not exist : "+ rangesExist.get("notExistingRanges"));
				invalidDelegate.add(user);
			} else if (rangesExist.get("notExistingRanges") == null) {
				user.setGammesString(dataRow.getCell(cellNames.get(DELEGATE_RANGES)).getStringCellValue());

			}
			
			Map<String,List<String>> rolesExist = getRoles(dataRow.getCell(cellNames.get(DELEGATE_ROLE)).getStringCellValue());
			if (rolesExist.get("notExistingRoles")  != null) {
				user.setLine(i);
				if(!"".equals(user.getMessage())){
					user.setMessage(user.getMessage() + " / ");
				}
				user.setMessage(user.getMessage() + "these roles do not exist :" + rolesExist.get("notExistingRoles"));
				invalidDelegate.add(user);
			} else if (rolesExist.get("notExistingRoles") == null) {
				user.setRolesString(dataRow.getCell(cellNames.get(DELEGATE_ROLE)).getStringCellValue());
			}
			
			Boolean networksExist = getNetwork(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());
			if (networksExist == false) {
				user.setLine(i);
				if(!"".equals(user.getMessage())){
					user.setMessage(user.getMessage() + " / ");
				}
				user.setMessage(user.getMessage() +"the network do not exist :" + dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());
				invalidDelegate.add(user);
			} else if (networksExist == true) {
				user.setNetworkString(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());
			}
			Map<String,List<String>> SupervisorsExist = getSupervisor(dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)).getStringCellValue());
			if (SupervisorsExist.get("notExistingSupervisors") != null) {
				user.setLine(i);
				if(!"".equals(user.getMessage())){
					user.setMessage(user.getMessage() + " / ");
				}
				user.setMessage(user.getMessage() + "these supervisors do not exist " + SupervisorsExist.get("notExistingSupervisors") );
				invalidDelegate.add(user);
			} else if (SupervisorsExist.get("notExistingSupervisors") == null ) {
				user.setSupervisorsString(dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)).getStringCellValue());
			}
		}
		return invalidDelegate;
	}

	private Boolean getNetwork(String networkNames) {
		Boolean testNetwork = true;
		if (networkRepository.findByName(networkNames.trim()) == null) {
			testNetwork = false;
		}
		return testNetwork;
	}
	
	private Map<String,List<String>> getRanges(String rangeNames) {
		Map<String,List<String>> rangeLists = new HashMap<>();
		Set<String> invalidRanges = new HashSet<>();
		
		String[] ranges = rangeNames.split(",");
		for (String rangeName : ranges) {
			if (gammeRepository.findByName(rangeName.trim()) == null) {
				invalidRanges.add(rangeName.trim());
			}
		}
		if(invalidRanges.size() != 0) {
			rangeLists.put("notExistingRanges",new ArrayList<>(invalidRanges));
		}
		return rangeLists;
	}

	private Map<String,List<String>> getRoles(String roleNames) {
		Map<String,List<String>> testRoles = new HashMap<>();
		Set<String> invalidRoles = new HashSet<>();
		String[] roles = roleNames.split(",");
		for (String roleName : roles) {			
			if (roleRepository.findByName(roleName.trim()) == null) {
				invalidRoles.add(roleName.trim());
			}
		}
		if(invalidRoles.size() != 0) {
			testRoles.put("notExistingRoles",new ArrayList<>(invalidRoles));
		}
		return testRoles;
	}

	private Map<String,List<String>> getSupervisor(String SupervisorNames) {
		Map<String,List<String>> testSupervisors = new HashMap<>();
		Set<String> invalidSupervisors = new HashSet<>();
		String[] roles = SupervisorNames.split(",");
		for (String SupervisorName : roles) {
			if (userRepository.findByUsername(SupervisorName.trim()) == null) {
				invalidSupervisors.add(SupervisorName);
			}			
		}
		if(invalidSupervisors.size() != 0) {
			testSupervisors.put("notExistingSupervisors", new ArrayList<>(invalidSupervisors));
		}
		return testSupervisors;
	}
	

	private Set<DelegateDto> checkDuplicateDelegate(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<DelegateDto> duplicateDelegate = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			DelegateDto user = new DelegateDto();

			user.setFirstName(dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)).getStringCellValue());
			user.setLastName(dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME)).getStringCellValue());
			user.setGammesString(dataRow.getCell(cellNames.get(DELEGATE_RANGES)).getStringCellValue());
			user.setNetworkString(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());
			user.setWorkType(dataRow.getCell(cellNames.get(DELEGATE_TYPE)).getStringCellValue());
			user.setRolesString(dataRow.getCell(cellNames.get(DELEGATE_ROLE)).getStringCellValue());
			user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_PHONE)).getNumericCellValue()));
			user.setCycle((short) dataRow.getCell(cellNames.get(DELEGATE_CYCLE)).getNumericCellValue());
			user.setUsername(dataRow.getCell(cellNames.get(DELEGATE_NAME)).getStringCellValue());
			user.setPassword(dataRow.getCell(cellNames.get(DELEGATE_PASSWORD)).getStringCellValue());
			user.setWorkingDaysPerWeek((int) dataRow.getCell(cellNames.get(DELEGATE_WORKING_DAYS)).getNumericCellValue());
			user.setSupervisorsString(dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)).getStringCellValue());
			if (dataRow.getCell(cellNames.get(DELEGATE_GENDER)) != null) {
				user.setGender(dataRow.getCell(cellNames.get(DELEGATE_GENDER)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)) != null) {
				user.setContractType(dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)).getStringCellValue());
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_EMAIL)) != null) {
				user.setEmail(dataRow.getCell(cellNames.get(DELEGATE_EMAIL)).getStringCellValue());
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_CIN)) != null) {
				user.setCin(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_CIN)).getNumericCellValue()));
			}

			if (dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)) != null) {
				user.setCarType(dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)).getStringCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)) != null) {
				user.setBirthdayDate(dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)).getDateCellValue());
			}
			if (dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)) != null) {
				user.setHiringDate(dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)).getDateCellValue());
			}

			Delegate userFromBD = checkIfDelegateIsDupplicate(
					dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)).getStringCellValue(),
					dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME)).getStringCellValue());
			if (userFromBD != null) {
				user.setLine(i);
				user.setMessage("duplicate from BD");
				duplicateDelegate.add(user);
			}
			for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
				if (i != j && rowsAreEqual(sheet.getRow(i), sheet.getRow(j))) {
					DelegateDto delegateFromFile = new DelegateDto();
					delegateFromFile.setLine(i);
					delegateFromFile.setMessage("duplicate from file");
					delegateFromFile.setFirstName(dataRow.getCell(cellNames.get(DELEGATE_FIRST_NAME)).getStringCellValue());
					delegateFromFile.setLastName(dataRow.getCell(cellNames.get(DELEGATE_LAST_NAME)).getStringCellValue());
					delegateFromFile.setGammesString(dataRow.getCell(cellNames.get(DELEGATE_RANGES)).getStringCellValue());
					delegateFromFile.setNetworkString(dataRow.getCell(cellNames.get(DELEGATE_NETWORK)).getStringCellValue());
					delegateFromFile.setWorkType(dataRow.getCell(cellNames.get(DELEGATE_TYPE)).getStringCellValue());
					delegateFromFile.setRolesString(dataRow.getCell(cellNames.get(DELEGATE_ROLE)).getStringCellValue());
					delegateFromFile.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_PHONE)).getNumericCellValue()));
					delegateFromFile.setCycle((short) dataRow.getCell(cellNames.get(DELEGATE_CYCLE)).getNumericCellValue());
					delegateFromFile.setUsername(dataRow.getCell(cellNames.get(DELEGATE_NAME)).getStringCellValue());
					delegateFromFile.setPassword(dataRow.getCell(cellNames.get(DELEGATE_PASSWORD)).getStringCellValue());
					delegateFromFile.setWorkingDaysPerWeek((int) dataRow.getCell(cellNames.get(DELEGATE_WORKING_DAYS)).getNumericCellValue());
					delegateFromFile.setSupervisorsString(
							dataRow.getCell(cellNames.get(DELEGATE_SUPERVISEURS)).getStringCellValue());
					if (dataRow.getCell(cellNames.get(DELEGATE_GENDER)) != null) {
						delegateFromFile.setGender(dataRow.getCell(cellNames.get(DELEGATE_GENDER)).getStringCellValue());
					}
					if (dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)) != null) {
						delegateFromFile.setContractType(
								dataRow.getCell(cellNames.get(DELEGATE_CONTRACT_TYPE)).getStringCellValue());
					}

					if (dataRow.getCell(cellNames.get(DELEGATE_EMAIL)) != null) {
						delegateFromFile.setEmail(dataRow.getCell(cellNames.get(DELEGATE_EMAIL)).getStringCellValue());
					}

					if (dataRow.getCell(cellNames.get(DELEGATE_CIN)) != null) {
						delegateFromFile.setCin(
								String.valueOf((int) dataRow.getCell(cellNames.get(DELEGATE_CIN)).getNumericCellValue()));
					}

					if (dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)) != null) {
						delegateFromFile.setCarType(dataRow.getCell(cellNames.get(DELEGATE_CAR_TYPE)).getStringCellValue());
					}
					if (dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)) != null) {
						delegateFromFile
								.setBirthdayDate(dataRow.getCell(cellNames.get(DELEGATE_BIRTHDAY_DATE)).getDateCellValue());
					}
					if (dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)) != null) {
						delegateFromFile.setHiringDate(dataRow.getCell(cellNames.get(DELEGATE_HIRING_DATE)).getDateCellValue());
					}

					duplicateDelegate.add(delegateFromFile);
				}
			}
		}
		return duplicateDelegate;

	}

	private Delegate checkIfDelegateIsDupplicate(String firstName, String lastName) {

		return delegateRepository.findByFirstLastName(firstName, lastName);
	}
	
	


	
	
	
	
	
	private static final String USER_SUPERVISEURS = "superviseurs";
	private static final String USER_ROLE = "role";
	private static final String USER_PHONE = "tel";
	private static final String USER_EMAIL = "email";
	private static final String USER_NAME = "username";
	private static final String USER_PASSWORD = "password";
	

	@Override
	public Map<String, List<UserDto>> getUserData(String filePath) throws ParseException, IOException {
		Sheet sheet = readFromExcelFile(filePath);
		Map<String, Integer> cellNames = getCellNames(sheet);
		Map<String, List<UserDto>> userList = new HashMap<>();
		if (cellNames.get(USER_SUPERVISEURS) == null
				|| cellNames.get(USER_ROLE) == null || cellNames.get(USER_PHONE) == null
				|| cellNames.get(USER_EMAIL) == null || cellNames.get(USER_NAME) == null
				|| cellNames.get(USER_PASSWORD) == null) {
			throw new IllegalArgumentException(String.format("Unexpected header"));
		}
		Set<UserDto> emptyUserData = new HashSet<>();
		Set<UserDto> invalidUser = new HashSet<>();
		Set<UserDto> duplicateUser = new HashSet<>();
		emptyUserData = checkEmptyUserData(sheet);
		if (emptyUserData.size() != 0) {
			userList.put("emptyData", new ArrayList<>(emptyUserData));
			return userList;
		} else {
			invalidUser = checkExistingUserData(sheet);
		}
		if (invalidUser.size() != 0) {
			userList.put("invalidData", new ArrayList<>(invalidUser));
			return userList;
		} else {
			duplicateUser = checkDuplicateUser(sheet);
		}
		if (duplicateUser.size() != 0) {
			userList.put("duplicateData", new ArrayList<>(duplicateUser));

			return userList;
		} else {
			Set<UserDto> listUser = new HashSet<>();

			for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
				Row dataRow = sheet.getRow(i);
				UserDto user = new UserDto();
				user.setRolesString(dataRow.getCell(cellNames.get(USER_ROLE)).getStringCellValue());
				user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(USER_PHONE)).getNumericCellValue()));
				user.setUsername(dataRow.getCell(cellNames.get(USER_NAME)).getStringCellValue());
				user.setPassword(dataRow.getCell(cellNames.get(USER_PASSWORD)).getStringCellValue());
				user.setSupervisorsString(dataRow.getCell(cellNames.get(USER_SUPERVISEURS)).getStringCellValue());
				if (dataRow.getCell(cellNames.get(USER_EMAIL)) != null) {
					user.setEmail(dataRow.getCell(cellNames.get(USER_EMAIL)).getStringCellValue());
				}
				listUser.add(user);

			}

			userList.put("validData", new ArrayList<>(listUser));
			return userList;
		}
	}

	private Set<UserDto> checkEmptyUserData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<UserDto> emptyUserData = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			UserDto user = new UserDto();
			user.setLine(i);
			user.setMessage("");
						
			if(dataRow.getCell(cellNames.get(USER_SUPERVISEURS)) != null) {
				user.setSupervisorsString(dataRow.getCell(cellNames.get(USER_SUPERVISEURS)).getStringCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Supervisors are empty /");
			}
			if(dataRow.getCell(cellNames.get(USER_ROLE)) !=  null) {
				user.setRolesString(dataRow.getCell(cellNames.get(USER_ROLE)).getStringCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Roles are empty /");
			}
			if(dataRow.getCell(cellNames.get(USER_PHONE)) != null){
				user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(USER_PHONE)).getNumericCellValue()));
				
			}else {
				user.setMessage(user.getMessage()+" User phone is empty /");
			}
			if(dataRow.getCell(cellNames.get(USER_NAME)) != null) {
				user.setUsername(dataRow.getCell(cellNames.get(USER_NAME)).getStringCellValue());
					
			}else {
				user.setMessage(user.getMessage()+" Username is empty /");
			}
			if(dataRow.getCell(cellNames.get(USER_PASSWORD)) != null) {
				user.setPassword(dataRow.getCell(cellNames.get(USER_PASSWORD)).getStringCellValue());
				
			}else {
				user.setMessage(user.getMessage()+" Password is empty /");
			}
			if (dataRow.getCell(cellNames.get(USER_EMAIL)) != null) {
				user.setEmail(dataRow.getCell(cellNames.get(USER_EMAIL)).getStringCellValue());
			}else {
				user.setMessage(user.getMessage()+" Email is empty /");
			}
			if (!"".equals(user.getMessage())) {
				emptyUserData.add(user);
			}
			
		}
		return emptyUserData;
	}

	private Set<UserDto> checkExistingUserData(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<UserDto> invalidUser = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);

			UserDto user = new UserDto();
			user.setMessage("");
			user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(USER_PHONE)).getNumericCellValue()));
			user.setUsername(dataRow.getCell(cellNames.get(USER_NAME)).getStringCellValue());
			user.setPassword(dataRow.getCell(cellNames.get(USER_PASSWORD)).getStringCellValue());
			
			if (dataRow.getCell(cellNames.get(USER_EMAIL)) != null) {
				user.setEmail(dataRow.getCell(cellNames.get(USER_EMAIL)).getStringCellValue());
			}

			Map<String,List<String>> rolesExist = getRoles(dataRow.getCell(cellNames.get(USER_ROLE)).getStringCellValue());
			if (rolesExist.get("notExistingRoles")  != null) {
				user.setLine(i);
				if(!"".equals(user.getMessage())){
					user.setMessage(user.getMessage() + " / ");
				}
				user.setMessage(user.getMessage() + "these roles do not exist :" + rolesExist.get("notExistingRoles"));
				invalidUser.add(user);
			} else if (rolesExist.get("notExistingRoles") == null) {
				user.setRolesString(dataRow.getCell(cellNames.get(USER_ROLE)).getStringCellValue());
			}
			
			
			Map<String,List<String>> SupervisorsExist = getSupervisor(dataRow.getCell(cellNames.get(USER_SUPERVISEURS)).getStringCellValue());
			if (SupervisorsExist.get("notExistingSupervisors") != null) {
				user.setLine(i);
				if(!"".equals(user.getMessage())){
					user.setMessage(user.getMessage() + " / ");
				}
				user.setMessage(user.getMessage() + "these supervisors do not exist " + SupervisorsExist.get("notExistingSupervisors") );
				invalidUser.add(user);
			} else if (SupervisorsExist.get("notExistingSupervisors") == null ) {
				user.setSupervisorsString(dataRow.getCell(cellNames.get(USER_SUPERVISEURS)).getStringCellValue());
			}
		}
		return invalidUser;
	}




	

	private Set<UserDto> checkDuplicateUser(Sheet sheet) throws ParseException {
		Map<String, Integer> cellNames = getCellNames(sheet);
		Set<UserDto> duplicateUser = new HashSet<>();
		for (int i = 1; i < sheet.getPhysicalNumberOfRows(); i++) {
			Row dataRow = sheet.getRow(i);
			UserDto user = new UserDto();
			user.setRolesString(dataRow.getCell(cellNames.get(USER_ROLE)).getStringCellValue());
			user.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(USER_PHONE)).getNumericCellValue()));
			user.setUsername(dataRow.getCell(cellNames.get(USER_NAME)).getStringCellValue());
			user.setPassword(dataRow.getCell(cellNames.get(USER_PASSWORD)).getStringCellValue());
			user.setSupervisorsString(dataRow.getCell(cellNames.get(USER_SUPERVISEURS)).getStringCellValue());
			
			if (dataRow.getCell(cellNames.get(USER_EMAIL)) != null) {
				user.setEmail(dataRow.getCell(cellNames.get(USER_EMAIL)).getStringCellValue());
			}

			User userFromBD = checkIfUserIsDupplicate(
					dataRow.getCell(cellNames.get(USER_NAME)).getStringCellValue());
			if (userFromBD != null) {
				user.setLine(i);
				user.setMessage("duplicate from BD");
				duplicateUser.add(user);
			}
			for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
				if (i != j && rowsAreEqual(sheet.getRow(i), sheet.getRow(j))) {
					UserDto userFromFile = new UserDto();
					userFromFile.setLine(i);
					userFromFile.setMessage("duplicate from file");
					userFromFile.setRolesString(dataRow.getCell(cellNames.get(USER_ROLE)).getStringCellValue());
					userFromFile.setPhone(String.valueOf((int) dataRow.getCell(cellNames.get(USER_PHONE)).getNumericCellValue()));
					userFromFile.setUsername(dataRow.getCell(cellNames.get(USER_NAME)).getStringCellValue());
					userFromFile.setPassword(dataRow.getCell(cellNames.get(USER_PASSWORD)).getStringCellValue());
					userFromFile.setSupervisorsString(
							dataRow.getCell(cellNames.get(USER_SUPERVISEURS)).getStringCellValue());
					

					if (dataRow.getCell(cellNames.get(USER_EMAIL)) != null) {
						userFromFile.setEmail(dataRow.getCell(cellNames.get(USER_EMAIL)).getStringCellValue());
					}

					duplicateUser.add(userFromFile);
				}
			}
		}
		return duplicateUser;

	}

	private User checkIfUserIsDupplicate(String userName) {

		return userRepository.findByUsername(userName);
	}

	@Override
	public void importProspect(String filePath) throws BirdnotesException {

		/*
		 * try { ReadExcelFileUtil<ProspectDto> readExcelFileUtil = new
		 * ReadExcelFileUtil<ProspectDto>(); readExcelFileUtil.setEntity(new
		 * ProspectDto()); List<ProspectDto> prospectDtos =
		 * readExcelFileUtil.readDataFromExcelFile(filePath);
		 * 
		 * for (ProspectDto prospectDto : prospectDtos) {
		 * 
		 * // if (prospectDto.getName() != null) {
		 * 
		 * Sector sector =
		 * sectorRepository.findFirstByNameIgnoreCase(prospectDto.getSectorDto().getName
		 * ().trim().toUpperCase()); if(sector == null) {
		 * 
		 * sector = new Sector();
		 * sector.setName(prospectDto.getSectorDto().getName().trim());
		 * sectorRepository.save(sector);
		 * 
		 * } prospectDto.getSectorDto().setId(sector.getId());
		 * 
		 * Locality locality =
		 * localityRepository.findByNameIgnoreCase(prospectDto.getLocalityDto().getName(
		 * ).trim().toUpperCase()); if(locality == null) { locality = new Locality();
		 * locality.setName(prospectDto.getLocalityDto().getName().trim());
		 * locality.setSector(sector); localityRepository.save(locality);
		 * 
		 * } prospectDto.getLocalityDto().setId(locality.getId()); Speciality speciality
		 * =
		 * specialityRepository.findFirstByNameIgnoreCase(prospectDto.getSpecialityDto()
		 * .getName().trim().toUpperCase()); if(speciality == null) { speciality = new
		 * Speciality();
		 * speciality.setName(prospectDto.getSpecialityDto().getName().trim());
		 * specialityRepository.save(speciality); }
		 * prospectDto.getSpecialityDto().setId(speciality.getId()); Potential potential
		 * =
		 * potentialRepository.findFirstByNameIgnoreCase(prospectDto.getPotentialDto().
		 * getName().trim().toUpperCase()); if(potential == null) { potential = new
		 * Potential();
		 * potential.setName(prospectDto.getPotentialDto().getName().trim());
		 * potentialRepository.save(potential); }
		 * prospectDto.getPotentialDto().setId(potential.getId()); //} }
		 * prospectService.saveAll(prospectDtos);
		 * 
		 * 
		 * } catch (Exception e) { e.printStackTrace(); }
		 */

	}

}
