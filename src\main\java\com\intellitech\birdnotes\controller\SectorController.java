package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Locale;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.dao.DataIntegrityViolationException;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SectorRequestDto;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/sectors")
public class SectorController {

	private static final Logger LOG = LoggerFactory.getLogger(SectorController.class);

	@Autowired
	private SectorService sectorService;
	@Autowired
	UserService userService;
	
	@Autowired
	private DownloadDataService downloadDataService;

	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllSectors(@RequestBody List<SectorRequestDto> sectorRequestDtos) {
		try {
			sectorService.addAll(sectorRequestDtos);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add sectors", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding the sectors", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	
	@RequestMapping(value = "/getAllDataForSector", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForSector() {
		try {
			if (userService.checkHasPermission("SECTOR_VIEW")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForSector(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the sectors lists", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findallsectors", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> findallSectors() {
		try {
			if (userService.checkHasPermission("SECTOR_VIEW")) {
				List<SectorDto> sectorDtos = sectorService.findAll();
				return new ResponseEntity<>(sectorDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findSectorByUserId/{id}", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> findSectorByUserId(@PathVariable("id") Long idSector) {
		try {
			if (userService.checkHasPermission("SECTOR_VIEW")) {
				List<SectorDto> sectorDtos = sectorService.findSectorByUserId(idSector);
				return new ResponseEntity<>(sectorDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addSector(@RequestBody SectorRequestDto sectorRequestDto) {
		try {
			if (userService.checkHasPermission("SECTOR_ADD")) {
				sectorService.add(sectorRequestDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add sector", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new sector", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteSector(@PathVariable("id") Long idSector) {
		try {
			if (userService.checkHasPermission("SECTOR_DELETE")) {
				sectorService.delete(idSector);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("An exception occurred while deleting the sector with id =" + idSector, e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}}



	@RequestMapping(value = "saveSector", method = RequestMethod.POST)
	public ResponseEntity<String> saveSector(@RequestBody SectorDto sectorDto) {

		try {
			if (userService.checkHasPermission("SECTOR_EDIT")) {
				Sector savedSector = sectorService.saveSector(sectorDto);
				if (savedSector != null) {
					return new ResponseEntity<>(savedSector.getId().toString(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving saveSector", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving saveSector", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}