package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.AutomationRuleFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.AutomationRule;
import com.intellitech.birdnotes.model.dto.AutomationRuleDto;
import com.intellitech.birdnotes.service.AutomationRuleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/automations")
public class AutomationRuleController {
	private static final Logger LOG = LoggerFactory.getLogger(AutomationRuleController.class);
	@Autowired
	private AutomationRuleService automationRuleService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "/findAllAutomationsRule", method = RequestMethod.GET)
	public ResponseEntity<List<AutomationRuleDto>> getAllAutomationsRule() {
		try {
			if (userService.checkHasPermission("AUTOMATION_RULE_VIEW")) {
				List<AutomationRuleDto> result = automationRuleService.findAll();
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all automationsRule ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllDataAutomationRuleForm", method = RequestMethod.GET)
	public ResponseEntity<AutomationRuleFormData> getAllDataFromAutomationsRule() {
		try {
			AutomationRuleFormData result = automationRuleService.getAllDataAutomationRuleForm();
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveAutomationRule", method = RequestMethod.POST)
	public ResponseEntity<Long> saveAutomationRule(@RequestBody AutomationRuleDto automationRuleDto) {
		try {
			if (userService.checkHasPermission("AUTOMATION_RULE_ADD")
					|| userService.checkHasPermission("AUTOMATION_RULE_EDIT")) {
				AutomationRule automationRuleSaved = automationRuleService.saveAutomationRule(automationRuleDto);
				if (automationRuleSaved != null) {
					return new ResponseEntity<>(automationRuleSaved.getId(), HttpStatus.OK);
				}

				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred while saving automationRule", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("An exception occurred while saving automationRule ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deleteAutomationRule/{automationRuleId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteAutomationRule(@PathVariable("automationRuleId") Long automationRuleId) {
		try {

			if (userService.checkHasPermission("AUTOMATION_RULE_DELETE")) {
				automationRuleService.deleteAutomationRule(automationRuleId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in delete automationRule", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
