package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;


public class VisitHistoryGroupDto  implements Serializable,Comparable<VisitHistoryGroupDto>{

	private static final long serialVersionUID = 1L;
	
	private Long id;
	private String name;
	private Float count;
	private String unit;
	private Float objective;
	private Float realizedVisitCount;
	private Float reportFillingRate;
	private Float workindDaysPerUser;
	
	public Float getReportFillingRate() {
		return reportFillingRate;
	}

	public void setReportFillingRate(Float reportFillingRate) {
		this.reportFillingRate = reportFillingRate;
	}
	
	

	public Float getWorkindDaysPerUser() {
		return workindDaysPerUser;
	}

	public void setWorkindDaysPerUser(Float workindDaysPerUser) {
		this.workindDaysPerUser = workindDaysPerUser;
	}

	public VisitHistoryGroupDto(String name, Float count) {
		this.name = name;
		this.count = count;
	}
	
	public VisitHistoryGroupDto(String name, Float count, Long id) {
		this(name, count);
		this.id = id;
	}
	
	public VisitHistoryGroupDto(String name, Long id, Float realizedVisitCount, Float objective, Float count) {
		this.id = id;
		this.name = name;
		this.count = count;
		this.objective = objective;
		this.realizedVisitCount = realizedVisitCount;
	}
	
	public VisitHistoryGroupDto(String name, Float count, String unit) {
		this(name, count);
		this.unit = unit;
	}
	
	public VisitHistoryGroupDto(String name, Float count, Float reportFillingRate) {
		super();
		this.name = name;
		this.count = count;
		this.reportFillingRate = reportFillingRate;
	}

	public VisitHistoryGroupDto() {
		super();
	}

	public VisitHistoryGroupDto(NameValueDto nameValueDto) {
		this.name = nameValueDto.getName();
		this.count = nameValueDto.getCount();
	}


	public VisitHistoryGroupDto(NameValueDto nameValueDto, String unit) {
		this.name = nameValueDto.getName();
		this.count = nameValueDto.getCount();
		this.unit = unit;
	}
	
	public Float getRealizedVisitCount() {
		return realizedVisitCount;
	}

	public void setRealizedVisitCount(Float realizedVisit) {
		this.realizedVisitCount = realizedVisit;
	}

	public Float getObjective() {
		return objective;
	}

	public void setObjective(Float objective) {
		this.objective = objective;
	}

	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Float getCount() {
		return count;
	}
	public void setCount(Float count) {
		this.count = count;
	}
	@Override
	public int compareTo(VisitHistoryGroupDto o) {
		return count.compareTo(o.getCount());
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	

}
