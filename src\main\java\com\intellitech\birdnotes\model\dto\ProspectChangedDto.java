package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class ProspectChangedDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String label;
	private String oldValue;
	private String newValue;
	private String updatePositionDate;
	private Date updateDate;
	public String getLabel() {
		return label;
	}
	public void setLabel(String label) {
		this.label = label;
	}
	public String getOldValue() {
		return oldValue;
	}
	public void setOldValue(String oldValue) {
		this.oldValue = oldValue;
	}
	public String getNewValue() {
		return newValue;
	}
	public void setNewValue(String newValue) {
		this.newValue = newValue;
	}
	public String getUpdatePositionDate() {
		return updatePositionDate;
	}
	public void setUpdatePositionDate(String updatePositionDate) {
		this.updatePositionDate = updatePositionDate;
	}
	
	public Date getUpdateDate() {
		return updateDate;
	}
	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}
	
	
}
