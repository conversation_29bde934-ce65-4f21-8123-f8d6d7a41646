<?xml version="1.0" encoding="UTF-8"?>
<!-- Created with Jaspersoft Studio version 6.20.0.final using JasperReports Library version 6.20.0-2bc7ab61c56f459e8176eb05c7705e145cd400ad  -->
<jasperReport xmlns="http://jasperreports.sourceforge.net/jasperreports" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports http://jasperreports.sourceforge.net/xsd/jasperreport.xsd" name="purchase_order" pageWidth="595" pageHeight="842" columnWidth="555" leftMargin="20" rightMargin="20" topMargin="20" bottomMargin="20" uuid="dbd44d80-3958-4e9d-b6a5-65a28ef9194f">
	<style name="Table_TH" mode="Opaque" backcolor="#F0F8FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_CH" mode="Opaque" backcolor="#BFE1FF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<style name="Table_TD" mode="Opaque" backcolor="#FFFFFF">
		<box>
			<pen lineWidth="0.5" lineColor="#000000"/>
			<topPen lineWidth="0.5" lineColor="#000000"/>
			<leftPen lineWidth="0.5" lineColor="#000000"/>
			<bottomPen lineWidth="0.5" lineColor="#000000"/>
			<rightPen lineWidth="0.5" lineColor="#000000"/>
		</box>
	</style>
	<subDataset name="purchaseOrderItem" uuid="c80397ab-59c8-4a2a-bbae-cdc0472c6722">
		<property name="com.jaspersoft.studio.data.defaultdataadapter" value="One Empty Record"/>
		<queryString>
			<![CDATA[]]>
		</queryString>
		<field name="productName" class="java.lang.String"/>
		<field name="orderQuantity" class="java.lang.Integer"/>
		<field name="freeOrder" class="java.lang.Integer"/>
		<field name="total" class="java.lang.Float"/>
		<field name="price" class="java.lang.Float"/>
		<variable name="poTotal" class="java.lang.Float" calculation="Sum">
			<variableExpression><![CDATA[$F{total}]]></variableExpression>
		</variable>
	</subDataset>
	<parameter name="purchaseOrderItem" class="net.sf.jasperreports.engine.data.JRBeanCollectionDataSource"/>
	<queryString>
		<![CDATA[]]>
	</queryString>
	<field name="id" class="java.lang.Long"/>
	<field name="date" class="java.lang.String"/>
	<field name="prospectName" class="java.lang.String"/>
	<field name="prospectAddress" class="java.lang.String"/>
	<field name="delegateName" class="java.lang.String"/>
	<field name="wholesalerName" class="java.lang.String"/>
	<field name="wholesalerAddress" class="java.lang.String"/>
	<field name="wholesalerEmail" class="java.lang.String"/>
	<field name="total" class="java.lang.Float"/>
	<field name="name" class="java.lang.String"/>
	<field name="logoUrl" class="java.lang.String"/>
	<background>
		<band splitType="Stretch"/>
	</background>
	<title>
		<band height="94" splitType="Stretch">
			<staticText>
				<reportElement x="128" y="6" width="198" height="30" uuid="82c063c7-2f12-40f9-a7e6-84243abca06d"/>
				<textElement textAlignment="Center" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Bon de commande numéro : ]]></text>
			</staticText>
			<textField>
				<reportElement x="322" y="6" width="60" height="30" uuid="8531e32a-5713-4c22-af36-055f0fa98509"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{id}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="441" y="6" width="49" height="30" uuid="ae8dcb9d-cf38-442c-87bf-9d7854c0aecd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Date :]]></text>
			</staticText>
			<textField>
				<reportElement x="485" y="6" width="74" height="30" uuid="cab81761-c432-408a-8935-10a2d2167efd"/>
				<textElement textAlignment="Left" verticalAlignment="Middle" markup="none">
					<font size="12"/>
				</textElement>
				<textFieldExpression><![CDATA[$F{date}]]></textFieldExpression>
			</textField>
			<image>
				<reportElement x="10" y="-4" width="70" height="60" uuid="c4fcda71-c5fe-42bd-9522-5bd5ac4766ef"/>
				<imageExpression><![CDATA[$F{logoUrl}]]></imageExpression>
			</image>
			<line>
				<reportElement x="-10" y="60" width="572" height="1" uuid="cb246e11-3d18-4eb3-a416-d32bb6f27f41"/>
			</line>
		</band>
	</title>
	<pageHeader>
		<band height="136" splitType="Stretch">
			<staticText>
				<reportElement x="9" y="0" width="85" height="21" uuid="d6301656-163c-4eb0-a73f-1b26e341bc63"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Pharmacie : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="58" width="85" height="22" uuid="b7cc6fd1-fb22-48c4-b0e0-04af8a292368"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Délégué : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="9" y="30" width="85" height="21" uuid="34f0f8d9-abc2-4075-b3de-8310005771b0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Addresse : ]]></text>
			</staticText>
			<textField>
				<reportElement x="98" y="2" width="100" height="19" uuid="f9bf90d6-3986-4212-b523-930c6a49f6e2"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{prospectName}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="29" width="221" height="22" uuid="3be572ce-da8e-4b7c-9a5c-e98529981560"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{prospectAddress}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="98" y="58" width="100" height="22" uuid="b191f86e-eeb7-49b9-879f-5e40c606b454"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{delegateName}]]></textFieldExpression>
			</textField>
			<staticText>
				<reportElement x="337" y="1" width="77" height="22" uuid="0fa15911-06f7-4951-bc5e-4eef201f7b2f"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Grossiste :]]></text>
			</staticText>
			<staticText>
				<reportElement x="337" y="30" width="77" height="21" uuid="bb470b4f-474a-434b-b186-0e0da299da3c"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Addresse : ]]></text>
			</staticText>
			<staticText>
				<reportElement x="335" y="58" width="77" height="22" uuid="e9d84990-2077-40bb-8a46-0bdbdb364ab0"/>
				<textElement textAlignment="Left" verticalAlignment="Middle">
					<font size="12" isBold="true"/>
				</textElement>
				<text><![CDATA[Email :]]></text>
			</staticText>
			<textField>
				<reportElement x="418" y="0" width="134" height="23" uuid="2fffcc5d-0b07-4d0b-8b75-0acb10d6fca1"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{wholesalerName}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="418" y="30" width="156" height="21" uuid="1c52e107-1c1d-41cc-a46e-cc8247637cb3"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{wholesalerAddress}]]></textFieldExpression>
			</textField>
			<textField isBlankWhenNull="true">
				<reportElement x="417" y="58" width="159" height="22" uuid="e962ddbe-dfde-41cc-947a-77591ebcb777"/>
				<textElement verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{wholesalerEmail}]]></textFieldExpression>
			</textField>
		</band>
	</pageHeader>
	<detail>
		<band height="198" splitType="Stretch">
			<componentElement>
				<reportElement x="25" y="4" width="510" height="152" uuid="67a5708f-9638-46bf-a7ad-52f252cd029b">
					<property name="com.jaspersoft.studio.layout" value="com.jaspersoft.studio.editor.layout.VerticalRowLayout"/>
					<property name="com.jaspersoft.studio.table.style.table_header" value="Table_TH"/>
					<property name="com.jaspersoft.studio.table.style.column_header" value="Table_CH"/>
					<property name="com.jaspersoft.studio.table.style.detail" value="Table_TD"/>
				</reportElement>
				<jr:table xmlns:jr="http://jasperreports.sourceforge.net/jasperreports/components" xsi:schemaLocation="http://jasperreports.sourceforge.net/jasperreports/components http://jasperreports.sourceforge.net/xsd/components.xsd">
					<datasetRun subDataset="purchaseOrderItem" uuid="8f6c0a54-65ff-4bf9-9538-05e93c9fa0a1">
						<dataSourceExpression><![CDATA[$P{purchaseOrderItem}]]></dataSourceExpression>
					</datasetRun>
					<jr:column width="170" uuid="e7745d40-096b-4334-82d6-cf2b35a9a457">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column1"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="170" height="30" uuid="b03c1214-53cb-445d-b319-1c89ab289dd5"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="12" isBold="true"/>
								</textElement>
								<text><![CDATA[Produit]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1"/>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="170" height="30" uuid="49267b1b-ee63-4cbb-9342-dbc8be6c17e9"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{productName}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="80" uuid="83edd70a-1884-4999-b539-51223568de80">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column2"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="80" height="30" uuid="d5ea03e4-5c12-47f8-9888-39a5c462756c"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="12" isBold="true"/>
								</textElement>
								<text><![CDATA[Quantity]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1"/>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="80" height="30" uuid="7e34af7a-7127-410a-a158-42734df4fbbe"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{orderQuantity}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="90" uuid="a1a83a21-e34a-42e2-a0fa-545a1d6af5ce">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column3"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="90" height="30" uuid="498b5d3b-801e-4b48-bc00-db67e11c30b6"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="12" isBold="true"/>
								</textElement>
								<text><![CDATA[Prix]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1"/>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="90" height="30" uuid="b8ece948-c1d5-4279-b991-60656f366409"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{price}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="80" uuid="cc1805f3-dd0c-479b-befe-fe008d995b57">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column4"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="80" height="30" uuid="86db7f73-3234-4a12-a064-2cd774169d6b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="12" isBold="true"/>
								</textElement>
								<text><![CDATA[Gratuité]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="80" height="30" uuid="3e2aa4a8-dee2-4537-9029-f0d98a52b0b1"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<text><![CDATA[Total]]></text>
							</staticText>
						</jr:tableFooter>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="80" height="30" uuid="e7c190fa-cb30-4876-9a5f-df02e4ac418b"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{freeOrder}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
					<jr:column width="90" uuid="bee15b90-9e07-4947-8c7c-331b395bdcb6">
						<property name="com.jaspersoft.studio.components.table.model.column.name" value="Column5"/>
						<jr:tableHeader style="Table_TH" height="30" rowSpan="1">
							<staticText>
								<reportElement x="0" y="0" width="90" height="30" uuid="00c3a747-3d39-47b2-90ff-166ade147c0f"/>
								<textElement textAlignment="Center" verticalAlignment="Middle">
									<font size="12" isBold="true"/>
								</textElement>
								<text><![CDATA[Total]]></text>
							</staticText>
						</jr:tableHeader>
						<jr:tableFooter style="Table_TH" height="30" rowSpan="1">
							<textField>
								<reportElement x="0" y="0" width="90" height="30" uuid="815f6750-7b2a-448a-bf18-273d0067348d"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$V{poTotal}]]></textFieldExpression>
							</textField>
						</jr:tableFooter>
						<jr:columnHeader style="Table_CH" height="30" rowSpan="1"/>
						<jr:columnFooter style="Table_CH" height="30" rowSpan="1"/>
						<jr:detailCell style="Table_TD" height="30">
							<textField>
								<reportElement x="0" y="0" width="90" height="30" uuid="1a7b3055-c86d-4c6c-b24b-e624679e04c7"/>
								<textElement textAlignment="Center" verticalAlignment="Middle"/>
								<textFieldExpression><![CDATA[$F{total}]]></textFieldExpression>
							</textField>
						</jr:detailCell>
					</jr:column>
				</jr:table>
			</componentElement>
		</band>
	</detail>
	<pageFooter>
		<band height="133">
			<staticText>
				<reportElement x="325" y="0" width="210" height="18" uuid="16f5df1d-b0d0-47d7-b50f-f0ff8091b228"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[Signature et cachet]]></text>
			</staticText>
			<staticText>
				<reportElement x="25" y="0" width="210" height="18" uuid="844726f3-94b7-40d2-b766-da445822e237"/>
				<box>
					<pen lineWidth="0.5"/>
				</box>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<text><![CDATA[Reçu conforme]]></text>
			</staticText>
			<textField isBlankWhenNull="true">
				<reportElement x="230" y="71" width="100" height="30" uuid="e39e4b38-492c-4a26-a9c7-f222d8912aef"/>
				<textElement textAlignment="Center" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[$F{name}]]></textFieldExpression>
			</textField>
			<textField>
				<reportElement x="300" y="110" width="176" height="23" uuid="0c1830c0-e23f-4687-9bb1-af51fe535600"/>
				<textElement textAlignment="Right" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA["Page " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
			<textField evaluationTime="Report">
				<reportElement x="476" y="110" width="100" height="22" uuid="361242d8-bb54-4669-878d-5ef9a2cdfa64"/>
				<textElement textAlignment="Left" verticalAlignment="Middle"/>
				<textFieldExpression><![CDATA[" of " + $V{PAGE_NUMBER}]]></textFieldExpression>
			</textField>
		</band>
	</pageFooter>
</jasperReport>
