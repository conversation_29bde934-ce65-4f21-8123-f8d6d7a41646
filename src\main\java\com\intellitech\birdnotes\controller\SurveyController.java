package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.SurveyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Survey;
import com.intellitech.birdnotes.model.dto.SurveyDto;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.SurveyService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/survey")
public class SurveyController {
	private static final Logger LOG = LoggerFactory.getLogger(SurveyController.class);

	@Autowired
	UserService userService;
	@Autowired
	private ProductService productService;
	@Autowired
	private SurveyService surveyService;

	@RequestMapping(value = "/saveSurvey", method = RequestMethod.POST)
	public ResponseEntity<Long> saveSurvey(@RequestBody SurveyDto surveyDto) {
		try {
			if (userService.checkHasPermission("PRODUCT_VIEW")) {
				 surveyService.saveSurvey(surveyDto);
				
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving surveySaved", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving surveySaved", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	/*
	 * @RequestMapping(value = "getAllProducts", method = RequestMethod.GET) public
	 * ResponseEntity<List<ProductDto>> getAllProducts() { try { if
	 * (userService.checkHasPermission("PRODUCT_VIEW")) { List<ProductDto>
	 * productDtos = productService.getAllProducts(); return new
	 * ResponseEntity<>(productDtos, HttpStatus.OK); } else { return new
	 * ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION); } } catch
	 * (Exception e) { LOG.error("Error in findAllProducts", e); return new
	 * ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED); } }
	 */

	@RequestMapping(value = "/getAllDataSurveyForm/{criteriaFileName}", method = RequestMethod.GET)
	public ResponseEntity<SurveyFormData> getAllDataSurveyForm(@PathVariable("criteriaFileName") String criteriaFileName ) {
		try {
	        SurveyFormData result = surveyService.getAllDataSurvey(criteriaFileName);
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getAllSurveys", method = RequestMethod.GET)
	public ResponseEntity<List<SurveyDto>> getAllSurveys() {
		try {
			List<SurveyDto> result = surveyService.getAllSurveys();
			return new ResponseEntity<>(result, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all AllSurveys ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	/*
	 * @RequestMapping(value = "/findAllCriterias", method = RequestMethod.GET)
	 * public ResponseEntity<List<Criteria>> getCriteriaXml() { try { List<Criteria>
	 * criterias = surveyService.getCriteriaXml(); return new
	 * ResponseEntity<>(criterias, HttpStatus.OK); } catch (Exception e) {
	 * LOG.error("An exception occurred while getting all criterias ", e); return
	 * new ResponseEntity<>(Collections.emptyList(),
	 * HttpStatus.INTERNAL_SERVER_ERROR); } }
	 */

}
