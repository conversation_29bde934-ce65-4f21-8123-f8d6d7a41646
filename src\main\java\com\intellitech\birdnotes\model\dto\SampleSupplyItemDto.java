package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

import com.intellitech.birdnotes.model.SampleSupplyItem;

public class SampleSupplyItemDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long productId;
	private String productName;
	private Long quantity;
	private Date manufactureDate;
	private Date expirationDate;
	private Long batchNumber;

	public SampleSupplyItemDto(SampleSupplyItem sampleSupply) {
		this.id = sampleSupply.getId();
		this.productId = sampleSupply.getProduct().getId();
		this.productName = sampleSupply.getProduct().getName();
		this.quantity = sampleSupply.getQuantity();
		this.manufactureDate = sampleSupply.getDateManufacture();
		this.expirationDate = sampleSupply.getExpirationDate();
		this.batchNumber = sampleSupply.getBatchNumber();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public SampleSupplyItemDto() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getQuantity() {
		return quantity;
	}

	public void setQuantity(Long quantity) {
		this.quantity = quantity;
	}

	public Date getManufactureDate() {
		return manufactureDate;
	}

	public void setManufactureDate(Date manufactureDate) {
		this.manufactureDate = manufactureDate;
	}

	public Date getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(Date expirationDate) {
		this.expirationDate = expirationDate;
	}

	public Long getBatchNumber() {
		return batchNumber;
	}

	public void setBatchNumber(Long batchNumber) {
		this.batchNumber = batchNumber;
	}

}
