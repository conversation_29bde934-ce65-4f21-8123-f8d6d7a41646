package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.model.User;

public class ActionMarketingDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String name;
	private Float budget;
	private Date date;
	private Set<ProductDto> products;
	private Set<ProspectDto> prospects;
	private String description;
	private List<Long> prospectIds;
	private List<Long> productIds;
	
	private User user;
	private String userName;

	private String status;
	private List <ValidationStatusDto> validationStatusDto;
	private Long userId;
	private Long marketingActionTypeId;
	private String type;



	public ActionMarketingDto(Long id, String name, Float budget, Date date, Set<ProductDto> products,
			Set<ProspectDto> prospects, User user, String userName, String status, String description,
			List <ValidationStatusDto> validationStatusDto) {
		super();
		this.id = id;
		this.name = name;
		this.budget = budget;
		this.date = date;
		this.products = products;
		this.prospects = prospects;
		this.user = user;
		this.userName = userName;
		this.status = status;
		this.description = description;
		this.validationStatusDto = validationStatusDto;
	}

	public ActionMarketingDto() {
		super();
	}

	
	

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Float getBudget() {
		return budget;
	}

	public void setBudget(Float budget) {
		this.budget = budget;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public Set<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(Set<ProductDto> products) {
		this.products = products;
	}

	public Set<ProspectDto> getProspects() {
		return prospects;
	}

	public void setProspects(Set<ProspectDto> prospects) {
		this.prospects = prospects;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public void setUserName(String findUserById) {
		this.userName = findUserById;

	}

	public String getUserName() {
		return userName;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public List<ValidationStatusDto> getValidationStatusDto() {
		return validationStatusDto;
	}

	public void setValidationStatusDto(List<ValidationStatusDto> validationStatusDto) {
		this.validationStatusDto = validationStatusDto;
	}

	public List<Long> getProspectIds() {
		return prospectIds;
	}

	public void setProspectIds(List<Long> prospectIds) {
		this.prospectIds = prospectIds;
	}

	public List<Long> getProductIds() {
		return productIds;
	}

	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}
	
	public Long getUserId() {
	    return userId;
	}

	public void setUserId(Long userId) {
	    this.userId = userId;
	}

	

	public Long getMarketingActionTypeId() {
		return marketingActionTypeId;
	}

	public void setMarketingActionTypeId(Long marketingActionTypeId) {
		this.marketingActionTypeId = marketingActionTypeId;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}
	

}
