package com.intellitech.birdnotes.data.dto;

import java.util.List;

public class ProductCompareDto {

	private Long productId;
	private String productName;
	private Long sampleNumbers;
	private Long orderNumbers;
	private List<SmilyDto> smilyDtos;

	public ProductCompareDto(Long productId, String productName, Long sampleNumbers, Long orderNumbers) {
		this.productId = productId;
		this.productName = productName;
		this.sampleNumbers = sampleNumbers;
		this.orderNumbers = orderNumbers;
	}
	
	public ProductCompareDto(Long productId, String productName, Long sampleNumbers, Long orderNumbers, List<SmilyDto> smilyDtos) {
		this.productId = productId;
		this.productName = productName;
		this.sampleNumbers = sampleNumbers;
		this.orderNumbers = orderNumbers;
		this.smilyDtos = smilyDtos;
	}

	public ProductCompareDto() {
		super();
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getSampleNumbers() {
		return sampleNumbers;
	}

	public void setSampleNumbers(Long sampleNumbers) {
		this.sampleNumbers = sampleNumbers;
	}

	public Long getOrderNumbers() {
		return orderNumbers;
	}

	public void setOrderNumbers(Long orderNumbers) {
		this.orderNumbers = orderNumbers;
	}

	public List<SmilyDto> getSmilyDtos() {
		return smilyDtos;
	}

	public void setSmilyDtos(List<SmilyDto> smilyDtos) {
		this.smilyDtos = smilyDtos;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((orderNumbers == null) ? 0 : orderNumbers.hashCode());
		result = prime * result + ((productId == null) ? 0 : productId.hashCode());
		result = prime * result + ((productName == null) ? 0 : productName.hashCode());
		result = prime * result + ((sampleNumbers == null) ? 0 : sampleNumbers.hashCode());
		result = prime * result + ((smilyDtos == null) ? 0 : smilyDtos.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProductCompareDto other = (ProductCompareDto) obj;
		if (orderNumbers == null) {
			if (other.orderNumbers != null)
				return false;
		} else if (!orderNumbers.equals(other.orderNumbers))
			return false;
		if (productId == null) {
			if (other.productId != null)
				return false;
		} else if (!productId.equals(other.productId))
			return false;
		if (productName == null) {
			if (other.productName != null)
				return false;
		} else if (!productName.equals(other.productName))
			return false;
		if (sampleNumbers == null) {
			if (other.sampleNumbers != null)
				return false;
		} else if (!sampleNumbers.equals(other.sampleNumbers))
			return false;
		if (smilyDtos == null) {
			if (other.smilyDtos != null)
				return false;
		} else if (!smilyDtos.equals(other.smilyDtos))
			return false;
		return true;
	}
}