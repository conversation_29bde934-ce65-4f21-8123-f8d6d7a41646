package com.intellitech.birdnotes.service.impl;

import java.net.MalformedURLException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.xmlrpc.client.XmlRpcClient;
import org.apache.xmlrpc.client.XmlRpcClientConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import com.intellitech.birdnotes.data.dto.OrdersPredictionRequest;
import com.intellitech.birdnotes.data.dto.PredictionResponse;
import com.intellitech.birdnotes.data.dto.ProductCompareDto;
import com.intellitech.birdnotes.data.dto.SmilyDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.convertor.ProductToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.FileDto;
import com.intellitech.birdnotes.model.dto.IdVersionDto;
import com.intellitech.birdnotes.model.dto.MinimizedUserDto;
import com.intellitech.birdnotes.model.dto.ProductCoverageDto;
import com.intellitech.birdnotes.model.dto.ProductCoverageResult;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.SageProductItemDto;
import com.intellitech.birdnotes.model.dto.SageProductListDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.RangeRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ImportService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service("productService")
@Transactional
public class ProductServiceImpl implements ProductService {

	private static final Integer NUMBER_OF_SMILIES = 6;

	private ProductRepository productRepository;

	private UserRepository userRepository;
	
	private DelegateRepository delegateRepository;

	private RangeRepository gammeRepository;

	private ProductToDtoConvertor productToDtoConvertor;

	private VisitsProductsRepository visitsProductsRepository;

	private SimpleDateFormat formatter = new SimpleDateFormat("dd-MM-yyyy");

	private static final Logger LOG = LoggerFactory.getLogger(ProductServiceImpl.class);
	
	@Autowired
	private RestTemplate restTemplate;
	

	
	@Autowired
	private ConfigurationRepository configurationRepository;
	
	
	@Autowired
	UserService userService;

	@Autowired
	private ImportService importService;
	@Autowired
	private StorageService storageService;
	@Autowired
	private ConfigurationRepository configureRepository;
	@Autowired
	private CurrentUser currentUser;
	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${productPath}")
	private String productPath;

	@Value("${erp.serverUrl}")
	private String erpServerUrl;

	@Value("${erp.login}")
	private String erpLogin;

	@Value("${erp.password}")
	private String erpPassword;

	@Autowired
	ProductServiceImpl(ProductRepository productRepository, UserRepository userRepository,
			RangeRepository gammeRepository, ProductToDtoConvertor productToDtoConvertor,
			VisitsProductsRepository visitsProductsRepository, DelegateRepository delegateRepository) {
		super();
		this.productRepository = productRepository;
		this.userRepository = userRepository;
		this.gammeRepository = gammeRepository;
		this.productToDtoConvertor = productToDtoConvertor;
		this.visitsProductsRepository = visitsProductsRepository;
		this.delegateRepository = delegateRepository;
	}

	@Autowired
	void setProductRepository(ProductRepository productRepository) {
		this.productRepository = productRepository;
	}

	@Override
	@Transactional(readOnly = true)
	public List<ProductDto> getNewVersionProducts(List<IdVersionDto> idVersions, Long userId)
			throws BirdnotesException {

		Map<Long, Long> idVersionMap = new HashMap<>();
		if (idVersions != null) {
			for (IdVersionDto idVersionDto : idVersions) {
				idVersionMap.put(idVersionDto.getId(), idVersionDto.getVersion());
			}
		}

		List<Product> products = productRepository.findByUserRange(userId);
		List<ProductDto> productDtos = new ArrayList<>();

		if (products != null && !products.isEmpty()) {
			for (Product product : products) {
				Long oldVersion = idVersionMap.get(product.getId());
				if (oldVersion == null || oldVersion < product.getVersion()) {

					ProductDto productDto = productToDtoConvertor.convert(product);
					List<FileDto> documents = storageService.loadFiles(uploadPath + productPath + "/" + productDto.getId());
					productDto.setDocumentDtoList(documents);
					productDtos.add(productDto);
				}

			}
		}

		return productDtos;
	}

	@Override
	@Transactional(readOnly = true)
	public List<ProductDto> getAllProducts() throws BirdnotesException {
		Long userId = currentUser.getBirdnotesUser().getUserDto().getId();
		List<Product> products = productRepository.findByUserRange(userId);
		List<ProductDto> productDtos = new ArrayList<>();
		OrdersPredictionRequest ordersPredictionDto = new OrdersPredictionRequest();
		Configuration config = configureRepository.findById(1);

		if (products != null && !products.isEmpty()) {
			for (Product product : products) {
				ProductDto productDto = productToDtoConvertor.convert(product);
				List<String> documents = storageService
						.getFilesNameInDirectory(uploadPath + productPath + "/" + productDto.getId());

				productDto.setDocumentRootPath(
						config.getBackendUrl() + uploadUrl + "/" + productPath + "/" + productDto.getId());
				productDto.setDocuments(documents);
				productDtos.add(productDto);
			}
		}

		return productDtos;
	}

	@Override
	public Product saveProduct(ProductDto productRequest) throws BirdnotesException {

		if (productRequest == null) {
			throw new BirdnotesException(Exceptions.NULL_PRODUCT);
		}

	    Product existingProduct = productRepository.findByNameAndAnotherId(productRequest.getName(), productRequest.getId());
	    if (existingProduct != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

		Product product = null;
		if(productRequest.getId() != null) {
			product = productRepository.findById(productRequest.getId());
			
		}
		if(product == null) {
			product = new Product();
		}

		product.setName(productRequest.getName());
		product.setNumberOfCapsules(productRequest.getNumberOfCapsules());
		product.setPrice(productRequest.getPrice());
		product.setBuyingPrice(productRequest.getBuyingPrice());
		product.setDescription(productRequest.getDescription());
		product.setQuantityUnit(productRequest.getQuantityUnit());
		product.setStock(productRequest.getStock());
		product.setCode(productRequest.getCode());
		product.setVat(productRequest.getVat());
		if(product.getVersion() == null) {
			product.setVersion(0L);
		}else {
			product.setVersion(product.getVersion() + 1);
		}

		List<Range> gammes = gammeRepository.findAll();
		if (gammes != null && !gammes.isEmpty()) {
			fillGammes(gammes, productRequest, product);
		}
		return productRepository.save(product);
	}

	@Override
	public void deleteProduct(Long productId) throws BirdnotesException {

		productRepository.delete(productId);
	}

	@Override
	public Product updateProduct(ProductDto productDto) throws BirdnotesException {

		if (productDto == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_DTO_PRODUCT);
		}

		Product productToUpdate = productRepository.findOne(productDto.getId());

		if (productToUpdate == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NO_PRODUCT_WITH_ID + productDto.getId());
		}

		if (checkProductNameIsUnique(productDto.getName(), productDto.getId())) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.PRODUCT_NAME_ALREADY_EXIST);
		}
		List<Range> gammes = gammeRepository.findAll();
		if (gammes != null && !gammes.isEmpty()) {
			fillGammes(gammes, productDto, productToUpdate);
		}
		productToUpdate.setName(productDto.getName());
		productToUpdate.setNumberOfCapsules(productDto.getNumberOfCapsules());
		productToUpdate.setPrice(productDto.getPrice());
		productToUpdate.setBuyingPrice(productDto.getBuyingPrice());
		productToUpdate.setDescription(productDto.getDescription());
		productToUpdate.setQuantityUnit(productDto.getQuantityUnit());
		productToUpdate.setCode(productDto.getCode());
		productToUpdate.setStock(productDto.getStock());
		if(productDto.getVat() != null) {
			productToUpdate.setVat(productDto.getVat());
		}

		if (productToUpdate.getVersion() == null) {
			productToUpdate.setVersion(0L);
		} else {
			productToUpdate.setVersion(productToUpdate.getVersion() + 1);
		}

		return productRepository.save(productToUpdate);
	}

	private void fillGammes(List<Range> gammes, ProductDto productDto, Product product) {

		Set<Range> gammeOfProduct = new HashSet<>();
		for (Integer gammeId : productDto.getRangeIds()) {
			for (Range gamme : gammes) {
				if (gamme.getId().equals(gammeId)) {
					gammeOfProduct.add(gamme);
				}
			}
		}
		product.setRanges(gammeOfProduct);

	}

	@Override
	public List<ProductDto> findProductsByGamme(List<Integer> rangeIds) throws BirdnotesException {
		List<ProductDto> productDtos = new ArrayList<>();
		List<Product> listOfProducts = gammeRepository.findByGamme(rangeIds);
		Collections.sort(listOfProducts, new Comparator<Product>(){
		     public int compare(Product p1, Product p2){
		    	 return p1.getName().compareTo(p2.getName());
		     }
		});
		if (listOfProducts != null && !listOfProducts.isEmpty()) {
			for (Product product: listOfProducts) {
				productDtos.add(productToDtoConvertor.convert(product));
			
			}
		}
		
		return productDtos;
	}


	@Override
	public boolean checkProductNameIsUnique(String productName) {
		Product product = productRepository.findByName(productName);

		return product != null;
	}

	private boolean checkProductNameIsUnique(String productName, Long productId) {
		Product product = productRepository.findByNameWithDiffId(productName, productId);

		return product != null;
	}

	@Override
	public ProductCoverageResult getProductCoverage(Long productId, String date) throws ParseException {
		Date firstDay = formatter.parse(date);
		Date lastDay = BirdnotesUtils.addDaysToDate(firstDay, 5);
		List<ProductCoverageDto> stats = productRepository.getProductCoverage(firstDay, lastDay, productId);
		List<Long> ids = stats.stream().map(e -> e.getId()).collect(Collectors.toList());
		List<MinimizedUserDto> users;
		if (ids != null && !ids.isEmpty()) {
			users = delegateRepository.getDelegatesExcept(ids);
		} else {
			users = delegateRepository.getDelegates();
		}
		users.stream().forEach(e -> {
			ProductCoverageDto productCoverageDto = new ProductCoverageDto(e.getFirstName(), e.getLastName(), e.getId(),
					0L, 0L);
			stats.add(productCoverageDto);
		});
		return new ProductCoverageResult(stats);
	}

	@Override
	public List<Product> saveAll(List<ProductDto> productRequests) throws BirdnotesException {
		List<Product> productsSaved = new ArrayList<>();
		for (ProductDto productRequest : productRequests) {
			productsSaved.add(saveProduct(productRequest));
		}

		return productsSaved;
	}

	@Override
	public List<ProductCompareDto> compareProductsPerMonth(Integer month, Integer year) {
		// query the database to get the data where there is a productId
		List<ProductCompareDto> productCompareDtos = visitsProductsRepository.compareProductsPerMonth(month, year);
		List<SmilyDto> smilyDtos = visitsProductsRepository.getSmilyPerMonth(month, year);
		return prepareDataToReturn(productCompareDtos, smilyDtos);
	}

	private List<ProductCompareDto> prepareDataToReturn(List<ProductCompareDto> productCompareDtos,
			List<SmilyDto> smilyDtos) {
		List<ProductCompareDto> toReturn = new ArrayList<>();
		// get the list of all products
		List<Product> products = productRepository.findAll();

		products.stream().forEach(product -> {
			Optional<ProductCompareDto> optionalProductCompareDto = getDbValue(productCompareDtos, product.getId());
			List<SmilyDto> smilies = getListofSmiliesForThisProduct(smilyDtos, product.getId());

			ProductCompareDto productCompareDto = new ProductCompareDto();
			productCompareDto.setProductId(product.getId());
			productCompareDto.setProductName(product.getName());
			productCompareDto.setSmilyDtos(smilies);
			if (optionalProductCompareDto.isPresent()) {
				productCompareDto.setOrderNumbers(optionalProductCompareDto.get().getOrderNumbers());
				productCompareDto.setSampleNumbers(optionalProductCompareDto.get().getSampleNumbers());
			} else {
				productCompareDto.setOrderNumbers(0L);
				productCompareDto.setSampleNumbers(0L);
			}
			toReturn.add(productCompareDto);
		});
		return toReturn;
	}

	private List<SmilyDto> getListofSmiliesForThisProduct(List<SmilyDto> smilyDtos, Long productId) {
		List<SmilyDto> back = new ArrayList<>();

		List<SmilyDto> listOfSmiliesForThisProduct = smilyDtos.stream()
				.filter(smilyDto -> smilyDto.getProductId().equals(productId)).collect(Collectors.toList());
		List<Integer> smilyids = new ArrayList<>();
		for (int i = 1; i < NUMBER_OF_SMILIES + 1; i++) {
			smilyids.add(i);
		}
		smilyids.stream().forEach(smilyId -> {
			Optional<SmilyDto> optionalSmilyDto = listOfSmiliesForThisProduct.stream()
					.filter(smilyDto -> smilyDto.getSmilyId().equals(smilyId)).findAny();
			if (optionalSmilyDto.isPresent()) {
				SmilyDto smilyDto = new SmilyDto(optionalSmilyDto.get().getSmilyId(),
						optionalSmilyDto.get().getCount());
				back.add(smilyDto);
			} else {
				SmilyDto smilyDto = new SmilyDto(smilyId, 0L);
				back.add(smilyDto);
			}
		});

		return back;
	}

	@Override
	public List<ProductCompareDto> compareProductsPerWeek(Date mondayDate) {
		// query the database to get the data where there is a productId
		Date saturdayDate = BirdnotesUtils.addDaysToDate(mondayDate, 5);
		List<ProductCompareDto> productCompareDtos = visitsProductsRepository.compareProductsPerWeek(mondayDate,
				saturdayDate);
		List<SmilyDto> smilyDtos = visitsProductsRepository.getSmilyPerWeek(mondayDate, saturdayDate);
		return prepareDataToReturn(productCompareDtos, smilyDtos);
	}

	private Optional<ProductCompareDto> getDbValue(List<ProductCompareDto> productCompareDtos, Long productId) {
		return productCompareDtos.stream()
				.filter(productCompareDto -> productCompareDto.getProductId().equals(productId)).findAny();
	}

	@Override
	public void updateQuantityFromErp() {
		Configuration configuration = configurationRepository.findById(1); 


    	SageProductListDto sageProductListDto= new SageProductListDto();


		List<Product> products = productRepository.getPoductCodes();

	    if ("Odoo".equalsIgnoreCase(configuration.getErpType())) {
	        int uid = 0;

	        try {
	            final XmlRpcClient client = new XmlRpcClient();
	            final XmlRpcClientConfigImpl commonConfig = new XmlRpcClientConfigImpl();

	            commonConfig.setServerURL(new URL(String.format("%s/xmlrpc/2/common", erpServerUrl)));
	            uid = (Integer) client.execute(commonConfig, "authenticate",
	                    Arrays.asList("odoo", erpLogin, erpPassword, ""));

	            final XmlRpcClient models = new XmlRpcClient() {
	                {
	                    setConfig(new XmlRpcClientConfigImpl() {
	                        {
	                            setServerURL(new URL(String.format("%s/xmlrpc/2/object", "http://194.163.132.114:8069")));
	                        }
	                    });
	                }
	            };

	            for (Product product : products) {
	                try {
	                    List<Object> productsResponse = Arrays.asList(
	                        (Object[]) models.execute("execute_kw", 
	                            Arrays.asList("odoo", uid, erpPassword,
	                            "product.template", "read", 
	                            Arrays.asList(Integer.parseInt(product.getCode())))));

	                    if (productsResponse.size() > 0) {
	                        Map<String, Object> productResponse = (Map) productsResponse.get(0);
	                        Double qtyAvailable = (Double) productResponse.get("qty_available");
	                        product.setStock(qtyAvailable.intValue());
	                        productRepository.save(product);
	                    }
	                } catch (Exception e) {
	                    LOG.error("Error getting product qty with code " + product.getCode(), e);
	                }
	            }
	        } catch (Exception e) {
	            LOG.error("Error in Odoo authenticate ", e);
	        }}
	    else if ("Sage".equalsIgnoreCase(configuration.getErpType())) {
	        for (Product product : products) {
	            SageProductItemDto sageProductItemDto = new SageProductItemDto();
	            sageProductItemDto.setProduct_code(product.getCode());
	            sageProductListDto.getProducts().add(sageProductItemDto);
	        }
	        HttpHeaders headers = new HttpHeaders();
			headers.set("Content-Type", "application/json");
	        URI uri;
	        try {
	            uri = new URI(configuration.getErpUrl() + "/products/batch/availability");
	            HttpEntity<SageProductListDto> request = new HttpEntity<>(sageProductListDto, headers);
	            ResponseEntity<SageProductListDto> response = restTemplate.postForEntity(uri, request, SageProductListDto.class);

	            if (response.getBody() != null && response.getBody().getItems() != null) {
	                for (SageProductItemDto sageItem : response.getBody().getItems()) {
	                    Product product = products.stream()
	                            .filter(p -> p.getCode().equals(sageItem.getProduct_code()))
	                            .findFirst()
	                            .orElse(null);

	                    if (product != null) {
	                        product.setStock(sageItem.getAvailable_quantity().intValue());
	                        productRepository.save(product);
	                    }
	                }
	            }

	        } catch (URISyntaxException e) {
	            LOG.error("Invalid URI for Sage ERP API: {}", configuration.getErpUrl(), e);
	        } catch (Exception e) {
	            LOG.error("Error while updating quantities from Sage ERP", e);
	        }
	    }

	}

	@Override
	public Map<String, List<ProductDto>> importProduct(String filePath) throws BirdnotesException {
		try {
			Map<String, List<ProductDto>> productDtos = importService.getProductData(filePath);
			if (productDtos.get("validData") != null) {
				for (ProductDto productDto : productDtos.get("validData")) {
					Product product = new Product();
					String rangesString = productDto.getRangesString();
					String[] ranges = rangesString.split(",");
					Set<Range> rangeList = new HashSet<>();
					for (String rangeName : ranges) {
							rangeList.add(gammeRepository.findByName(rangeName.trim()));
					}
					product.setRanges(rangeList);
					product.setCode(productDto.getCode());
					product.setDescription(productDto.getDescription());
					product.setBuyingPrice(productDto.getBuyingPrice());
					product.setName(productDto.getName());
					product.setNumberOfCapsules(productDto.getNumberOfCapsules());
					product.setQuantityUnit(productDto.getQuantityUnit());
					product.setPrice(productDto.getPrice());
					product.setStock(productDto.getStock());
					productRepository.save(product);
				}
			}
			return productDtos;
		} catch (Exception e) {
			LOG.error("Error in importing product ", e);
		}
		return null;

	}
	
	@Override
	@Transactional(readOnly = true)
	public List<Map<String, Object>> getAllProductsIdAndName() {
	    List<Product> products = productRepository.findAll();
	    return products.stream()
	        .map(product -> {
	            Map<String, Object> map = new HashMap<>();
	            map.put("id", product.getId());
	            map.put("name", product.getName());
	            return map;
	        })
	        .collect(Collectors.toList());
	}

}
