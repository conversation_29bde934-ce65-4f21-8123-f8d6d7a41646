package com.intellitech.birdnotes.controller;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.dto.ActivityCalanderRequestDto;
import com.intellitech.birdnotes.model.dto.ActivityDataDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.service.ActivityService;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/activity")
public class ActivityController {

	@Autowired
	private ActivityService activityService;

	@Autowired
	UserService userService;
	@Autowired
	DelegateService delegateService;

	private static final Logger LOG = LoggerFactory.getLogger(LocalityController.class);

	@RequestMapping(value = "/getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> findAll() {
		try {
			if (userService.checkHasPermission("RELATED_ACTIVITIES_VIEW")) {
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting users list", e);
			return new ResponseEntity<>(new ArrayList<DelegateDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "statistic", method = RequestMethod.POST)

	public ResponseEntity<ActivityDataDto> getStatisticActivity(
			@RequestBody ActivityCalanderRequestDto activityCalanderRequestDto) {
		try {
			if (userService.checkHasPermission("RELATED_ACTIVITIES_VIEW")) {
				ActivityDataDto response = activityService.getStatisticActivity(activityCalanderRequestDto);

				return new ResponseEntity<>(response, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred ", e);
			return new ResponseEntity<>(ActivityDataDto.SINGLE_INSTANCE, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/getActivityById/{id}", method = RequestMethod.GET)
	public ResponseEntity<ActivityDto> findActivityById(@PathVariable("id") Long id) {
		try {
			if (userService.checkHasPermission("RELATED_ACTIVITIES_VIEW")) {
				ActivityDto activityDto = activityService.findActivityById(id);
				return new ResponseEntity<>(activityDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting activity", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveActivity", method = RequestMethod.POST)
	public ResponseEntity<Long> saveActivity(@RequestBody ActivityDto activityDto) {
		try {
			Activity activitySaved = activityService.saveActivity(activityDto);
			if (activitySaved != null) {
				return new ResponseEntity<>(activitySaved.getId(), HttpStatus.OK);
			}
			return new ResponseEntity<>(null, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("An exception occurred while saving activitySaved", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "deleteActivity/{activityId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteActivity(@PathVariable("activityId") Long activityId) {
		try {

			activityService.deleteActivity(activityId);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);


		} catch (Exception e) {
			LOG.error("Error in deleteActivity", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}