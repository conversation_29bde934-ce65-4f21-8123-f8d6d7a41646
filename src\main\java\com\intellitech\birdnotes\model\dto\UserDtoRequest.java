package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.model.User;

public class UserDtoRequest implements Serializable {

	protected static final long serialVersionUID = 1L;
	protected Long id;
	protected String email;
	protected String phone;
	protected String username;
	protected String password;
	protected List<Integer> roleIds;
	protected List<Long> superiors;
	protected List<Integer> rangeIds;
	protected Boolean active;
	public UserDtoRequest() {
		super();
		
	}
	
	public UserDtoRequest(User user) {
		
		this.id= user.getId();
		this.password=user.getPassword();
		this.phone=user.getPhone();
		this.email=user.getEmail();
		this.username = user.getUsername();	
		this.active = user.getActive();
		
	}
	
	public List<Integer> getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(List<Integer> roleIds) {
		this.roleIds = roleIds;
	}



	public List<Integer> getRangeIds() {
		return rangeIds;
	}

	public void setRangeIds(List<Integer> rangeIds) {
		this.rangeIds = rangeIds;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public List<Long> getSuperiors() {
		return superiors;
	}

	public void setSuperiors(List<Long> superiors) {
		this.superiors = superiors;
	}
	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}
}
