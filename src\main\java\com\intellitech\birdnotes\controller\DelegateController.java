package com.intellitech.birdnotes.controller;

import java.io.File;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


import com.intellitech.birdnotes.data.dto.UserDataDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.RoleDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.UserDtoRequest;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.DelegateDtoRequest;
import com.intellitech.birdnotes.model.dto.VisitHistoryParamDto;
import com.intellitech.birdnotes.model.request.GoalRequest;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.RoleService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/delegates")
public class DelegateController {

	private static final Logger LOG = LoggerFactory.getLogger(DelegateController.class);

	@Autowired
	private DelegateService delegateService;
	
	@Autowired
	private UserService userService;
	
	@Autowired
	private RoleService roleService;
	


	
	
	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("DELEGATE_VIEW")) {
				List<DelegateDto> delegateDtos = delegateService.findAllDelegate();
				return new ResponseEntity<>(delegateDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "/findAllRoles", method = RequestMethod.GET)
	public ResponseEntity<List<RoleDto>> findAllRoles() {
		try {
			if (userService.checkHasPermission("DELEGATE_EDIT")) {
				List<RoleDto> rolesDtos = roleService.findAll();
				return new ResponseEntity<>(rolesDtos, HttpStatus.OK);
			}
			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all roles", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "getAllDelegatesWithoutGoal", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> findAllDelegatesWithoutGoal(@RequestBody GoalRequest goalsRequestDto) {
		try {
			if (userService.checkHasPermission("DELEGATE_VIEW")) {
				List<DelegateDto> delegateDtos = delegateService.findAllDelegatesWithoutGoal(goalsRequestDto);
				return new ResponseEntity<>(delegateDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("Error in getAllDelegatesWithoutGoal", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "sendEmailForSynch", method = RequestMethod.GET)
	public ResponseEntity<List<String>> sendMailForSynchronization() {
		try {
			List<String> delegates = delegateService.sendMailForSynchronization();
			return new ResponseEntity<>(delegates, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in sendMailForSynchronization", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "sendCredentials", method = RequestMethod.POST)
	public ResponseEntity<String> sendCredntials(@RequestBody UserDto userDto) {
		try {
			if (userService.checkHasPermission("DELEGATE_ADD")) {
				userService.sendCredentials(userDto);
				return new ResponseEntity<>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in sendCredntials", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "findHistoryParams", method = RequestMethod.POST)
	public ResponseEntity<VisitHistoryParamDto> findHistoryParams(@RequestBody UserDataDto delegatesDataRequest) {
		try {
			VisitHistoryParamDto back = delegateService.findHistoryParams(delegatesDataRequest.getUserIds(), delegatesDataRequest.getType());
			return new ResponseEntity<>(back, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in sectors and localities of delegates", e);
			return new ResponseEntity<>(new VisitHistoryParamDto(), HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "saveDelegate", method = RequestMethod.POST)
	public ResponseEntity<String> saveDelegate(@RequestPart("delegateRequest") DelegateDtoRequest delegateRequest, 
	                                            @RequestPart(name = "file", required = false) MultipartFile file) {
	    try {
	        if (userService.checkHasPermission("DELEGATE_ADD")) {
	            Delegate delegateSaved = delegateService.saveDelegate(delegateRequest, file);
	            if (delegateSaved != null) {
	                return new ResponseEntity<>(delegateSaved.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }
	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving delegate", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving delegate", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}



	@RequestMapping(value = "deleteDelegate/{delegateId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteDelegate(@PathVariable("delegateId") Long delegateId) {
		try {
			if (userService.checkHasPermission("DELEGATE_DELETE")) {
				delegateService.deleteDelegate(delegateId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);

		} catch (Exception e) {
			LOG.error("Error in deleteDelegate", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "updateDelegate", method = RequestMethod.PUT)
	public ResponseEntity<String> updateDelegate(@RequestPart("delegateRequest") DelegateDtoRequest delegateRequest, @RequestPart(name = "file", required = false) MultipartFile file) {
		try {
			if (userService.checkHasPermission("DELEGATE_EDIT")) {
				delegateService.saveDelegate(delegateRequest, file);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("Error Non-Authoritative Information in update Delegate", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("Error in updateDelegate", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}

	
	@RequestMapping(value = "getAllSupervisors", method = RequestMethod.POST)
	public ResponseEntity<List<UserDto>> getAllSupervisors(@RequestBody List<Integer> roleIds) {
		try {
			List<UserDto> supervisorDtos = userService.getAllSupervisors(roleIds);
			return new ResponseEntity<>(supervisorDtos, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in getAllSupervisors", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}


@RequestMapping(value ="import", method = RequestMethod.POST)
	
	public ResponseEntity<Map<String,List<DelegateDto>>> importDelegate(@RequestParam("file") MultipartFile file) throws BirdnotesException {
		try {
			File tmpFile = File.createTempFile("import", file.getOriginalFilename());
			file.transferTo(tmpFile);
			if (userService.checkHasPermission("DELEGATE_ADD")) {

				Map<String,List<DelegateDto>> delegateList = delegateService.importDelegate(tmpFile.getPath());

				return new ResponseEntity<>(delegateList, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}
			
			
			
		}  catch (Exception e) {

			LOG.error("An exception occurred while importing delegate", e);

			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
@RequestMapping(value = "/calculateDailyDistance", method = RequestMethod.GET)
public ResponseEntity<?> calculateDailyDistance(
        @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
    try {
        Map<String, Object> response = delegateService.calculateDailyDistance(date);
        return ResponseEntity.ok(response);
    } catch (Exception e) {
        LOG.error("Error calculating daily distances", e);
        Map<String, String> errorResponse = new HashMap<>();
        errorResponse.put("error", "Error calculating distances");
        errorResponse.put("message", e.getMessage());
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }
}

@RequestMapping(value = "/updateUserStatus/{delegateId}/{active}", method = RequestMethod.PUT)
public ResponseEntity<String> updateUserStatus(@PathVariable("delegateId") Long delegateId, @PathVariable("active") Boolean active) {
    try {
        if (userService.checkHasPermission("DELEGATE_EDIT")) {
            delegateService.updateUserStatus(delegateId, active);
            return new ResponseEntity<>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);
        } else {
            return new ResponseEntity<>("Unauthorized", HttpStatus.UNAUTHORIZED);
        }
    } catch (BirdnotesException e) {
        LOG.error("Error updating user status", e);
        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
    } catch (Exception e) {
        LOG.error("Unexpected error updating user status", e);
        return new ResponseEntity<>(BirdnotesConstants.Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
    }
}

}


	


