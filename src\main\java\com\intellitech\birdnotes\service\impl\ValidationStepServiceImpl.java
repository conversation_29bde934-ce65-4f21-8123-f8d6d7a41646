package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Set;

import javax.transaction.Transactional;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.DelegateCommission;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.ValidationStep;
import com.intellitech.birdnotes.model.ValidationType;
import com.intellitech.birdnotes.model.ValidationTypeList;
import com.intellitech.birdnotes.model.convertor.UserToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.model.dto.ValidationStepDto;
import com.intellitech.birdnotes.model.request.ValidationStepItem;
import com.intellitech.birdnotes.model.request.ValidationStepRequest;
import com.intellitech.birdnotes.repository.ActionMarketingRepository;
import com.intellitech.birdnotes.repository.DelegateCommissionRepository;
import com.intellitech.birdnotes.repository.ExpenseReportRepository;
import com.intellitech.birdnotes.repository.PlanningValidationRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.repository.ValidationStepRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.NotificationRuleService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

@Service("validationStepService")
@Transactional
public class ValidationStepServiceImpl implements ValidationStepService{
	
	private UserRepository userRepository;
	private RoleRepository roleRepository;
	private ValidationStepRepository validationStepRepository;
	private ExpenseReportRepository noteFraisRepository;
	private ActionMarketingRepository actionMarketingRepository;
	private PlanningValidationRepository planningValidationRepository;
	private ValidationStatusRepository validationStatusRepository;
	private ProspectRepository prospectRepository;
	private DelegateCommissionRepository delegateCommissionRepository;
	private CurrentUser currentUser;
	private NotificationRuleService notificationManagerService;
	private NotificationMessageBuilder notificationMessageBuilder;
	private NotificationService notificationService;
	private UserToDtoConvertor userToDtoConvertor;
	private ConfigurationService configurationService;

	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	ResourceLoader resourceLoader;
	@Autowired
	private MessageSource messageSource;
	@Autowired
	private UserService userService;

	
	@Autowired
	public ValidationStepServiceImpl(
			UserRepository userRepository,
			RoleRepository roleRepository,
			ValidationStepRepository validationStepRepository,
			ExpenseReportRepository noteFraisRepository,
			ActionMarketingRepository actionMarketingRepository,
			PlanningValidationRepository planningValidationRepository,
			ProspectRepository prospectRepository,
			DelegateCommissionRepository delegateCommissionRepository,
			ValidationStatusRepository validationStatusRepository,
			CurrentUser currentUser,
			NotificationRuleService notificationManagerService,
			NotificationMessageBuilder notificationMessageBuilder,
			NotificationService notificationService,
			UserToDtoConvertor userToDtoConvertor) {
		super();
		this.userRepository=userRepository;
		this.roleRepository=roleRepository;
		this.validationStepRepository=validationStepRepository;
		this.noteFraisRepository =noteFraisRepository;
		this.actionMarketingRepository =actionMarketingRepository;
		this.planningValidationRepository =planningValidationRepository;
		this.prospectRepository =prospectRepository;
		this.delegateCommissionRepository = delegateCommissionRepository;
		this.validationStatusRepository =validationStatusRepository;
		this.currentUser =currentUser;
		this.notificationManagerService =notificationManagerService;
		this.notificationMessageBuilder = notificationMessageBuilder;
		this.notificationService = notificationService;
		this.userToDtoConvertor = userToDtoConvertor;}
	
	
	

    
	@Override
	public void cancelAccept(List<ValidationStatus> allValidationStatus, Long validationStatusId, Long entityId) {
		
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		userValidationStatus.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
		validationStatusRepository.save(userValidationStatus);
	}
	
	@Override
	public boolean accept(List<ValidationStatus> allValidationStatus, Long validationStatusId, Long entityId) {
		
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(validationStatusId);
		userValidationStatus.setStatus(UserValidationStatus.ACCEPTED);
		validationStatusRepository.save(userValidationStatus);


		int maxRank = 0;
		int acceptedByUserCount = 0;
		
		for (ValidationStatus validationStatus : allValidationStatus) {
			// count current number of users that accepted
			if (validationStatus.getStatus().equals(UserValidationStatus.ACCEPTED) && validationStatus.getRank().equals(userValidationStatus.getRank())) {
				acceptedByUserCount++;
			}
			if (validationStatus.getRank() > maxRank) {
				maxRank = validationStatus.getRank();
			}
		}
		
		boolean isWorkflowFinished = false;

		if (acceptedByUserCount == userValidationStatus.getValidationStep().getMinValidatorsNumber()) {

			if ( userValidationStatus.getRank() < maxRank) {
				
				// update next users status that need to validate
				for (ValidationStatus validationStatus : allValidationStatus) {
					
					if (validationStatus.getStatus().equals(UserValidationStatus.NEW) && validationStatus.getRank().equals( userValidationStatus.getRank() +1 )) {
						
						validationStatus.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
						validationStatusRepository.save(validationStatus);
						notificationMessageBuilder.setMessageType("planningValidationRequest");
						notificationMessageBuilder.setUser(validationStatus.getUser());
						notificationMessageBuilder.setTagetUser(validationStatus.getUser());
						notificationMessageBuilder.setEntityId(entityId);
						Notification notification = notificationMessageBuilder.Build();
						notificationService.generateUsingAllNotificationMethods(notification);
						
					}
				}
			} else {
				isWorkflowFinished = true;
			}

		}
		return isWorkflowFinished;
	}
	
	@Override
	public void refuse (Long planningValidationId) {
		
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(planningValidationId);
		userValidationStatus.setStatus(UserValidationStatus.REFUSED);
		validationStatusRepository.save(userValidationStatus);
		
	}
	
	@Override
	public void review (Long planningValidationId) {
		
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(planningValidationId);
		userValidationStatus.setStatus(UserValidationStatus.TO_BE_REVIEWED);
		validationStatusRepository.save(userValidationStatus);
		
	}
	
	@Override
	public boolean add(ValidationStepRequest validationStepRequest) throws BirdnotesException {
		
		if (validationStepRequest.getSelectedValidationType() == null || "".equals(validationStepRequest.getSelectedValidationType() )) {
			throw new BirdnotesException(Exceptions.VALIDATION_TYPE_IS_EMPTY);
		}
		for (ValidationStepItem validationStepItem : validationStepRequest.getValidationSteps()) {
			if (validationStepItem.getNumberValidators() == 0) {
				throw new BirdnotesException(Exceptions.NUMBER_OF_VALIDATORS_IS_EMPTY);
			}
		}
		List<Long> validationStepIdsToDelete = validationStepRepository.findValidationStepIdsByType(validationStepRequest.getSelectedValidationType());
		//deleteByValidationType(validationStepRequest.getSelectedValidationType());
		int rank = 1;
		List<ValidationStatus> validationStatusToUpdate = null;
		List<Long> userIds = new ArrayList<>();
		for (ValidationStepItem validationStepItem : validationStepRequest.getValidationSteps()) {
			ValidationStep validationStep = new ValidationStep ();
			
			validationStep.setValidationType(validationStepRequest.getSelectedValidationType());
			validationStep.setMinValidatorsNumber(validationStepItem.getNumberValidators());
			validationStep.setRank(rank++);
			if (validationStepItem.getIdRole() != null ) {
				Role role = roleRepository.findOne(validationStepItem.getIdRole());
				validationStep.setRole(role);
				userIds = userRepository.getUsersByRole(validationStepItem.getIdRole());
				validationStatusToUpdate = validationStatusRepository.findByUserIdsAndValidationStepIds(userIds, validationStepIdsToDelete);
			}
			if (validationStepItem.getIdUser() != null ) {
				User user = userRepository.findOne(validationStepItem.getIdUser());
				validationStep.setUser(user);
				userIds.add(user.getId());
				validationStatusToUpdate = validationStatusRepository.findByUserIdsAndValidationStepIds(userIds, validationStepIdsToDelete);
			}
			
			ValidationStep savedValidationStep = validationStepRepository.save(validationStep);
			for(ValidationStatus validationStatus : validationStatusToUpdate) {
				validationStatus.setValidationStep(savedValidationStep);
				validationStatusRepository.save(validationStatus);
			}
		}
		
		validationStepRepository.deleteByValidationStep(validationStepIdsToDelete);
		return true;
	}
	@Override
	public void deleteByValidationType(String validationType) {
		
			validationStepRepository.deleteByValidationType(validationType);		
	}

	@Override
	public List<ValidationType> getValidationTypeXml() throws IOException {
		try {
			Resource resource = resourceLoader.getResource("classpath:validationType.xml");
			InputStream input = resource.getInputStream();
			File file = resource.getFile();
			JAXBContext jaxbContext = JAXBContext.newInstance(ValidationTypeList.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			ValidationTypeList validationTypeList = (ValidationTypeList) jaxbUnmarshaller.unmarshal(file);

			return validationTypeList.getValidationType();
			
		} catch (Exception e) {
			log.error("error in getXml " , e);
			return new ArrayList<>();
		}
	}

	@Override
	public List<ValidationStepDto> findValidationStepByType(String validationType) throws BirdnotesException {
		List<ValidationStep> validationSteps=validationStepRepository.findValidationStepByType(validationType);
		List<ValidationStepDto> validationStepDtos = new ArrayList<>();
		if (validationSteps != null && !validationSteps.isEmpty()) {
			for (ValidationStep validationStep : validationSteps) {
				ValidationStepDto validationStepDto = new ValidationStepDto();
				validationStepDto.setMinValidatorsNumber(validationStep.getMinValidatorsNumber());
				if (validationStep.getRole() != null) {
					Role role = roleRepository.findOne(validationStep.getRole().getId());
					validationStepDto.setRoleId(role.getId());
					validationStepDto.setName(role.getName());
				}
				if(validationStep.getUser() != null) {
					User user = userRepository.findOne(validationStep.getUser().getId());
					validationStepDto.setUserId(user.getId());
					validationStepDto.setName(user.getUsername());
				}
				validationStepDtos.add(validationStepDto);
			}
		}
		return validationStepDtos;
	}

	@Override
	public void addValidationStatus(String validationType , long id, long idDelegate) throws BirdnotesException {
		
		List<ValidationStep> validationSteps = validationStepRepository.findValidationStepByType(validationType);		
		
		for(ValidationStep validationStep : validationSteps) {
	
			if(validationStep.getUser() != null ) {	
				createValidationStatus (validationType ,id, validationStep.getUser(), validationStep);				
			}
			
			if(validationStep.getRole() !=null ) {
				
				Set <User> listSuperiorsByRole =notificationManagerService.getSuperiorsByRole(idDelegate, validationStep.getRole().getName());
				for(User superior : listSuperiorsByRole) {
					
					createValidationStatus (validationType ,id, superior, validationStep);
									
				}
			}	
		}
	}
	
	private void createValidationStatus (String validationType , long id, User user, ValidationStep validationStep)throws BirdnotesException {
		
		ValidationStatus validationStatus = new ValidationStatus ();
		validationStatus.setUser(user);
		
		if(validationType.equals("NEW_MARKETING_ACTION")) {
			ActionMarketing actionMarketing = actionMarketingRepository.findOne(id);
			validationStatus.setActionMarketing(actionMarketing);
		}
		if(validationType.equals("NEW_PLANIFICATION")) {
			PlanningValidation planningValidation = planningValidationRepository.findOne(id);
			validationStatus.setPlanningValidation(planningValidation);		
		}
		if(validationType.equals("NEW_UPDATE_PROSPECT")) {
			Prospect prospect = prospectRepository.findOne(id);
			validationStatus.setProspect(prospect);	
		}
		if(validationType.equals("NEW_EXPENSE")) {
			ExpenseReport noteFrais = noteFraisRepository.findOne(id);
			validationStatus.setNoteFrais(noteFrais);
		}
		if(validationType.equals("NEW_DELEGATE_COMMISSION")) {
			DelegateCommission delegateCommission = delegateCommissionRepository.findOne(id);
			validationStatus.setDelegateCommission(delegateCommission);
		}
		validationStatus.setRank(validationStep.getRank());

		validationStatus.setValidationStep(validationStep);
		validationStatus.setCreationDate(new Date());
		
		if (validationStep.getRank() == 1) {
			
			validationStatus.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			
			if(validationType.equals("NEW_EXPENSE")) {
				notificationMessageBuilder.setMessageType("noteFraisValidationRequest");
				ExpenseReport noteFrais = noteFraisRepository.findOne(id);
				notificationMessageBuilder.setUser(noteFrais.getDelegate().getUser());
				notificationMessageBuilder.setEntityId(noteFrais.getId());
				
				}
			if(validationType.equals("NEW_MARKETING_ACTION")) {
				ActionMarketing actionMarketing = actionMarketingRepository.findOne(id);
				notificationMessageBuilder.setMessageType("actionMarketingValidationRequest");
				notificationMessageBuilder.setUser(actionMarketing.getUser());
				notificationMessageBuilder.setEntityId(actionMarketing.getId());
				
			}
			if(validationType.equals("NEW_PLANIFICATION")) {
				
				notificationMessageBuilder.setMessageType("planningValidationRequest");
				PlanningValidation planningValidation = planningValidationRepository.findOne(id);
				notificationMessageBuilder.setUser(planningValidation.getDelegate().getUser());
				notificationMessageBuilder.setEntityId(planningValidation.getId());
				
				}
			if(validationType.equals("NEW_UPDATE_PROSPECT")) {
				notificationMessageBuilder.setMessageType("prospectValidationRequest");
				Prospect prospect = prospectRepository.findOne(id);
				notificationMessageBuilder.setUser(prospect.getUser());
				notificationMessageBuilder.setEntityId(prospect.getId());
				
			}
			
			if(validationType.equals("NEW_DELEGATE_COMMISSION")) {
				notificationMessageBuilder.setMessageType("delegateCommissionValidationRequest");
				DelegateCommission delegateCommission = delegateCommissionRepository.findOne(id);
				notificationMessageBuilder.setUser(delegateCommission.getUser());
				notificationMessageBuilder.setEntityId(delegateCommission.getId());
			}
			
			notificationMessageBuilder.setTagetUser(validationStatus.getUser());
			Notification notification = notificationMessageBuilder.Build();
			notificationService.generateUsingAllNotificationMethods(notification);
			
		}else{
			validationStatus.setStatus(UserValidationStatus.NEW);
		}

		validationStatusRepository.save(validationStatus);
	}

	
	@Override
	public List<ValidationStatusDto> findValidationStatusNoteFrais() throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}
		
		List<ValidationStatusDto> listValidationStatusDto= new ArrayList <ValidationStatusDto>();

		List<ValidationStatus> AllValidationStatus =validationStatusRepository.findUserValidationStatus();
		
		for(ValidationStatus validationStatus : AllValidationStatus) {
			ValidationStatusDto validationStatusDto = new ValidationStatusDto();
			validationStatusDto.setId(validationStatus.getId());
			validationStatusDto.setName(validationStatus.getUser().getUsername());
			validationStatusDto.setStatus(validationStatus.getStatus().toString());
			if (validationStatus.getUser().getId().equals( birdnotesUser.getUserDto().getId()) )
			{
				validationStatusDto.setCurrentUser(true);
			}
			else {validationStatusDto.setCurrentUser(false);}
			listValidationStatusDto.add(validationStatusDto);
		}
		return listValidationStatusDto;
	}

	@Override
	public List<ValidationStatusDto> findByNoteFrais(ExpenseReport noteFrais) throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}
		
		List<ValidationStatusDto> listValidationStatusDto= new ArrayList <ValidationStatusDto>();

		List<ValidationStatus> ValidationStatus =validationStatusRepository.findByNoteFraisOrderByRankAsc (noteFrais);
		for(ValidationStatus validationStatus : ValidationStatus) {
			
			ValidationStatusDto validationStatusDto = new ValidationStatusDto();
			validationStatusDto.setId(validationStatus.getId());
			validationStatusDto.setName(validationStatus.getUser().getUsername());
			validationStatusDto.setStatus(validationStatus.getStatus().toString());
			if (validationStatus.getUser().getId().equals( birdnotesUser.getUserDto().getId()) )
			{
				validationStatusDto.setCurrentUser(true);
			}
			else {validationStatusDto.setCurrentUser(false);}
			listValidationStatusDto.add(validationStatusDto);
		}
		
		return listValidationStatusDto;
	}

	@Override
	public List<ValidationStatusDto> findByDelegateCommission(DelegateCommission delegateCommission) throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}
		
		List<ValidationStatusDto> listValidationStatusDto= new ArrayList <ValidationStatusDto>();

		List<ValidationStatus> ValidationStatus =validationStatusRepository.findByDelegateCommissionOrderByRankAsc(delegateCommission);
		for(ValidationStatus validationStatus : ValidationStatus) {
			
			ValidationStatusDto validationStatusDto = new ValidationStatusDto();
			validationStatusDto.setId(validationStatus.getId());
			validationStatusDto.setName(validationStatus.getUser().getUsername());
			validationStatusDto.setStatus(validationStatus.getStatus().toString());
			if (validationStatus.getUser().getId().equals( birdnotesUser.getUserDto().getId()) )
			{
				validationStatusDto.setCurrentUser(true);
			}
			else {validationStatusDto.setCurrentUser(false);}
			listValidationStatusDto.add(validationStatusDto);
		}
		
		return listValidationStatusDto;
	}
	
	@Override
	public List<ValidationStatusDto> findByPlanningValidation(PlanningValidation planningValidation)
			throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}
		
		List<ValidationStatusDto> listValidationStatusDto= new ArrayList <ValidationStatusDto>();

		List<ValidationStatus> ValidationStatus =validationStatusRepository.findByPlanningValidationOrderByRankAsc (planningValidation);
		for(ValidationStatus validationStatus : ValidationStatus) {
			
			ValidationStatusDto validationStatusDto = new ValidationStatusDto();
			validationStatusDto.setId(validationStatus.getId());
			validationStatusDto.setName(validationStatus.getUser().getUsername());
			validationStatusDto.setStatus(validationStatus.getStatus().toString());
			if (validationStatus.getUser().getId().equals( birdnotesUser.getUserDto().getId()) )
			{
				validationStatusDto.setCurrentUser(true);
			}
			else {validationStatusDto.setCurrentUser(false);}
			listValidationStatusDto.add(validationStatusDto);
		}
		
		return listValidationStatusDto;

	}
	@Override
	public List<ValidationStatusDto> findByActionMarketing(ActionMarketing actionMarketing) throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}
		
		List<ValidationStatusDto> listValidationStatusDto= new ArrayList <ValidationStatusDto>();

		List<ValidationStatus> ValidationStatus =validationStatusRepository.findByActionMarketingOrderByRankAsc (actionMarketing);
		for(ValidationStatus validationStatus : ValidationStatus) {
			
			ValidationStatusDto validationStatusDto = new ValidationStatusDto();
			validationStatusDto.setId(validationStatus.getId());
			validationStatusDto.setName(validationStatus.getUser().getUsername() );
			validationStatusDto.setStatus(validationStatus.getStatus().toString());
			if (validationStatus.getUser().getId().equals( birdnotesUser.getUserDto().getId()) )
			{
				validationStatusDto.setCurrentUser(true);
			}
			else {validationStatusDto.setCurrentUser(false);}
			listValidationStatusDto.add(validationStatusDto);
		}
		
		return listValidationStatusDto;
	}

	@Override
	public List<ValidationStatusDto> findByProspect(long prospectId) throws BirdnotesException {
		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {
			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);
		}
		
		List<ValidationStatusDto> listValidationStatusDto= new ArrayList <ValidationStatusDto>();
		Prospect prospect = prospectRepository.findOne(prospectId);
		List<ValidationStatus> ValidationStatus =validationStatusRepository.findByProspectOrderByRankAsc (prospect);
		for(ValidationStatus validationStatus : ValidationStatus) {
			
			ValidationStatusDto validationStatusDto = new ValidationStatusDto();
			validationStatusDto.setId(validationStatus.getId());
			validationStatusDto.setName(validationStatus.getUser().getUsername() );
			validationStatusDto.setStatus(validationStatus.getStatus().toString());
			if (validationStatus.getUser().getId().equals( birdnotesUser.getUserDto().getId()) )
			{
				validationStatusDto.setCurrentUser(true);
			}
			else {validationStatusDto.setCurrentUser(false);}
			listValidationStatusDto.add(validationStatusDto);
		}
		return listValidationStatusDto;
	}

	

}
