package com.intellitech.birdnotes.model.request;

import java.util.List;
import java.util.Set;

public class ProductRequest {

	private String name;
	private Float price;
	private Integer numberOfCapsules;
	private String description;
	private Integer categoryId;
	private List<Integer> categoryIds;
	private Set<String> categoriesAsString;
	private Integer gammeId;
	private List<Integer> rangeIds;
	private Set<String> gammesAsString;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Float getPrice() {
		return price;
	}

	public void setPrice(Float price) {
		this.price = price;
	}

	public Integer getNumberOfCapsules() {
		return numberOfCapsules;
	}

	public void setNumberOfCapsules(Integer numberOfCapsules) {
		this.numberOfCapsules = numberOfCapsules;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public List<Integer> getCategoryIds() {
		return categoryIds;
	}

	public void setCategoryIds(List<Integer> categoryIds) {
		this.categoryIds = categoryIds;
	}

	public Set<String> getCategoriesAsString() {
		return categoriesAsString;
	}

	public void setCategoriesAsString(Set<String> categoriesAsString) {
		this.categoriesAsString = categoriesAsString;
	}

	public Integer getGammeId() {
		return gammeId;
	}

	public void setGammeId(Integer gammeId) {
		this.gammeId = gammeId;
	}



	public List<Integer> getRangeIds() {
		return rangeIds;
	}

	public void setRangeIds(List<Integer> rangeIds) {
		this.rangeIds = rangeIds;
	}

	public Set<String> getGammesAsString() {
		return gammesAsString;
	}

	public void setGammesAsString(Set<String> gammesAsString) {
		this.gammesAsString = gammesAsString;
	}
	
	

}
