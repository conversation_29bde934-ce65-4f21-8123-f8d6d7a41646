package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

public class SurveyDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	private Long productId;
	private String productName;
	private String evaluationCriteriaName;
	private Float evaluationCriteriaValue;
	private Date evaluationDate;
	private String comment;
	private String criteriaFileName;
	
	private Map<String, Float> ratings;

	public Map<String, Float> getRatings() {
		return ratings;
	}

	public void setRatings(Map<String, Float> ratings) {
		this.ratings = ratings;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public String getEvaluationCriteriaName() {
		return evaluationCriteriaName;
	}

	public void setEvaluationCriteriaName(String evaluationCriteriaName) {
		this.evaluationCriteriaName = evaluationCriteriaName;
	}

	public Float getEvaluationCriteriaValue() {
		return evaluationCriteriaValue;
	}

	public void setEvaluationCriteriaValue(Float evaluationCriteriaValue) {
		this.evaluationCriteriaValue = evaluationCriteriaValue;
	}

	public Date getEvaluationDate() {
		return evaluationDate;
	}

	public void setEvaluationDate(Date evaluationDate) {
		this.evaluationDate = evaluationDate;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public String getCriteriaFileName() {
		return criteriaFileName;
	}

	public void setCriteriaFileName(String criteriaFileName) {
		this.criteriaFileName = criteriaFileName;
	}

}
