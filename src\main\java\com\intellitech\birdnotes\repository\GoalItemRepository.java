package com.intellitech.birdnotes.repository;

import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;

@Repository
public interface GoalItemRepository extends JpaRepository<GoalItem, Long>{
	
	Set<GoalItem> findByGoal(Goal goal);
	
	@Modifying
	@Query("DELETE FROM GoalItem gi where gi.goal.id =:id")
	void deleteById(@Param("id") Long id);
	
	/*@Query("SELECT gt FROM User u  join u.goals.goalItems gt where u.id=:userId")
	List<GoalItem> getGoalByUser(@Param ("userId") Long userId);*/
}
