package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.data.dto.UserAffectationDetailsDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.DelegateProspectsDto;
import com.intellitech.birdnotes.model.dto.PreAffectationDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
//import com.intellitech.birdnotes.model.dto.ProspectsAffectationRequestDto;
import com.intellitech.birdnotes.model.dto.ProspectsAffectationRequestDto;
import com.intellitech.birdnotes.model.dto.SelectedDataForSourceProspectsRequestDto;



public interface ProspectsAffectationService {
	
	public void saveAffectation(ProspectsAffectationRequestDto prospectsAffectationDto) throws BirdnotesException; 
	public List<ProspectDto> findprospectsByUser(Long userId)throws BirdnotesException;
	public boolean delete (Long id);
	public List<ProspectDto> findSourceProspects(Long userId) throws BirdnotesException;
	
	public List<ProspectDto> findprospectsByUserAndSectors(Long userId, List<Long> sectorIds)throws BirdnotesException;
	public List<ProspectDto> findSourceProspectsWithSectors(Long userId,List<Long> sectorIds) throws BirdnotesException;
	public List<ProspectDto> findSourceProspectsByMultipleSelection(SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest)throws BirdnotesException;
	public void saveAffectation(List<Long>usersIds,Prospect prospect) throws BirdnotesException;
	public DelegateProspectsDto findDelegateProspectsByMultipleSelection(
			SelectedDataForSourceProspectsRequestDto selectedDataForSourceProspectsRequest) throws BirdnotesException;
	public List<PreAffectationDto> findPreAffectationByUser(Long userId) throws BirdnotesException;
	public void deletePreAffectation(Long id) throws BirdnotesException ;
	void deleteAffectation(ProspectsAffectationRequestDto prospectsAffectationDto) throws BirdnotesException;
	UserAffectationDetailsDto getUserAffectationDetails(Long userId) throws BirdnotesException;

}
