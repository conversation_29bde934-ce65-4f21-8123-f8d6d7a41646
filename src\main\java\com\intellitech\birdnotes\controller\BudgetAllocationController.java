package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.dao.DataIntegrityViolationException;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.BudgetAllocation;
import com.intellitech.birdnotes.model.dto.BudgetAllocationDto;


import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.BudgetAllocationService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/budgetAllocations")
public class BudgetAllocationController {

	private static final Logger LOG = LoggerFactory.getLogger(BudgetAllocationController.class);

	@Autowired
	private BudgetAllocationService budgetAllocationService;

	@Autowired
	UserService userService;
	
	
	@RequestMapping(value = "/findAllBudgetAllocations", method = RequestMethod.GET)
	public ResponseEntity<List<BudgetAllocationDto>> getAllBudgetAllocation() {
		try {
			if (userService.checkHasPermission("BUDGET_ALLOCATION_VIEW")) {
				List<BudgetAllocationDto> result = budgetAllocationService.findAll();
				return new ResponseEntity<>(result, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all budget allocations ", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	@RequestMapping(value = "/saveBudgetAllocation", method = RequestMethod.POST)
	public ResponseEntity<String> saveBudgetAllocation(@RequestBody BudgetAllocationDto budgetAllocationDto) {
	    try {
	        if (userService.checkHasPermission("BUDGET_ALLOCATION_EDIT")) {
	        	BudgetAllocation savedBudgetAllocation = budgetAllocationService.saveBudgetAllocation(budgetAllocationDto);
	            if (savedBudgetAllocation != null) {
	                return new ResponseEntity<>(savedBudgetAllocation.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving budget allocation", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving budget allocation", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteBudgetAllocation(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("BUDGET_ALLOCATION_DELETE")) {
	        	budgetAllocationService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting budget allocation", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the budget allocation with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	}
