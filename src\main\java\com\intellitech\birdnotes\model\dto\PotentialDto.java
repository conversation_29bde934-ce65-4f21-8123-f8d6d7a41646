package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class PotentialDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String name;
	private float weight;


    //Ajouté pour le test
	public PotentialDto(String name) {
		super();
		this.name = name;
	}
	
	public PotentialDto() {
		super();
		
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public float getWeight() {
		return weight;
	}

	public void setWeight(float weight) {
		this.weight = weight;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

}
