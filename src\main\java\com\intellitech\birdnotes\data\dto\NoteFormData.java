package com.intellitech.birdnotes.data.dto;

import java.util.List;

import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;

public class NoteFormData {
	private List<SectorDto> sectors;
	private List<SpecialityDto> specialities;

	public List<SectorDto> getSectors() {
		return sectors;
	}

	public void setSectors(List<SectorDto> sectors) {
		this.sectors = sectors;
	}

	public List<SpecialityDto> getSpecialities() {
		return specialities;
	}

	public void setSpecialities(List<SpecialityDto> specialities) {
		this.specialities = specialities;
	}

}
