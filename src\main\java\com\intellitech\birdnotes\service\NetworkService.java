package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.dto.NetworkDto;

public interface NetworkService {

	Network add(Network network) throws BirdnotesException;
	void delete(Long id) throws BirdnotesException;
	List<Network> findAll() throws BirdnotesException;
	public boolean checkNetworkNameIsUnique(String networkName);
	public Network saveNetwork(NetworkDto networkDto) throws BirdnotesException;
}
