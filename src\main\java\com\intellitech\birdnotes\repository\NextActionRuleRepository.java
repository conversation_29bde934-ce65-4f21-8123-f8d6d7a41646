package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.NextActionRule;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Speciality;

@Repository
public interface NextActionRuleRepository extends JpaRepository<NextActionRule, Long> {
	
	

	
	
	
	




	
	
}