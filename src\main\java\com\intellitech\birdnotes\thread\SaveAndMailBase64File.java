package com.intellitech.birdnotes.thread;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.MessageFormat;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.springframework.mail.javamail.JavaMailSender;


import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.enumeration.EmailType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToPurchaseOrder;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.util.BirdnotesUtils;

import net.sf.jasperreports.engine.JRException;

public class SaveAndMailBase64File implements Runnable {

	private String imageString; 
	private String imageName; 
	private String pathUpload;
	private String[] to;
	private String from;
	private String subject;
	private String htmlBody;
	private String pathAttachment;
	private String nameAttachment;
	private JavaMailSender javaMailSender;
	private List<String> CCs;
	private boolean purchaseOrder;
	private List<PurchaseOrderDto> purchaseOrderList;
	private static final String IMGBALISE = " <br/> <img src='";
	
	
	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String[] getTo() {
		return to;
	}

	public void setTo(String[] to) {
		this.to = to;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getHtmlBody() {
		return htmlBody;
	}

	public void setHtmlBody(String htmlBody) {
		this.htmlBody = htmlBody;
	}

	public String getPathAttachment() {
		return pathAttachment;
	}

	public void setPathAttachment(String pathAttachment) {
		this.pathAttachment = pathAttachment;
	}

	public String getNameAttachment() {
		return nameAttachment;
	}

	public void setNameAttachment(String nameAttachment) {
		this.nameAttachment = nameAttachment;
	}

	public JavaMailSender getJavaMailSender() {
		return javaMailSender;
	}

	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}

	public String getImageString() {
		return imageString;
	}

	public void setImageString(String imageString) {
		this.imageString = imageString;
	}

	public String getImageName() {
		return imageName;
	}

	public void setImageName(String imageName) {
		this.imageName = imageName;
	}

	public String getPathUpload() {
		return pathUpload;
	}

	public void setPathUpload(String pathUpload) {
		this.pathUpload = pathUpload;
	}

	
	
	public List<PurchaseOrderDto> getPurchaseOrderList() {
		return purchaseOrderList;
	}

	public void setPurchaseOrderList(List<PurchaseOrderDto> purchaseOrderList) {
		this.purchaseOrderList = purchaseOrderList;
	}

	public SaveAndMailBase64File(String imageString, String imageName, String pathUpload, boolean purchaseOrder, 
			JavaMailSender javaMailSender, List<String> CCs, String from, String[] to, String subject, String htmlBody) {
		this.imageString = imageString;
		this.imageName = imageName;
		this.pathUpload = pathUpload;
		this.purchaseOrder=purchaseOrder;
		this.javaMailSender = javaMailSender;
		this.to = to;
		this.from=from;
		this.CCs = CCs;
		this.subject = subject;
		this.htmlBody = htmlBody;

	}
	
	private String createFolder(Long id, String path, String uploadPath) throws BirdnotesException {
		
		String pathUpload = uploadPath + path + File.separator + id;
		File dirName = new File(pathUpload);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		return pathUpload;
	}
	
	
	
	
	@Override
	public void run() {
		
		
		
		if(imageString==null || imageName==null || "".equals(imageName) || "".equals(imageString)) {
			if(to != null && to.length > 0) {
				BirdnotesUtils.sendEmail(javaMailSender, to, subject, htmlBody,from);
			}
		}		
		else {
				BirdnotesUtils.saveBase64ToImage(imageString, imageName, pathUpload);
				
				if(imageString!=null && imageName!=null && purchaseOrder == true) {
					
					if(to != null && to.length > 0) {

						BirdnotesUtils.sendEmailWithAttachment(javaMailSender,from, to,CCs, subject, 
								htmlBody, pathUpload, imageName, null, null, null,null);
					
					
				}
				}
			} 
	}
	}

	




