package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.SampleSupplyItem;

public interface SampleSupplyRepository extends JpaRepository<SampleSupply, Long> {
	@Query("SELECT distinct s  from  SampleSupply s join  s.sampleSupplyItems si where (DATE(s.deliveryDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) AND s.delegate.id=:userId  AND si.product.id=:productId")
	List<SampleSupply> findSamplesSupplyByUserDateAndProduct(@Param("firstDate") Date firstDate,
			@Param("lastDate") Date lastDate, @Param("userId") Long userId, @Param("productId") Long productId);

	
	@Query("SELECT s from SampleSupply s WHERE DATE(s.deliveryDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate) ")
	List<SampleSupply> findByDate(@Param("firstDate") Date startDate, @Param("lastDate") Date endDate);
	
	@Query("SELECT s.sampleSupplyItems from SampleSupply s WHERE s.id=:sampleSupplyId ")
	List<SampleSupplyItem> findBySampleSupplyId(@Param("sampleSupplyId") Long sampleSupplyId);
	
	@Modifying
	@Query("DELETE FROM SampleSupply where id = :sampleSupplyId")
	void deleteById(@Param("sampleSupplyId") Long sampleSupplyId);

}
