package com.intellitech.birdnotes.repository;

import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.enumeration.GiftType;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.User;

public interface GiftRepository extends JpaRepository<Gift, Long> {

	Gift findByName(String name);
	
	

	@Query("Select g from Gift g ORDER BY g.price, g.name ASC")
	List<Gift> findAll();

	@Query("Select g from Gift g where g.type=:giftType ORDER BY g.name ASC")
	List<Gift> findByGiftType(@Param("giftType") GiftType giftType);

	
	@Modifying
	@Query("DELETE FROM Gift where id=:id")
	void deleteById(@Param("id") Long id);

	@Query("SELECT p.id from Gift p WHERE p.id in (:gadgets)")
	List<Integer> findWhereIdIn(@Param("gadgets") List<Integer> gadgets);
	
	@Query("SELECT g from Gift g join g.purchaseOrderTemplates pot where pot.id =:id ")
	Set<Gift> findGiftsOfPot(@Param("id") Long id);
	
	@Query("SELECT g from Gift g where  LOWER(name) = LOWER(?1) AND id != ?2")
	Gift findByNameAndAnotherId(String name, Long id);

}
