package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.dto.ExpenseTypeDto;

@Component("convertTypeNoteFraisToDto")
public class ConvertTypeNoteFraisToDto {
	private static final Logger LOG = LoggerFactory.getLogger(ConvertTypeNoteFraisToDto.class);

	public ExpenseTypeDto convert(ExpenseType typeNoteFrais) throws BirdnotesException {

		if (typeNoteFrais == null) {
			LOG.error("typeNoteFrais is null");
			throw new BirdnotesException("typeNoteFrais is null");
		}
		ExpenseTypeDto typeNoteFraisDto = new ExpenseTypeDto();
		typeNoteFraisDto.setName(typeNoteFrais.getName());
		typeNoteFraisDto.setId(typeNoteFrais.getId());
		typeNoteFraisDto.setPrice(typeNoteFrais.getPrice());
		return typeNoteFraisDto;
	}

}
