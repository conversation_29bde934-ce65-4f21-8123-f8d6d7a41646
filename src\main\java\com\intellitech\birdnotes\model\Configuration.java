package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Lob;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.OrderValidation;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;

@Entity
@Table(name = BirdnotesConstants.Tables.CONFIGURATION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Configuration implements Serializable {

	private static final long serialVersionUID = 1L;
	private Integer id;
	private String name;
	private String logo;
	private Boolean syncro;
	private Boolean autoSync;
	private Boolean lockAfterSync;
	private Integer openReportPeriod;
	private Integer openExpensePeriod;
	private Boolean multiWholesaler;
	private Integer cycle;
	private Integer syncCycle;
	private Integer acceptedPointingDistance;
	private Boolean sendSyncReminder;
	private String syncReminderPeriod;
	private String holidays;
	private String commentsDictionary;
	private String commentsRatingNotification;
	private String orderPredictionCron;
	private String biPanels;
	private String erpParams;
	private String serverPath;
	private String mlServerUrl;
	private Boolean autoExpense;
	private String packageName;
	private OrderValidation orderValidation;
	private String biServerUrl;
	private Date reportingStartingTime;
	private Date reportingEndingTime;
	private Integer delayedReportingTolerence;
	private Integer noGeolocationTolerence;
	private String host;
	private Integer port;
	private String email;
	private String emailPassword;
	private Integer databaseVersion;
	private String language;
	private String displacementCron;
	private String erpType;
	private String erpUrl;
	private String erpSyncCron;
	private String reportValidationCron;
	private String reportingReminderCron;
	private String expenseDistanceCron;
	private String backendUrl;
	private Double defaultLatitude;
	private Double defaultLongitude;
	private String fieldParameters;

	public Configuration() {
		super();
	}

	public Configuration(Integer id, String name, String logo, Boolean syncro, Integer cycle, Boolean autoSync,
			Boolean lockAfterSync, Integer openReportPeriod, Integer openExpensePeriod, Boolean multiWholesaler,
			Integer syncCycle) {
		super();
		this.id = id;
		this.name = name;
		this.logo = logo;
		this.syncro = syncro;
		this.cycle = cycle;
		this.autoSync = autoSync;
		this.lockAfterSync = lockAfterSync;
		this.openReportPeriod = openReportPeriod;
		this.openExpensePeriod = openExpensePeriod;
		this.multiWholesaler = multiWholesaler;
		this.syncCycle = syncCycle;
	}

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.CONFIGURATION_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.CONFIGURATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.CONFIGURATION_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = BirdnotesConstants.Columns.NAME, length = BirdnotesConstants.Numbers.N_85)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = BirdnotesConstants.Columns.PACKAGE_NAME, length = BirdnotesConstants.Numbers.N_85)
	public String getPackageName() {
		return packageName;
	}

	public void setPackageName(String packageName) {
		this.packageName = packageName;
	}

	@Column(name = BirdnotesConstants.Columns.LOGO)
	public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	@Column(name = BirdnotesConstants.Columns.SYNCRO)
	public Boolean getSyncro() {
		return syncro;
	}

	public void setSyncro(Boolean syncro) {
		this.syncro = syncro;
	}

	@Column(name = BirdnotesConstants.Columns.AUTOSYNC)
	public Boolean getAutoSync() {
		return autoSync;
	}

	public void setAutoSync(Boolean autoSync) {
		this.autoSync = autoSync;
	}

	@Column(name = BirdnotesConstants.Columns.AUTOEXPENSE)
	public Boolean getAutoExpense() {
		return autoExpense;
	}

	public void setAutoExpense(Boolean autoExpense) {
		this.autoExpense = autoExpense;
	}

	@Column(name = BirdnotesConstants.Columns.ORDER_VALIDATION)
	public OrderValidation getOrderValidation() {
		return orderValidation;
	}

	public void setOrderValidation(OrderValidation orderValidation) {
		this.orderValidation = orderValidation;
	}

	@Column(name = BirdnotesConstants.Columns.OPEN_REPORT_PERIOD)
	public Integer getOpenReportPeriod() {
		return openReportPeriod;
	}

	public void setOpenReportPeriod(Integer openReportPeriod) {
		this.openReportPeriod = openReportPeriod;
	}

	@Column(name = BirdnotesConstants.Columns.OPEN_EXPENSE_PERIOD)
	public Integer getOpenExpensePeriod() {
		return openExpensePeriod;
	}

	public void setOpenExpensePeriod(Integer openExpensePeriod) {
		this.openExpensePeriod = openExpensePeriod;
	}

	@Column(name = BirdnotesConstants.Columns.LOCK_AFTER_SYNC)
	public Boolean getLockAfterSync() {
		return lockAfterSync;
	}

	public void setLockAfterSync(Boolean lockAfterSync) {
		this.lockAfterSync = lockAfterSync;
	}

	@Column(name = BirdnotesConstants.Columns.MULTI_WHOLESALER)
	public Boolean getMultiWholesaler() {
		return multiWholesaler;
	}

	public void setMultiWholesaler(Boolean multiWholesaler) {
		this.multiWholesaler = multiWholesaler;
	}

	@Column(name = BirdnotesConstants.Columns.CYCLE)
	public Integer getCycle() {
		return cycle;
	}

	public void setCycle(Integer cycle) {
		this.cycle = cycle;
	}

	@Column(name = BirdnotesConstants.Columns.SYNCCYCLE)
	public Integer getSyncCycle() {
		return syncCycle;
	}

	public void setSyncCycle(Integer syncCycle) {
		this.syncCycle = syncCycle;
	}

	@Column(name = BirdnotesConstants.Columns.ACCEPTED_POINTING_DISTANCE)
	public Integer getAcceptedPointingDistance() {
		return acceptedPointingDistance;
	}

	public void setAcceptedPointingDistance(Integer acceptedPointingDistance) {
		this.acceptedPointingDistance = acceptedPointingDistance;
	}

	public Boolean getSendSyncReminder() {
		return sendSyncReminder;
	}

	public void setSendSyncReminder(Boolean sendSyncReminder) {
		this.sendSyncReminder = sendSyncReminder;
	}

	public String getSyncReminderPeriod() {
		return syncReminderPeriod;
	}

	public void setSyncReminderPeriod(String syncReminderPeriod) {
		this.syncReminderPeriod = syncReminderPeriod;
	}

	@Column(length = Numbers.N_255)
	public String getHolidays() {
		return holidays;
	}

	public void setHolidays(String holidays) {
		this.holidays = holidays;
	}

	public String getCommentsDictionary() {
		return commentsDictionary;
	}

	public void setCommentsDictionary(String commentsDictionary) {
		this.commentsDictionary = commentsDictionary;
	}

	
	
	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	@Lob
	@Column(name = BirdnotesConstants.Columns.BI_PANELS)
	public String getBiPanels() {
		return biPanels;
	}

	public void setBiPanels(String biPanels) {
		this.biPanels = biPanels;
	}

	public String getServerPath() {
		return serverPath;
	}

	public void setServerPath(String serverPath) {
		this.serverPath = serverPath;
	}

	public String getMlServerUrl() {
		return mlServerUrl;
	}

	public void setMlServerUrl(String mlServerUrl) {
		this.mlServerUrl = mlServerUrl;
	}

	public String getBiServerUrl() {
		return biServerUrl;
	}

	public void setBiServerUrl(String biServerUrl) {
		this.biServerUrl = biServerUrl;
	}

	@Column(name = BirdnotesConstants.Columns.COMMENT_RATING_NOTIFICATION)
	public String getCommentsRatingNotification() {
		return commentsRatingNotification;
	}

	public void setCommentsRatingNotification(String commentsRatingNotification) {
		this.commentsRatingNotification = commentsRatingNotification;
	}

	@Column(name = BirdnotesConstants.Columns.ORDER_PREDICTION_CRON)
	public String getOrderPredictionCron() {
		return orderPredictionCron;
	}

	public void setOrderPredictionCron(String orderPredictionCron) {
		this.orderPredictionCron = orderPredictionCron;
	}

	@Column(name = Columns.REPORTING_STARTING_TIME)
	public void setReportingStartingTime(Date reportingStartingTime) {
		this.reportingStartingTime = reportingStartingTime;
	}

	public Date getReportingStartingTime() {
		return reportingStartingTime;
	}

	@Column(name = Columns.REPORTING_ENDING_TIME)
	public void setReportingEndingTime(Date reportingEndingTime) {
		this.reportingEndingTime = reportingEndingTime;
	}

	public Date getReportingEndingTime() {
		return reportingEndingTime;
	}

	@Column(name = Columns.DELAYED_REPORTING_TOLERENCE)
	public Integer getDelayedReportingTolerence() {
		return delayedReportingTolerence;
	}

	public void setDelayedReportingTolerence(Integer delayedReportingTolerence) {
		this.delayedReportingTolerence = delayedReportingTolerence;
	}

	@Column(name = Columns.NO_GEOLOCATION_TOLERENCE)
	public Integer getNoGeolocationTolerence() {
		return noGeolocationTolerence;
	}

	public void setNoGeolocationTolerence(Integer noGeolocationTolerence) {
		this.noGeolocationTolerence = noGeolocationTolerence;
	}

	@Column(name = Columns.HOST)
	public String getHost() {
		return host;
	}

	public void setHost(String host) {
		this.host = host;
	}

	@Column(name = Columns.PORT)
	public Integer getPort() {
		return port;
	}

	public void setPort(Integer port) {
		this.port = port;
	}

	@Column(name = Columns.EMAIL)
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = Columns.EMAIL_PASSWORD)
	public String getEmailPassword() {
		return emailPassword;
	}

	public void setEmailPassword(String emailPassword) {
		this.emailPassword = emailPassword;
	}

	public Integer getDatabaseVersion() {
		return databaseVersion;
	}

	public void setDatabaseVersion(Integer databaseVersion) {
		this.databaseVersion = databaseVersion;
	}

	
	@Column(name = BirdnotesConstants.Columns.DISPLACEMENT_CRON)
	public String getDisplacementCron(){
		return displacementCron;
	}
	
	public void setDisplacementCron(String displacementCron) {
		this.displacementCron = displacementCron;
	}
	
	@Column(name = BirdnotesConstants.Columns.ERP_TYPE)
	public String getErpType() {
		return erpType;
	}
	
	public void setErpType(String erpType) {
		this.erpType = erpType;
	}
	
	@Column(name = BirdnotesConstants.Columns.ERP_URL)
	public String getErpUrl() {
		return erpUrl;
	}
	
	public void setErpUrl(String erpUrl) {
		this.erpUrl = erpUrl;
	}
		
	@Column(name = BirdnotesConstants.Columns.ERP_SYNC_CRON)
	public String getErpSyncCron() {
		return erpSyncCron;
	}

	public void setErpSyncCron(String erpSyncCron) {
		this.erpSyncCron = erpSyncCron;
	}
	
	
	@Column(name = BirdnotesConstants.Columns.REPORT_VALIDATION_CRON)
	public String getReportValidationCron() {
		return reportValidationCron;
	}

	public void setReportValidationCron(String reportValidationCron) {
		this.reportValidationCron = reportValidationCron;
	}
	
	@Column(name = BirdnotesConstants.Columns.REPORTING_REMINDER_CRON)
	public String getReportingReminderCron() {
		return reportingReminderCron;
	}

	public void setReportingReminderCron(String reportingReminderCron) {
		this.reportingReminderCron = reportingReminderCron;
	}
	
	@Column(name = BirdnotesConstants.Columns.EXPENSE_DISTANCE_CRON)
	public String getExpenseDistanceCron() {
		return expenseDistanceCron;
	}

	public void setExpenseDistanceCron(String expenseDistanceCron) {
		this.expenseDistanceCron = expenseDistanceCron;
	}

	public String getBackendUrl() {
		return backendUrl;
	}

	public void setBackendUrl(String backendUrl) {
		this.backendUrl = backendUrl;
	}

	public Double getDefaultLatitude() {
		return defaultLatitude;
	}

	public void setDefaultLatitude(Double defaultLatitude) {
		this.defaultLatitude = defaultLatitude;
	}

	public Double getDefaultLongitude() {
		return defaultLongitude;
	}

	public void setDefaultLongitude(Double defaultLongitude) {
		this.defaultLongitude = defaultLongitude;
	}

	public String getFieldParameters() {
		return fieldParameters;
	}

	public void setFieldParameters(String fieldParameters) {
		this.fieldParameters = fieldParameters;
	}

	public String getErpParams() {
		return erpParams;
	}

	public void setErpParams(String erpParams) {
		this.erpParams = erpParams;
	}
	

	
	
}
