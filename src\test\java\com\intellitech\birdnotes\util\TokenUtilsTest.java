package com.intellitech.birdnotes.util;

import org.junit.Test;
import org.springframework.security.core.userdetails.UserDetails;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;

public class TokenUtilsTest {

    @Test
    public void testCreateTokenForUser() {
        // Setup
        final UserDetails userDetails = null;

        // Run the test
//        final String result = TokenUtils.createTokenForUser(userDetails);

        // Verify the results
        //       assertEquals("result", result);
    }

    @Test
    public void testComputeSignature() {
        // Setup
        final UserDetails userDetails = null;

        // Run the test
//        final String result = TokenUtils.computeSignature(userDetails, 0L);

        // Verify the results
//        assertEquals("result", result);
    }

    @Test
    public void testGetUserNameFromToken() {
//        assertEquals("result", TokenUtils.getUserNameFromToken("authToken"));
    }

    @Test
    public void testValidateToken() {
        // Setup
        final UserDetails userDetails = null;

        // Run the test
//        final boolean result = TokenUtils.validateToken("authToken", userDetails);

        // Verify the results
        //       assertFalse(result);
    }
}
