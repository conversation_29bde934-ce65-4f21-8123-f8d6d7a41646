package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.LocationService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/geolocation")
public class GeolocationController {
	private static final Logger LOG = LoggerFactory.getLogger(GeolocationController.class);
	@Autowired
	private LocationService locationService;
	@Autowired
	UserService userService;
	@Autowired
	DelegateService delegateService;
	@Autowired
	private SpecialityService specialityService;
	
	@Autowired
	private CurrentUser currentUser;
	
	@RequestMapping(value = "/findallspecialities", method = RequestMethod.GET)
	public ResponseEntity<List<SpecialityDto>> findAllSpecialities() {
		try {
			if (userService.checkHasPermission("LOCALISATION_DELEGUE_VIEW")) {
				List<SpecialityDto> specialityDtos = specialityService.findAll();
				return new ResponseEntity<>(specialityDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all specialities", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findAllLocationByUserAndDate/{user_id}/{startDate}/{endDate}/{showOnlyVisistsPositions}", method = RequestMethod.GET)
	public ResponseEntity<List<LocationDto>> findAlluserLocation(@PathVariable("user_id") Long user_id,
			@PathVariable("startDate") Date startDate, @PathVariable("endDate") Date endDate, @PathVariable("showOnlyVisistsPositions") boolean showOnlyVisistsPositions ) {
		try {
			if (userService.checkHasPermission("LOCALISATION_DELEGUE_VIEW")) {
				List<LocationDto> locationDto = locationService.findLocation(user_id, startDate, endDate, showOnlyVisistsPositions);
				return new ResponseEntity<>(locationDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all locations", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "findAllLocationByUserDateAndProspect/{prospect_id}/{date}", method = RequestMethod.GET)
	public ResponseEntity<List<LocationDto>> findAllLocationByUserDateAndProspect(
			@PathVariable("prospect_id") Long prospect_id, @PathVariable("date") Date date) {
		try {
			if (userService.checkHasPermission("LOCALISATION_DELEGUE_VIEW")) {
				List<LocationDto> locationDto = locationService.findLocationByProspectAndDate(prospect_id, date);
				return new ResponseEntity<>(locationDto, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all locations", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {
			if (userService.checkHasPermission("LOCALISATION_DELEGUE_VIEW")) {
				BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();
				List<DelegateDto> userDtos = delegateService.findAllDelegates();
				return new ResponseEntity<>(userDtos, HttpStatus.OK);
			}

			else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

}
