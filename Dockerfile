# Stage 1: Compile and Build 
# Use birdnotes image as the base image (louzar/birdnotes)
# Try to use an image that have the same maven and java version as louzar/birdnotes image
#FROM maven:3.6.1-jdk-13 as build
FROM maven:3.8.4-openjdk-8 as build
# Set the working directory 
RUN mkdir /home/<USER>
WORKDIR /home/<USER>

# Add the source code to app
COPY ./ /home/<USER>/
# Generate the build of the application
RUN mvn clean install -Dmaven.test.skip=true -P devops 
# && mv birdnotes-web-back-1.0-SNAPSHOT.war ROOT.war
#RUN mv scritp.sh /usr/local/tomcat/script.sh
#RUN chmod 777 /usr/local/tomcat/script.sh
#RUN ./script.sh

# Stage 2: Serve war with tomcat server

# Use official nginx image as the base image
#FROM tomcat:alpine
FROM tomcat:9.0.20-jre8

# Set the environment variable for the timezone
ENV TZ=Africa/Tunis

# Install the necessary packages
RUN cp /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# clean webapps
RUN rm -rf /usr/local/tomcat/webapps/*
RUN mkdir /usr/local/tomcat/webapps/attachments
# Copy the build output to replace the default nginx contents.
COPY --from=build /home/<USER>/target/birdnotes-web-back-1.0-SNAPSHOT.war /usr/local/tomcat/webapps/ROOT.war
COPY scritp.sh run_loki.sh /usr/local/tomcat/
RUN chmod 777 /usr/local/tomcat/scritp.sh
RUN chmod 777 /usr/local/tomcat/run_loki.sh
RUN /usr/local/tomcat/scritp.sh

# Expose port 8080
EXPOSE 8080
CMD ["catalina.sh","run"]
