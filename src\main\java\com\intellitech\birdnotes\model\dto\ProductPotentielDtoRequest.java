package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class ProductPotentielDtoRequest implements Serializable {


    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Long prospectId;

	private List<Long> productIds;
    private List<String> potentialNames;

  	public List<String> getPotentialNames() {
		return potentialNames;
	}

	public void setPotentialNames(List<String> potentialNames) {
		this.potentialNames = potentialNames;
	}




	public List<Long> getProductIds() {
		return productIds;
	}


	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}

	public Long getProspectId() {
		return prospectId;
	}


	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}
	public ProductPotentielDtoRequest() {
		super();
	
	}

	
}
