package com.intellitech.birdnotes.model.dto;

import com.intellitech.birdnotes.enumeration.WorkType;

public class MinimizedUserDtoV1 extends MinimizedUserDto {

	private static final long serialVersionUID = 1L;

	
	private WorkType workType;

	public MinimizedUserDtoV1(Long id, String firstName, String lastName) {
		super(id, firstName, lastName);
	}

	public MinimizedUserDtoV1(Long id, String firstName, String lastName, WorkType workType) {
		super(id, firstName, lastName);
		this.workType = workType;
	}

	public WorkType getWorkType() {
		return workType;
	}

	public void setWorkType(WorkType workType) {
		this.workType = workType;
	}

}
