package com.intellitech.birdnotes.repository;



import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.NotificationRule;



public interface NotificationRuleRepository extends JpaRepository<NotificationRule, Integer> {
	

	@Query("SELECT n from NotificationRule n WHERE n.eventType =:name")
	List<NotificationRule> findNotificationByEvent(@Param("name") String eventType);
	void deleteByEventType(String eventType);

}
