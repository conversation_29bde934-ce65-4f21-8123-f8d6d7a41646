package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import com.intellitech.birdnotes.dao.DynamicQueries;
import com.intellitech.birdnotes.data.dto.SampleSupplyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Mission;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.SampleSupplyItem;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.dto.MissionDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyItemDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.MissionRepository;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.SampleSupplyItemRepository;
import com.intellitech.birdnotes.repository.SampleSupplyRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.SampleSupplyService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesUtils;

import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

@Service("sampleSupplyService")
@Transactional
public class SampleSupplyServiceImpl implements SampleSupplyService {
	@Value("${logoPath}")
	private String logoPath;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${missionReportPath}")
	private String missionReportPath;

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired
	private ProductRepository productRepository;

	@Autowired
	private UserRepository userRepository;
	@Autowired
	private ConfigurationRepository configurationRepository;
	@Autowired
	private SampleSupplyRepository sampleSupplyRepository;
	@Autowired
	private SampleSupplyItemRepository sampleSupplyItemRepository;

	private DynamicQueries dynamicQueries;

	private UserService userService;
	@Autowired
	private DelegateRepository delegateRepository;

	@Autowired
	private MissionRepository missionRepository;
	@Autowired
	private SectorRepository sectorRepository;
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");

	public void setDelegateRepository(DelegateRepository delegateRepository) {
		this.delegateRepository = delegateRepository;
	}

	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;

	}

	@Autowired
	public void setDynamicQueries(DynamicQueries dynamicQueries) {
		this.dynamicQueries = dynamicQueries;
	}

	@Override
	public SampleSupply saveSampleSupply(SampleSupplyDto sampleSupplyDto) throws BirdnotesException {
		SampleSupply sampleSupply = null;
		if (sampleSupplyDto.getId() != null) {
			sampleSupply = sampleSupplyRepository.findOne(sampleSupplyDto.getId());
		}
		if (sampleSupply == null) {
			sampleSupply = new SampleSupply();
		}

		if (sampleSupplyDto.getDelegateId() != null) {
			Delegate delegate = delegateRepository.findById(sampleSupplyDto.getDelegateId());
			sampleSupply.setDelegate(delegate);
		}
		if (sampleSupplyDto.getDeliveryDate() != null) {
			sampleSupply.setDeliveryDate(sampleSupplyDto.getDeliveryDate());
		}

		sampleSupplyRepository.save(sampleSupply);

		List<SampleSupplyItem> sampleSupplyItemList = new ArrayList<>();
		for (SampleSupplyItemDto item : sampleSupplyDto.getSampleSupplyItems()) {
			SampleSupplyItem sampleSupplyItem = null;
			if (item.getId() != null) {
				sampleSupplyItem = sampleSupplyItemRepository.findOne(item.getId());
			}
			if (sampleSupplyItem == null) {
				sampleSupplyItem = new SampleSupplyItem();
			}

			sampleSupplyItem.setQuantity(item.getQuantity());
			Product product = productRepository.findByName(item.getProductName());

			sampleSupplyItem.setProduct(product);
			sampleSupplyItem.setBatchNumber(item.getBatchNumber());
			sampleSupplyItem.setDateManufacture(item.getManufactureDate());
			sampleSupplyItem.setExpirationDate(item.getExpirationDate());
			sampleSupplyItem.setSampleSupply(sampleSupply);
			sampleSupplyItemList.add(sampleSupplyItem);
			product.setSamplesSupplyItem(sampleSupplyItem);

			sampleSupplyItemRepository.save(sampleSupplyItem);

		}

		sampleSupply.setSampleSupplyItems(sampleSupplyItemList);
		return sampleSupplyRepository.save(sampleSupply);
	}

	public List<SampleSupplyDto> getSampleByUserProductAndDate(SampleRequestDto sampleRequestDto)
			throws BirdnotesException {

		StringBuilder query = new StringBuilder();
		List<SampleSupplyDto> sampleSupplyDtoList = new ArrayList<>();
		List<SampleSupply> sampleSupplyList = null;
		Map<String, Object> parameters = new HashMap<>();
		query.append("SELECT distinct ss "
				+ " from SampleSupply ss join ss.sampleSupplyItems ssi");

		query.append(" where (date(ss.deliveryDate) between date(:firstDate) and date(:lastDate))");
		parameters.put(BirdnotesConstants.QueryBuild.FIRST_DATE, sampleRequestDto.getFirstDate());
		parameters.put(BirdnotesConstants.QueryBuild.LAST_DATE, sampleRequestDto.getLastDate());

		if (sampleRequestDto.getSelectedUser() != null && sampleRequestDto.getSelectedUser() != 0) {
			query.append(" AND ss.delegate.id=:userId  ");
			parameters.put("userId", sampleRequestDto.getSelectedUser());
		}
		if (sampleRequestDto.getSelectedProduct() != null && sampleRequestDto.getSelectedProduct() != 0) {
			query.append(" AND ssi.product.id=:productId  ");
			parameters.put("productId", sampleRequestDto.getSelectedProduct());
		}
		List<Long> subUsersIds = userService.getSubUsersIds();
		query.append(" AND ss.delegate.id IN (:subUsersIds) ");
		parameters.put("subUsersIds", subUsersIds);

		//query.append(" order by ss.delegate.id, ssi.product.id, ss.deliveryDate ");
		sampleSupplyList = dynamicQueries.findSample(query.toString(), parameters);
		Collections.sort(sampleSupplyList, new Comparator<SampleSupply>() {
			public int compare(SampleSupply s1, SampleSupply s2) {
				return s1.getDelegate().getFirstName().compareTo(s2.getDelegate().getFirstName());
			}
		});
		for (SampleSupply sampleSupply : sampleSupplyList) {
			sampleSupplyDtoList.add(new SampleSupplyDto(sampleSupply));
		}
		
		return sampleSupplyDtoList;
	}

	@Override
	public List<SampleSupplyDto> findSampleSupplyByUserAndDateAndProduct(Date firstDate, Date lastDate, Long userId,
			Long productId) throws BirdnotesException {
		List<SampleSupplyDto> result = new ArrayList<>();
		List<SampleSupply> samplesSupply = sampleSupplyRepository.findSamplesSupplyByUserDateAndProduct(firstDate,
				lastDate, userId, productId);
		for (SampleSupply sampleSupply : samplesSupply) {
			SampleSupplyDto sampleSupplyDto = new SampleSupplyDto();
			sampleSupplyDto.setDelegateName(
					sampleSupply.getDelegate().getFirstName() + " " + sampleSupply.getDelegate().getLastName());
			sampleSupplyDto.setDeliveryDate(sampleSupply.getDeliveryDate());

			sampleSupplyDto.setId(sampleSupply.getId());
			result.add(sampleSupplyDto);

		}
		return result;
	}

	@Override
	public void deleteSampleSupply(Long sampleSupplyId) {
		SampleSupply sampleSupply = sampleSupplyRepository.findOne(sampleSupplyId);

		if (sampleSupply != null) {
			sampleSupplyItemRepository.deleteBySampleSupplyId(sampleSupplyId);
			sampleSupplyRepository.deleteById(sampleSupplyId);
		}
	}

	@Override
	public SampleSupplyFormData getAllDataSampleSupplyForm() throws IOException, BirdnotesException {
		List<UserDto> delegateDto = userService.getSubUsers();
		List<SampleSupplyItemDto> simpleSupplyItemDto = getSampleSupplyItemsWithAllProduct();
		SampleSupplyFormData sampleSupplyFormData = new SampleSupplyFormData();
		sampleSupplyFormData.setDelegates(delegateDto);
		sampleSupplyFormData.setSampleSupplyItems(simpleSupplyItemDto);

		return sampleSupplyFormData;
	}

	@Override
	public List<SampleSupplyItemDto> getSampleSupplyItemsWithAllProduct() {
		List<SampleSupplyItemDto> sampleSupplyItemDtoList = new ArrayList<>();
		// List<SampleSupplyItem> sampleSupplyItems = new ArrayList<>();
		List<String> ListproductNames = productRepository.findAllProductName();
		for (String productName : ListproductNames) {
			SampleSupplyItemDto sampleSupplyItemDto = new SampleSupplyItemDto();
			sampleSupplyItemDto.setQuantity(null);
			sampleSupplyItemDto.setProductName(productName);
			sampleSupplyItemDto.setExpirationDate(null);
			sampleSupplyItemDto.setManufactureDate(null);
			sampleSupplyItemDto.setBatchNumber(null);

			sampleSupplyItemDtoList.add(sampleSupplyItemDto);
		}

		return sampleSupplyItemDtoList;
	}

	@Override
	public List<SampleSupplyItemDto> getItemsBySampleSupplyId(Long sampleSupplyId) {
		List<SampleSupplyItemDto> allProductSampleSupplyItem = getSampleSupplyItemsWithAllProduct();
		List<SampleSupplyItem> sampleSupplyItems = sampleSupplyRepository.findBySampleSupplyId(sampleSupplyId);
		for (SampleSupplyItem sampleSupplyItem : sampleSupplyItems) {
			for (SampleSupplyItemDto item : allProductSampleSupplyItem) {
				if (sampleSupplyItem.getProduct().getName().equals(item.getProductName())) {
					item.setBatchNumber(sampleSupplyItem.getBatchNumber());
					item.setExpirationDate(sampleSupplyItem.getExpirationDate());
					item.setId(sampleSupplyItem.getId());
					item.setManufactureDate(sampleSupplyItem.getDateManufacture());
					item.setQuantity(sampleSupplyItem.getQuantity());
				}
			}
		}
		return allProductSampleSupplyItem;
	}

	@Override
	public Mission saveMission(MissionDto missionDto) throws BirdnotesException {
		Mission mission = null;
		if (missionDto.getId() != null) {
			mission = missionRepository.findOne(missionDto.getId());
		}
		if (mission == null) {
			mission = new Mission();
		}

		if (missionDto.getEndDate() != null) {
			mission.setEndDate(missionDto.getEndDate());
		}
		SampleSupply sampleSupply = null;
		if (missionDto.getSampleSupplyId() != null) {
			sampleSupply = sampleSupplyRepository.findOne(missionDto.getSampleSupplyId());
			mission.setSampleSupply(sampleSupply);
		}

		if (missionDto.getSectorId() != null) {
			Sector sector = sectorRepository.findById(missionDto.getSectorId());
			mission.setSector(sector);
		}
		Mission savedMission = missionRepository.save(mission);
		sampleSupply.setMission(savedMission);
		sampleSupplyRepository.save(sampleSupply);
		return savedMission;

	}

	public List<Map<String, Object>> getMissionReportData(Date deliveryDate, Date endDate, String FirstName,
			String sectorName, String carRegistration) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		Configuration config = configurationRepository.findOne(1);

		Map<String, Object> item = new HashMap<String, Object>();
		String pathLogo = uploadPath + logoPath + File.separator + config.getLogo();
		item.put("name", config.getName());
		item.put("logoUrl", pathLogo);
		item.put("date", format.format(new Date()));

		item.put("carRegistration", carRegistration);
		item.put("deliveryDate", format.format(deliveryDate));
		item.put("delegateName", FirstName);
		item.put("sectorName", sectorName);
		item.put("endDate", format.format(endDate));

		result.add(item);
		return result;
	}

	@Override
	public void generateMissionReport(String fileName, Mission mission)
			throws FileNotFoundException, JRException, BirdnotesException {
		SampleSupply sampleSupply = mission.getSampleSupply();
		Date deliveryDate = sampleSupply.getDeliveryDate();
		String sectorName = mission.getSector().getName();
		String carRegistration = sampleSupply.getDelegate().getCarRegistration();
		Date endDate = mission.getEndDate();
		String delegateFullName = sampleSupply.getDelegate().getFirstName() + " "
				+ sampleSupply.getDelegate().getLastName();

		// getJasperReport
		File template = ResourceUtils.getFile("classpath:jasper/mission_report.jrxml");
		JasperReport jasperReport = JasperCompileManager.compileReport(template.getAbsolutePath());

		// get data source
		Collection<SampleSupplyItemDto> ExpenseReportItem = findBySampleSupplyId(sampleSupply);
		JRBeanCollectionDataSource jdParameters = new JRBeanCollectionDataSource(ExpenseReportItem);

		// set parameters
		Map<String, Object> parameters = new HashMap<>();
		parameters.put("ExpenseReportItem", jdParameters);
		List<Map<String, Object>> dataSource = getMissionReportData(deliveryDate, endDate, delegateFullName, sectorName,
				carRegistration);

		JRBeanCollectionDataSource jdDataSource = new JRBeanCollectionDataSource(dataSource);
		log.info("Generating jasper file for mission report...");

		JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, jdDataSource);
		JasperExportManager.exportReportToPdfFile(jasperPrint, fileName);

		log.info("Terminating generation of jasper file for mission report");
	}

	private Collection<SampleSupplyItemDto> findBySampleSupplyId(SampleSupply sampleSupply) {
		List<SampleSupplyItemDto> sampleSupplyItemDtos = new ArrayList<>();
		for (SampleSupplyItem item : sampleSupply.getSampleSupplyItems()) {
			SampleSupplyItemDto sampleSupplyItemDto = new SampleSupplyItemDto();
			sampleSupplyItemDto.setQuantity(item.getQuantity());
			sampleSupplyItemDto.setBatchNumber(item.getBatchNumber());
			sampleSupplyItemDto.setProductName(item.getProduct().getName());
			sampleSupplyItemDto.setManufactureDate(item.getDateManufacture());
			sampleSupplyItemDto.setExpirationDate(item.getExpirationDate());
			sampleSupplyItemDtos.add(sampleSupplyItemDto);
		}
		return sampleSupplyItemDtos;
	}

	@Override
	public String generateMissionPDF(Mission mission) throws BirdnotesException {
		String fileName = BirdnotesUtils.createFolder(mission.getId(), missionReportPath, uploadPath);
		fileName = fileName + "/generatedMissionReport.pdf";
		try {
			generateMissionReport(fileName, mission);
		} catch (FileNotFoundException | JRException | BirdnotesException e) {
			log.error("Error in generation mission report as PDF ", e);
		}
		return fileName;
	}
}
