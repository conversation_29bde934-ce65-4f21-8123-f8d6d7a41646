package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.enumeration.WholesalerStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;
import com.intellitech.birdnotes.model.dto.WholesalerDto;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.WholesalerRepository;
import com.intellitech.birdnotes.service.WholesalerService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;



@Service("wholesalerService")
@Transactional
public class WholesalerServiceImpl implements WholesalerService  {
		
	private WholesalerRepository wholesalerRepository;
	private SectorRepository sectorRepository;
	private WholesalerToDtoConvertor wholesalerToDtoConvertor;
	private ProspectRepository prospectRepository;
	
	@Autowired
	WholesalerServiceImpl(WholesalerRepository wholesalerRepository, 
			SectorRepository sectorRepository, LocalityRepository localityRepository,
			WholesalerToDtoConvertor wholesalerToDtoConvertor, ProspectRepository prospectRepository) {
		super();
		this.wholesalerRepository = wholesalerRepository;
		this.sectorRepository = sectorRepository;
		this.wholesalerToDtoConvertor = wholesalerToDtoConvertor;
		this.prospectRepository = prospectRepository;
	}
	
	@Autowired
	 void setWholesalerRepository(WholesalerRepository wholesalerRepository) {
			this.wholesalerRepository = wholesalerRepository;
		}
	@Autowired
	 void setSectorRepository(SectorRepository sectorRepository) {
			this.sectorRepository = sectorRepository;
		}
	
	/*@Override
	@Transactional(readOnly = true)
	public List<WholesalerDto> getAllWholesalers() throws BirdnotesException {
		List<Wholesaler> wholesalers = wholesalerRepository.findAll();
		List<WholesalerDto> wholesalerDtos = new ArrayList<>();

		if (wholesalers != null && !wholesalers.isEmpty()) {
			for (Wholesaler wholesaler : wholesalers) {
				WholesalerDto wholesalerDto = wholesalerToDtoConvertor.convert(wholesaler);
				wholesalerDtos.add(wholesalerDto);
			}
		}

		return wholesalerDtos;
		
	}*/
	
	@Override
	@Transactional(readOnly = true)
	public List<WholesalerDto> getWholesalersByStatus() throws BirdnotesException {
		List<Wholesaler> wholesalers = wholesalerRepository.findByStatus();
		List<WholesalerDto> wholesalerDtos = new ArrayList<>();

		if (wholesalers != null && !wholesalers.isEmpty()) {
			for (Wholesaler wholesaler : wholesalers) {
				WholesalerDto wholesalerDto = wholesalerToDtoConvertor.convert(wholesaler);
				wholesalerDtos.add(wholesalerDto);
			}
		}

		return wholesalerDtos;
		
	}
	
	@Override
	public Wholesaler saveWholesaler(WholesalerDto wholesalerRequest) throws BirdnotesException{
		if (wholesalerRequest == null) {
			throw new BirdnotesException(Exceptions.NULL_WHOLESALER);
		}
		Wholesaler wholesaler = null;
		if(wholesalerRequest.getId() != null) {
			wholesaler = wholesalerRepository.findById(wholesalerRequest.getId());
		} 
		if(wholesaler == null) {
			wholesaler = new Wholesaler();
		}
		wholesaler.setName(wholesalerRequest.getName());
		wholesaler.setResponsible(wholesalerRequest.getResponsible());
		wholesaler.setPhone(wholesalerRequest.getPhone());
		wholesaler.setAddress(wholesalerRequest.getAddress());
		wholesaler.setDescription(wholesalerRequest.getDescription());
		wholesaler.setEmail(wholesalerRequest.getEmail());
		wholesaler.setDiscount(wholesalerRequest.getDiscount());
		wholesaler.setStatus(WholesalerStatus.valueOf(wholesalerRequest.getStatus()));
		Sector sector = sectorRepository.findOne(wholesalerRequest.getSectorId());
		if(sector!=null) {
			wholesaler.setSector(sector);
		}
		
		
		//wholesaler.setSaleEmail(wholesalerRequest.getSaleEmail());
		return wholesalerRepository.save(wholesaler);
	}

	/*
	 * public List<Wholesaler> saveAll(List<WholesalerDto> WholesalerRequests)
	 * throws BirdnotesException{
	 * 
	 * }
	 */


	
	@Override
	public void deleteWholesaler(Long wholesalerId) throws BirdnotesException {
		/*Long countWholesalerPerVisitProduct = visitsProductsRepository.countWholesalerPerVisitProduct(wholesalerId);
		if (countWholesalerPerVisitProduct > 0) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.CANT_DELETE_WHOLESALER);
		}*/
		wholesalerRepository.delete(wholesalerId);
	}

	@Override
	public Wholesaler updateWholesaler(WholesalerDto wholesalerDto) throws BirdnotesException {

		if (wholesalerDto == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NULL_DTO_WHOLESALER);
		}

		Wholesaler wholesalerToUpdate = wholesalerRepository.findOne(wholesalerDto.getId());

		if (wholesalerToUpdate == null) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.NO_WHOLESALER_WITH_ID + wholesalerDto.getId());
		}

		wholesalerToUpdate.setName(wholesalerDto.getName());
		wholesalerToUpdate.setResponsible(wholesalerDto.getResponsible());
		wholesalerToUpdate.setPhone(wholesalerDto.getPhone());
		wholesalerToUpdate.setAddress(wholesalerDto.getAddress());
		wholesalerToUpdate.setDescription(wholesalerDto.getDescription());
		wholesalerToUpdate.setEmail(wholesalerDto.getEmail());
		wholesalerToUpdate.setDiscount(wholesalerDto.getDiscount());
		wholesalerToUpdate.setStatus(WholesalerStatus.valueOf(wholesalerDto.getStatus()));
		if(wholesalerDto.getProspectId() != null) {
			Prospect prospect = prospectRepository.findOne(wholesalerDto.getProspectId());
			if(prospect != null) {
				wholesalerToUpdate.setProspect(prospect);
			}
		}
				
		Sector sector = sectorRepository.findOne(wholesalerDto.getSectorId());
		if(sector!=null) {
			wholesalerToUpdate.setSector(sector);
		}
		
		//wholesalerToUpdate.setSaleEmail(wholesalerDto.getSaleEmail());

		return wholesalerRepository.save(wholesalerToUpdate);
	}

	@Override
	public List<WholesalerDto> getWholesalersByUser(Long userId) throws BirdnotesException {
		List<Wholesaler> wholesalers = wholesalerRepository.getWholesalersByUser(userId);
		List<WholesalerDto> wholesalerDtos = new ArrayList<>();

		if (wholesalers != null && !wholesalers.isEmpty()) {
			for (Wholesaler wholesaler : wholesalers) {
				WholesalerDto wholesalerDto = wholesalerToDtoConvertor.convert(wholesaler);
				wholesalerDtos.add(wholesalerDto);
			}
		}

		return wholesalerDtos;
	}
	
	
	/*
	 * private boolean checkWholesalerNameIsUnique(String wholesalerName, Long
	 * wholesalerId) { Wholesaler wholesaler =
	 * wholesalerRepository.findByNameExcludeId(wholesalerName, wholesalerId);
	 * 
	 * return wholesaler != null; }
	 * 
	 * @Override public boolean checkWholesalerNameIsUnique(String wholesalerName) {
	 * Wholesaler wholesaler = wholesalerRepository.findByName(wholesalerName);
	 * 
	 * return wholesaler != null; }
	 */

}
