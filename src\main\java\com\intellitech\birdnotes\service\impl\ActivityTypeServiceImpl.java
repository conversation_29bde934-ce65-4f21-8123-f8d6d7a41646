package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.convertor.ConvertActivityTypeToDto;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.repository.ActivityTypeRepository;
import com.intellitech.birdnotes.service.ActivityTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;


@Service("activityTypeService")
@Transactional
public class ActivityTypeServiceImpl implements ActivityTypeService {

	private ActivityTypeRepository activityTypeRepository;

	private ConvertActivityTypeToDto convertActivityTypeToDto;

	@Autowired
	public  ActivityTypeServiceImpl(ActivityTypeRepository activityTypeRepository,
			ConvertActivityTypeToDto convertActivityTypeToDto) {
		this.activityTypeRepository = activityTypeRepository;
		this.convertActivityTypeToDto = convertActivityTypeToDto;
		
		}
	@Autowired
	UserService userService;
	

	@Override
	public List<ActivityTypeDto> findAll() throws BirdnotesException {
		List<ActivityTypeDto> back = new ArrayList<>();
		List<ActivityType> allActivityTypes = activityTypeRepository.findAll();
		for (ActivityType activityTypes : allActivityTypes) {
			back.add(convertActivityTypeToDto.convert(activityTypes));
		}
		return back;
	}

	public ActivityType add(ActivityType activityType) throws BirdnotesException {
		
		return activityTypeRepository.save(activityType);
	}
	
	@Override
	public void delete(Long id) throws BirdnotesException {

			activityTypeRepository.delete(id);



	}
	
	
	@Override
	public ActivityType saveActivityType(ActivityTypeDto activityTypeDto) throws BirdnotesException {
	    ActivityType existingActivityType = activityTypeRepository.findByNameAndAnotherId(activityTypeDto.getName(), activityTypeDto.getId());
	    if (existingActivityType != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    ActivityType activityType = null;
	    if (activityTypeDto.getId() != null) {
	        activityType = activityTypeRepository.findOne(activityTypeDto.getId());
	    }
	    if (activityType == null) {
	        activityType = new ActivityType();
	    }

	    activityType.setName(activityTypeDto.getName());
	    return activityTypeRepository.save(activityType);
	}






}
