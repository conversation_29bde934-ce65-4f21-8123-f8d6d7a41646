package com.intellitech.birdnotes.service;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.data.dto.SampleSupplyFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Mission;
import com.intellitech.birdnotes.model.SampleSupply;
import com.intellitech.birdnotes.model.dto.MissionDto;
import com.intellitech.birdnotes.model.dto.SampleRequestDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyDto;
import com.intellitech.birdnotes.model.dto.SampleSupplyItemDto;

import net.sf.jasperreports.engine.JRException;

public interface SampleSupplyService {
	SampleSupply saveSampleSupply(SampleSupplyDto sampleSupplyDto) throws BirdnotesException;

	Mission saveMission(MissionDto missionDto) throws BirdnotesException;

	List<SampleSupplyDto> getSampleByUserProductAndDate(SampleRequestDto sampleRequestDto) throws BirdnotesException;

	SampleSupplyFormData getAllDataSampleSupplyForm() throws IOException, BirdnotesException;

	List<SampleSupplyDto> findSampleSupplyByUserAndDateAndProduct(Date firstDate, Date lastDate, Long userId,
			Long productId) throws BirdnotesException;

	void deleteSampleSupply(Long sampleSupplyId);

	List<SampleSupplyItemDto> getItemsBySampleSupplyId(Long sampleSupplyId);

	List<SampleSupplyItemDto> getSampleSupplyItemsWithAllProduct();

	void generateMissionReport(String fileName, Mission mission)
			throws FileNotFoundException, JRException, BirdnotesException;

	String generateMissionPDF(Mission mission) throws BirdnotesException;

}
