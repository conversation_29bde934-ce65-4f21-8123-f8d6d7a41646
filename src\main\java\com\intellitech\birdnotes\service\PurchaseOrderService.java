package com.intellitech.birdnotes.service;

import java.io.FileNotFoundException;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;

import net.sf.jasperreports.engine.JRException;


public interface PurchaseOrderService {
	public List<PurchaseOrderDto> getAllByUser(Long userId) throws BirdnotesException;

	
	void sendPurchaseOrderMail(PurchaseOrderDto PurchaseOrderDto) throws BirdnotesException;

	void generatePurchaseOrder(String destFileName, Long purchaseOrderId, String type)
			throws FileNotFoundException, JRException;


}

