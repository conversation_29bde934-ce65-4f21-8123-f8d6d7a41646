package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.PotentielProduit;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.LabelValueDto;

@Repository
public interface ProspectRepository extends PagingAndSortingRepository<Prospect, Long> {

	@Query(value = " select  p.id as prospectId, prd.id as productId , pt.weight as potencial ,l.name as locality, sp.name as speciality, prd.name as product,\r\n"
			+ "sum (vp.order_quantity) as cumul_cmd,sum (vp.sample_quantity) as cumul_ech, count(v.id) as cumul_visit,\r\n"
			+ "min (v.visit_date) as fistVisitDate,\r\n" + "max (v.visit_date  ) as lastVisitDate\r\n" + "from \r\n"
			+ "charge_plan cp\r\n" + "inner join product prd on (cp.product_id = prd.id )\r\n"
			+ "inner join prospect p on (cp.speciality_id = p.speciality_id)\r\n"
			+ "inner join user_prospects up on (p.id = up.prospect_id)\r\n"
			+ "inner join potential pt on (pt.id = p.potential_id)\r\n"
			+ "inner join speciality sp   on (sp.id = p.speciality_id)\r\n"
			+ "inner join locality l on (p.locality_id = l.id)\r\n"
			+ "FULL OUTER JOIN visit v on ( p.id = v.prospect_id)\r\n"
			+ "FULL OUTER JOIN visits_products vp on ( vp.visit_id = v.id )\r\n"
			+ "group by p.id, prd.id , pt.weight, l.name, sp.name, prd.name", nativeQuery = true)

	List<Object[]> getProspectsForPrediction();

	@Query("SELECT p FROM Prospect p WHERE p.firstName =:firstName AND p.lastName=:lastName")
	Prospect findByFirstNameAndLastName(@Param("firstName") String firstName, @Param("lastName") String lastName);

	@Query("SELECT p FROM Prospect p WHERE p.user.id = :userId")
	List<Prospect> findByUserCreatorId(@Param("userId") Long userId);

	Prospect findById(Long id);

	@Query("SELECT p.potentielProduits from Prospect p WHERE p.id=:id")
	List<PotentielProduit> findPotentialProductById(@Param("id") Long id);

	@Query("SELECT p from Prospect p WHERE p.sector.id=:sectorId order by p.firstName")
	List<Prospect> findBySectorId(@Param("sectorId") Long sectorId);

	@Query("SELECT p from Prospect p WHERE p.locality.id=:id order by p.firstName")
	List<Prospect> findByLocalityId(@Param("id") Long id);

	List<Prospect> findByGsm(String gsm);

	List<Prospect> findByEmail(String email);

	List<Prospect> findByPhone(String phone);

	@Query("SELECT COUNT(pa.id) from ProspectsAffectation pa where pa.delegate.id=:userId")
	Long countProspectByDelegue(@Param("userId") Long userId);

	@Query("SELECT p from Prospect p where upper (p.firstName) like %:keywords%  OR upper(p.lastName) like %:keywords% AND p.status = 'VALID'  order by p.firstName , p.lastName ASC")
	List<Prospect> findAllWithPagination(@Param("keywords") String keywords, Pageable pageable);

	@Query("SELECT p.delegate from ProspectsAffectation p where p.prospect.id=:prospectId")
	List<Delegate> findProspectsAffectation(@Param("prospectId") Long prospectId);

	@Modifying
	@Query("DELETE Prospect p WHERE p.id = ?1")
	void deleteById(Long idProspect);

	@Modifying
	@Query("DELETE Prospect p WHERE p.id IN ( SELECT p.idprospect FROM Prospect p where p.id=:idS)")
	void OldLine(Long idProspect);

	@Query("SELECT p.id from Prospect p WHERE p.id in (:prospects) ")
	List<Long> findWhereIdIn(@Param("prospects") List<Long> prospects);

	@Query("SELECT pa.prospect from ProspectsAffectation pa  where pa.delegate.id=:id")
	List<Prospect> findByUser(@Param("id") Long id);

	@Query("SELECT p from Prospect p WHERE   p.status = 'UPDATE'")
	List<Prospect> findProspectsForUpdate();

	@Query("SELECT p from Prospect p WHERE p.id in (:prospectsIds)")
	Set<Prospect> findProspectsByIds(@Param("prospectsIds") List<Long> prospectsIds);

	@Query("SELECT p from Prospect p WHERE p.identifier in (:prospectsIdentifiers)")
	Set<Prospect> findProspectsByIdentifiers(@Param("prospectsIdentifiers") List<Long> prospectsIds);

	@Query("SELECT p from Prospect p where p.status ='NEW' OR p.status ='UPDATE'  order by p.id DESC")
	List<Prospect> findProspectWithStatus();

	@Query("SELECT p from Prospect p where (p.status ='NEW' OR p.status ='UPDATE') and p.user.id in (:userIds) order by p.id DESC")
	List<Prospect> findProspectWithStatus(@Param("userIds") List<Long> userIds);

	@Query("SELECT p FROM Prospect p where p.id=:prospectId")
	Prospect findProspectChanged(@Param("prospectId") Long prospectId);

	@Query("SELECT p FROM Prospect p WHERE p.identifier=:identifier AND status = 'VALID' order by p.firstName")
	Prospect findOldProspect(@Param("identifier") Long identifier);

	@Query("SELECT COUNT(p.id) from Prospect p where p.speciality.id=:specialtyId")
	Long countProspectPerSpeciality(@Param("specialtyId") Long specialtyId);

	@Query("SELECT COUNT(p.id) from Prospect p where p.potential=:potential")
	Long countProspectsPerPotential(@Param("potential") String potential);

	@Query("SELECT COUNT(p.id) from Prospect p where p.activity=:activity")
	Long countProspectsPerActivity(@Param("activity") String activity);

	@Query("SELECT COUNT(p.id) from Prospect p where p.sector.id=:sectorId")
	Long countProspectsPerSector(@Param("sectorId") Long sectorId);

	@Query("SELECT CONCAT(p.firstName,' ',p.lastName) from Prospect p where p.status= 'VALID' and p.id=:prospectId")
	String findPharmacistName(@Param("prospectId") Long i);

	@Query("SELECT  distinct v.prospect from VisitsProducts vp join vp.visit v where DATE(v.visitDate) >=  DATE(:stardDate) AND DATE(v.visitDate) <=  DATE(:endDate) and v.delegate.id=:userId and v.prospect.status <> 'DELETED'")
	List<Prospect> getVisitedProspect(@Param("userId") Long userId, @Param("stardDate") Date stardDate,
			@Param("endDate") Date endDate);

	@Query("SELECT pa.prospect from ProspectsAffectation pa where pa.delegate.id=:userId and pa.prospect.id NOT IN (select v.prospect.id from Visit v where v.delegate.id=:userId and DATE(v.visitDate) >=  DATE(:stardDate) AND DATE(v.visitDate) <=  DATE(:endDate)  and pa.prospect.status <> 'DELETED') ")
	List<Prospect> getNotVisitedProspect(@Param("userId") Long userId, @Param("stardDate") Date stardDate,
			@Param("endDate") Date endDate);

	@Query("SELECT p.id from Prospect p where p.user.id=:userId and status = 'NEW' OR status = 'MERGED'")
	List<Long> getWaitingForApprovalProspects(@Param("userId") Long userId);

	@Query("SELECT p from Prospect p where p.identifier in :identifiers and status = 'MERGED'")
	List<Prospect> getMergedProspects(@Param("identifiers") List<Long> identifiers);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(p) from Prospect p where id !=:prospectId AND p.status= 'VALID' AND (UPPER(CONCAT(p.firstName,' ',p.lastName)) like %:name% OR UPPER(CONCAT(p.lastName,' ',p.firstName)) like %:name% )  ORDER BY p.firstName ASC, p.lastName ASC")
	List<LabelValueDto> findByName(@Param("name") String name, @Param("prospectId") Long prospectId);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(p) from Prospect p where p.status = 'VALID' AND UPPER(CONCAT(p.firstName,' ',p.lastName)) like %:name%")
	List<LabelValueDto> findByName(@Param("name") String name);

	@Query("SELECT p from Prospect p where p.identifier = :identifier AND p.status = 'VALID'")
	Prospect findByIdentifier(@Param("identifier") Long identifier);

	@Query("SELECT p from Prospect p where p.identifier = :identifier AND (p.status =:newStatus  OR p.status =:validStatus)")
	Prospect findValidOrNewProspects(@Param("identifier") Long identifier,
			@Param("newStatus") UserValidationStatus newStatus, @Param("validStatus") UserValidationStatus validStatus);

	@Query("SELECT p from Prospect p where p.identifier = :identifier AND p.status <> 'UPDATE' and p.status <> 'ACCEPTED' and p.status <>'DELETED'")
	Prospect findByIdentifierAndStatus(@Param("identifier") Long identifier);

	@Query("SELECT p from Prospect p where p.identifier = :identifier AND status = 'REFUSED'")
	Prospect getRefusedProspectWaitingForValidation(@Param("identifier") Long identifier);

	Prospect findByIdprospectAndStatus(Long identifier, UserValidationStatus status);

	Prospect findByIdentifierAndStatus(Long identifier, UserValidationStatus status);

	@Query("SELECT p from Prospect p where p.identifier in (:identifiers)")
	List<Prospect> getByIdentifiers(@Param("identifiers") List<Long> identifiers);

	@Query("SELECT p.idprospect from Prospect p where p.idprospect in :identifiers AND status = 'UPDATE'")
	List<Long> getUpdatedProspectsWaitingForApproval(@Param("identifiers") List<Long> identifiers);

	@Query("SELECT p from Prospect p where p.user.id=:userId  AND (status = 'UPDATE')")
	List<Prospect> getNewAndUpdated(@Param("userId") Long userId);

	@Query("SELECT p from Prospect p WHERE p.firstName=:firstName AND p.lastName=:lastName AND p.id!=:id and p.status='VALID'")
	Prospect findByNameWithDiffId(@Param("firstName") String firstName, @Param("lastName") String lastName,
			@Param("id") Long id);

	List<Prospect> findAll();
	
	@Query("SELECT p from Prospect p where p.status = 'VALID' and p.speciality is null  order by p.firstName , p.lastName ASC ")
	List<Prospect> findAllPatient(Pageable pageable);


	@Query("SELECT p from Prospect p where p.appUser.id =:appUserId and p.wholesaler.prospect.id is not null)")
	Prospect findWholesalerByprospect(@Param("appUserId") Long appUserId);

	@Query("SELECT p.appUser from Prospect p join p.appUser u where u.id =:userId ")
	User findUserByProspectAppUserId(@Param("userId") Long userId);

	@Query("SELECT p from Prospect p join p.speciality s where MOD(s.action, 2) <> 0 and p.active = true and p.status ='VALID'")
	List<Prospect> getWholesalersByStatus();

	@Query("SELECT p from Prospect p where p.id in (:wholesalerIds)")
	List<Prospect> getWholesalersByIds(@Param("wholesalerIds") List<Long> wholesalerIds);
	
	/*
	 * @Query(value = "SELECT p from Prospect  Order by id LIMIT 10") List<Prospect>
	 * getPatientsLimited();
	 */
	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(p.id, p.firstName, p.lastName) from Prospect p ORDER BY p.firstName ASC, p.lastName ASC")
	List<LabelValueDto> getAllProspectIdAndName(Pageable pageable);
	
	@Modifying
	@Query("UPDATE Prospect p set p.sector.id = ?1 where p.locality.id = ?2")
	void updateProspectLocality(Long newSectorId, Long localityId);


}
