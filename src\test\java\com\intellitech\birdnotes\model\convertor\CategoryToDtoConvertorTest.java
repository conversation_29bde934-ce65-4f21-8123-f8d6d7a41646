package com.intellitech.birdnotes.model.convertor;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Category;
import com.intellitech.birdnotes.model.dto.ValueTypeDto;
import org.junit.Before;
import org.junit.Test;

public class CategoryToDtoConvertorTest {

    private CategoryToDtoConvertor categoryToDtoConvertorUnderTest;

    @Before
    public void setUp() throws Exception {
        categoryToDtoConvertorUnderTest = new CategoryToDtoConvertor();
    }

    @Test
    public void testConvert() throws Exception {
        // Setup
        final Category category = new Category(0, "name");

        // Run the test
        final ValueTypeDto result = categoryToDtoConvertorUnderTest.convert(category);

        // Verify the results
    }
}
