package com.intellitech.birdnotes.model;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

import com.intellitech.birdnotes.util.BirdnotesConstants;

@Converter(autoApply = true)
public class GenderConverter implements AttributeConverter<Gender, String>{

	@Override
	public String convertToDatabaseColumn(Gender gender) {
		
		if(gender.equals(Gender.MEN)) {
			return BirdnotesConstants.Gender.MEN;
		}
		return BirdnotesConstants.Gender.WOMEN;
	}

	@Override
	public Gender convertToEntityAttribute(String dbData) {
		if(dbData.equals(BirdnotesConstants.Gender.MEN)) {
			return Gender.MEN;
		}
		return Gender.WOMEN;
	}

}
