package com.intellitech.birdnotes.thread;

import java.util.List;

import org.springframework.mail.javamail.JavaMailSender;

import com.intellitech.birdnotes.util.BirdnotesUtils;

public class ThreadSendEmail implements Runnable {
	private String[] to;
	private String from;
	private String subject;
	private String htmlBody;
	private String pathAttachment;
	private String nameAttachment;
	private JavaMailSender javaMailSender;
	private List<String> CCs;
	
	public ThreadSendEmail() {
		super();
	}

	public ThreadSendEmail(JavaMailSender javaMailSender, String from, String[] to,List<String>  CCs, String subject, String htmlBody, String pathAttachment, String nameAttachment) {
		this.javaMailSender = javaMailSender;
		this.to = to;
		this.from = from;
		this.CCs = CCs;
		this.subject = subject;
		this.htmlBody = htmlBody;
		this.pathAttachment = pathAttachment;
		this.nameAttachment = nameAttachment;
	}
	
	public ThreadSendEmail(JavaMailSender javaMailSender, String[] to, String subject, String htmlBody) {
		this.javaMailSender = javaMailSender;
		this.to = to;
		this.subject = subject;
		this.htmlBody = htmlBody;
		
	}

	@Override
	public void run() {
		if(pathAttachment==null && nameAttachment==null && "".equals(pathAttachment) && "".equals(nameAttachment))
			BirdnotesUtils.sendEmail(javaMailSender, to, subject, htmlBody, from);
		else
			BirdnotesUtils.sendEmailWithAttachment(javaMailSender, from, to, CCs, subject, htmlBody, pathAttachment, nameAttachment, null, null, null, null);
	}

	public String getFrom() {
		return from;
	}

	public void setFrom(String from) {
		this.from = from;
	}

	public String getPathAttachment() {
		return pathAttachment;
	}

	public void setPathAttachment(String pathAttachment) {
		this.pathAttachment = pathAttachment;
	}

	public String getNameAttachment() {
		return nameAttachment;
	}

	public void setNameAttachment(String nameAttachment) {
		this.nameAttachment = nameAttachment;
	}

	public JavaMailSender getJavaMailSender() {
		return javaMailSender;
	}

	public void setJavaMailSender(JavaMailSender javaMailSender) {
		this.javaMailSender = javaMailSender;
	}


	public String[] getTo() {
		return to;
	}

	public void setTo(String[] to) {
		this.to = to;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getHtmlBody() {
		return htmlBody;
	}

	public void setHtmlBody(String htmlBody) {
		this.htmlBody = htmlBody;
	}

}
