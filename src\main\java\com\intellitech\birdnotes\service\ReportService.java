package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.data.dto.ProspectFilterDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.ProductDto;



public interface ReportService {

	List<LocalityDto> findLocalityBySectorId(Long sectorId)throws BirdnotesException ;
	
	List<ProspectDto> findProspectBySectorIdAndPotentialIdAndActivityId(ProspectFilterDto prospectFilterDto) throws BirdnotesException;

	List<ProspectDto> findAllProspect() throws BirdnotesException;
	
	Visit addVisit(Visit visit) throws BirdnotesException ;	
	
	VisitsProducts addVisitsProducts(VisitsProducts visitsProducts) throws BirdnotesException ;
	
	List<ProductDto> getProductsHistoryForProspect(Long prospectId,Date date) throws BirdnotesException;
	
	List<ProspectDto> getVisit(Date date) throws BirdnotesException;
	
	
}
