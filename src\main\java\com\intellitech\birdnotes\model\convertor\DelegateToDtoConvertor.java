package com.intellitech.birdnotes.model.convertor;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;

@Component("delegateToDtoConvertor")
public class DelegateToDtoConvertor implements Converter<Delegate, DelegateDto> {

	private static final Logger LOG = LoggerFactory.getLogger(DelegateToDtoConvertor.class);

	private ConvertGoalToDto convertGoalToDto;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${userPath}")
	private String userPath;

	private ConfigurationRepository configureRepository;

	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}

	@Autowired
	public void setConvertGoalToDto(ConvertGoalToDto convertGoalToDto) {
		this.convertGoalToDto = convertGoalToDto;

	}

	@Override
	public DelegateDto convert(Delegate delegate) {

		if (delegate == null) {
			LOG.error("delegate is null");
			return null;
		}

		DelegateDto delegateDto = new DelegateDto();
		delegateDto.setId(delegate.getId());
		delegateDto.setFirstName(delegate.getFirstName());
		delegateDto.setLastName(delegate.getLastName());
		delegateDto.setBirthdayDate(delegate.getBirthdayDate());
		delegateDto.setCin(delegate.getCin());	
		delegateDto.setWorkingDaysPerWeek(delegate.getWorkingDaysPerWeek());
		delegateDto.setMobileAppVersion(delegate.getMobileAppVersion());
		delegateDto.setOneSignalUserId(delegate.getOneSignalUserId());
		delegateDto.setLat(delegate.getLatitude());
		delegateDto.setLng(delegate.getLongitude());
		delegateDto.setCycle(delegate.getCycle());
		delegateDto.setEmail(delegate.getUser().getEmail());
		delegateDto.setPassword(delegate.getUser().getPassword());
		delegateDto.setUsername(delegate.getUser().getUsername());
		delegateDto.setPhone(delegate.getUser().getPhone());
		delegateDto.setActive(delegate.getUser().getActive());
		DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm");
		if (delegate.getCarType() != null) {
			delegateDto.setCarType(delegate.getCarType().toString());
		}
		if (delegate.getUser().getLastLogin() != null) {
			delegateDto.setLastLogin(df.format(delegate.getUser().getLastLogin()));
		}
		if (delegate.getLastSyncro() != null) {
			delegateDto.setLastSynco(df.format(delegate.getLastSyncro()));
		}
		putWorkingCondition(delegate, delegateDto);
		if (delegate.getHiringDate() != null) {
			delegateDto.setHiringDate(delegate.getHiringDate());
		}
		if (delegate.getUser().getRoles() != null) {
			delegateDto.setRoleIds(delegate.getUser().getRoles().stream().map(Role::getId).collect(Collectors.toList()));
			delegateDto.setRoles(delegate.getUser().getRoles());
		}
		if (delegate.getUser().getRanges() != null) {
			delegateDto.setRangeIds(delegate.getUser().getRanges().stream().map(Range::getId).collect(Collectors.toList()));
			delegateDto.setRanges(delegate.getUser().getRanges());
		}
		if(delegate.getUser().getSuperiors() != null) {
			delegateDto.setSuperiors(delegate.getUser().getSuperiors().stream().map(User::getId).collect(Collectors.toList()));
		}
		if (delegate.getNetwork() != null) {
			delegateDto.setNetwork(delegate.getNetwork());
		}
		if (delegate.getPhoto() != null) {
			Configuration config = configureRepository.findById(1);
			delegateDto.setPhoto(
					config.getBackendUrl() + uploadUrl + userPath + "/" + delegate.getId() + "/" + delegate.getPhoto());
		}
		if (delegate.getCarRegistration() != null) {
			delegateDto.setCarRegistration(delegate.getCarRegistration());
		}
		return delegateDto;
	}

	private void putWorkingCondition(Delegate delegate, DelegateDto delegateDto) {
		if (delegate.getContractType() != null) {

			delegateDto.setContractType(delegate.getContractType().getName());
		}
		if (delegate.getGender() != null) {
			delegateDto.setGender(delegate.getGender().getName());
		}

		if (delegate.getWorkType() != null) {
			delegateDto.setWorkType(delegate.getWorkType().getName());
		}
	}

}
