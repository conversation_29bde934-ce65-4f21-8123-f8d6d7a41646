package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;

import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import javax.persistence.InheritanceType;
import javax.persistence.Inheritance;


@Entity
@Table(name = BirdnotesConstants.Tables.USER, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class User implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String email;
	private String username;
	private String password;
	private String phone;
	private Set<Role> roles = new HashSet<>();
	private Date lastLogin;;
	public Set<User> superiors;
	private String oneSignalUserId;
	private Set<Goal> goals;
	private List<ValidationStep> validationSteps;
	private List<NotificationRule> notificationsManager;
	private List<ValidationStatus> validationsStatus;
	private ReportCron reportCron;
	private Set<Commission> commissions;
	private Delegate delegate;
	private Prospect prospect;
	private Set<Range> ranges = new HashSet<>();

	private Boolean active;
	
	public User() {
		super();
	}

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.USER_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.USER_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.USER_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = BirdnotesConstants.Columns.EMAIL, length = BirdnotesConstants.Numbers.N_120)
	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	@Column(name = BirdnotesConstants.Columns.USERNAME, length = BirdnotesConstants.Numbers.N_85)
	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	@Column(name = BirdnotesConstants.Columns.PASS, length = BirdnotesConstants.Numbers.N_64)
	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	@Column(name = BirdnotesConstants.Columns.PHONE, length = BirdnotesConstants.Numbers.N_14)
	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.USERS_ROLES, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.USER_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.ROLE_ID, nullable = false, updatable = false) })
	public Set<Role> getRoles() {
		return roles;
	}

	public void setRoles(Set<Role> roles) {
		this.roles = roles;
	}

	public Date getLastLogin() {
		return lastLogin;
	}

	public void setLastLogin(Date lastLogin) {
		this.lastLogin = lastLogin;
	}

	@Column(name = BirdnotesConstants.Columns.ONESIGNAL_USER_ID)
	public String getOneSignalUserId() {
		return oneSignalUserId;
	}

	public void setOneSignalUserId(String oneSignalUserId) {
		this.oneSignalUserId = oneSignalUserId;
	}

	@ManyToMany(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
	public Set<User> getSuperiors() {
	    return superiors;
	}

	public void setSuperiors(Set<User> superiors) {
		this.superiors = superiors;
	}

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.USER_GOALS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.USER_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.GOAL_ID, nullable = false, updatable = false) })
	public Set<Goal> getGoals() {
		return goals;
	}

	public void setGoals(Set<Goal> goals) {
		this.goals = goals;
	}
	@JsonIgnore
	@OneToMany(mappedBy = "user")
	public List<ValidationStep> getValidationSteps() {
		return validationSteps;
	}

	public void setValidationSteps(List<ValidationStep> validationSteps) {
		this.validationSteps = validationSteps;
	}

	@OneToMany(mappedBy = "user", fetch = FetchType.EAGER)
	@JsonIgnore
	public List<NotificationRule> getNotificationsManager() {
		return notificationsManager;
	}

	public void setNotificationsManager(List<NotificationRule> notificationsManager) {
		this.notificationsManager = notificationsManager;
	}

	@JsonIgnore
	@OneToMany(mappedBy = "user")
		public List<ValidationStatus> getValidationsStatus() {
		return validationsStatus;
	}

	public void setValidationsStatus(List<ValidationStatus> validationsStatus) {
		this.validationsStatus = validationsStatus;
	}
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.REPORT_CRON_ID)
	public ReportCron getReportCron() {
		return reportCron;
	}

	public void setReportCron(ReportCron reportCron) {
		this.reportCron = reportCron;
	}
	
	@ManyToMany(mappedBy = "users")
	public Set<Commission> getCommissions() {
		return commissions;
	}

	public void setCommissions(Set<Commission> commissions) {
		this.commissions = commissions;
	}
	
	@JsonIgnore
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "user", targetEntity = Delegate.class)
    public Delegate getDelegate() {
		return delegate;
	}

	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}
	
	@JsonIgnore
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "appUser", targetEntity = Prospect.class)
    public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}


	
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.USER_RANGE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.USER_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.RANGE_ID, nullable = false, updatable = false) })
	public Set<Range> getRanges() {
		return ranges;
	}

	public void setRanges(Set<Range> ranges) {
		this.ranges = ranges;
	}
	
	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}
}