package com.intellitech.birdnotes;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;

import javax.servlet.FilterChain;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AuthTokenFilterTest {

    @Mock
    private UserDetailsService mockBirdnotesUserDetailsService;

    private AuthTokenFilter authTokenFilterUnderTest;

    @Before
    public void setUp() throws Exception {
        authTokenFilterUnderTest = new AuthTokenFilter(mockBirdnotesUserDetailsService);
    }

    @Test
    public void testDoFilter() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final FilterChain filterChain = null;
        when(mockBirdnotesUserDetailsService.loadUserByUsername("username")).thenReturn(null);

        // Run the test
        authTokenFilterUnderTest.doFilter(request, response, filterChain);

        // Verify the results
    }

    @Test
    public void testDoFilter_UserDetailsServiceThrowsUsernameNotFoundException() {
        // Setup
        final MockHttpServletRequest request = new MockHttpServletRequest();
        final MockHttpServletResponse response = new MockHttpServletResponse();
        final FilterChain filterChain = null;
        when(mockBirdnotesUserDetailsService.loadUserByUsername("username")).thenThrow(UsernameNotFoundException.class);

        // Run the test
        authTokenFilterUnderTest.doFilter(request, response, filterChain);

        // Verify the results
    }
}
