package com.intellitech.birdnotes.model;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.util.List;

@XmlRootElement(name = "criteriaList")
@XmlAccessorType(XmlAccessType.FIELD)
public class CriteriaList {

    @XmlAttribute
    private String destination;

    @XmlElement(name = "criteria")
    private List<Criteria> criteria;

    public CriteriaList() {}

    public CriteriaList(String destination, List<Criteria> criteria) {
        this.destination = destination;
        this.criteria = criteria;
    }

    public String getDestination() {
        return destination;
    }

    public void setDestination(String destination) {
        this.destination = destination;
    }

    public List<Criteria> getCriteria() {
        return criteria;
    }

    public void setCriteria(List<Criteria> criteria) {
        this.criteria = criteria;
    }
}
