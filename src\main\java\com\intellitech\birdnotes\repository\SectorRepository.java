package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Sector;

@Repository
public interface SectorRepository extends JpaRepository<Sector, Long> {

	Sector findByName(String name);

	@Modifying
	@Query("DELETE from Sector WHERE id=:id")
	void deleteByID(@Param("id") Long id);

	Sector findById(Long sectorId);

	@Override
	@Query("SELECT s from Sector s ORDER BY s.name")
	List<Sector> findAll();

	@Query("SELECT pa.prospect.sector from ProspectsAffectation pa where pa.delegate.id = ?  ")
	List<Sector> findByUserId(Long id);

	Sector findFirstByNameIgnoreCase(String name);

	@Query("SELECT s FROM Sector s WHERE LOWER(s.name) = LOWER(?1) AND s.id != ?2")
	Sector findByNameAndAnotherId(String name, Long id);

	@Query("SELECT s.id from Sector s WHERE s.id in (:sectors) order by s.name")
	List<Long> findWhereIdIn(@Param("sectors") List<Long> sectors);

	@Query("SELECT s.id FROM Sector s")
	List<Long> getAllSectorsIds();

	@Query("SELECT s.name FROM Sector s")
	List<String> findAllSectorNames();

	// List<Sector> findByNote(Note note);
}