package com.intellitech.birdnotes.enumeration;

import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum ContractType {
	
	CDI(BirdnotesConstants.ContractType.CDI),
	CDD(BirdnotesConstants.ContractType.CDD),
	SIVP(BirdnotesConstants.ContractType.SIVP);
	
	private String name;
	
	ContractType(String name) {
		this.name = name;
	}
	
	public String getName() {
		return name;
	}
	
	@Override
	public String toString() {
		if(BirdnotesConstants.ContractType.CDI.equals(name)) {
			return BirdnotesConstants.ContractType.CDI;
		}
		if(BirdnotesConstants.ContractType.CDD.equals(name)) {
			return BirdnotesConstants.ContractType.CDD;
		}
		return BirdnotesConstants.ContractType.SIVP;
	}

}
