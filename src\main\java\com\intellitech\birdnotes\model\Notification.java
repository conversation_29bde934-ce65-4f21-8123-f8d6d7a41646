package com.intellitech.birdnotes.model;
import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.NOTIFICATION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Notification implements Serializable {
	
	private static final long serialVersionUID = 1L;
	@Id
	@SequenceGenerator(name = Sequences.NOTIFICATION_SEQUENCE, sequenceName = Sequences.NOTIFICATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.NOTIFICATION_SEQUENCE)
	
	@Column(name = Columns.ID)
	private Long id;
	
	@Lob
	@Column(name = Columns.TEXT)
	private String text;

	@Column(name = Columns.STATUS)
	private boolean status;

	@ManyToOne
	@JoinColumn(name = Columns.TARGET_USER_ID)
	private User targetUser;

	//@Temporal(TemporalType.DATE)
	@Column(name = Columns.NOTIFICATION_DATE)
	private Date date;
	
	public Notification() {
		
	}
	
	public Notification(Notification notification, User targetUser) {
		this.targetUser = targetUser;
		this.text = notification.getText();
		this.date = notification.getDate();
		this.status = notification.getStatus();
		
		
	}
	
	public Notification(String text, boolean status, User targetUser) {
		super();
		this.text = text;
		this.status = status;
		this.targetUser = targetUser;
		this.date = new Date();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getText() {
		return text;
	}

	public void setText(String text) {
		this.text = text;
	}

	public boolean getStatus() {
		return status;
	}

	public void setStatus(boolean status) {
		this.status = status;
	}

	public User getTarget_user_ID() {
		return targetUser;
	}

	public void setTarget_user_ID(User target_user_ID) {
		this.targetUser = target_user_ID;
	}

	public void setTargetUser(User targetUser) {
		this.targetUser = targetUser;
	}


	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public User getTargetUser() {
		return targetUser;
	}
	

}
