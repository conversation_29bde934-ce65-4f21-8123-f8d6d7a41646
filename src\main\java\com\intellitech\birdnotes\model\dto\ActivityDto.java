package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class ActivityDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;

	private Long identifier;

	private DelegateDto delegateDto;

	private Date date;

	private String stringDate;

	private int day;

	private int month;

	private int year;

	private ActivityTypeDto activityTypeDto;
	private Long activityTypeId;
	private Long typeActivity;

	private Long hourNumber;

	private String comment;

	private String userName;

	private String activityTypeName;
	private Long prospectId;
	private String prospectName;
	private Date endDate;
	private Long delegateId;
	private String planifiedActivityDate;
	private String planifiedActivityStatus;
	private Integer nbDaysPlanifiedVsExecuted;
	
	
	

	public ActivityDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public int getDay() {
		return day;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public void setDay(int day) {
		this.day = day;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public ActivityTypeDto getActivityTypeDto() {
		return activityTypeDto;
	}

	public void setActivityTypeDto(ActivityTypeDto activityTypeDto) {
		this.activityTypeDto = activityTypeDto;
	}

	public Long getHourNumber() {
		return hourNumber;
	}

	public void setHourNumber(Long hourNumber) {
		this.hourNumber = hourNumber;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	public DelegateDto getDelegateDto() {
		return delegateDto;
	}

	public void setDelegateDto(DelegateDto delegateDto) {
		this.delegateDto = delegateDto;
	}

	public Long getTypeActivity() {
		return typeActivity;
	}

	public void setTypeActivity(Long typeActivity) {
		this.typeActivity = typeActivity;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getActivityTypeName() {
		return activityTypeName;
	}

	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}

	public String getStringDate() {
		return stringDate;
	}

	public void setStringDate(String stringDate) {
		this.stringDate = stringDate;
	}

	public Long getProspectId() {
		return prospectId;
	}

	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Long getDelegateId() {
		return delegateId;
	}

	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}

	public Long getActivityTypeId() {
		return activityTypeId;
	}

	public void setActivityTypeId(Long activityTypeId) {
		this.activityTypeId = activityTypeId;
	}

	public String getProspectName() {
		return prospectName;
	}

	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}

	public String getPlanifiedActivityStatus() {
		return planifiedActivityStatus;
	}

	public void setPlanifiedActivityStatus(String planifiedActivityStatus) {
		this.planifiedActivityStatus = planifiedActivityStatus;
	}

	public String getPlanifiedActivityDate() {
		return planifiedActivityDate;
	}

	public void setPlanifiedActivityDate(String planifiedActivityDate) {
		this.planifiedActivityDate = planifiedActivityDate;
	}

	public Integer getNbDaysPlanifiedVsExecuted() {
		return nbDaysPlanifiedVsExecuted;
	}

	public void setNbDaysPlanifiedVsExecuted(Integer nbDaysPlanifiedVsExecuted) {
		this.nbDaysPlanifiedVsExecuted = nbDaysPlanifiedVsExecuted;
	}

	
	
}
