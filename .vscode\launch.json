{"version": "0.2.0", "configurations": [{"type": "java", "name": "Spring Boot-BirdnotesWebBackApplication<birdnotes-web-back>", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.intellitech.birdnotes.BirdnotesWebBackApplication", "projectName": "birdnotes-web-back", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": "-Dspring.profiles.active=dev"}, {"type": "java", "name": "Debug Spring Boot App", "request": "launch", "cwd": "${workspaceFolder}", "mainClass": "com.intellitech.birdnotes.BirdnotesWebBackApplication", "projectName": "birdnotes-web-back", "args": "", "envFile": "${workspaceFolder}/.env", "vmArgs": "-Dspring.profiles.active=dev -Xdebug -Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5005"}]}