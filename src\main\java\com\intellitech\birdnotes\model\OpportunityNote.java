package com.intellitech.birdnotes.model;

import java.util.Date;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.hibernate.annotations.Type;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.OPPORTUNITYNOTE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class OpportunityNote {
	@Id
	@SequenceGenerator(name = Sequences.OPPORTUNITYNOTE_SEQUENCE, sequenceName = Sequences.OPPORTUNITYNOTE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.OPPORTUNITYNOTE_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.NAME)
	private String name;

	@Column(name = Columns.BUDGET)
	private Float budget;

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.OPPORTUNITY_NOTE_DATE)
	private Date date;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.OPPORTUNITYNOTE_PROSPECTS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.IDPROSPECT, nullable = false, updatable = false) })
	private Set<Prospect> prospects;

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.OPPORTUNITYNOTE_PHARMACIES, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.IDPROSPECT, nullable = false, updatable = false) })
	private Set<Prospect> pharmacies;

	@ManyToMany(fetch = FetchType.EAGER)
	@JoinTable(name = BirdnotesConstants.Tables.OPPORTUNITYNOTE_PRODUCTS, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, nullable = false, updatable = false) })
	private Set<Product> products;

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	private UserValidationStatus status;
	
	@Column(name = BirdnotesConstants.Columns.DESCRIPTION_EXPENSE_REPORT, length = BirdnotesConstants.Numbers.N_255)
	private String description;
	
	
	@Column(name = BirdnotesConstants.Columns.ATTACHMENT_NAME, length = BirdnotesConstants.Numbers.N_255)
	private String pieceJointe;

	@Column(name = BirdnotesConstants.Columns.ATTACHEMENT_BASE64)
	@Type(type = "text")
	private String attachmentBase64;
	
	

	public OpportunityNote(Long id, String name, Float budget, Date date, User user, Set<Prospect> prospects,Set<Prospect> pharmacies,
			Set<Product> products,String attachmentBase64,String pieceJointe, UserValidationStatus status) {
		super();
		this.id = id;
		this.name = name;
		this.budget = budget;
		this.date = date;
		this.user = user;
		this.prospects = prospects;
		this.pharmacies=pharmacies;
		this.products = products;
		this.attachmentBase64 = attachmentBase64;
		this.pieceJointe= pieceJointe;
		this.status = status;
	}

	public String getPieceJointe() {
		return pieceJointe;
	}

	public void setPieceJointe(String pieceJointe) {
		this.pieceJointe = pieceJointe;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public Set<Prospect> getPharmacies() {
		return pharmacies;
	}

	public void setPharmacies(Set<Prospect> pharmacies) {
		this.pharmacies = pharmacies;
	}

	public OpportunityNote() {
		super();
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public Set<Product> getProducts() {
		return products;
	}

	public void setProducts(Set<Product> products) {
		this.products = products;
	}

	public Set<Prospect> getProspects() {
		return prospects;
	}

	public void setProspects(Set<Prospect> prospects) {
		this.prospects = prospects;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Float getBudget() {
		return budget;
	}

	public void setBudget(Float budget) {
		this.budget = budget;
	}
	
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
