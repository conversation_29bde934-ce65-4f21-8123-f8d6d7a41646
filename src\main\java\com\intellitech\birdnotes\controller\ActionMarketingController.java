package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.dto.ActionMarketingDto;
import com.intellitech.birdnotes.model.dto.ActionMarketingFormDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.MarketingActionOrderPredictionDto;
import com.intellitech.birdnotes.model.dto.MarketingActionTypeDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.model.request.ActionMarketingRequest;
import com.intellitech.birdnotes.repository.ProductRepository;
import com.intellitech.birdnotes.repository.ProspectRepository;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ActionMarketingService;
import com.intellitech.birdnotes.service.MarketingActionTypeService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController

@RequestMapping("/actionMarketing")

public class ActionMarketingController {

	private static final Logger LOG = LoggerFactory.getLogger(ActionMarketingController.class);

	@Autowired

	
	private ActionMarketingService actionMarketingService;
	
	@Autowired

	
	private MarketingActionTypeService marketingActionTypeService;

	@Autowired

	UserService userService;
	
	@Autowired
	private ProductService productService;
	
	@Autowired
	private ProspectService prospectService;
	
	
	
    @Autowired
    private ProspectRepository prospectRepository;
    
    @Autowired
    private ProductRepository productRepository;

	@Autowired

	private CurrentUser currentUser;

	@RequestMapping(value = "/add", method = RequestMethod.POST)

	public ResponseEntity<String> addActionMarketing(@RequestBody ActionMarketingRequest actionMarketingRequest) {

		try {

			if (userService.checkHasPermission("MARKETING_ACTIONS_ADD")) {

				ActionMarketing actionMarketingSaved = actionMarketingService.add(actionMarketingRequest);

				if (actionMarketingSaved != null) {

					return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);

				}

				return new ResponseEntity<String>(BirdnotesConstants.Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (BirdnotesException fe) {

			return new ResponseEntity<String>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {

			LOG.error("Error in save actionMarketing", e);

			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);

		}

	}

	@RequestMapping(value = "findActionMarketingByDate/{startDate}/{endDate}", method = RequestMethod.GET)

	public ResponseEntity<List<ActionMarketingDto>> findActionMarketingByDate(@PathVariable("startDate") Date startDate,

			@PathVariable("endDate") Date endDate) {

		try {

			if (userService.checkHasPermission("MARKETING_ACTIONS_VIEW")) {

				List<ActionMarketingDto> actionMarketingDto = actionMarketingService

						.findActionMarketingByDate(startDate, endDate);

				return new ResponseEntity<>(actionMarketingDto, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (Exception e) {

			LOG.error("An exception occurred while getting all actionMarketing", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/getActionMarketingById/{id}", method = RequestMethod.GET)

	public ResponseEntity<ActionMarketingDto> findActionMarketingById(@PathVariable("id") Long id) {

		try {
			if (userService.checkHasPermission("MARKETING_ACTIONS_VIEW")) {

				ActionMarketingDto actionMarketingDto = actionMarketingService.getActionMarketingById(id);

				return new ResponseEntity<>(actionMarketingDto, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}
		} catch (Exception e) {

			LOG.error("An exception occurred while getting action marketing", e);

			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteActionMarketing(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("MARKETING_ACTIONS_DELETE")) {
	            actionMarketingService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.ACTIONMARKETING_TO_DELETE_ALREADY_DELETED, 
	                    HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting action marketing", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the action marketing with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "/saveMarketingAction", method = RequestMethod.POST)

	public ResponseEntity<Long> saveMarketingAction(@RequestBody ActionMarketingDto actionMarketingDto) {

		try {
			if (userService.checkHasPermission("MARKETING_ACTIONS_EDIT")) {
				ActionMarketing savedActionMarketing = actionMarketingService.saveMarketingAction(actionMarketingDto);
				if (savedActionMarketing != null) {
					return new ResponseEntity<>(savedActionMarketing.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving saveMarketingAction", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving saveMarketingAction", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/acceptValidationStep/{id}", method = RequestMethod.PUT)

	public ResponseEntity<String> acceptValidationStep(@PathVariable("id") long id) {

		try {

			if (userService.checkHasPermission("MARKETING_ACTIONS_VALIDATION")) {

				actionMarketingService.acceptValidationStep(id);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (BirdnotesException e) {

			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);

			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/refuseValidationStep/{id}", method = RequestMethod.PUT)

	public ResponseEntity<String> refuseValidationStep(@PathVariable("id") Long id) {

		try {

			if (userService.checkHasPermission("MARKETING_ACTIONS_VALIDATION")) {

				actionMarketingService.refuseValidationStep(id);

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);

			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);

			}

		} catch (BirdnotesException e) {

			LOG.error("An exception occurred :Non-Authoritative Information when update validation Status", e);

			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {

			LOG.error("An exception occurred while updating validation Status", e);

			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	
	@RequestMapping(value = "/loadFormData", method = RequestMethod.GET)
	public ResponseEntity<ActionMarketingFormDto> loadFormData(
	        @RequestParam(defaultValue = "0") int page,
	        @RequestParam(defaultValue = "100") int size) {
	    
	    ActionMarketingFormDto marketingActionDto = new ActionMarketingFormDto();
	    ActionMarketingFormDto userDto = new ActionMarketingFormDto();
	    
	    try {
	        // Load products
	        List<LabelValueDto> products = productRepository.getAllProductIdAndName();
	        marketingActionDto.setProducts(products);
	        
	        // Create Pageable object for pagination
	        Pageable pageable = new PageRequest(page, size);	        
	        // Load prospects with pagination
	        List<LabelValueDto> prospects = prospectRepository.getAllProspectIdAndName(pageable);
	        marketingActionDto.setProspects(prospects);
	        
	        List<MarketingActionTypeDto> marketingActionTypes = marketingActionTypeService.findAll();
	        marketingActionDto.setMarketingActionTypes(marketingActionTypes);
	        
	        List<UserDto> users = userService.getSubUsers();
	        marketingActionDto.setUsers(users);

	        
	    } catch (Exception e) {
	        LOG.error("Error loading form data", e);
	        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
	    }

	    return ResponseEntity.ok(marketingActionDto);
	}
	
	@RequestMapping(value = "/marketingActionOrderPrediction", method = RequestMethod.POST)
	public ResponseEntity<Long> predictMarketingActionOrder(@RequestBody MarketingActionOrderPredictionDto predictionDto) {
	    try {
	        if (!userService.checkHasPermission("MARKETING_ACTIONS_VIEW")) {
	            return new ResponseEntity<>(HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	        Long prediction = actionMarketingService.predictMarketingActionOrder(predictionDto);

	        return new ResponseEntity<>(prediction, HttpStatus.OK);
	        
	    } catch (BirdnotesException e) {
	        LOG.error("Error in marketing action prediction", e);
	        return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
	    } catch (Exception e) {
	        LOG.error("Unexpected error occurred in marketing action prediction", e);
	        return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	
}


	


	
	


