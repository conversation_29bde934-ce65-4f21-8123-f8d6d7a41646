package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class PlanningValidationDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Long identifier;
	private Long user;
	private Date date;
	private String notes;
	private String status;
	private List<Long> prospectIds;
	private boolean specificWeek;
	private Integer weekNumber;
	List<PlanifiedProspectDto> planifiedProspectDto;
	private List<ValidationStatusDto> validationStatusDto;

	public PlanningValidationDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Long getUser() {
		return user;
	}

	public void setUser(Long user) {
		this.user = user;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getNotes() {
		return notes;
	}

	public void setNotes(String notes) {
		this.notes = notes;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public List<ValidationStatusDto> getValidationStatusDto() {
		return validationStatusDto;
	}

	public void setValidationStatusDto(List<ValidationStatusDto> validationStatusDto) {
		this.validationStatusDto = validationStatusDto;
	}

	public List<PlanifiedProspectDto> getPlanifiedProspectDto() {
		return planifiedProspectDto;
	}

	public void setPlanifiedProspectDto(List<PlanifiedProspectDto> planifiedProspectDto) {
		this.planifiedProspectDto = planifiedProspectDto;
	}

	public List<Long> getProspectIds() {
		return prospectIds;
	}

	public void setProspectIds(List<Long> prospectIds) {
		this.prospectIds = prospectIds;
	}

	public boolean isSpecificWeek() {
		return specificWeek;
	}

	public void setSpecificWeek(boolean specificWeek) {
		this.specificWeek = specificWeek;
	}

	public Integer getWeekNumber() {
		return weekNumber;
	}

	public void setWeekNumber(Integer weekNumber) {
		this.weekNumber = weekNumber;
	}

}
