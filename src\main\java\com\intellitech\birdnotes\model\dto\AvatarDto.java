package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class AvatarDto implements Serializable {
	
	private static final long serialVersionUID = 1L;
	
	private String nameFile;

	private String typeFile;

	private String valueFile;

	public AvatarDto(String nameFile, String typeFile, String valueFile) {
		super();
		this.nameFile = nameFile;
		this.typeFile = typeFile;
		this.valueFile = valueFile;
	}

	public AvatarDto() {
		super();
	}

	public String getNameFile() {
		return nameFile;
	}

	public void setNameFile(String nameFile) {
		this.nameFile = nameFile;
	}

	public String getTypeFile() {
		return typeFile;
	}

	public void setTypeFile(String typeFile) {
		this.typeFile = typeFile;
	}

	public String getValueFile() {
		return valueFile;
	}

	public void setValueFile(String valueFile) {
		this.valueFile = valueFile;
	}

}
