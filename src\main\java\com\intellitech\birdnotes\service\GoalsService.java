package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.data.dto.GoalsRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.model.dto.GoalItemDto;

public interface GoalsService {

	//public boolean saveGoalsByPotentials(GoalsRequestDto goalsRequestDto) throws BirdnotesException ;
	public List<GoalDto> getGoalByUser (Long userId)throws BirdnotesException;
	List<GoalItemDto> getGoalItemByUser(Long userId) throws BirdnotesException;

}
