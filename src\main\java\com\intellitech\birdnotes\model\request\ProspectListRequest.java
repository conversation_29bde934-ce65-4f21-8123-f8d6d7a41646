package com.intellitech.birdnotes.model.request;

public class ProspectListRequest {
	
	
	private Integer first;
	private Integer rows;
	private String  sortField;
	private Integer  sortOrder;
	private String globalFilter;

	
	
	public Integer getFirst() {
		return first;
	}
	public void setFirst(Integer first) {
		this.first = first;
	}
	public Integer getRows() {
		return rows;
	}
	public void setRows(Integer rows) {
		this.rows = rows;
	}
	public String getSortField() {
		return sortField;
	}
	public void setSortField(String sortField) {
		this.sortField = sortField;
	}
	public Integer getSortOrder() {
		return sortOrder;
	}
	public void setSortOrder(Integer sortOrder) {
		this.sortOrder = sortOrder;
	}
	public String getGlobalFilter() {
		return globalFilter;
	}
	public void setGlobalFilter(String globalFilter) {
		this.globalFilter = globalFilter;
	}

	

}
