package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.EstablishmentService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/establishments")
public class EstablishmentController {

	private static final Logger LOG = LoggerFactory.getLogger(EstablishmentController.class);

	@Autowired
	private EstablishmentService establishmentService;

	@Autowired
	UserService userService;
	
	@Autowired
	private SectorService sectorService;

	@RequestMapping(value = "getAllEstablishments", method = RequestMethod.GET)
	public ResponseEntity<List<EstablishmentDto>> getAllEstablishments() {
		try {
			if (userService.checkHasPermission("GROSSISTE_VIEW")) {
				List<EstablishmentDto> establishmentDtos = establishmentService.findAll();
				return new ResponseEntity<>(establishmentDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in getAllEstablishments", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "saveEstablishment", method = RequestMethod.POST)
	public ResponseEntity<String> saveEstablishment(@RequestBody EstablishmentDto establishmentRequest) {
	    try {
	        if (userService.checkHasPermission("GROSSISTE_ADD")) {
	            Establishment establishmentSaved = establishmentService.saveEstablishment(establishmentRequest);
	            if (establishmentSaved != null) {
	                return new ResponseEntity<>(establishmentSaved.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving establishmentSaved", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving establishmentSaved", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}


	@RequestMapping(value = "deleteEstablishment/{establishmentId}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteEstablishment(@PathVariable("establishmentId") Long establishmentId) {
		try {
			if (userService.checkHasPermission("GROSSISTE_DELETE")) {
				establishmentService.deleteEstablishment(establishmentId);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>("", HttpStatus.CONFLICT);
			}
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting sector", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	
		} catch (Exception e) {
			LOG.error("Error in deleteEstablishment", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}}


	@RequestMapping(value = "updateEstablishment", method = RequestMethod.PUT)
	public ResponseEntity<String> updateEstablishment(@RequestBody EstablishmentDto establishmentDto) {
		try {
			if (userService.checkHasPermission("GROSSISTE_EDIT")) {
				establishmentService.updateEstablishment(establishmentDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException fe) {
			LOG.error("An exception occurred :Non-Authoritative Information when update establishment", fe);
			return new ResponseEntity<>(fe.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);

		} catch (Exception e) {
			LOG.error("Error in updateEstablishment", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	
	@RequestMapping(value = "findallsectors", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> findallSectors() {
		try {
			if (userService.checkHasPermission("GROSSISTE_VIEW") || userService.checkHasPermission("GROSSISTE_EDIT")) {
				List<SectorDto> sectorDtos = sectorService.findAll();
				return new ResponseEntity<>(sectorDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllLocalities(@RequestBody List<EstablishmentDto> establishmentDtos) {
		try {
			establishmentService.saveAllEstablishments(establishmentDtos);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add Localities", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding localities", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
