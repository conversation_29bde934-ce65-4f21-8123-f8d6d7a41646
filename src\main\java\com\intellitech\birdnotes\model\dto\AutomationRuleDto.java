package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

import com.intellitech.birdnotes.enumeration.Periodicity;

public class AutomationRuleDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private String eventType;
	private String actionType;
	private Long activityTypeId;
	private String activityTypeName;
	private Periodicity periodicity;
	private Integer repeatingPeriod;
	private Integer repetationEach;
	private Float startAfter;
	private Long activityTypeEvent;

	public AutomationRuleDto(Long id, String eventType, String actionType, Long activityTypeId, String activityTypeName,
			Periodicity periodicity, Integer repetationEach, Float startAfter, Integer repeatingPeriod,
			Long activityTypeEvent) {
		super();
		this.id = id;
		this.eventType = eventType;
		this.actionType = actionType;
		this.activityTypeId = activityTypeId;
		this.activityTypeName = activityTypeName;
		this.periodicity = periodicity;
		this.repetationEach = repetationEach;
		this.startAfter = startAfter;
		this.repeatingPeriod = repeatingPeriod;
		this.activityTypeEvent = activityTypeEvent;
	}

	public AutomationRuleDto() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public String getActionType() {
		return actionType;
	}

	public void setActionType(String actionType) {
		this.actionType = actionType;
	}

	public Long getActivityTypeId() {
		return activityTypeId;
	}

	public void setActivityTypeId(Long activityTypeId) {
		this.activityTypeId = activityTypeId;
	}

	public String getActivityTypeName() {
		return activityTypeName;
	}

	public void setActivityTypeName(String activityTypeName) {
		this.activityTypeName = activityTypeName;
	}

	public Periodicity getPeriodicity() {
		return periodicity;
	}

	public void setPeriodicity(Periodicity periodicity) {
		this.periodicity = periodicity;
	}

	public Integer getRepetationEach() {
		return repetationEach;
	}

	public void setRepetationEach(Integer repetationEach) {
		this.repetationEach = repetationEach;
	}

	public Float getStartAfter() {
		return startAfter;
	}

	public void setStartAfter(Float startAfter) {
		this.startAfter = startAfter;
	}

	public Integer getRepeatingPeriod() {
		return repeatingPeriod;
	}

	public void setRepeatingPeriod(Integer repeatingPeriod) {
		this.repeatingPeriod = repeatingPeriod;
	}

	public Long getActivityTypeEvent() {
		return activityTypeEvent;
	}

	public void setActivityTypeEvent(Long activityTypeEvent) {
		this.activityTypeEvent = activityTypeEvent;
	}

}
