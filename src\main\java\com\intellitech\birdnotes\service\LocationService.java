package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.model.dto.UserDto;

public interface LocationService {
	
	List<LocationDto> findLocationByProspectAndDate(Long prospectId, Date date);
	List<LocationDto> findLocation(Long userId, Date firstDate, Date lastDate, boolean showOnlyVisistsPositions);
	void addExpensesByDistance(Date day, UserDto delegate);
	
}