package com.intellitech.birdnotes.model.convertor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.dto.VisitProductDto;
import com.intellitech.birdnotes.repository.ProductRepository;

@Component("convertDtoToVisitProduct")
public class ConvertDtoToVisitProduct {
	
	@Autowired
	private ProductRepository productRepository;
	
	public VisitsProducts convert(VisitProductDto visitsProductsDto,Visit visit) throws BirdnotesException {
		if(visitsProductsDto == null) {
			throw new BirdnotesException("visitdto is null");
		}
		VisitsProducts visitsProducts = new VisitsProducts();
		visitsProducts.setComment(visitsProductsDto.getComment());
		visitsProducts.setFreeOrder(visitsProductsDto.getFreeOrder());
		visitsProducts.setId(visitsProductsDto.getId());
		visitsProducts.setIdentifier(visitsProductsDto.getIdentifier());
		visitsProducts.setLabGratuity(visitsProductsDto.getLabGratuity());
		visitsProducts.setOrderQuantity(visitsProductsDto.getOrderQuantity());
		visitsProducts.setPrescriptionQuantity(visitsProductsDto.getPrescriptionQuantity());
		Product product = productRepository.findById(visitsProductsDto.getProductId());
		visitsProducts.setProduct(product);
		visitsProducts.setPurchaseOrder(null);
		visitsProducts.setUrgent(visitsProductsDto.isUrgent());
		visitsProducts.setRank(visitsProductsDto.getRank());
		//visitsProducts.setWholesaler(null);
		visitsProducts.setSmily(visitsProductsDto.getSmily());
		visitsProducts.setVisit(visit);
		visitsProducts.setSampleQuantity(visitsProductsDto.getSampleQuantity());
		visitsProducts.setSaleQuantity(visitsProductsDto.getSaleQuantity());
		
		
		
		
		return visitsProducts;
	}

}
