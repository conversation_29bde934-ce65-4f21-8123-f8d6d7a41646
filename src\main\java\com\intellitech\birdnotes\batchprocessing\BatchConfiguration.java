package com.intellitech.birdnotes.batchprocessing;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.batch.core.BatchStatus;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobExecution;
import org.springframework.batch.core.JobExecutionListener;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.EnableBatchProcessing;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.batch.item.ItemWriter;
import org.springframework.batch.item.file.FlatFileItemReader;
import org.springframework.batch.item.file.mapping.DefaultLineMapper;
import org.springframework.batch.item.file.transform.DelimitedLineTokenizer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.enumeration.ImportStep;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ImportProspectDto;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.dto.DuplicateProspectDto;
import com.intellitech.birdnotes.model.dto.EstablishmentDto;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.repository.EstablishmentRepository;
import com.intellitech.birdnotes.repository.LocalityRepository;
import com.intellitech.birdnotes.repository.PotentialRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.LocalityService;
import com.intellitech.birdnotes.service.SectorService;

@Configuration
@EnableBatchProcessing
@Service("batchConfiguration")
public class BatchConfiguration {

	@Autowired
	private JobBuilderFactory jobBuilderFactory;
	@Autowired
	private StepBuilderFactory stepBuilderFactory;
	@Autowired
	LocalityRepository localityRepository;
	@Autowired
	SectorRepository sectorRepository;
	@Autowired
	SpecialityRepository specialityRepository;
	@Autowired
	EstablishmentRepository establishmentRepository;
	@Autowired
	PotentialRepository potentialRepository;
	
	private static final String OVERRIDDEN_BY_EXPRESSION = null;
	private static final Logger log = LoggerFactory.getLogger(BatchConfiguration.class);
	static Long savedProspects;
	 static Set<ProspectDto> invalidProspects = new HashSet<>();
	 static Set<SectorDto> sectorDtosNotFound = new HashSet<>();
	 static Set<SpecialityDto> specialityDtosNotFound = new HashSet<>();
	 static Set<PotentialDto> potentialDtosNotFound = new HashSet<>();
	 static Set<LocalityDto> localityDtosNotFound = new HashSet<>();
	 static List<String> allLocalities = new ArrayList<>();
	 static List<String> allSectors = new ArrayList<>();
	 static List<String> allSpecialities = new ArrayList<>();
	 static List<String> allPotentials = new ArrayList<>();
	 static List<String> allEstablishment = new ArrayList<>();
	 static List<String> allProspectType = new ArrayList<>();
	 
	 static Set<EstablishmentDto> establishmentDtosNotFound = new HashSet<>();
	 static Set<ProspectTypeDto> prospectTypeDtosNotFound = new HashSet<>();
	 static Set<DuplicateProspectDto> duplicateProspectsList = new HashSet<>();
	 private HashMap<String, Object> invalidProspectsData = new HashMap<>();
	 static HashMap<String, Set<String>> localitySectorsMap = new HashMap<>();
	 static HashMap<String, Set<String>> localityDtosInManySectors = new HashMap<>();
		

	public Set<DuplicateProspectDto> getDuplicateProspectsList() {
		return duplicateProspectsList;
	}

	public Set<SectorDto> getSectorDtosNotFound() {
		return sectorDtosNotFound;
	}

	public Map<String, Object> getInvalidProspectsData() {
		return invalidProspectsData;
	}

	public Set<SpecialityDto> getSpecialityDtosNotFound() {
		return specialityDtosNotFound;
	}

	public Set<PotentialDto> getPotentialDtosNotFound() {
		return potentialDtosNotFound;
	}

	public Set<LocalityDto> getLocalityDtosNotFound() {
		return localityDtosNotFound;
	}

	
	public static List<String> getAllSpecialities() {
		return allSpecialities;
	}

	public static void setAllSpecialities(List<String> allSpecialities) {
		BatchConfiguration.allSpecialities = allSpecialities;
	}

	public static List<String> getAllSectors() {
		return allSectors;
	}

	public static void setAllSectors(List<String> allSectors) {
		BatchConfiguration.allSectors = allSectors;
	}

	public static List<String> getAllLocalities() {
		return allLocalities;
	}

	public static void setAllLocalities(List<String> allLocalities) {
		BatchConfiguration.allLocalities = allLocalities;
	}

	public static HashMap<String, Set<String>> getLocalityDtosInManySectors() {
		return localityDtosInManySectors;
	}

	public static void setLocalityDtosInManySectors(HashMap<String, Set<String>> localityDtosInManySectors) {
		BatchConfiguration.localityDtosInManySectors = localityDtosInManySectors;
	}

	public Set<EstablishmentDto> getEstablishmentDtosNotFound() {
		return establishmentDtosNotFound;
	}
	public Set<ProspectTypeDto> getProspectTypeDtosNotFound() {
		return prospectTypeDtosNotFound;
	}

	public Set<ProspectDto> getPospectList() {
		return invalidProspects;
	}

	@Bean
	public Job importProspectsData(ItemWriter<ProspectDto> itemWriter)
			throws BirdnotesException {

		Step step = stepBuilderFactory.get("load-data").<Prospect, Prospect>chunk(100)
				.reader(getProspectsList(OVERRIDDEN_BY_EXPRESSION)).processor(ImportedProspectsValidation(OVERRIDDEN_BY_EXPRESSION, OVERRIDDEN_BY_EXPRESSION))
				.writer(itemWriter).build();

		return jobBuilderFactory.get("importData").incrementer(new RunIdIncrementer()).listener(listener()).start(step)
				.build();
	}

	@Bean
	@StepScope
	public FlatFileItemReader getProspectsList(@Value("#{jobParameters['inputFile']}") String inputFile) {

		FlatFileItemReader<Prospect> reader = new FlatFileItemReader<>();
		reader.setSkippedLinesCallback(line -> {
			   // Verify file header is what we expect
			   if (!line.trim().equalsIgnoreCase("firstName|lastName|Speciality|Phone|GSM|address|Sector|Locality|Activity|Potential|Type|Email|Note|Delegates|longitude|latitude|grade|identifier|Establishment")) {
				   throw new IllegalArgumentException(String.format("Unexpected header: [%s]", line));
			   }
			 });
		reader.setResource(new FileSystemResource(inputFile));
		reader.setLinesToSkip(1);

		DefaultLineMapper lineMapper = new DefaultLineMapper<>();
		DelimitedLineTokenizer delimitedLineTokenizer = new DelimitedLineTokenizer();
		delimitedLineTokenizer.setDelimiter("|");
		lineMapper.setLineTokenizer(delimitedLineTokenizer);
		lineMapper.setFieldSetMapper(new ProspectFieldSetMapper());
		reader.setLineMapper(lineMapper);
		reader.setRecordSeparatorPolicy(new SkipBlankLine());
		reader.open(new ExecutionContext());
		return reader;
	}

	@Bean
	@StepScope
	public ItemProcessor<ImportProspectDto , ProspectDto> ImportedProspectsValidation(@Value("#{jobParameters['importStep']}") String importStep, @Value("#{jobParameters['geocodeAdress']}") String   geocodeAdressStr) {
		Boolean geocodeAdress = Boolean.valueOf(geocodeAdressStr);
		return new ProspectValidationProcess(ImportStep.valueOf(importStep), geocodeAdress );
	}
	
	@Bean
	public JobExecutionListener listener() {
		return new JobExecutionListener() {

			@Override
			public void beforeJob(JobExecution jobExecution) {
				invalidProspects.clear();
				sectorDtosNotFound.clear();
				specialityDtosNotFound.clear();
				potentialDtosNotFound.clear();
				localityDtosNotFound.clear();
				localityDtosInManySectors.clear();
				establishmentDtosNotFound.clear();
				prospectTypeDtosNotFound.clear();
				duplicateProspectsList.clear();
				savedProspects=0L;
				allLocalities.clear();
				allSectors.clear();
				localitySectorsMap.clear();
			}

			@Override
			public void afterJob(JobExecution jobExecution) {
				if (jobExecution.getStatus() == BatchStatus.COMPLETED) {
					log.info("!!! JOB FINISHED!");

				}
				checkIfLocalityInManySector();
				invalidProspectsData.put("similarLocalities",findSimilarLocalities());
				invalidProspectsData.put("similarSectors",findSimilarSectors());
				invalidProspectsData.put("similarSpecialities",findSimilarSpecialities());
				invalidProspectsData.put("localitiesInManySectors", localityDtosInManySectors);
				invalidProspectsData.put("invalidProspects", invalidProspects);
				invalidProspectsData.put("invalidSectors", sectorDtosNotFound);
				invalidProspectsData.put("invalidSpecialities", specialityDtosNotFound);
				invalidProspectsData.put("invalidPotentials", potentialDtosNotFound);
				invalidProspectsData.put("invalidLocalities", localityDtosNotFound);
				invalidProspectsData.put("invalidEstablishments", establishmentDtosNotFound);
				invalidProspectsData.put("invalidProspectTypes", prospectTypeDtosNotFound);
				invalidProspectsData.put("duplicateProspects", duplicateProspectsList);
				invalidProspectsData.put("savedProspects", savedProspects);
			}
		};
	}
	
	public void checkIfLocalityInManySector() {
		for (String key : localitySectorsMap.keySet()) {
			if (localitySectorsMap.get(key).size()>1) {
				localityDtosInManySectors.put(key, localitySectorsMap.get(key));
			}
		}	
	}
	
	private static String normalizeString(String str) {
        return str.toLowerCase().replaceAll("[^a-z0-9]", "").trim();
    }
	
	public static Map<String, Set<String>> findAllSimilarities(List<String> strings) {
        Map<String, Set<String>> similarityMap = new HashMap<>();
        for (String str : strings) {
            String normalizedStr = normalizeString(str);
            Set<String> similarStrings = new HashSet<>();
            for (String otherStr : strings) {
                if (!str.equals(otherStr)) { // Skip itself
                    String normalizedOtherStr = normalizeString(otherStr);
                    // Check for substring match
                    if (normalizedOtherStr.contains(normalizedStr) || normalizedStr.contains(normalizedOtherStr)) {
                        similarStrings.add(otherStr);
                    }
                }
            }
            if(similarStrings.size() != 0) {
            	similarityMap.put(str, similarStrings);
            }   
        }
        return similarityMap;
    }
	
	
	public Map<String, Set<String>> findSimilarLocalities() {
		List<String> savedLocalities = localityRepository.findAllLocalityNames();
		allLocalities.addAll(savedLocalities);
		Map<String, Set<String>> similarities = findAllSimilarities(allLocalities);
		return similarities;
	}
	public Map<String, Set<String>> findSimilarSectors() {
		List<String> savedSectors = sectorRepository.findAllSectorNames();
		allSectors.addAll(savedSectors);
		Map<String, Set<String>> similarities = findAllSimilarities(allSectors);
		return similarities;
	}
	public Map<String, Set<String>> findSimilarSpecialities() {
		List<String> savedSpecialities = specialityRepository.findAllSpecialityNames();
		allSpecialities.addAll(savedSpecialities);
		Map<String, Set<String>> similarities = findAllSimilarities(allSpecialities);
		return similarities;
	}
	public Map<String, Set<String>> findSimilarPotentials() {
		List<String> savedPotentials = potentialRepository.findAllPotentialNames();
		allPotentials.addAll(savedPotentials);
		Map<String, Set<String>> similarities = findAllSimilarities(allPotentials);
		return similarities;
	}
	public Map<String, Set<String>> findSimilarEstablishments() {
		List<String> savedEstablishments = establishmentRepository.findAllEstablishmentsNames();
		allEstablishment.addAll(savedEstablishments);
		Map<String, Set<String>> similarities = findAllSimilarities(allEstablishment);
		return similarities;
	}
}
