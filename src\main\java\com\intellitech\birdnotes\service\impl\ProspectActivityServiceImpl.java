package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.ProspectActivity;
import com.intellitech.birdnotes.model.convertor.ConvertProspectActivityToDto;
import com.intellitech.birdnotes.model.dto.ProspectActivityDto;
import com.intellitech.birdnotes.repository.ProspectActivityRepository;
import com.intellitech.birdnotes.service.ProspectActivityService;
import com.intellitech.birdnotes.service.UserService;

@Service("prospectActivityService")
@Transactional
public class ProspectActivityServiceImpl implements ProspectActivityService {
	private ProspectActivityRepository prospectActivityRepository;
	private ConvertProspectActivityToDto convertprospectActivityToDto;
	
	@Autowired
	ProspectActivityServiceImpl(ProspectActivityRepository prospectActivityRepository, ConvertProspectActivityToDto convertprospectActivityToDto) {
		
		this.prospectActivityRepository = prospectActivityRepository;
		this.convertprospectActivityToDto = convertprospectActivityToDto;
	}




	
	@Autowired
	UserService userService;



	@Override
	public List<ProspectActivityDto> findAll() throws BirdnotesException {
		List<ProspectActivityDto> back = new ArrayList<>();
		List<ProspectActivity> allprospectActivities = prospectActivityRepository.findAll();
		for (ProspectActivity prospectActivity : allprospectActivities) {
			back.add(convertprospectActivityToDto.convert(prospectActivity));
		}
		return back;
	}
	
	@Override
	public ProspectActivity saveprospectActivity(ProspectActivityDto prospectActivityDto) throws BirdnotesException {

		ProspectActivity existingprospectActivity = prospectActivityRepository.findByName(prospectActivityDto.getName());
	    if (existingprospectActivity != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    ProspectActivity prospectActivity = null;
	    if (prospectActivityDto.getId() != null) {
	    	prospectActivity = prospectActivityRepository.findOne(prospectActivityDto.getId());
	    }
	    if (prospectActivity == null) {
	    	prospectActivity = new ProspectActivity();
	    }

	    prospectActivity.setId(prospectActivityDto.getId().longValue());
	    prospectActivity.setName(prospectActivityDto.getName());
	    return prospectActivityRepository.save(prospectActivity);
	}
	
	@Override
	public void delete (long id)throws BirdnotesException {
		prospectActivityRepository.delete(id);
	}
	
}