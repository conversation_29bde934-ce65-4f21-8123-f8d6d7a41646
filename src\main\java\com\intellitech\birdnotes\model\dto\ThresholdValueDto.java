package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class ThresholdValueDto implements Serializable{
	private static final long serialVersionUID = 1L;
	private Float threshold;
	private Float value;
	public Float getThreshold() {
		return threshold;
	}
	public void setThreshold(Float threshold) {
		this.threshold = threshold;
	}
	public Float getValue() {
		return value;
	}
	public void setValue(Float value) {
		this.value = value;
	}
	



}
