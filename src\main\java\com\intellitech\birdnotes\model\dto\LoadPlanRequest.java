package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class LoadPlanRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private String name;
	private List<String> productNames;
	private List<Integer> selectedRanges;
	private List<Long> selectedSpecialities;
	private List<Long> productIds;
	private Long loadPlanId;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public List<String> getProductNames() {
		return productNames;
	}

	public void setProductNames(List<String> productNames) {
		this.productNames = productNames;
	}

	public LoadPlanRequest() {
		super();

	}



	public Long getLoadPlanId() {
		return loadPlanId;
	}

	public void setLoadPlanId(Long loadPlanId) {
		this.loadPlanId = loadPlanId;
	}

	public List<Integer> getSelectedRanges() {
		return selectedRanges;
	}

	public void setSelectedRanges(List<Integer> selectedRanges) {
		this.selectedRanges = selectedRanges;
	}

	public List<Long> getSelectedSpecialities() {
		return selectedSpecialities;
	}

	public void setSelectedSpecialities(List<Long> selectedSpecialities) {
		this.selectedSpecialities = selectedSpecialities;
	}

	public List<Long> getProductIds() {
		return productIds;
	}

	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}

}
