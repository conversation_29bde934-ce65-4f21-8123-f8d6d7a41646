package com.intellitech.birdnotes.model.convertor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Category;
import com.intellitech.birdnotes.model.dto.ValueTypeDto;

@Component("categoryToDtoConvertor")
public class CategoryToDtoConvertor {

	private static final Logger LOG = LoggerFactory.getLogger(CategoryToDtoConvertor.class);

	public ValueTypeDto convert(Category category) throws BirdnotesException {

		if (category == null) {
			LOG.error("category is null");
			throw new BirdnotesException("sector is null");
		}
		ValueTypeDto categoryDto = new ValueTypeDto();
		categoryDto.setId(category.getId());
		categoryDto.setName(category.getName());
		return categoryDto;
	}

}
