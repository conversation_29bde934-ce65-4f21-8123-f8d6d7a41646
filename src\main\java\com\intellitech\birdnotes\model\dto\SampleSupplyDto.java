package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.intellitech.birdnotes.model.SampleSupply;

public class SampleSupplyDto implements Serializable {
	private static final long serialVersionUID = 1L;
	private Long id;
	private Long delegateId;
	private String delegateName;
	private Date deliveryDate;
	private Long missionId;
	private List<SampleSupplyItemDto> sampleSupplyItems;

	public SampleSupplyDto(SampleSupply sampleSupply) {
		this.id = sampleSupply.getId();
		this.delegateId = sampleSupply.getDelegate().getId();
		this.delegateName = sampleSupply.getDelegate().getFirstName() + ' ' + sampleSupply.getDelegate().getLastName();
		this.deliveryDate = sampleSupply.getDeliveryDate();
		// this.missionId = sampleSupply.getMission().getId();
		if (sampleSupply.getMission() != null) {
			this.missionId = sampleSupply.getMission().getId();
		} else {
			this.missionId = null;
		}
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public SampleSupplyDto() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Long getDelegateId() {
		return delegateId;
	}

	public void setDelegateId(Long delegateId) {
		this.delegateId = delegateId;
	}

	public String getDelegateName() {
		return delegateName;
	}

	public void setDelegateName(String delegateName) {
		this.delegateName = delegateName;
	}

	public Date getDeliveryDate() {
		return deliveryDate;
	}

	public void setDeliveryDate(Date deliveryDate) {
		this.deliveryDate = deliveryDate;
	}

	public List<SampleSupplyItemDto> getSampleSupplyItems() {
		return sampleSupplyItems;
	}

	public void setSampleSupplyItems(List<SampleSupplyItemDto> sampleSupplyItems) {
		this.sampleSupplyItems = sampleSupplyItems;
	}

	public Long getMissionId() {
		return missionId;
	}

	public void setMissionId(Long missionId) {
		this.missionId = missionId;
	}

}
