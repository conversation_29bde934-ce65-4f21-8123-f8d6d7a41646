package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.LoadPlanDto;
import com.intellitech.birdnotes.model.dto.LoadPlanItemDto;
import com.intellitech.birdnotes.model.dto.LoadPlanRequest;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.service.LoadPlanItemService;
import com.intellitech.birdnotes.service.ProductService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/loadPlanItem")
public class LoadPlanItemController {
	private static final Logger LOG = LoggerFactory.getLogger(LoadPlanItemController.class);

	@Autowired
	private LoadPlanItemService loadPlanItemService;
	@Autowired
	UserService userService;
	@Autowired
	private ProductService productService;

	@RequestMapping(value = "/addLoadPlanItem", method = RequestMethod.POST)
	public ResponseEntity<String> saveloadPlan(@RequestBody List<LoadPlanRequest> loadPlanItemRequest) {
		try {
			if (userService.checkHasPermission("LOAD_PLAN_EDIT")) {
				loadPlanItemService.saveLoadPlan(loadPlanItemRequest);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("Error Non-Authoritative Information in save load plan", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("Error in save load plan", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.EXPECTATION_FAILED);
		}
	}
	
	@RequestMapping(value = "/findAll", method = RequestMethod.POST)
	public ResponseEntity<List<LoadPlanItemDto>> findAll(@RequestBody LoadPlanRequest loadPlanRequest) {
		try {
			if (userService.checkHasPermission("LOAD_PLAN_VIEW")) {
				List<LoadPlanItemDto> loadPlanDtos = loadPlanItemService.findAll(loadPlanRequest.getSelectedRanges(), loadPlanRequest.getSelectedSpecialities() ,loadPlanRequest.getLoadPlanId());
				return new ResponseEntity<>(loadPlanDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting charge plan", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@RequestMapping(value = "getAllProducts", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getAllProducts() {
		try {
			if (userService.checkHasPermission("LOAD_PLAN_VIEW")) {
				List<ProductDto> productDtos = productService.getAllProducts();
				return new ResponseEntity<>(productDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("Error in findAllProducts", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.EXPECTATION_FAILED);
		}
	}

}
