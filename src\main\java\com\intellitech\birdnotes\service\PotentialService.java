package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.PotentialDto;

public interface PotentialService {
	
	Potential add (PotentialDto potentialRequestDto) throws BirdnotesException;
	List<PotentialDto> findAll() throws BirdnotesException;
	void delete (long id) throws BirdnotesException;
	PotentialDto findPotentialByName(String name) throws BirdnotesException;
	void addAll(List<PotentialDto> potentialRequestDtos)throws BirdnotesException;
	PotentialDto findPotentialDto(String specialityName, List<PotentialDto> potentialDtos) ;
	Potential savePotential(PotentialDto potentialDto) throws BirdnotesException;
}
