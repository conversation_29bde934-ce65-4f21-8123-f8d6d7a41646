package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.PROSPECT, schema = Common.PUBLIC_SCHEMA)
public class Prospect implements Serializable {

	private static final long serialVersionUID = 1L;


	@Id
	@SequenceGenerator(name = Sequences.PROSPECT_SEQUENCE, sequenceName = Sequences.PROSPECT_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PROSPECT_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.IDENTIFIER)
	private Long identifier;

	@Column(name = Columns.FIRST_NAME, length = Numbers.N_85)
	private String firstName;

	@Column(name = Columns.LAST_NAME, length = Numbers.N_85)
	private String lastName;
	
	@Column(name = BirdnotesConstants.Columns.CODE, length = BirdnotesConstants.Numbers.N_85)
	private String code;
	
	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS, length = Numbers.N_85)
	private UserValidationStatus status;

	private Boolean active;

	@Column(name = Columns.IDPROSPECT)
	private Long idprospect;

	@Column(name = Columns.ACTIVITY, length = Numbers.N_85)
	private String activity;

	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.POTENTIAL_ID)
	private Potential potential;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_TYPE_ID)
	private ProspectType prospectType;

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PROSPECT_INTEREST, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.INTEREST_ID, nullable = false, updatable = false) })

	private Set<Interest> interests;


	
	public Set<Interest> getInterests() {
		return interests;
	}

	public void setInterests(Set<Interest> interests) {
		this.interests = interests;
	}

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PROSPECT_CONTACT_TYPE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.CONTACT_TYPE_ID, nullable = false, updatable = false) })

	private Set<ContactType> contactTypes;

	public Set<ContactType> getContactTypes() {
		return contactTypes;
	}

	public void setContactTypes(Set<ContactType> contactTypes) {
		this.contactTypes = contactTypes;
	}
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.INTEREST_ID)
	private Interest interest;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.CONTACT_TYPE_ID)
	private ContactType conactType;

	@Column(name = Columns.ADDRESS, length = Numbers.N_255)
	private String address;

	@Column(name = Columns.GSM, length = Numbers.N_85)
	private String gsm;

	@Column(name = Columns.PHONE, length = Numbers.N_85)
	private String phone;

	@Column(name = Columns.EMAIL, length = Numbers.N_85)
	private String email;

	@Column(name = Columns.NOTE, length = Numbers.N_1024)
	private String note;
	
	@Column(name = Columns.SOCIAL_MEDIA, length = Numbers.N_1024)
	private String socialMedia;
	
	@Column(name = Columns.TAX_ID_NUMBER, length = Numbers.N_1024)
	private String taxIdNumber;

	@Column(name = Columns.GRADE, length = Numbers.N_85)
	private String grade;

	@Column(name = Columns.SECRETARY, length = Numbers.N_85)
	private String secretary;

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "prospect", targetEntity = Visit.class)
	private Set<Visit> visits;

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.SPECIALITY_ID)
	private Speciality speciality;

	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.SECTOR_ID)
	private Sector sector;

	@ManyToOne(optional = false)
	@JoinColumn(name = BirdnotesConstants.Columns.LOCALITY_ID)
	private Locality locality;

	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.ESTABLISHMENT_ID)
	private Establishment establishment;

	@JsonIgnore
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;

	@Column(name = Columns.SCRAPPING_ID, length = Numbers.N_85)
	private String scrappingId;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = BirdnotesConstants.Columns.CREATION_DATE)
	private Date creationDate;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = BirdnotesConstants.Columns.UPDATE_DATE)
	private Date updateDate;

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = BirdnotesConstants.Columns.UPDATE_POSITION_DATE)
	private Date updatePositionDate;

	@Column(name = BirdnotesConstants.Columns.LATITUDE)
	private Double latitude;

	@Column(name = BirdnotesConstants.Columns.LONGITUDE)
	private Double longitude;

	@Column(name = BirdnotesConstants.Columns.MAP_ADDRESS, length = BirdnotesConstants.Numbers.N_255)
	private String mapAddress;

	@JsonIgnore
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "prospect", targetEntity = Wholesaler.class)
	private Wholesaler wholesaler;

	@JsonIgnore
	@OneToOne(optional = true)
	@JoinColumn(name = Columns.USER_APP_ID)
	private User appUser;

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "prospect", targetEntity = ProspectsAffectation.class)
	private Set<ProspectsAffectation> prospectsAffectation;

	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "prospect", targetEntity = PotentielProduit.class)
	private Set<PotentielProduit> potentielProduits;

	@JsonIgnore
	@OneToMany(mappedBy = "prospect")
	private List<ValidationStatus> validationStatus;

	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PROSPECT_RANGE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.RANGE_ID, nullable = false, updatable = false) })

	private Set<Range> ranges;
	
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PROSPECT_PREFERENCE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.PREFERENCE_ID, nullable = false, updatable = false) })

	private Set<Preference> preferences;
	
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.DOCTOR_ID)
	private Prospect doctor;

	public Set<Range> getRanges() {
		return ranges;
	}

	public void setRanges(Set<Range> ranges) {
		this.ranges = ranges;
	}

	public Set<PotentielProduit> getPotentielProduit() {
		return potentielProduits;
	}

	public void setPotentielProduit(Set<PotentielProduit> potentielProduits) {
		this.potentielProduits = potentielProduits;
	}

	public Set<ProspectsAffectation> getProspectsAffectation() {
		return prospectsAffectation;
	}

	public void setProspectsAffectation(Set<ProspectsAffectation> prospectsAffectation) {
		this.prospectsAffectation = prospectsAffectation;
	}

	public Long getId() {
		return id;
	}

	public Prospect() {
		super();
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}

	public Potential getPotential() {
		return potential;
	}

	public void setPotential(Potential potential) {
		this.potential = potential;
	}

	public ProspectType getProspectType() {
		return prospectType;
	}
	
	public Interest getInterest() {
		return interest;
	}
	
	public ContactType getContactType() {
		return conactType;
	}
	
	

	public void setProspectType(ProspectType prospectType) {
		this.prospectType = prospectType;
	}
	
	public void setInterest(Interest interest) {
		this.interest = interest;
	}
	

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getGsm() {
		return gsm;
	}

	public void setGsm(String gsm) {
		this.gsm = gsm;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getNote() {
		return note;
	}
	public String getSocialMedia() {
		return socialMedia;
	}

	public void setNote(String note) {
		this.note = note;
	}
	
	public void setSocialMedia(String socialMedia) {
		this.socialMedia = socialMedia;
	}

	public String getGrade() {
		return grade;
	}

	public void setGrade(String grade) {
		this.grade = grade;
	}

	public String getSecretary() {
		return secretary;
	}

	public void setSecretary(String secretary) {
		this.secretary = secretary;
	}
	
	

	public Set<Visit> getVisits() {
		return visits;
	}

	public void setVisits(Set<Visit> visits) {
		this.visits = visits;
	}

	public Speciality getSpeciality() {
		return speciality;
	}

	public void setSpeciality(Speciality speciality) {
		this.speciality = speciality;
	}

	public Sector getSector() {
		return sector;
	}

	public void setSector(Sector sector) {
		this.sector = sector;
	}

	public Locality getLocality() {
		return locality;
	}

	public void setLocality(Locality locality) {
		this.locality = locality;
	}

	public Establishment getEstablishment() {
		return establishment;
	}

	public void setEstablishment(Establishment establishment) {
		this.establishment = establishment;
	}

	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Date getUpdatePositionDate() {
		return updatePositionDate;
	}

	public void setUpdatePositionDate(Date updatePositionDate) {
		this.updatePositionDate = updatePositionDate;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public String getMapAddress() {
		return mapAddress;
	}

	public void setMapAddress(String mapAddress) {
		this.mapAddress = mapAddress;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public void setIdprospect(Long idprospect) {
		this.idprospect = idprospect;
	}

	public Long getIdprospect() {
		return idprospect;
	}

	public Wholesaler getWholesaler() {
		return wholesaler;
	}

	public void setWholesaler(Wholesaler wholesaler) {
		this.wholesaler = wholesaler;
	}

	public List<ValidationStatus> getValidationStatus() {
		return validationStatus;
	}

	public void setValidationStatus(List<ValidationStatus> validationStatus) {
		this.validationStatus = validationStatus;
	}

	@Override
	public String toString() {
		return "Prospect [id=" + id + ", firstName=" + firstName + ", lastName=" + lastName + ", status="
				+ status.toString() + ", idprospect=" + idprospect + ", activity=" + activity + ", potential="
				+ potential + ", address=" + address + ", gsm=" + gsm + ", phone=" + phone + ", email=" + email
				+ ", note=" + note + ", secretary=" + secretary + ", visits=" + visits + ", speciality=" + speciality
				+ ", sector=" + sector + ", locality=" + locality + ", delegate=" + user + ", creationDate="
				+ creationDate + ", latitude=" + latitude + ", longitude=" + longitude + ", mapAddress=" + mapAddress
				+ ", prospectsAffectation=" + prospectsAffectation + ", potentielProduits=" + potentielProduits
				+ ", gammes=" + ranges + ", code=" + code + "]";
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Set<PotentielProduit> getPotentielProduits() {
		return potentielProduits;
	}

	public void setPotentielProduits(Set<PotentielProduit> potentielProduits) {
		this.potentielProduits = potentielProduits;
	}

	public User getAppUser() {
		return appUser;
	}

	public void setAppUser(User appUser) {
		this.appUser = appUser;

	}

	public String getScrappingId() {
		return scrappingId;
	}

	public void setScrappingId(String scrappingId) {
		this.scrappingId = scrappingId;

	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public Prospect getDoctor() {
		return doctor;
	}

	public void setDoctor(Prospect doctor) {
		this.doctor = doctor;
	}

	public Set<Preference> getPreferences() {
		return preferences;
	}

	public void setPreferences(Set<Preference> preferences) {
		this.preferences = preferences;
	}

	public String getTaxIdNumber() {
		return taxIdNumber;
	}

	public void setTaxIdNumber(String taxIdNumber) {
		this.taxIdNumber = taxIdNumber;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}







}