package com.intellitech.birdnotes.model.request;


public class GoalItemRequest {
	private Integer value;
	private String activity;
	private Long productId;
	private Long potentialId;
	private Long sectorId;
	private Long specialityId;
	private Long prospectTypeId;

	
	public Integer getValue() {
		return value;
	}
	public void setValue(Integer value) {
		this.value = value;
	}
	public String getActivity() {
		return activity;
	}
	public void setActivity(String activity) {
		this.activity = activity;
	}
	public Long getProductId() {
		return productId;
	}
	public void setProductId(Long productId) {
		this.productId = productId;
	}
	
	public Long getPotentialId() {
		return potentialId;
	}
	public void setPotentialId(Long potentialId) {
		this.potentialId = potentialId;
	}
	public Long getSectorId() {
		return sectorId;
	}
	public void setSectorId(Long sectorId) {
		this.sectorId = sectorId;
	}
	public Long getSpecialityId() {
		return specialityId;
	}
	public void setSpecialityId(Long specialityId) {
		this.specialityId = specialityId;
	}
	public Long getProspectTypeId() {
		return prospectTypeId;
	}
	public void setProspectTypeId(Long prospectTypeId) {
		this.prospectTypeId = prospectTypeId;
	}


	
}
