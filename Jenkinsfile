node {
  stage('SCM') {
    checkout scm    
  }
  stage('SonarQube Analysis') {
    def mvn = tool 'Default Maven';
    withSonarQubeEnv() {
      sh "${mvn}/bin/mvn sonar:sonar   -Dsonar.projectKey=BirdNotes -Dsonar.login=****************************************"
    }

  }




  post {
        success {
            echo 'Pipeline passed!'
            script {
                slackSend(channel: "#bird-notes", message: "<PERSON> passed successfully")
            }
        }

        failure  {
           echo 'Pipeline failed!'
            script {
                slackSend(channel: "bird-notes", message: "<PERSON> job failed")
            }
        }
    }
}

  stage('notifyProductionDeploy') {
    // do stuff
    notifyProductionDeploy()
  }