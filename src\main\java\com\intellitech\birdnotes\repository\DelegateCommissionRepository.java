package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.DelegateCommission;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;

@Repository
public interface DelegateCommissionRepository extends JpaRepository<DelegateCommission, Long>{
	
	@Query("Select dc from DelegateCommission dc where (date(dc.month) between date(:firstDate) and date(:endDate)) "
			+ "and dc.user.id=:userId "
			+ "and dc.status <> 'VALID' "
			+ "and dc.commission.id=:commissionId")
public DelegateCommission findCommissionByUserAndDate (@Param("firstDate")  Date firstDate, @Param("endDate")  Date endDate ,
		@Param("userId") Long  userId ,@Param("commissionId") Long  commissionId);
	
}
