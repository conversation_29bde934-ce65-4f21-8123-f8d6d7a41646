package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.ActionMarketing;
import com.intellitech.birdnotes.model.DelegateCommission;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.PlanningValidation;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.ValidationStep;

@Repository
public interface ValidationStatusRepository extends JpaRepository<ValidationStatus, Long>{
	
	List <ValidationStatus> findByNoteFraisOrderByRankAsc(ExpenseReport noteFrais);
	List <ValidationStatus> findByPlanningValidationOrderByRankAsc(PlanningValidation planningValidation);
	List <ValidationStatus> findByActionMarketingOrderByRankAsc(ActionMarketing actionMarketing);
	List <ValidationStatus> findByProspectOrderByRankAsc(Prospect prospect);
	List <ValidationStatus> findByDelegateCommissionOrderByRankAsc(DelegateCommission delegateCommission);
	
	@Query ("SELECT v FROM ValidationStatus v where v.noteFrais IS NOT NULL")
	List <ValidationStatus> findUserValidationStatus ();
	
	
	@Modifying
	@Query("DELETE ValidationStatus v WHERE v.noteFrais.id = ?1")
	void deleteById(Long idNoteFrais);
	
	@Modifying
	@Query("DELETE ValidationStatus v WHERE v.prospect.id = ?1")
	void deleteByProspectId(Long idProspect);

	@Modifying
	@Query("DELETE ValidationStatus v WHERE v.actionMarketing.id = ?1")
	void deleteByActionMarketingId(Long id);
	
	@Modifying
	@Query("DELETE ValidationStatus v WHERE v.planningValidation.id = ?1")
	void deleteByPlanningValidation(Long id);

	@Modifying
	@Query("DELETE ValidationStatus v WHERE v.noteFrais.id = ?1")
	void deleteByExpenseValidation(Long id);
	
	ValidationStatus findByUser(User user);
	
	@Query ("SELECT v FROM ValidationStatus v where v.user.id in (?1) and v.validationStep.id in (?2)")
	List<ValidationStatus> findByUserIdsAndValidationStepIds(List<Long> userIds, List<Long> validationStepIds);
}
