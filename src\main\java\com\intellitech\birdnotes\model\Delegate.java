package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.PrimaryKeyJoinColumn;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import org.springframework.context.annotation.Lazy;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.enumeration.CarType;
import com.intellitech.birdnotes.enumeration.ContractType;
import com.intellitech.birdnotes.enumeration.WorkType;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;

@Entity
@Table(name = BirdnotesConstants.Tables.DELEGATE, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Delegate implements Serializable{

	private static final long serialVersionUID = 1L;
	private Long id;
	private String firstName;
	private String lastName;
	private Gender gender;
	private ContractType contractType;
	private Date birthdayDate;
	private String cin;
	private Date hiringDate;
	private WorkType workType;
	@Column(name = Columns.PHOTO)
	private String photo;
	private Date lastSyncro;
	private Set<ProspectsAffectation> prospectsAffectation = new HashSet<>();

	private Network network;
	private Short cycle;
	private String oneSignalUserId;
	private String mobileAppVersion;
	private List<SampleSupply> samplesSupply;
	private List<GiftSupply> gadgetsSupply;
	
	private Set<PurchaseOrderTemplate> purchaseOrderTemplates;
	
	private Set<ExpenseType> expenseTypes;
	private Integer workingDaysPerWeek;
	private CarType carType;
	private String carRegistration;
	private String image;
	private Double latitude;
	private Double longitude;
	private User user;
	public Delegate() {
		super();
	}

	@Id
	@SequenceGenerator(name = BirdnotesConstants.Sequences.DELEGATE_SEQUENCE, sequenceName = BirdnotesConstants.Sequences.DELEGATE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = BirdnotesConstants.Sequences.DELEGATE_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@Column(name = BirdnotesConstants.Columns.FIRST_NAME, length = BirdnotesConstants.Numbers.N_85)
	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}


	@Temporal(TemporalType.DATE)
	@Column(name = BirdnotesConstants.Columns.HIRING_DATE)
	public Date getHiringDate() {
		return hiringDate;
	}

	public void setHiringDate(Date hiringDate) {
		this.hiringDate = hiringDate;
	}

	@Column(name = BirdnotesConstants.Columns.LAST_NAME, length = BirdnotesConstants.Numbers.N_120)
	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

		
	@Column(name = BirdnotesConstants.Columns.IMAGE, length = BirdnotesConstants.Numbers.N_85)
	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}


	@Column(name = BirdnotesConstants.Columns.GENDER)
	public Gender getGender() {
		return gender;
	}

	public void setGender(Gender gender) {
		this.gender = gender;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = BirdnotesConstants.Columns.BIRTHDAY_DATE)
	public Date getBirthdayDate() {
		return birthdayDate;
	}

	public void setBirthdayDate(Date birthdayDate) {
		this.birthdayDate = birthdayDate;
	}

	@Column(name = BirdnotesConstants.Columns.CIN, length = 8)
	public String getCin() {
		return cin;
	}

	public void setCin(String cin) {
		this.cin = cin;
	}

	@JsonIgnore
	@OneToMany(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "delegate", targetEntity = ProspectsAffectation.class)
	public Set<ProspectsAffectation> getProspectsAffectation() {
		return prospectsAffectation;
	}

	public void setProspectsAffectation(Set<ProspectsAffectation> prospectsAffectation) {
		this.prospectsAffectation = prospectsAffectation;
	}
	

	@Column(name = BirdnotesConstants.Columns.CONTRACT_TYPE)
	public ContractType getContractType() {
		return contractType;
	}

	public void setContractType(ContractType contractType) {
		this.contractType = contractType;
	}
	
	@Column(name = BirdnotesConstants.Columns.CAR_TYPE)
	public CarType getCarType() {
		return carType;
	}

	public void setCarType(CarType carType) {
		this.carType = carType;
	}
	@Column(name = BirdnotesConstants.Columns.CAR_REGISTRATION)
	public String getCarRegistration() {
		return carRegistration;
	}

	public void setCarRegistration(String carRegistration) {
		this.carRegistration = carRegistration;
	}
	
	@Column(name = BirdnotesConstants.Columns.WORK_TYPE)
	public WorkType getWorkType() {
		return workType;
	}

	
	public void setWorkType(WorkType workType) {
		this.workType = workType;
	}
	
	@Column(name = BirdnotesConstants.Columns.WEEK_WORKING_DAYS)
	public Integer getWorkingDaysPerWeek() {
		return workingDaysPerWeek;
	}

	public void setWorkingDaysPerWeek(Integer workingDaysPerWeek) {
		this.workingDaysPerWeek = workingDaysPerWeek;
	}


	public Date getLastSyncro() {
		return lastSyncro;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = BirdnotesConstants.Columns.LAST_SYNCRO)
	public void setLastSyncro(Date lastSyncro) {
		this.lastSyncro = lastSyncro;
	}

	@ManyToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.NETWORK_ID)

	public Network getNetwork() {
		return network;
	}

	public void setNetwork(Network network) {
		this.network = network;
	}

	

	public Short getCycle() {
		return cycle;
	}

	public void setCycle(Short cycle) {
		this.cycle = cycle;
	}

	@Column(name = BirdnotesConstants.Columns.ONESIGNAL_USER_ID)
	public String getOneSignalUserId() {
		return oneSignalUserId;
	}

	public void setOneSignalUserId(String oneSignalUserId) {
		this.oneSignalUserId = oneSignalUserId;
	}

	@Column(name = BirdnotesConstants.Columns.MOBILE_APP_VERSION)
	public String getMobileAppVersion() {
		return mobileAppVersion;
	}

	public void setMobileAppVersion(String mobileAppVersion) {
		this.mobileAppVersion = mobileAppVersion;
	}


	@JsonIgnore
	@OneToMany(mappedBy = "delegate")
	public List<SampleSupply> getSamplesSupply() {
		return samplesSupply;
	}

	public void setSamplesSupply(List<SampleSupply> samplesSupply) {
		this.samplesSupply = samplesSupply;
	}
	
	@JsonIgnore
	@OneToMany(mappedBy = "delegate")
	public List<GiftSupply> getGadgetsSupply() {
		return gadgetsSupply;
	}

	public void setGadgetsSupply(List<GiftSupply> gadgetsSupply) {
		this.gadgetsSupply = gadgetsSupply;
	}
	
	@ManyToMany(mappedBy = "delegates")
	public Set<PurchaseOrderTemplate> getPurchaseOrderTemplates() {
		return purchaseOrderTemplates;
	}

	public void setPurchaseOrderTemplates(Set<PurchaseOrderTemplate> purchaseOrderTemplates) {
		this.purchaseOrderTemplates = purchaseOrderTemplates;
	}
	
	

	@ManyToMany(mappedBy = "delegates")
	public Set<ExpenseType> getExpenseTypes() {
		return expenseTypes;
	}

	public void setExpenseTypes(Set<ExpenseType> expenseTypes) {
		this.expenseTypes = expenseTypes;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}
	
	@Column(name = BirdnotesConstants.Columns.LATITUDE)
	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	@Column(name = BirdnotesConstants.Columns.LONGITUDE)
	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}
	
	@JsonIgnore
	@Lazy
	@OneToOne(optional = true)
	@JoinColumn(name = Columns.USER_ID)
	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}
	
	
}