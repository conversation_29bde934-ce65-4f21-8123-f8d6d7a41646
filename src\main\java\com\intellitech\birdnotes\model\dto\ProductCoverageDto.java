package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class ProductCoverageDto implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private String userName;
	private Long orderQuantity;
	private Long sampleQuantity;
	private Long id;
	
	public ProductCoverageDto(String firstName, String lastName, Long id, Long orderQuantity, Long sampleQuantity) {
		this.userName = firstName+" "+lastName;
		this.id = id;
		this.orderQuantity = orderQuantity;
		this.sampleQuantity = sampleQuantity;
	}
	
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Long getOrderQuantity() {
		return orderQuantity;
	}
	public void setOrderQuantity(Long orderQuantity) {
		this.orderQuantity = orderQuantity;
	}
	public Long getSampleQuantity() {
		return sampleQuantity;
	}
	public void setSampleQuantity(Long sampleQuantity) {
		this.sampleQuantity = sampleQuantity;
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
}