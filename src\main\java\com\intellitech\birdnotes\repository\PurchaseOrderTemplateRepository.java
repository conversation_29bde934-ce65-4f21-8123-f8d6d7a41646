package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.Sector;

@Repository
public interface PurchaseOrderTemplateRepository extends JpaRepository<PurchaseOrderTemplate, Long>{
	
	
	@Query("SELECT  pot from PurchaseOrderTemplate pot join pot.delegates u  where u.id =:id ")
	Set<PurchaseOrderTemplate> findPurchaseOrderTemplatesOfUser(@Param("id") Long id);
	
	@Query("SELECT pot FROM PurchaseOrderTemplate pot WHERE LOWER(pot.name) = LOWER(?1) AND pot.id != ?2")
	PurchaseOrderTemplate findByNameAndAnotherId(String name, Long id);
	

}
