package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.RolePermission;

@Repository
public interface RolePermissionRepository extends JpaRepository<RolePermission,Long>{
	
	List<RolePermission> findByPermissionName(String permissionName);

}
