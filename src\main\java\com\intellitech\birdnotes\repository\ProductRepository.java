package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.ProductCoverageDto;

@Repository
public interface ProductRepository extends JpaRepository<Product, Long> {

	Product findByName(String name);

	Product findById(Long id);

	@Query("SELECT p from Product p WHERE p.name=:name AND p.id!=:id order by p.name")
	Product findByNameWithDiffId(@Param("name") String name, @Param("id") Long id);

	@Override
	@Query("SELECT p from Product p order by p.name")
	List<Product> findAll();

	@Query("SELECT distinct p from Product p join p.ranges pg join pg.users u where u.id =:userId order by p.name")
	List<Product> findByUserRange(@Param("userId") Long userId);

	@Query("SELECT " + "new com.intellitech.birdnotes.model.dto.ProductCoverageDto"
			+ "(vp.visit.delegate.firstName, vp.visit.delegate.lastName, vp.visit.delegate.id, SUM(vp.orderQuantity), SUM(vp.sampleQuantity))"
			+ " FROM VisitsProducts vp " + "WHERE (DATE(vp.visit.visitDate) BETWEEN DATE(:firstDay) AND DATE(:lastDay))"
			+ " AND  (vp.product.id = :productId)"
			+ " GROUP BY vp.visit.delegate.firstName, vp.visit.delegate.lastName, vp.visit.delegate.id")
	List<ProductCoverageDto> getProductCoverage(@Param("firstDay") Date firstDay, @Param("lastDay") Date lastDay,
			@Param("productId") Long productId);

	@Query("SELECT p.id from Product p WHERE p.id in (:products) order by p.name")
	List<Long> findWhereIdIn(@Param("products") List<Long> products);

	@Query("SELECT p.name from Product p where  p.id=:productId")
	String findProductName(@Param("productId") Long i);

	@Query("SELECT p.name from Product p ")
	List<String> findAllProductName();

	@Query("SELECT p from Product p WHERE p.id in (:id)")
	Set<Product> findProducts(@Param("id") List<Long> id);

	@Query("SELECT p from Product p WHERE p.code is not null AND p.code <> '' ")
	List<Product> getPoductCodes();

	@Query("SELECT p from Product p join p.commissions com where com.id =:id ")
	Set<Product> findProductOfCommission(@Param("id") Long id);

	/*
	 * @Query("select distinct pro.id from FreeQuantityRule f join f.products pro join f.prospectTypes pt where pt.id in :prospectTypeList"
	 * ) List<Product> findProductWithRule(@Param("prospectTypeList") List<Long>
	 * prospectTypeList);
	 */

	@Query("SELECT  p from Product p where p.id not in (select pro.id from FreeQuantityRule f join f.products pro join f.prospectTypes pt where pt.id in :prospectTypeList) order by p.name")
	List<Product> findProductWithoutRuleWithType(@Param("prospectTypeList") List<Long> prospectTypeList);

	@Query("SELECT  p from Product p where p.id not in (select pro.id from FreeQuantityRule f join f.products pro) order by p.name")
	List<Product> findProductWithoutRule();

	@Query("SELECT p from Product p where p.id not in (select d.product.id from Discount d)")
	List<Product> findProductWithoutDiscount();

	@Query("SELECT p.id from Product p join p.ranges pg WHERE pg.id in (:rangeIds)  ")
	List<Long> findProductByRange(@Param("rangeIds") List<Integer> rangeIds);
	
	@Query("SELECT p FROM Product p WHERE LOWER(p.name) = LOWER(?1) AND p.id != ?2")
	Product findByNameAndAnotherId(String name, Long id);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.LabelValueDto(p.name, p.id) FROM Product p ORDER BY p.name ASC")
	List<LabelValueDto> getAllProductIdAndName();


}
