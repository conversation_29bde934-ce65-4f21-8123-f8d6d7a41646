package com.intellitech.birdnotes.controller;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Locale;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.web.bind.annotation.RequestHeader;



import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Locality;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.LocalityRequestDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.service.DownloadDataService;
import com.intellitech.birdnotes.service.LocalityService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/localities")
public class LocalityController {

	private static final Logger LOG = LoggerFactory.getLogger(LocalityController.class);

	@Autowired
	private LocalityService localityService;
	@Autowired
	UserService userService;
	@Autowired
	private SectorService sectorService;
	@Autowired
	private DownloadDataService downloadDataService;

	@RequestMapping(value = "findalllocalities", method = RequestMethod.GET)
	public ResponseEntity<List<LocalityDto>> findAllLocalities() {
		try {
			if (userService.checkHasPermission("LOCALITY_VIEW")) {
				List<LocalityDto> localityDtos = localityService.findAll();
				return new ResponseEntity<>(localityDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (Exception e) {
			LOG.error("An exception occurred while getting all localities", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
	
	@RequestMapping(value = "/getAllDataForLocality", method = RequestMethod.GET)
	public ResponseEntity<Map<String, Object>> getAllDataForLocality() {
		try {
			if (userService.checkHasPermission("LOCALITY_VIEW")) {
				return new ResponseEntity<>(downloadDataService.findAllDataForLocality(), HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data for the list of locality", e);
			return new ResponseEntity<>(Collections.emptyMap(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}


	@RequestMapping(value = "/addAll", method = RequestMethod.POST)
	public ResponseEntity<String> addAllLocalities(@RequestBody List<LocalityRequestDto> localityRequestDtos) {
		try {
			localityService.saveAllLocalities(localityRequestDtos);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add Localities", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding localities", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseEntity<String> addLocality(@RequestBody LocalityRequestDto localityRequestDto) {
		try {
			if (userService.checkHasPermission("LOCALITY_ADD")) {
				localityService.add(localityRequestDto);
				return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(Exceptions.OK, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to add Locality", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new locality", e);
			return new ResponseEntity<>(Exceptions.KO, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findBySectorId/{sectorid}", method = RequestMethod.GET)
	public ResponseEntity<List<LocalityDto>> findBySector(@PathVariable("sectorid") Long sectorId) {
		try {
			if (userService.checkHasPermission("LOCALITY_VIEW")) {
				List<LocalityDto> localities = localityService.findBySector(sectorId);
				return new ResponseEntity<>(localities, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred :Non-Authoritative Information to find locality by sector", e);
			return new ResponseEntity<>(new ArrayList<LocalityDto>(), HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while adding a new locality", e);
			return new ResponseEntity<>(new ArrayList<LocalityDto>(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> deleteLocality(@PathVariable("id") Long id) {
	    try {
	        if (userService.checkHasPermission("LOCALITY_DELETE")) {
	            localityService.delete(id);
	            return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK); // 200
	        } else {
	            return new ResponseEntity<>(Exceptions.LOCALITY_TO_DELETE_ALREADY_DELETED, 
	                    HttpStatus.NON_AUTHORITATIVE_INFORMATION); // 203
	        }
	    } catch (DataIntegrityViolationException e) {
	        LOG.error("A DataIntegrityViolationException occurred when deleting locality", e);
	        return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while deleting the locality with id =" + id, e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}




	@RequestMapping(value = "saveLocality", method = RequestMethod.POST)
	public ResponseEntity<String> saveLocality(@RequestBody LocalityDto localityDto) {

	    try {
	        if (userService.checkHasPermission("LOCALITY_EDIT") || userService.checkHasPermission("LOCALITY_ADD")) {
	            Locality savedLocality = localityService.saveLocality(localityDto);
	            if (savedLocality != null) {
	                return new ResponseEntity<>(savedLocality.getId().toString(), HttpStatus.OK);
	            }
	            return new ResponseEntity<>(null, HttpStatus.OK);
	        } else {
	            return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
	        }

	    } catch (BirdnotesException e) {
	        LOG.error("An exception occurred when saving locality", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    } catch (Exception e) {
	        LOG.error("An exception occurred while saving locality", e);
	        return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
	    }
	}

	
	@RequestMapping(value = "findallsectors", method = RequestMethod.GET)
	public ResponseEntity<List<SectorDto>> findallSectors() {
		try {
			if (userService.checkHasPermission("LOCALITY_EDIT")) {
				List<SectorDto> sectorDtos = sectorService.findAll();
				return new ResponseEntity<>(sectorDtos, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all sectors", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

}
