package com.intellitech.birdnotes.enumeration;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.springframework.context.MessageSource;

import com.intellitech.birdnotes.model.dto.ValueTypeDto;
import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum GroupType {

	DELEGUE(1, "DELEGUE"), 
	PROSPECT(2, "PROSPECT"), 
	SECTOR(3,"SECTOR"), 
	LOCALITY(4, "LOCALITY"), 
	ACTIVITY(5,"ACTIVITY"), 
	POTENTIAL(6, "POTENTIAL"), 
	SPECIALITY(7, "SPECIALITY"),
	PRODUCT(8, "PRODUCT");
	//SATISFACTION (9, BirdnotesConstants.GroupType.SATISFACTION),
	//PRESENTATION_ORDER (9, BirdnotesConstants.GroupType.PRESENTATION_ORDER);
	private String type;
	private Integer id;

	private GroupType(int id, String type) {
		this.id = id;
		this.type = type;
	}

	public Integer getId() {
		return id;
	}

	public String getType() {
		return type;
	}

	@Override
	public String toString() {
		String groupType = "";
		if (DELEGUE.type.equals(type)) {
			groupType = "DELEGUE";
		} else if (PROSPECT.type.equals(type)) {
			groupType = "PROSPECT";
		} else if (SECTOR.type.equals(type)) {
			groupType = "SECTOR";
		} else if (LOCALITY.type.equals(type)) {
			groupType = "LOCALITY";
		} else if (ACTIVITY.type.equals(type)) {
			groupType = "ACTIVITY";
		} else if (POTENTIAL.type.equals(type)) {
			groupType = "POTENTIAL";
		} else if (SPECIALITY.type.equals(type)) {
			groupType = "SPECIALITY";
		}else if (PRODUCT.type.equals(type)) {
			groupType = "PRODUCT";
			
		} 
		return groupType;
	}

	public static List<ValueTypeDto> getAllGroupType(MessageSource messageSource, Locale locale) {
		List<ValueTypeDto> groupType = new ArrayList<>();
		for (GroupType value : values()) {
			ValueTypeDto groupTypeDto = new ValueTypeDto();
			groupTypeDto.setId(value.id);
			groupTypeDto.setName(messageSource.getMessage(value.toString(), null, locale));
			groupType.add(groupTypeDto);
		}
		return groupType;
	}

	public static List<ValueTypeDto> getAllGroupTypeToCoverage(MessageSource messageSource, Locale locale) {
		List<ValueTypeDto> groupType = new ArrayList<>(values().length);
		for (GroupType value : values()) {
			if ((value.toString() != GroupType.PROSPECT.getType())
					&& (value.toString() != GroupType.LOCALITY.getType())&& (value.toString() != GroupType.PRODUCT.getType()) ) {
				ValueTypeDto groupTypeDto = new ValueTypeDto();
				groupTypeDto.setId(value.getId());
				groupTypeDto.setName(messageSource.getMessage(value.toString(), null, locale));
				groupType.add(groupTypeDto);
			}
		}
		return groupType;
	}
	public static List<ValueTypeDto> getAllGroupTypeToProspectDistribution(MessageSource messageSource, Locale locale) {
		List<ValueTypeDto> groupType = new ArrayList<>();
		for (GroupType value : values()) {
			if ((value.toString() != GroupType.PROSPECT.getType())
					&& (value.toString() != GroupType.PRODUCT.getType()) ) {
				ValueTypeDto groupTypeDto = new ValueTypeDto();
				groupTypeDto.setId(value.getId());
				String vv = value.toString();
				groupTypeDto.setName(messageSource.getMessage(value.toString(), null, locale));
				groupType.add(groupTypeDto);
			}
		}
		return groupType;
	}
}
