package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.SAMPLE_SUPPLY_ITEM, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class SampleSupplyItem implements Serializable {
	private static final long serialVersionUID = 1L;

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public SampleSupplyItem() {
		super();

	}

	// Ajouté pour le test
	public SampleSupplyItem(Long id, Product product, Delegate delegate, Long quantity, Date deliveryDate, Date dateManufacture,
			Date expirationDate, Long batchNumber) {
		super();
		this.id = id;
		this.product = product;
		this.quantity = quantity;
		this.dateManufacture = dateManufacture;
		this.expirationDate = expirationDate;
		this.batchNumber = batchNumber;

	}

	@Id
	@SequenceGenerator(name = Sequences.SAMPLE_SUPPLY_ITEM_SEQUENCE, sequenceName = Sequences.SAMPLE_SUPPLY_ITEM_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.SAMPLE_SUPPLY_ITEM_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	@JsonIgnore
	@OneToOne(fetch = FetchType.EAGER, optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID)
	private Product product;

	public Product getProduct() {
		return product;
	}

	public void setProduct(Product product) {
		this.product = product;
	}
	
	
	@JsonIgnore
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = BirdnotesConstants.Columns.SAMPLE_SUPPLLY_ID)
	private SampleSupply sampleSupply;

	public SampleSupply getSampleSupply() {
		return sampleSupply;
	}

	public void setSampleSupply(SampleSupply sampleSupply) {
		this.sampleSupply = sampleSupply;
	}

	@Column(name = Columns.QUANTITY)
	private Long quantity;

	public Long getQuantity() {
		return quantity;
	}

	public void setQuantity(Long quantity) {
		this.quantity = quantity;
	}


	@Temporal(TemporalType.DATE)
	@Column(name = Columns.DATE_MANUFACTURE)
	private Date dateManufacture;

	public Date getDateManufacture() {
		return dateManufacture;
	}

	public void setDateManufacture(Date dateManufacture) {
		this.dateManufacture = dateManufacture;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.EXPIRATION_DATE)
	private Date expirationDate;

	public Date getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(Date expirationDate) {
		this.expirationDate = expirationDate;
	}

	@Column(name = Columns.BATCH_NUMBER)
	private Long batchNumber;

	public Long getBatchNumber() {
		return batchNumber;
	}

	public void setBatchNumber(Long batchNumber) {
		this.batchNumber = batchNumber;
	}

}
