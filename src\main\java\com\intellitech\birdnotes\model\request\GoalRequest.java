package com.intellitech.birdnotes.model.request;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.enumeration.GoalType;


public class GoalRequest {
	private Long id;

	private String name;
	
	private String goalType;
	
	private String period;

	private Date firstDate;
	
	private Date lastDate;
	
	private String itemsOrder;

	private Set<Long> userIds;
	
	private List<GoalItemRequest> goalItems;
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	
	public String getItemsOrder() {
		return itemsOrder;
	}

	public void setItemsOrder(String itemsOrder) {
		this.itemsOrder = itemsOrder;
	}

	public Set<Long> getUserIds() {
		return userIds;
	}

	public void setUserIds(Set<Long> userIds) {
		this.userIds = userIds;
	}

	public List<GoalItemRequest> getGoalItems() {
		return goalItems;
	}

	public void setGoalItems(List<GoalItemRequest> goalItems) {
		this.goalItems = goalItems;
	}

	public Date getFirstDate() {
		return firstDate;
	}

	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}

	public Date getLastDate() {
		return lastDate;
	}

	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}
	
	public String getGoalType() {
		return goalType;
	}

	public void setGoalType(String goalType) {
		this.goalType = goalType;
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

}
