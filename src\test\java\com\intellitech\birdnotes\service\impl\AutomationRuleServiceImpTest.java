package com.intellitech.birdnotes.service.impl;

import com.intellitech.birdnotes.dao.XmlReader;
import com.intellitech.birdnotes.data.dto.AutomationRuleFormData;
import com.intellitech.birdnotes.enumeration.Periodicity;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.*;
import com.intellitech.birdnotes.model.dto.ActivityTypeDto;
import com.intellitech.birdnotes.model.dto.AutomationRuleDto;
import com.intellitech.birdnotes.repository.*;
import com.intellitech.birdnotes.service.ActivityTypeService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.core.io.ResourceLoader;

import java.io.IOException;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AutomationRuleServiceImpTest {

    @Mock
    private AutomationRuleRepository mockAutomationRuleRepository;
    @Mock
    private ActivityTypeRepository mockActivityTypeRepository;
    @Mock
    private PlanningValidationRepository mockPlanningValidationRepository;
    @Mock
    private PlanningRepository mockPlanningRepository;
    @Mock
    private ActivityRepository mockActivityRepository;
    @Mock
    private ActivityTypeService mockActivityTypeService;
    @Mock
    private XmlReader mockXmlReader;
    @Mock
    private ResourceLoader mockResourceLoader;

    private AutomationRuleServiceImp automationRuleServiceImpUnderTest;

    @Before
    public void setUp() throws Exception {
        automationRuleServiceImpUnderTest = new AutomationRuleServiceImp(mockAutomationRuleRepository,
                mockActivityTypeRepository, mockPlanningValidationRepository, mockPlanningRepository,
                mockActivityRepository, mockActivityTypeService, mockXmlReader);
        automationRuleServiceImpUnderTest.resourceLoader = mockResourceLoader;
    }

    @Test
    public void testSaveAutomationRule() throws Exception {
        // Setup
        final AutomationRuleDto automationRuleDto = new AutomationRuleDto();
        automationRuleDto.setId(0L);
        automationRuleDto.setEventType("eventType");
        automationRuleDto.setActionType("actionType");
        automationRuleDto.setActivityTypeId(0L);
        automationRuleDto.setActivityTypeName("activityTypeName");
        automationRuleDto.setPeriodicity(Periodicity.ONCE);
        automationRuleDto.setRepetationEach(0);
        automationRuleDto.setStartAfter(0.0f);
        automationRuleDto.setRepeatingPeriod(0);
        automationRuleDto.setActivityTypeEvent(0L);

        // Configure AutomationRuleRepository.findOne(...).
        final AutomationRule automationRule = new AutomationRule();
        automationRule.setId(0L);
        automationRule.setEventType("eventType");
        automationRule.setActionType("actionType");
        final ActivityType activityType = new ActivityType();
        activityType.setId(0L);
        activityType.setName("activityTypeName");
        automationRule.setActivityType(activityType);
        automationRule.setPeriodicity(Periodicity.ONCE);
        automationRule.setStartAfter(0.0f);
        automationRule.setRepetationEach(0);
        automationRule.setRepeatingPeriod(0);
        final ActivityType activityTypeEvent = new ActivityType();
        activityTypeEvent.setId(0L);
        activityTypeEvent.setName("activityTypeName");
        automationRule.setActivityTypeEvent(activityTypeEvent);
        when(mockAutomationRuleRepository.findOne(0L)).thenReturn(automationRule);

        when(mockActivityTypeRepository.findOne(0L)).thenReturn(new ActivityType(0L, "activityTypeName"));

        // Configure AutomationRuleRepository.save(...).
        final AutomationRule automationRule1 = new AutomationRule();
        automationRule1.setId(0L);
        automationRule1.setEventType("eventType");
        automationRule1.setActionType("actionType");
        final ActivityType activityType1 = new ActivityType();
        activityType1.setId(0L);
        activityType1.setName("activityTypeName");
        automationRule1.setActivityType(activityType1);
        automationRule1.setPeriodicity(Periodicity.ONCE);
        automationRule1.setStartAfter(0.0f);
        automationRule1.setRepetationEach(0);
        automationRule1.setRepeatingPeriod(0);
        final ActivityType activityTypeEvent1 = new ActivityType();
        activityTypeEvent1.setId(0L);
        activityTypeEvent1.setName("activityTypeName");
        automationRule1.setActivityTypeEvent(activityTypeEvent1);
        when(mockAutomationRuleRepository.save(any(AutomationRule.class))).thenReturn(automationRule1);

        // Run the test
        final AutomationRule result = automationRuleServiceImpUnderTest.saveAutomationRule(automationRuleDto);

        // Verify the results
    }

    @Test
    public void testGeneratePlanningsFromRules() {
        // Setup
        final AutomationRule automationRule = new AutomationRule();
        automationRule.setId(0L);
        automationRule.setEventType("eventType");
        automationRule.setActionType("actionType");
        final ActivityType activityType = new ActivityType();
        activityType.setId(0L);
        activityType.setName("activityTypeName");
        automationRule.setActivityType(activityType);
        automationRule.setPeriodicity(Periodicity.ONCE);
        automationRule.setStartAfter(0.0f);
        automationRule.setRepetationEach(0);
        automationRule.setRepeatingPeriod(0);
        final ActivityType activityTypeEvent = new ActivityType();
        activityTypeEvent.setId(0L);
        activityTypeEvent.setName("activityTypeName");
        automationRule.setActivityTypeEvent(activityTypeEvent);
        final List<AutomationRule> automationRuleList = Arrays.asList(automationRule);
        final Activity activity = new Activity();
        activity.setIdentifier(0L);
        activity.setActivityDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activity.setHourNumber(0L);
        final ActivityType activityType1 = new ActivityType();
        activityType1.setId(0L);
        activityType1.setName("activityTypeName");
        activity.setActivityType(activityType1);
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setWorkingDaysPerWeek(1);
        activity.setDelegate(delegate);
        final Planning planning = new Planning();
        planning.setIdentifier(0L);
        final Prospect prospect = new Prospect();
        planning.setProspect(prospect);
        final Delegate delegate1 = new Delegate();
        delegate1.setId(0L);
        delegate1.setWorkingDaysPerWeek(1);
        planning.setDelegate(delegate1);
        planning.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activity.setPlanning(planning);
        final Prospect prospect1 = new Prospect();
        activity.setProspect(prospect1);

        // Configure PlanningValidationRepository.findByUserAndDate(...).
        final PlanningValidation planningValidation = new PlanningValidation();
        planningValidation.setIdentifier(0L);
        final Delegate delegate2 = new Delegate();
        delegate2.setId(0L);
        delegate2.setWorkingDaysPerWeek(1);
        planningValidation.setDelegate(delegate2);
        planningValidation.setSpecificWeek(false);
        planningValidation.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        planningValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
        when(mockPlanningValidationRepository.findByUserAndDate(0L,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(planningValidation);

        // Run the test
        automationRuleServiceImpUnderTest.generatePlanningsFromRules(automationRuleList, activity);

        // Verify the results
        verify(mockActivityRepository).save(any(Activity.class));
        verify(mockPlanningRepository).save(any(Planning.class));
        verify(mockPlanningValidationRepository).save(any(PlanningValidation.class));
    }

    @Test
    public void testGeneratePlanningsFromRules_PlanningValidationRepositoryFindByUserAndDateReturnsNull() {
        // Setup
        final AutomationRule automationRule = new AutomationRule();
        automationRule.setId(0L);
        automationRule.setEventType("eventType");
        automationRule.setActionType("actionType");
        final ActivityType activityType = new ActivityType();
        activityType.setId(0L);
        activityType.setName("activityTypeName");
        automationRule.setActivityType(activityType);
        automationRule.setPeriodicity(Periodicity.ONCE);
        automationRule.setStartAfter(0.0f);
        automationRule.setRepetationEach(0);
        automationRule.setRepeatingPeriod(0);
        final ActivityType activityTypeEvent = new ActivityType();
        activityTypeEvent.setId(0L);
        activityTypeEvent.setName("activityTypeName");
        automationRule.setActivityTypeEvent(activityTypeEvent);
        final List<AutomationRule> automationRuleList = Arrays.asList(automationRule);
        final Activity activity = new Activity();
        activity.setIdentifier(0L);
        activity.setActivityDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activity.setHourNumber(0L);
        final ActivityType activityType1 = new ActivityType();
        activityType1.setId(0L);
        activityType1.setName("activityTypeName");
        activity.setActivityType(activityType1);
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setWorkingDaysPerWeek(1);
        activity.setDelegate(delegate);
        final Planning planning = new Planning();
        planning.setIdentifier(0L);
        final Prospect prospect = new Prospect();
        planning.setProspect(prospect);
        final Delegate delegate1 = new Delegate();
        delegate1.setId(0L);
        delegate1.setWorkingDaysPerWeek(1);
        planning.setDelegate(delegate1);
        planning.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activity.setPlanning(planning);
        final Prospect prospect1 = new Prospect();
        activity.setProspect(prospect1);

        when(mockPlanningValidationRepository.findByUserAndDate(0L,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Run the test
        automationRuleServiceImpUnderTest.generatePlanningsFromRules(automationRuleList, activity);

        // Verify the results
        verify(mockActivityRepository).save(any(Activity.class));
        verify(mockPlanningRepository).save(any(Planning.class));
        verify(mockPlanningValidationRepository).save(any(PlanningValidation.class));
    }

    @Test
    public void testCreatePlanning() {
        // Setup
        final Activity activityEvent = new Activity();
        activityEvent.setIdentifier(0L);
        activityEvent.setActivityDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activityEvent.setHourNumber(0L);
        final ActivityType activityType = new ActivityType();
        activityType.setId(0L);
        activityType.setName("activityTypeName");
        activityEvent.setActivityType(activityType);
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setWorkingDaysPerWeek(1);
        activityEvent.setDelegate(delegate);
        final Planning planning = new Planning();
        planning.setIdentifier(0L);
        final Prospect prospect = new Prospect();
        planning.setProspect(prospect);
        final Delegate delegate1 = new Delegate();
        delegate1.setId(0L);
        delegate1.setWorkingDaysPerWeek(1);
        planning.setDelegate(delegate1);
        planning.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activityEvent.setPlanning(planning);
        final Prospect prospect1 = new Prospect();
        activityEvent.setProspect(prospect1);

        final ActivityType activityType1 = new ActivityType(0L, "activityTypeName");

        // Configure PlanningValidationRepository.findByUserAndDate(...).
        final PlanningValidation planningValidation = new PlanningValidation();
        planningValidation.setIdentifier(0L);
        final Delegate delegate2 = new Delegate();
        delegate2.setId(0L);
        delegate2.setWorkingDaysPerWeek(1);
        planningValidation.setDelegate(delegate2);
        planningValidation.setSpecificWeek(false);
        planningValidation.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        planningValidation.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
        when(mockPlanningValidationRepository.findByUserAndDate(0L,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(planningValidation);

        // Run the test
        final Planning result = automationRuleServiceImpUnderTest.createPlanning(activityEvent, activityType1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockActivityRepository).save(any(Activity.class));
        verify(mockPlanningRepository).save(any(Planning.class));
    }

    @Test
    public void testCreatePlanning_PlanningValidationRepositoryFindByUserAndDateReturnsNull() {
        // Setup
        final Activity activityEvent = new Activity();
        activityEvent.setIdentifier(0L);
        activityEvent.setActivityDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activityEvent.setHourNumber(0L);
        final ActivityType activityType = new ActivityType();
        activityType.setId(0L);
        activityType.setName("activityTypeName");
        activityEvent.setActivityType(activityType);
        final Delegate delegate = new Delegate();
        delegate.setId(0L);
        delegate.setWorkingDaysPerWeek(1);
        activityEvent.setDelegate(delegate);
        final Planning planning = new Planning();
        planning.setIdentifier(0L);
        final Prospect prospect = new Prospect();
        planning.setProspect(prospect);
        final Delegate delegate1 = new Delegate();
        delegate1.setId(0L);
        delegate1.setWorkingDaysPerWeek(1);
        planning.setDelegate(delegate1);
        planning.setDate(new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());
        activityEvent.setPlanning(planning);
        final Prospect prospect1 = new Prospect();
        activityEvent.setProspect(prospect1);

        final ActivityType activityType1 = new ActivityType(0L, "activityTypeName");
        when(mockPlanningValidationRepository.findByUserAndDate(0L,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime())).thenReturn(null);

        // Run the test
        final Planning result = automationRuleServiceImpUnderTest.createPlanning(activityEvent, activityType1,
                new GregorianCalendar(2020, Calendar.JANUARY, 1).getTime());

        // Verify the results
        verify(mockActivityRepository).save(any(Activity.class));
        verify(mockPlanningRepository).save(any(Planning.class));
        verify(mockPlanningValidationRepository).save(any(PlanningValidation.class));
    }

    @Test
    public void testFindAll() throws Exception {
        // Setup
        // Configure AutomationRuleRepository.findAll(...).
        final AutomationRule automationRule = new AutomationRule();
        automationRule.setId(0L);
        automationRule.setEventType("eventType");
        automationRule.setActionType("actionType");
        final ActivityType activityType = new ActivityType();
        activityType.setId(0L);
        activityType.setName("activityTypeName");
        automationRule.setActivityType(activityType);
        automationRule.setPeriodicity(Periodicity.ONCE);
        automationRule.setStartAfter(0.0f);
        automationRule.setRepetationEach(0);
        automationRule.setRepeatingPeriod(0);
        final ActivityType activityTypeEvent = new ActivityType();
        activityTypeEvent.setId(0L);
        activityTypeEvent.setName("activityTypeName");
        automationRule.setActivityTypeEvent(activityTypeEvent);
        final List<AutomationRule> automationRules = Arrays.asList(automationRule);
        when(mockAutomationRuleRepository.findAll()).thenReturn(automationRules);

        // Run the test
        final List<AutomationRuleDto> result = automationRuleServiceImpUnderTest.findAll();

        // Verify the results
    }

    @Test
    public void testFindAll_AutomationRuleRepositoryReturnsNull() throws Exception {
        // Setup
        when(mockAutomationRuleRepository.findAll()).thenReturn(null);

        // Run the test
        final List<AutomationRuleDto> result = automationRuleServiceImpUnderTest.findAll();

        // Verify the results
    }

    @Test
    public void testFindAll_AutomationRuleRepositoryReturnsNoItems() throws Exception {
        // Setup
        when(mockAutomationRuleRepository.findAll()).thenReturn(Collections.emptyList());

        // Run the test
        final List<AutomationRuleDto> result = automationRuleServiceImpUnderTest.findAll();

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    public void testGetAllDataAutomationRuleForm() throws Exception {
        // Setup
        // Configure XmlReader.getEventXml(...).
        final List<Event> events = Arrays.asList(new Event("name", "label"));
        when(mockXmlReader.getEventXml()).thenReturn(events);

        // Configure XmlReader.getActionXml(...).
        final List<Action> actions = Arrays.asList(new Action("name", "label"));
        when(mockXmlReader.getActionXml()).thenReturn(actions);

        // Configure ActivityTypeService.findAll(...).
        final List<ActivityTypeDto> activityTypeDtos = Arrays.asList(new ActivityTypeDto("name", 0L));
        when(mockActivityTypeService.findAll()).thenReturn(activityTypeDtos);

        // Run the test
        final AutomationRuleFormData result = automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm();

        // Verify the results
    }

    @Test
    public void testGetAllDataAutomationRuleForm_XmlReaderGetEventXmlReturnsNoItems() throws Exception {
        // Setup
        when(mockXmlReader.getEventXml()).thenReturn(Collections.emptyList());

        // Configure XmlReader.getActionXml(...).
        final List<Action> actions = Arrays.asList(new Action("name", "label"));
        when(mockXmlReader.getActionXml()).thenReturn(actions);

        // Configure ActivityTypeService.findAll(...).
        final List<ActivityTypeDto> activityTypeDtos = Arrays.asList(new ActivityTypeDto("name", 0L));
        when(mockActivityTypeService.findAll()).thenReturn(activityTypeDtos);

        // Run the test
        final AutomationRuleFormData result = automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm();

        // Verify the results
    }

    @Test
    public void testGetAllDataAutomationRuleForm_XmlReaderGetEventXmlThrowsIOException() throws Exception {
        // Setup
        when(mockXmlReader.getEventXml()).thenThrow(IOException.class);

        // Run the test
        assertThatThrownBy(() -> automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm())
                .isInstanceOf(IOException.class);
    }

    @Test
    public void testGetAllDataAutomationRuleForm_XmlReaderGetActionXmlReturnsNoItems() throws Exception {
        // Setup
        // Configure XmlReader.getEventXml(...).
        final List<Event> events = Arrays.asList(new Event("name", "label"));
        when(mockXmlReader.getEventXml()).thenReturn(events);

        when(mockXmlReader.getActionXml()).thenReturn(Collections.emptyList());

        // Configure ActivityTypeService.findAll(...).
        final List<ActivityTypeDto> activityTypeDtos = Arrays.asList(new ActivityTypeDto("name", 0L));
        when(mockActivityTypeService.findAll()).thenReturn(activityTypeDtos);

        // Run the test
        final AutomationRuleFormData result = automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm();

        // Verify the results
    }

    @Test
    public void testGetAllDataAutomationRuleForm_XmlReaderGetActionXmlThrowsIOException() throws Exception {
        // Setup
        // Configure XmlReader.getEventXml(...).
        final List<Event> events = Arrays.asList(new Event("name", "label"));
        when(mockXmlReader.getEventXml()).thenReturn(events);

        when(mockXmlReader.getActionXml()).thenThrow(IOException.class);

        // Run the test
        assertThatThrownBy(() -> automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm())
                .isInstanceOf(IOException.class);
    }

    @Test
    public void testGetAllDataAutomationRuleForm_ActivityTypeServiceReturnsNoItems() throws Exception {
        // Setup
        // Configure XmlReader.getEventXml(...).
        final List<Event> events = Arrays.asList(new Event("name", "label"));
        when(mockXmlReader.getEventXml()).thenReturn(events);

        // Configure XmlReader.getActionXml(...).
        final List<Action> actions = Arrays.asList(new Action("name", "label"));
        when(mockXmlReader.getActionXml()).thenReturn(actions);

        when(mockActivityTypeService.findAll()).thenReturn(Collections.emptyList());

        // Run the test
        final AutomationRuleFormData result = automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm();

        // Verify the results
    }

    @Test
    public void testGetAllDataAutomationRuleForm_ActivityTypeServiceThrowsBirdnotesException() throws Exception {
        // Setup
        // Configure XmlReader.getEventXml(...).
        final List<Event> events = Arrays.asList(new Event("name", "label"));
        when(mockXmlReader.getEventXml()).thenReturn(events);

        // Configure XmlReader.getActionXml(...).
        final List<Action> actions = Arrays.asList(new Action("name", "label"));
        when(mockXmlReader.getActionXml()).thenReturn(actions);

        when(mockActivityTypeService.findAll()).thenThrow(BirdnotesException.class);

        // Run the test
        assertThatThrownBy(() -> automationRuleServiceImpUnderTest.getAllDataAutomationRuleForm())
                .isInstanceOf(BirdnotesException.class);
    }

    @Test
    public void testDeleteAutomationRule() {
        // Setup
        // Run the test
        automationRuleServiceImpUnderTest.deleteAutomationRule(0L);

        // Verify the results
        verify(mockAutomationRuleRepository).delete(0L);
    }
}
