package com.intellitech.birdnotes.model.request;

import java.util.List;

public class ReportCronRequest {
	private String period;
	private String reportLink;
	private List<Long> users;
	
	public ReportCronRequest() {
		super();
	}

	public String getPeriod() {
		return period;
	}

	public void setPeriod(String period) {
		this.period = period;
	}

	public String getReportLink() {
		return reportLink;
	}

	public void setReportLink(String reportLink) {
		this.reportLink = reportLink;
	}

	public List<Long> getUsers() {
		return users;
	}

	public void setUsers(List<Long> users) {
		this.users = users;
	}
	
}
