package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Interest;
import com.intellitech.birdnotes.model.MarketingActionType;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.dto.InterestDto;
import com.intellitech.birdnotes.model.dto.MarketingActionTypeDto;
import com.intellitech.birdnotes.repository.InterestRepository;
import com.intellitech.birdnotes.repository.MarketingActionTypeRepository;
import com.intellitech.birdnotes.service.InterestService;
import com.intellitech.birdnotes.service.MarketingActionTypeService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("marketingActionTypeService")
@Transactional
public class MarketingActionTypeServiceImpl implements MarketingActionTypeService {

	private MarketingActionTypeRepository marketingActionTypeRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;

	
	

	@Autowired
	MarketingActionTypeServiceImpl(MarketingActionTypeRepository marketingActionTypeRepository) {
		super();
		this.marketingActionTypeRepository = marketingActionTypeRepository;
	}



	@Override
	public List<MarketingActionTypeDto> findAll() throws BirdnotesException {
		List<MarketingActionTypeDto> back = new ArrayList<>();
		List<MarketingActionType> allTypes= marketingActionTypeRepository.findAll();
		for (MarketingActionType marketingActionType : allTypes) {
			MarketingActionTypeDto marketingActionTypeDto = new MarketingActionTypeDto();
			marketingActionTypeDto.setId(marketingActionType.getId());
			marketingActionTypeDto.setName(marketingActionType.getName());
			back.add(marketingActionTypeDto);
		}
		return back;
	}

	@Override
	public MarketingActionTypeDto findMarketingActionTypeDto(String marketingActionTypeName, List<MarketingActionTypeDto>marketingActionTypeDtos)   {
		for (MarketingActionTypeDto marketingActionTypeDto : marketingActionTypeDtos) {
			if (marketingActionTypeDto.getName().equalsIgnoreCase(marketingActionTypeName)) {
				return marketingActionTypeDto;
			}
		}
		return null;
	}
	
	@Override
	public void saveAllMarketingActionTypes(List<MarketingActionTypeDto> marketingActionTypeDtos) throws BirdnotesException {
		for(MarketingActionTypeDto marketingActionTypeDto:marketingActionTypeDtos) {
			MarketingActionType marketingActionType = new MarketingActionType();
		if (marketingActionTypeDto.getName() == null || marketingActionTypeDto.getName().equals("")) {
			throw new BirdnotesException("Marketing action type name is empty");
		}
		
		marketingActionType.setName(marketingActionTypeDto.getName());
		marketingActionTypeRepository.save(marketingActionType);
		}
	}
	@Override
	public MarketingActionType saveMarketingActionType(MarketingActionTypeDto marketingActionTypeDto) throws BirdnotesException {
	    if (marketingActionTypeDto == null || marketingActionTypeDto.getId() == null) {
	        throw new BirdnotesException(Exceptions.MARKETING_ACTION_TYPE_DTO_IS_NULL);
	    }

	    if (marketingActionTypeDto.getName() == null || marketingActionTypeDto.getName().isEmpty()) {
	        throw new BirdnotesException(Exceptions.MARKETING_ACTION_TYPE_NAME_IS_EMPTY);
	    }

	    MarketingActionType existingMarketingActionType = marketingActionTypeRepository.findByNameAndAnotherId(marketingActionTypeDto.getName(), marketingActionTypeDto.getId());
	    if (existingMarketingActionType != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    MarketingActionType marketingActionType = null;
	    if (marketingActionTypeDto.getId() != null) {
	    	marketingActionType = marketingActionTypeRepository.findOne(marketingActionTypeDto.getId());
	    }
	    if (marketingActionType == null) {
	    	marketingActionType = new MarketingActionType();
	    }

	    marketingActionType.setId(marketingActionTypeDto.getId());
	    marketingActionType.setName(marketingActionTypeDto.getName());
	    return marketingActionTypeRepository.save(marketingActionType);
	}

	
	public void delete (long id)throws BirdnotesException {
		marketingActionTypeRepository.deleteById(id);
	}
}
