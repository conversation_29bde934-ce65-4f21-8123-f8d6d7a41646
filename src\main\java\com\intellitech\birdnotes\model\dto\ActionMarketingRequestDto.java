package com.intellitech.birdnotes.model.dto;

import java.util.Date;
import java.util.List;



public class ActionMarketingRequestDto {

	private Long id;
	private Long identifier;
	private String name;
	private Float budget;
	private Date date;
	private String description;

	private List<Long> prospectsIds;
	private List<Long> productsIds;
	
	
	public ActionMarketingRequestDto() {
		super();
	}
	public ActionMarketingRequestDto(Long id, Long identifier, String name, Float budget, Date date, List<Long> prospectsIds,
			List<Long> productsIds, String description) {
		super();
		this.id = id;
		this.identifier = identifier;
		this.name = name;
		this.budget = budget;
		this.date = date;
		this.prospectsIds = prospectsIds;
		this.productsIds = productsIds;
		this.setDescription(description);
	}
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	
	public Long getIdentifier() {
		return identifier;
	}
	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public Float getBudget() {
		return budget;
	}
	public void setBudget(Float budget) {
		this.budget = budget;
	}
	public Date getDate() {
		return date;
	}
	public void setDate(Date date) {
		this.date = date;
	}
	public List<Long> getProspectsIds() {
		return prospectsIds;
	}
	public List<Long> getProductsIds() {
		return productsIds;
	}
	public void setProductsIds(List<Long> productsIds) {
		this.productsIds = productsIds;
	}
	public void setProspectsIds(List<Long> prospectsIds) {
		this.prospectsIds = prospectsIds;
	}
	public String getDescription() {
		return description;
	}
	public void setDescription(String description) {
		this.description = description;
	}

	
	
	
	
	
	
	
	
	
	
	
}
