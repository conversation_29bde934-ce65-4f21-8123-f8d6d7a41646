package com.intellitech.birdnotes.repository;


import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Wholesaler;

@Repository
public interface WholesalerRepository extends JpaRepository <Wholesaler, Long> {
	
	Wholesaler findByName(String name);
	
	Wholesaler findById(Long id);
	
	@Query("SELECT p from Wholesaler p WHERE p.name=:name AND p.id!=:id order by p.name")
	Wholesaler findByNameExcludeId(@Param("name")String name, @Param("id")Long id);
	
	@Override
	@Query("SELECT w from Wholesaler w order by  name ,w.sector.name")
	List<Wholesaler> findAll();
	
	
	@Query("SELECT w from Wholesaler w where w.status ='ENABLED'")
	List<Wholesaler> findByStatus();
	
	@Query("SELECT w.id from Wholesaler w WHERE w.id in (:wholesalers) order by w.name")
	List<Long> findWhereIdIn(@Param("wholesalers") List<Long> wholesalers);
	
	@Query("SELECT w from Wholesaler w, Prospect p, ProspectsAffectation pa WHERE  pa.prospect=p.id and w.sector.id =p.sector.id  and pa.delegate.id=:userId ")
	List<Wholesaler>  getWholesalersByUser(@Param("userId")Long userId);

	

	
	void deleteById(@Param("id") Long id);
}
