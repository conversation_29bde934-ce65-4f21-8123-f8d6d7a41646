package com.intellitech.birdnotes.model.dto;

import java.util.List;



public class VisitHistorySummaryDto {

	public static final VisitHistorySummaryDto SINGLE_INSTANCE = new VisitHistorySummaryDto();

	//private List<ProspectDtoForStatisticTable> nonvisitedProspects;
	private long numberVisitedProspects;
	private int numberOfDay;
	private int numberOfMonth;
	private int numberOfYear;
	private Object numberResultSearch;
	private Object groupOfSearch;
	private String valueName;
	private String groupName;
	private List<LabelValueDto> commentRatingAverage;
	private List<VisitHistoryDto> visitHistoryList;
	//private List<VisitHistoryDtoForStatisticTable> visitsForStatisticTable;	

	public VisitHistorySummaryDto() {
		super();
	}

	public Object getGroupOfSearch() {
		return groupOfSearch;
	}

	public void setGroupOfSearch(Object groupOfSearch) {
		this.groupOfSearch = groupOfSearch;
	}

	public int getNumberOfDay() {
		return numberOfDay;
	}

	public void setNumberOfDay(int numberOfDay) {
		this.numberOfDay = numberOfDay;
	}

	public int getNumberOfMonth() {
		return numberOfMonth;
	}

	public void setNumberOfMonth(int numberOfMonth) {
		this.numberOfMonth = numberOfMonth;
	}

	public int getNumberOfYear() {
		return numberOfYear;
	}

	public void setNumberOfYear(int numberOfYear) {
		this.numberOfYear = numberOfYear;
	}

	public Object getNumberResultSearch() {
		return numberResultSearch;
	}

	public void setNumberResultSearch(Object numberResultSearch) {
		this.numberResultSearch = numberResultSearch;
	}

	public long getNumberVisitedProspects() {
		return numberVisitedProspects;
	}

	public void setNumberVisitedProspects(long numberVisitedProspects) {
		this.numberVisitedProspects = numberVisitedProspects;
	}

	public String getValueName() {
		return valueName;
	}

	public void setValueName(String valueName) {
		this.valueName = valueName;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public List<VisitHistoryDto> getVisitHistoryList() {
		return visitHistoryList;
	}

	public void setVisitHistoryList(List<VisitHistoryDto> visitHistoryList) {
		this.visitHistoryList = visitHistoryList;
	}

	public List<LabelValueDto> getCommentRatingAverage() {
		return commentRatingAverage;
	}

	public void setCommentRatingAverage(List<LabelValueDto> commentRatingAverage) {
		this.commentRatingAverage = commentRatingAverage;
	}

	
	

}