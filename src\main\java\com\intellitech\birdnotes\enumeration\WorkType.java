package com.intellitech.birdnotes.enumeration;

import com.intellitech.birdnotes.util.BirdnotesConstants;

public enum WorkType {

	MEDICAL(BirdnotesConstants.WorkType.MEDICAL),
	PHARMACEUTICAL(BirdnotesConstants.WorkType.PHARMACEUTICAL),
	ADMIN(BirdnotesConstants.WorkType.ADMIN);

	private String name;

	private WorkType(String name) {
		this.name = name;
	}

	public String getName() {
		return name;
	}

	@Override
	public String toString() {
		if (BirdnotesConstants.WorkType.MEDICAL.equals(name)) {
			return BirdnotesConstants.WorkType.MEDICAL;
		}
		if (BirdnotesConstants.WorkType.PHARMACEUTICAL.equals(name)) {
			return BirdnotesConstants.WorkType.PHARMACEUTICAL;
		}
		return BirdnotesConstants.WorkType.ADMIN;
	}
}
