package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.dto.ActivityCalanderRequestDto;
import com.intellitech.birdnotes.model.dto.ActivityDataDto;
import com.intellitech.birdnotes.model.dto.ActivityDto;

public interface ActivityService {

	ActivityDataDto getStatisticActivity(ActivityCalanderRequestDto activityCalanderRequestDto)
			throws BirdnotesException;

	void updateActivity(ActivityDto activityDto) throws BirdnotesException;

	List<ActivityDto> findActivityByUser(Long userId) throws BirdnotesException;

	ActivityDto findActivityById(long id) throws BirdnotesException;

	Activity saveActivity(ActivityDto activityDto) throws BirdnotesException;

	void deleteActivity(Long activityId);
}