package com.intellitech.birdnotes.model.dto;
import java.io.Serializable;
import java.util.List;

import com.intellitech.birdnotes.model.Role;


public class PermissionDto implements Serializable{
	private static final long serialVersionUID = 1L;

	private String name;
	private String module;
	private List<Role> roles;
	
	public PermissionDto() {
		super();
		
	}

	public PermissionDto(String name, String module, List<Role> roles) {
		super();
		this.name = name;
		this.module = module;
		this.roles = roles;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getModule() {
		return module;
	}

	public void setModule(String module) {
		this.module = module;
	}

	public List<Role> getRoles() {
		return roles;
	}

	public void setRoles(List<Role> roles) {
		this.roles = roles;
	}
	
}
