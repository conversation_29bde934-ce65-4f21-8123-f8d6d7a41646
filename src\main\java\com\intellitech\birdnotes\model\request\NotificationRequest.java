package com.intellitech.birdnotes.model.request;

import java.util.Date;

public class NotificationRequest {
	private Date startDate;
	private Date endDate;
	
	public NotificationRequest(Date startDate, Date endDate) {
		super();
		this.startDate = startDate;
		this.endDate = endDate;
	}
	
	public NotificationRequest() {
		super();
	}

	public Date getStartDate() {
		return startDate;
	}
	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}
	public Date getEndDate() {
		return endDate;
	}
	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}
	
}
