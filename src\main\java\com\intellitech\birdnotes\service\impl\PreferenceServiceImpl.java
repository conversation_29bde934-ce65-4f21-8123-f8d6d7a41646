package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Preference;
import com.intellitech.birdnotes.model.dto.PreferenceDto;
import com.intellitech.birdnotes.repository.PreferenceRepository;
import com.intellitech.birdnotes.service.PreferenceService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("preferenceService")
@Transactional
public class PreferenceServiceImpl implements PreferenceService {

	private PreferenceRepository preferenceRepository;
	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	
	@Autowired
	UserService userService;

	
	

	@Autowired
	PreferenceServiceImpl(PreferenceRepository preferenceRepository ) {
		super();
		this.preferenceRepository = preferenceRepository;
	}



	@Override
	public List<PreferenceDto> findAll() throws BirdnotesException {
		List<PreferenceDto> back = new ArrayList<>();
		List<Preference> allTypes= preferenceRepository.findAll();
		for (Preference preference : allTypes) {
			PreferenceDto preferenceDto = new PreferenceDto();
			preferenceDto.setId(preference.getId());
			preferenceDto.setName(preference.getName());
			back.add(preferenceDto);
		}
		return back;
	}

	@Override
	public PreferenceDto findPreferenceDto(String preferenceName, List<PreferenceDto>preferenceDtos)   {
		for (PreferenceDto preferenceDto : preferenceDtos) {
			if (preferenceDto.getName().equalsIgnoreCase(preferenceName)) {
				return preferenceDto;
			}
		}
		return null;
	}
	

	@Override
	public Preference savePreference(PreferenceDto preferenceDto) throws BirdnotesException {

	    Preference existingPreference = preferenceRepository.findByName(preferenceDto.getName());
	    if (existingPreference != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    Preference preference = null;
	    if (preferenceDto.getId() != null) {
	    	preference = preferenceRepository.findOne(preferenceDto.getId());
	    }
	    if (preference == null) {
	    	preference = new Preference();
	    }

	    preference.setId(preferenceDto.getId());
	    preference.setName(preferenceDto.getName());
	    return preferenceRepository.save(preference);
	}

	
	public void delete (long id)throws BirdnotesException {
		preferenceRepository.delete(id);
	}
	
	@Override
	public Set<Preference>findPreferencesByIds(List<Long>preferenceIds){
		Set<Preference> preferences = new HashSet<>(preferenceRepository.findAll(preferenceIds));
		return preferences;
	}
	
}
