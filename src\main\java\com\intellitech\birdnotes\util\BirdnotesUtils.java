package com.intellitech.birdnotes.util;

import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.MessageFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Calendar;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


import javax.imageio.ImageIO;
import javax.mail.internet.MimeMessage;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.dao.DataIntegrityViolationException;

import com.google.maps.GeoApiContext;
import com.google.maps.GeocodingApi;
import com.google.maps.model.GeocodingResult;
import com.google.maps.model.LatLng;
import com.intellitech.birdnotes.enumeration.EmailType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.NotificationRule;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToPurchaseOrder;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.repository.NotificationRuleRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.PurchaseOrderService;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;

public class BirdnotesUtils {

	private static final Logger LOG = LoggerFactory.getLogger(BirdnotesUtils.class);
	public static ConfigurationDto configuration = null;

	private BirdnotesUtils() {
		throw new IllegalStateException("Utility class");
	}

	public static void sendEmail(JavaMailSender javaMailSender, String[] to, String subject, String htmlBody,
			String from) {
		try {
			MimeMessage message = javaMailSender.createMimeMessage();
			MimeMessageHelper helper;
			helper = new MimeMessageHelper(message);
			helper.setSubject(subject);
			helper.setFrom("<EMAIL>", from);
			helper.setTo(to);
			helper.setText(htmlBody, true);
			javaMailSender.send(message);
		} catch (Exception e) {
			LOG.error("Error in send email to " + to, e);
		}

	}

	public static LatLng geocode(String searshText) {

		try {

			GeoApiContext context = new GeoApiContext.Builder().apiKey("AIzaSyDrbGjkGz-iyftuEw8jLosAsdh4l6qBhcY")
					.build();

			GeocodingResult[] results;
			results = GeocodingApi.geocode(context, searshText + " Tunisie").await();
			if (results.length > 0 && results[0].geometry != null) {
				return results[0].geometry.location;
			} else {
				return null;
			}
		} catch (Exception e) {
			return null;
		}

	}

	public static void sendEmailWithAttachment(JavaMailSender javaMailSender, String from, String[] to,
			List<String> CCs, String subject, String htmlBody, String pathAttachment1, String nameAttachment1,
			String pathAttachment2, String nameAttachment2, String pathAttachment3, String nameAttachment3) {
		try {
			MimeMessage message = javaMailSender.createMimeMessage();
			MimeMessageHelper helper;
			helper = new MimeMessageHelper(message, true);
			helper.setSubject(subject);
			helper.setFrom("<EMAIL>", from);
			helper.setTo(to);
			if (CCs != null) {
				for (String cc : CCs) {
					helper.addCc(cc);
				}
			}

			helper.setText(htmlBody, true);
			try {
				if (pathAttachment1 != null && nameAttachment1 != null && !"".equals(nameAttachment1)) {
					helper.addAttachment("Captured purchase order",
							new File(pathAttachment1 + File.separator + nameAttachment1));

				}
			} catch (Exception e) {
				LOG.error("Error in creating attachment captured file : " + e);
			}

			try {
				if (pathAttachment2 != null && nameAttachment2 != null) {
					helper.addAttachment("Generated purchase order ",
							new File(pathAttachment2 + File.separator + nameAttachment2));

				}
			} catch (Exception e) {
				LOG.error("Error in creating attachment generated file : " + e);
			}
			try {
				if (pathAttachment3 != null && nameAttachment3 != null) {
					helper.addAttachment("Generated delivery order ",
							new File(pathAttachment3 + File.separator + nameAttachment3));

				}
			} catch (Exception e) {
				LOG.error("Error in creating attachment generated file : " + e);
			}

			javaMailSender.send(message);
		} catch (Exception e) {
			LOG.error("Error in send email to " + to, e);
		}

	}

	public static boolean checkIfSunday(Date date) {
		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		boolean isSunday = localDate.getDayOfWeek().equals(DayOfWeek.SUNDAY);
		return isSunday;
	}

	public static boolean checkIfSaturday(Date date) {
		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		boolean isSaturday = localDate.getDayOfWeek().equals(DayOfWeek.SATURDAY);
		return isSaturday;
	}

	public static Date postponeIfWeekend(Date date, int workingDaysPerWeek) {
		if (BirdnotesUtils.checkIfSunday(date)) {
			date = BirdnotesUtils.addDaysToDate(date, 1);
		}
		if (workingDaysPerWeek == 5) {
			if (BirdnotesUtils.checkIfSaturday(date)) {
				date = BirdnotesUtils.addDaysToDate(date, 2);
			}
		}
		return date;
	}

	public static Date addDaysToDate(Date date, int dayToAdd) {
		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate finalDate = localDate.plusDays(dayToAdd);
		return Date.from(finalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
	}

	public static Date minusDaysToDate(Date date, int dayToMinus) {
		LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		LocalDate finalDate = localDate.minusDays(dayToMinus);
		return Date.from(finalDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
	}
	
	public static int getHoursfromDate(Date date) {
		Calendar dateCal = Calendar.getInstance();
		dateCal.setTime(date);		
		return dateCal.HOUR_OF_DAY;
	}
	
	public static int getMinutesfromDate(Date date) {
		Calendar dateCal = Calendar.getInstance();
		dateCal.setTime(date);		
		return dateCal.MINUTE;
	}
	

	public static int getWorkingDaysBetweenTwoDates(Date startDate, Date endDate, boolean removeSaturday) {
		Calendar startCal = Calendar.getInstance();
		startCal.setTime(startDate);

		Calendar endCal = Calendar.getInstance();
		endCal.setTime(endDate);
		int workDays = 0;
		if (startCal.getTimeInMillis() == endCal.getTimeInMillis()) {
			return 0;
		}

		if (startCal.getTimeInMillis() > endCal.getTimeInMillis()) {
			startCal.setTime(endDate);
			endCal.setTime(startDate);
		}

		do {

			startCal.add(Calendar.DAY_OF_MONTH, 1);
			if (removeSaturday) {
				if (startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY
						&& startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
					++workDays;
				}
			} else {
				if (startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
					++workDays;
				}
			}

		} while (startCal.getTimeInMillis() < endCal.getTimeInMillis()); // excluding end date

		return workDays;
	}

	public static int getWorkingDaysBetweenTwoDatesIncludingTheEndDate(Date startDate, Date endDate,
			boolean WorkSaturday) {
		Calendar startCal = Calendar.getInstance();
		startCal.setTime(startDate);

		Calendar endCal = Calendar.getInstance();
		endCal.setTime(endDate);
		int workDays = 0;
		if (startCal.getTimeInMillis() == endCal.getTimeInMillis()) {
			return 1;
		}

		if (startCal.getTimeInMillis() > endCal.getTimeInMillis()) {
			startCal.setTime(endDate);
			endCal.setTime(startDate);
		}

		do {

			startCal.add(Calendar.DAY_OF_MONTH, 1);
			if (WorkSaturday) {
				if (startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
					++workDays;
				}

			} else {
				if (startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SATURDAY
						&& startCal.get(Calendar.DAY_OF_WEEK) != Calendar.SUNDAY) {
					++workDays;
				}
			}

		} while (startCal.getTimeInMillis() <= endCal.getTimeInMillis()); // excluding end date

		return workDays;
	}

	public static boolean checkDateInTheSameWeek(Date date) {

		LocalDate today = LocalDate.now();
		LocalDate monday = today;
		while (monday.getDayOfWeek() != DayOfWeek.MONDAY) {
			monday = monday.minusDays(1);
		}

		LocalDate sunday = today;
		while (sunday.getDayOfWeek() != DayOfWeek.SUNDAY) {
			sunday = sunday.plusDays(1);
		}

		LocalDate dateToLocalDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

		return dateToLocalDate.compareTo(monday) >= 0 && dateToLocalDate.compareTo(sunday) <= 0;
	}

	public static Date getMondayOfWeek(Date date) {
		LocalDate monday = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		;
		while (monday.getDayOfWeek() != DayOfWeek.MONDAY) {
			monday = monday.minusDays(1);
		}
		Date mondayDate = Date.from(monday.atStartOfDay(ZoneId.systemDefault()).toInstant());

		return mondayDate;
	}

	public static Period getPeriod(Date dateInitiale, Date dateFinale) {
		Calendar startCal = Calendar.getInstance();
		startCal.setTime(dateInitiale);

		Calendar endCal = Calendar.getInstance();
		endCal.setTime(dateFinale);

		return Period.between(
				LocalDate.of(startCal.get(Calendar.YEAR), startCal.get(Calendar.MONTH) + 1,
						startCal.get(Calendar.DAY_OF_MONTH)),
				LocalDate.of(endCal.get(Calendar.YEAR), endCal.get(Calendar.MONTH) + 1,
						endCal.get(Calendar.DAY_OF_MONTH)));
	}

	public static void saveBase64ToImage(String imageString, String imageName, String pathUpload) {

		String base64Image = imageString.split(",")[1];
		byte[] imageBytes = javax.xml.bind.DatatypeConverter.parseBase64Binary(base64Image);
		BufferedImage bufferedImage = null;
		try {
			ImageIO.setUseCache(false);
			bufferedImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
		} catch (IOException e) {
			LOG.error("Exception in read image", e);
		}
		File imageFile = new File(pathUpload + File.separator + imageName);
		String extension = imageName.substring(imageName.indexOf('.') + 1, imageName.length());
		boolean result = false;
		try {
			result = ImageIO.write(bufferedImage, extension, imageFile);
		} catch (IOException e) {
			LOG.error("Exception in write image", e);
		}

	}

	public static String encodeFile(String filePath) {

		byte[] fileContent;
		try {
			Path path = new File(filePath).toPath();
			fileContent = FileUtils.readFileToByteArray(new File(filePath));
			String mimeType = Files.probeContentType(path);
			return "data:" + mimeType + ";base64," + Base64.getEncoder().encodeToString(fileContent);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return null;

	}

	public static Date getFirstDayOfCurrentWeek() {

		LocalDate today = LocalDate.now();
		LocalDate monday = today;

		while (monday.getDayOfWeek() != DayOfWeek.MONDAY) {
			monday = monday.minusDays(1);
		}
		ZoneId defaultZoneId = ZoneId.systemDefault();
		return Date.from(monday.atStartOfDay(defaultZoneId).toInstant());

	}

	public static double distanceBetweenTwoPositions(Double lng1, Double lat1, Double lng2, Double lat2) {
		if ((lat1 == lat2) && (lng1 == lng2)) {
			return 0;
		}
		double theta = lng1 - lng2;
		double dist = Math.sin(Math.toRadians(lat1)) * Math.sin(Math.toRadians(lat2))
				+ Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) * Math.cos(Math.toRadians(theta));
		dist = Math.acos(dist);
		dist = Math.toDegrees(dist);
		dist = dist * 60 * 1.1515;
		dist = dist * 1.609344 * 1000;

		/*
		 * double theta = visitHistoryDto.getProspectDto().getLongitude() -
		 * visitHistoryDto.getLocation().getLongitude(); double dist =
		 * Math.sin(Math.toRadians(visitHistoryDto.getProspectDto().getLatitude())) *
		 * Math.sin(Math.toRadians(visitHistoryDto.getLocation().getLatitude())) +
		 * Math.cos(Math.toRadians(visitHistoryDto.getProspectDto().getLatitude())) *
		 * Math.cos(Math.toRadians(visitHistoryDto.getLocation().getLatitude()))
		 * Math.cos(Math.toRadians(theta)); dist = Math.acos(dist); dist =
		 * Math.toDegrees(dist); dist = dist * 60 * 1.1515; dist = dist * 1.609344 *
		 * 1000;
		 */

		return dist;

	}

	public static void generateDocument(JasperReport jasperReport, Map<String, Object> parameters,
			JRDataSource dataSource, String destFileName) throws FileNotFoundException, JRException {

		LOG.info("generating jasper file...");

		JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, dataSource);
		JasperExportManager.exportReportToPdfFile(jasperPrint, destFileName);

		LOG.info("terminate generating jasper file");
	}

	public static void sendMailPurchaseOrder(PurchaseOrderDto mergedPurchaseOrder, ConfigurationDto config,
			UserRepository userRepository, PurchaseOrderService purchaseOrderService,
			PurchaseOrderRepository purchaseOrderRepository, ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder,
			String uploadUrl, String logoPath, String sendWholesalerMailSubject, String sendCancellationMailSubject,
			String sendCancellationMailHtmlBody, String sendWholesalerMailHtmlBodyWithAttachment,
			String sendWholesalerMailHtmlBodyWithoutAttachment, String purchaseOrderPath, String generatedDocumentPath,
			String uploadPath, String attachmentPath, String poPath, String doPath, JavaMailSender javaMailSender,
			NotificationRuleRepository notificationRuleRepository) {

		String IMGBALISE = " <br/> <img src='";
		String pathLogo = config.getBackendUrl() + uploadUrl + logoPath + "/" + config.getLogo();

		MessageFormat subjectFormat = new MessageFormat(sendWholesalerMailSubject);

		MessageFormat messageFormat;
		if (mergedPurchaseOrder.getEmailType().equals(EmailType.CANCELLATION.toString())) {
			subjectFormat = new MessageFormat(sendCancellationMailSubject);
			messageFormat = new MessageFormat(sendCancellationMailHtmlBody);
		} else {
			subjectFormat = new MessageFormat(sendWholesalerMailSubject);

			if (!"".equals(mergedPurchaseOrder.getAttachmentBase64())
					&& !"".equals(mergedPurchaseOrder.getAttachmentName())) {
				messageFormat = new MessageFormat(sendWholesalerMailHtmlBodyWithAttachment);
			} else {
				messageFormat = new MessageFormat(sendWholesalerMailHtmlBodyWithoutAttachment);
			}
		}

		List<User> supervisors = userRepository.getSuperviserOfUser(mergedPurchaseOrder.getDelegate().getId());
		Set<String> to = new HashSet<String>();
		User user = userRepository.findUserByDelegateId(mergedPurchaseOrder.getDelegate().getId());
		if (user.getEmail() != null && !"".equals(user.getEmail())) {
			to.add(user.getEmail());
		} else {
			LOG.error("Delegate  email is null, user id = " + user.getId());
		}
		for (User supervisor : supervisors) {
			if (supervisor.getEmail() != null) {
				to.add(supervisor.getEmail());
			}
		}

		for (User topUser : userRepository.getTopUsers()) {
			if (topUser.getEmail() != null) {
				to.add(topUser.getEmail());
			}
		}

		/*List<NotificationRule> purchaseOrderNotificationRules = notificationRuleRepository
				.findNotificationByEvent("NEW_PURCHASE_ORDER");
		for (NotificationRule purchaseOrderNotificationRule : purchaseOrderNotificationRules) {
			if (purchaseOrderNotificationRule.getNotificationReceiver() != null) {
				to.add(purchaseOrderNotificationRule.getNotificationReceiver());
			}
		}*/
		
		List<NotificationRule>purchaseOrderNotificationRules = null;
		if(mergedPurchaseOrder.getProspect().getSpecialityDto().getAction() % 2 != 1 ) {
			purchaseOrderNotificationRules = notificationRuleRepository.findNotificationByEvent("NEW_PURCHASE_WHOLESALER_ORDER");
			
		}else {
			purchaseOrderNotificationRules = notificationRuleRepository.findNotificationByEvent("NEW_PURCHASE_NOT_WHOLESALER_ORDER");	
		}
		
		if(purchaseOrderNotificationRules != null) {
			for(NotificationRule purchaseOrderNotificationRule : purchaseOrderNotificationRules) {
				if(purchaseOrderNotificationRule.getNotificationReceiver() != null) {
					to.add(purchaseOrderNotificationRule.getNotificationReceiver());
				}
			}
		}


		ProspectDto wholesaler = null;
		String attachmentNameToSend = "";
		String attachmentBase64ToSend = "";
		if (mergedPurchaseOrder.getEmailType().equals(EmailType.CANCELLATION.toString())) {
			wholesaler = mergedPurchaseOrder.getOldWholesaler();
			attachmentNameToSend = mergedPurchaseOrder.getOldAttachmentName();
			attachmentBase64ToSend = mergedPurchaseOrder.getOldAttachmentBase64();

		} else {
			wholesaler = mergedPurchaseOrder.getWholesaler();
			attachmentNameToSend = mergedPurchaseOrder.getAttachmentName();
			attachmentBase64ToSend = mergedPurchaseOrder.getAttachmentBase64();
		}
		String status = "";
		if(mergedPurchaseOrder.getStatus().equals("ACCEPTED")){
			status = "Accepté";
		}else if(mergedPurchaseOrder.getStatus().equals("WAITING_FOR_VALIDATION")){
			status = "En attente de validation";
		}
		String[] args = {
				mergedPurchaseOrder.getProspect().getFirstName() + " "
						+ mergedPurchaseOrder.getProspect().getLastName(),
				mergedPurchaseOrder.getProspect().getAddress(),
				wholesaler.getFirstName() + " " + wholesaler.getLastName(), config.getName(),
				mergedPurchaseOrder.getProspect().getSectorDto().getName(),
				mergedPurchaseOrder.getProspect().getLocalityDto().getName(), status};

		String subject = subjectFormat.format(args);
		String fileUrl = config.getBackendUrl() + uploadUrl + purchaseOrderPath + "/" + mergedPurchaseOrder.getId()
				+ "/" + attachmentNameToSend;
		String htmlBody = messageFormat.format(args);

		if (!"".equals(attachmentNameToSend) && !"".equals(attachmentBase64ToSend)) {
			htmlBody = htmlBody + " <br>  <a href = '" + fileUrl
					+ "' target ='_blank' >Cliquez ici pour voir le bon de commande en ligne </a> <br> " + IMGBALISE
					+ pathLogo + "'>";
		}

		if (wholesaler.getEmail() != null && !"".equals(wholesaler.getEmail())) {
			to.add(wholesaler.getEmail());
		}

		if (mergedPurchaseOrder.getEmailType().equals(EmailType.VALIDATION.toString())) {

			PurchaseOrder purchaseOrder = purchaseOrderRepository.findById(mergedPurchaseOrder.getId());
			purchaseOrder.setMailSent(true);
			purchaseOrderRepository.save(purchaseOrder);
		}

		if ((attachmentBase64ToSend == null || attachmentNameToSend == null || "".equals(attachmentNameToSend)
				|| "".equals(attachmentBase64ToSend)) && (poPath == null || "".equals(poPath))) {
			if (to != null && to.stream().toArray(String[]::new).length > 0) {
				BirdnotesUtils.sendEmail(javaMailSender, to.stream().toArray(String[]::new), subject, htmlBody,
						config.getName());
			}
		} else {

			if (attachmentBase64ToSend != null && attachmentNameToSend != null) {

				if (to != null && to.stream().toArray(String[]::new).length > 0) {
					if(mergedPurchaseOrder.getGeneratePo() == true && mergedPurchaseOrder.getGenerateDo() == true) {
						BirdnotesUtils.sendEmailWithAttachment(javaMailSender, config.getName(),
								to.stream().toArray(String[]::new), null, subject, htmlBody, attachmentPath,
								attachmentNameToSend, poPath, "generatedPurchaseOrder.pdf", doPath, "generatedDeliveryOrder.pdf");
					}
					else if(mergedPurchaseOrder.getGeneratePo() == true && mergedPurchaseOrder.getGenerateDo() == false) {
						BirdnotesUtils.sendEmailWithAttachment(javaMailSender, config.getName(),
								to.stream().toArray(String[]::new), null, subject, htmlBody, attachmentPath,
								attachmentNameToSend, poPath, "generatedPurchaseOrder.pdf", null, null);
					}
					else if(mergedPurchaseOrder.getGeneratePo() == false && mergedPurchaseOrder.getGenerateDo() == true) {
						BirdnotesUtils.sendEmailWithAttachment(javaMailSender, config.getName(),
								to.stream().toArray(String[]::new), null, subject, htmlBody, attachmentPath,
								attachmentNameToSend, null, null, doPath, "generatedDeliveryOrder.pdf");
					}
				}
			}
		}

	}

	public static String createFolder(Long id, String path, String uploadPath) throws BirdnotesException {

		String pathUpload = uploadPath + path + File.separator + id;
		File dirName = new File(pathUpload);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		return pathUpload;
	}
    // Method to extract the table name having the constraint from DataIntegrityViolationException
    public static String extractTableWithConstraint(DataIntegrityViolationException ex) {
    	try {
    		
    		String errorMessage = ex.getCause().getCause().getMessage();
            // Step 1: Check if the error message contains constraint information
            String regex = "violates foreign key constraint.*on table \"(\\w+)\"";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(errorMessage);
            
            if (matcher.find()) {
                // Retourne le nom de la table
                return matcher.group(1);
            }else {
            	return "";
            }
    		
    	}catch(Exception e) {
    		return "";
    	}
        
          
       
    }


}
