package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Value;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;

public class DelegateDto extends DelegateDtoRequest implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	public String SupervisorsString;
	private String firstName;
	private String lastName;
	private String gender;
	private String contractType;
	private String cin;
	private String carType;
	private String carRegistration;
	private Integer workingDaysPerWeek;
	private String email;
	private Date hiringDate;
	private Date birthdayDate;
	private String phone;
	private String photo;
	private String username;
	private String password;
	private String workType;
	private String rolesString;
	private String lastSyncro;
	private String gammesString;
	private Network network;
	private String networkString;
	private String oneSignalUserId;
	private String mobileAppVersion;
	private List<GoalDto> goals;
	private String lastLogin;
	private Double lat;
	private Double lng;
	private String pathFile;
	private String image;
	private Short cycle;
	private Integer line;
	private String message;
	private Set<Range> ranges;
	private Set<Role> roles;
	private List<Integer> rangeIds;
	private List<Integer> roleIds;
	private List<Long> superiors;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${userImagePath}")
	private String userImagePath;

	public DelegateDto() {
		super();

	}

	public DelegateDto(Delegate delegate) {
		this.firstName = delegate.getFirstName();
		this.lastName = delegate.getLastName();
		this.birthdayDate = delegate.getBirthdayDate();
		this.cin = delegate.getCin();
		this.contractType = delegate.getContractType().toString();
		this.gender = delegate.getGender().toString();
		this.hiringDate = delegate.getHiringDate();
		this.id = delegate.getId();
		this.network = delegate.getNetwork();
		this.password = delegate.getUser().getPassword();
		this.phone = delegate.getUser().getPhone();
		this.email = delegate.getUser().getEmail();
		this.lat = delegate.getLatitude();
		this.lng = delegate.getLongitude();
		if (delegate.getUser().getRoles() != null) {
			this.roleIds = delegate.getUser().getRoles().stream().map(Role::getId).collect(Collectors.toList());
		}
		if (delegate.getUser().getRanges() != null) {
			this.rangeIds = delegate.getUser().getRanges().stream().map(Range::getId).collect(Collectors.toList());
		}
		if (delegate.getUser().getSuperiors() != null) {
			this.superiors = delegate.getUser().getSuperiors().stream().map(User::getId).collect(Collectors.toList());
		}
		if (delegate.getCarType() != null) {
			this.carType = delegate.getCarType().toString();
		}
		if (delegate.getImage() != null && !delegate.getImage().isEmpty()) {
			this.image = delegate.getImage();
			this.pathFile = uploadUrl + userImagePath;
		}
		this.workingDaysPerWeek = delegate.getWorkingDaysPerWeek();
		this.carRegistration = delegate.getCarRegistration();
	}

	public DelegateDto(Delegate delegate, User u) {
		this.firstName = delegate.getFirstName();
		this.lastName = delegate.getLastName();
		this.birthdayDate = delegate.getBirthdayDate();
		this.cin = delegate.getCin();
		this.contractType = delegate.getContractType().toString();
		this.gender = delegate.getGender().toString();
		this.hiringDate = delegate.getHiringDate();
		this.id = delegate.getId();
		this.network = delegate.getNetwork();
		this.password = delegate.getUser().getPassword();
		this.phone = delegate.getUser().getPhone();
		this.email = delegate.getUser().getEmail();
		this.roles = delegate.getUser().getRoles();
		this.lat = delegate.getLatitude();
		this.lng = delegate.getLongitude();
		if (delegate.getCarType() != null) {
			this.carType = delegate.getCarType().toString();
		}
		if (delegate.getImage() != null && !delegate.getImage().isEmpty()) {
			this.image = delegate.getImage();
			this.pathFile = uploadUrl + userImagePath;
		}
		this.workingDaysPerWeek = delegate.getWorkingDaysPerWeek();
		this.carRegistration = delegate.getCarRegistration();
	}

	public DelegateDto(Long id, String firstName, String lastName, String gender, String contractType, String cin,
			String carType, String carRegistration, Integer workingDaysPerWeek, String email, Date hiringDate,
			Date birthdayDate, String phone, String username, String password, String workType, Set<Role> roles,
			Set<Range> gammes, String gammesString, Network network, String networkString, Short cycle, Integer line,
			String message, String rolesString, String SupervisorsString) {
		super();
		this.id = id;
		this.firstName = firstName;
		this.lastName = lastName;
		this.gammesString = gammesString;
		this.networkString = networkString;
		this.network = network;
		this.gender = gender;
		this.contractType = contractType;
		this.SupervisorsString = SupervisorsString;
		this.rolesString = rolesString;
		this.phone = phone;
		this.cin = cin;
		this.email = email;
		this.username = username;
		this.password = password;
		this.carType = carType;
		this.carRegistration = carRegistration;
		this.workingDaysPerWeek = workingDaysPerWeek;
		this.hiringDate = hiringDate;
		this.birthdayDate = birthdayDate;
		this.workType = workType;
		this.roles = roles;
		this.cycle = cycle;
		this.line = line;
		this.message = message;
	}

	public String getSupervisorsString() {
		return SupervisorsString;
	}

	public void setSupervisorsString(String SupervisorsString) {
		this.SupervisorsString = SupervisorsString;
	}

	public String getRolesString() {
		return rolesString;
	}

	public void setRolesString(String rolesString) {
		this.rolesString = rolesString;
	}

	public List<Long> getSuperiors() {
		return superiors;
	}

	public void setSuperiors(List<Long> superiors) {
		this.superiors = superiors;
	}

	public String getNetworkString() {
		return networkString;
	}

	public void setNetworkString(String networkString) {
		this.networkString = networkString;
	}

	public List<Integer> getRangeIds() {
		return rangeIds;
	}

	public void setRangeIds(List<Integer> rangeIds) {
		this.rangeIds = rangeIds;
	}

	public Set<Range> getRanges() {
		return ranges;
	}

	public void setRanges(Set<Range> ranges) {
		this.ranges = ranges;
	}

	public Integer getLine() {
		return line;
	}

	public void setLine(Integer line) {
		this.line = line;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Short getCycle() {
		return cycle;
	}

	public void setCycle(Short cycle) {
		this.cycle = cycle;
	}

	public String getPathFile() {
		return pathFile;
	}

	public void setPathFile(String pathFile) {
		this.pathFile = pathFile;
	}

	public String getImage() {
		return image;
	}

	public void setImage(String image) {
		this.image = image;
	}

	public String getCarType() {
		return carType;
	}

	public void setCarType(String carType) {
		this.carType = carType;
	}

	public Integer getWorkingDaysPerWeek() {
		return workingDaysPerWeek;
	}

	public void setWorkingDaysPerWeek(Integer workingDaysPerWeek) {
		this.workingDaysPerWeek = workingDaysPerWeek;
	}

	public List<Integer> getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(List<Integer> roleIds) {
		this.roleIds = roleIds;
	}

	public Network getNetwork() {
		return network;
	}

	public void setNetwork(Network network) {
		this.network = network;
	}

	public String getGammesString() {
		return gammesString;
	}

	public void setGammesString(String gammesString) {
		this.gammesString = gammesString;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Set<Role> getRoles() {
		return roles;
	}

	public void setRoles(Set<Role> roles) {
		this.roles = roles;
	}

	public void setLastSynco(String lastSyncro) {
		this.lastSyncro = lastSyncro;

	}

	public String getLastSyncro() {
		return lastSyncro;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getContractType() {
		return contractType;
	}

	public void setContractType(String contractType) {
		this.contractType = contractType;
	}

	public String getCin() {
		return cin;
	}

	public void setCin(String cin) {
		this.cin = cin;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public Date getHiringDate() {
		return hiringDate;
	}

	public void setHiringDate(Date hiringDate) {
		this.hiringDate = hiringDate;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public String getWorkType() {
		return workType;
	}

	public void setWorkType(String workType) {
		this.workType = workType;
	}

	public void setLastSyncro(String lastSyncro) {
		this.lastSyncro = lastSyncro;
	}

	public Date getBirthdayDate() {
		return birthdayDate;
	}

	public void setBirthdayDate(Date birthdayDate) {
		this.birthdayDate = birthdayDate;
	}

	public Long getNetworkId() {
		return networkId;
	}

	public void setNetworkId(Long networkId) {
		this.networkId = networkId;
	}

	public Double getLat() {
		return lat;
	}

	public void setLat(Double lat) {
		this.lat = lat;
	}

	public Double getLng() {
		return lng;
	}

	public void setLng(Double lng) {
		this.lng = lng;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((birthdayDate == null) ? 0 : birthdayDate.hashCode());
		result = prime * result + ((cin == null) ? 0 : cin.hashCode());
		result = prime * result + ((contractType == null) ? 0 : contractType.hashCode());
		result = prime * result + ((email == null) ? 0 : email.hashCode());
		result = prime * result + ((firstName == null) ? 0 : firstName.hashCode());
		result = prime * result + ((gender == null) ? 0 : gender.hashCode());
		result = prime * result + ((hiringDate == null) ? 0 : hiringDate.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((lastName == null) ? 0 : lastName.hashCode());
		result = prime * result + ((lastSyncro == null) ? 0 : lastSyncro.hashCode());
		result = prime * result + ((password == null) ? 0 : password.hashCode());
		result = prime * result + ((phone == null) ? 0 : phone.hashCode());
		result = prime * result + ((roles == null) ? 0 : roles.hashCode());
		result = prime * result + ((username == null) ? 0 : username.hashCode());
		result = prime * result + ((workType == null) ? 0 : workType.hashCode());
		result = prime * result + ((networkId == null) ? 0 : networkId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		DelegateDto other = (DelegateDto) obj;
		if (birthdayDate == null) {
			if (other.birthdayDate != null)
				return false;
		} else if (!birthdayDate.equals(other.birthdayDate))
			return false;
		if (cin == null) {
			if (other.cin != null)
				return false;
		} else if (!cin.equals(other.cin))
			return false;
		if (contractType == null) {
			if (other.contractType != null)
				return false;
		} else if (!contractType.equals(other.contractType))
			return false;
		if (email == null) {
			if (other.email != null)
				return false;
		} else if (!email.equals(other.email))
			return false;
		if (firstName == null) {
			if (other.firstName != null)
				return false;
		} else if (!firstName.equals(other.firstName))
			return false;
		if (gender == null) {
			if (other.gender != null)
				return false;
		} else if (!gender.equals(other.gender))
			return false;
		if (hiringDate == null) {
			if (other.hiringDate != null)
				return false;
		} else if (!hiringDate.equals(other.hiringDate))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (lastName == null) {
			if (other.lastName != null)
				return false;
		} else if (!lastName.equals(other.lastName))
			return false;
		if (lastSyncro == null) {
			if (other.lastSyncro != null)
				return false;
		} else if (!lastSyncro.equals(other.lastSyncro))
			return false;
		if (password == null) {
			if (other.password != null)
				return false;
		} else if (!password.equals(other.password))
			return false;
		if (phone == null) {
			if (other.phone != null)
				return false;
		} else if (!phone.equals(other.phone))
			return false;
		if (roles == null) {
			if (other.roles != null)
				return false;
		} else if (!roles.equals(other.roles))
			return false;
		if (username == null) {
			if (other.username != null)
				return false;
		} else if (!username.equals(other.username))
			return false;
		if (workType == null) {
			if (other.workType != null)
				return false;
		} else if (!workType.equals(other.workType))
			return false;
		if (networkId == null) {
			if (other.networkId != null)
				return false;
		} else if (!networkId.equals(other.networkId))
			return false;

		return true;
	}

	public String getOneSignalUserId() {
		return oneSignalUserId;
	}

	public void setOneSignalUserId(String oneSignalUserId) {
		this.oneSignalUserId = oneSignalUserId;
	}

	public String getMobileAppVersion() {
		return mobileAppVersion;
	}

	public void setMobileAppVersion(String mobileAppVersion) {
		this.mobileAppVersion = mobileAppVersion;
	}

	public List<GoalDto> getGoals() {
		return goals;
	}

	public void setGoals(List<GoalDto> goals) {
		this.goals = goals;
	}

	public String getLastLogin() {
		return lastLogin;
	}

	public void setLastLogin(String lastLogin) {
		this.lastLogin = lastLogin;
	}

	public String getPhoto() {
		return photo;
	}

	public void setPhoto(String photo) {
		this.photo = photo;
	}

}