package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import org.hibernate.annotations.Type;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.PURCHASEORDER, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class PurchaseOrder implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.PURCHASEORDER_SEQUENCE, sequenceName = Sequences.PURCHASEORDER_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.PURCHASEORDER_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	@Column(name = Columns.IDENTIFIER)
	private Long identifier;
	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.VISIT_ID, unique = false)
	private Visit visit;
	
	/*@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.WHOLESALER_ID, unique = false)
	private Wholesaler wholesaler;*/
	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.PROSPECT_ID, unique = false)
	private Prospect wholesaler;

	@Column(name = BirdnotesConstants.Columns.ATTACHMENT_NAME, length = BirdnotesConstants.Numbers.N_255)
	private String attachmentName;

	@Column(name = BirdnotesConstants.Columns.ATTACHEMENT_BASE64)
	@Type(type = "text")
	private String attachmentBase64;
	
	@Column(name = BirdnotesConstants.Columns.PLACEMENT_METHOD, length = BirdnotesConstants.Numbers.N_64)
	private String placementMethod;
	
	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS, length = Numbers.N_85)
	private UserValidationStatus status;
	
	
	private Boolean mailSent;
	
	@JsonIgnore
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.PURCHASE_ORDER_TEMPLATE_ID)
	private PurchaseOrderTemplate purchaseOrderTemplate;
	
	

	public PurchaseOrder() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public Visit getVisit() {
		return visit;
	}

	public void setVisit(Visit visit) {
		this.visit = visit;
	}
	
	public Prospect getWholesaler() {
		return wholesaler;
	}

	public void setWholesaler(Prospect wholesaler) {
		this.wholesaler = wholesaler;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public String getPlacementMethod() {
		return placementMethod;
	}

	public void setPlacementMethod(String placementMethod) {
		this.placementMethod = placementMethod;
	}
	
	public Boolean getMailSent() {
		return mailSent;
	}

	public void setMailSent(Boolean mailSent) {
		this.mailSent = mailSent;
	}

	public PurchaseOrderTemplate getPurchaseOrderTemplate() {
		return purchaseOrderTemplate;
	}

	public void setPurchaseOrderTemplate(PurchaseOrderTemplate purchaseOrderTemplate) {
		this.purchaseOrderTemplate = purchaseOrderTemplate;
	}
	
	
	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}




}
