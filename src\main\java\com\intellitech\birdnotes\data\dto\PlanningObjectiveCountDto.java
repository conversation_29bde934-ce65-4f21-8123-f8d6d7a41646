package com.intellitech.birdnotes.data.dto;

public class PlanningObjectiveCountDto {

	private long groupId;
	private String groupName;
	private long count;
	
	public PlanningObjectiveCountDto() {
		super();
	}

	public PlanningObjectiveCountDto(long groupId, String GroupName, long count) {
		this.groupId = groupId;
		this.groupName = GroupName;
		this.count = count;

	}
	public PlanningObjectiveCountDto(String GroupName, long count) {
		this.groupName = GroupName;
		this.count = count;

	}
	
	public long getGroupId() {
		return groupId;
	}

	public void setGroupId(long groupId) {
		this.groupId = groupId;
	}

	public String getGroupName() {
		return groupName;
	}

	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}

	public long getCount() {
		return count;
	}

	public void setCount(long count) {
		this.count = count;
	}
	
	

}