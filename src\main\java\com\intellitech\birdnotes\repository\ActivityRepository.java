package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Activity;
import com.intellitech.birdnotes.model.ActivityType;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.dto.StatisticActivity;

public interface ActivityRepository extends JpaRepository<Activity, Long> {

	Activity findById(Long id);
	@Modifying
	@Query("UPDATE Activity SET activityDate=:activityDate, hourNumber=:hourNumber, activityType=:activityType, comment=:comment WHERE id=:id")
	void update(@Param("activityDate") Date activityDate, @Param("hourNumber") Long hourNumber, @Param("activityType") ActivityType activityType,
			@Param("comment") String comment, @Param("id") Long id);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.StatisticActivity (a.activityType.name , SUM(a.hourNumber) as hourNumber) from Activity a  where a.delegate.id=:userId AND (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) Group BY a.activityType.name")
	List<StatisticActivity> getStatisticActivity(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("userId") Long userId);

	@Query("SELECT a  from Activity a  where a.delegate.id=:userId AND (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) ")
	List<Activity> getActivitiesByUser(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("userId") Long userId);
	
	@Query("SELECT a  from Activity a  where a.delegate.user.id IN (:subUsersIds) AND (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) order by a.activityDate")
	List<Activity> getActivities(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("subUsersIds") List <Long> subUsersIds);
	
	@Query("SELECT a  from Activity a join a.delegate.user.superiors s where (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) AND s.id = :superVisorId")
	List<Activity> getActivitiesBySuperVisor(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("superVisorId") Long superVisorId);
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.StatisticActivity (a.activityType.name , SUM(a.hourNumber) as hourNumber) from Activity a  where  a.delegate.user.id IN (:subUsersIds) AND (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) Group BY a.activityType.name")
	List<StatisticActivity> getStatisticActivity(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("subUsersIds") List <Long> subUsersIds);

	@Query("SELECT new com.intellitech.birdnotes.model.dto.StatisticActivity (a.activityType.name , SUM(a.hourNumber) as hourNumber) from Activity a join a.delegate.user.superiors s where  (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) AND s.id = :superVisorId Group BY a.activityType.name ")
	List<StatisticActivity> getStatisticActivityBySuperVisor(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("superVisorId") Long superVisorId);
	@Modifying
	@Query("DELETE Activity a WHERE a.id in (:activityReportIds)")
	void deleteById(@Param("activityReportIds") List<Long> activityReportIds);
	
	@Query("SELECT a from Activity a  where a.delegate.id=:userId ")
	List<Activity> findActivityByUser(@Param("userId") Long userId);

	
	@Query("SELECT a FROM Activity a WHERE a.identifier = ?1 And a.delegate.id = ?2")
	Activity findByIdentifier(Long identifier, Long userId);
	
	@Modifying
	@Query("Delete FROM Activity a WHERE a.identifier = ?1 And a.delegate.id = ?2")
	void deleteByIdentifier(Long identifier, Long userId);
	
	
	@Query("SELECT new com.intellitech.birdnotes.model.dto.StatisticActivity (a.delegate.id , SUM(a.hourNumber))  from Activity a where a.delegate.user.id IN (:subUsersIds) AND (DATE(a.activityDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate)) Group by a.delegate.id ")
	List<StatisticActivity>  getActivitiesGroupByUser(@Param("firstDate")Date firstDate, @Param("lastDate")Date lastDate, @Param("subUsersIds") List <Long> subUsersIds);

}
