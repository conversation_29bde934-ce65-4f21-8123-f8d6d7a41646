package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Attachment;
import com.intellitech.birdnotes.model.Recovery;
import com.intellitech.birdnotes.model.convertor.RecoveryToDtoConvertor;


public class RecoveryDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long identifier;
	
	private Long purchaseOrderId;
	
	private Long attachmentId;
	
	private Date date;
	
	private String stringDate;
	
	private Float amount;
	
	private String payment;
	
	private String description;
	
	private String attachmentBase64;
	
	private String attachmentName;
	
	private FileNamePath nameFile;

	public RecoveryDto() {
		super();
	}

	public RecoveryDto(Recovery recovery) throws BirdnotesException {
		RecoveryToDtoConvertor recoveryToDtoConvertor = new RecoveryToDtoConvertor();
		recoveryToDtoConvertor.convert(recovery);
	}

	public RecoveryDto(String payment, Float amount, String description, Date recoveryDate, Long identifier, Attachment attachment) {
		this.identifier = identifier;
		this.amount = amount;
		this.description = description;
		this.payment = payment;
		this.date = recoveryDate;
		this.attachmentId = attachment.getId();
		this.attachmentBase64 = attachment.getAttachmentBase64();
		this.attachmentName = attachment.getAttachmentName();
	
		
	}
	public Long getId() {
		return id;
	}


	public void setId(Long id) {
		this.id = id;
	}


	public Long getIdentifier() {
		return identifier;
	}


	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}


	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	public String getStringDate() {
		return stringDate;
	}


	public void setStringDate(String stringDate) {
		this.stringDate = stringDate;
	}

	
	public Float getAmount() {
		return amount;
	}

	public void setAmount(Float amount) {
		this.amount = amount;
	}


	public String getPayment() {
		return payment;
	}


	public void setPayment(String payment) {
		this.payment = payment;
	}


	public String getDescription() {
		return description;
	}


	public void setDescription(String description) {
		this.description = description;
	}





	public Long getPurchaseOrderId() {
		return purchaseOrderId;
	}


	public void setPurchaseOrderId(Long purchaseOrderId) {
		this.purchaseOrderId = purchaseOrderId;
	}


	public Long getAttachmentId() {
		return attachmentId;
	}


	public void setAttachmentId(Long attachmentId) {
		this.attachmentId = attachmentId;
	}

	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

	public FileNamePath getNameFile() {
		return nameFile;
	}

	public void setNameFile(FileNamePath nameFile) {
		this.nameFile = nameFile;
	}

	

}
