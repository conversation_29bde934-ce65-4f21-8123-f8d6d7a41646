package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class DelegateProspectsDto {
	
	private List<ProspectDto> delegateProspects;
	
	private Short cycle;
	
	private Integer delegateNumber;

	public Short getCycle() {
		return cycle;
	}

	public void setCycle(Short cycle) {
		this.cycle = cycle;
	}

	public List<ProspectDto> getDelegateProspects() {
		return delegateProspects;
	}

	public void setDelegateProspects(List<ProspectDto> delegateProspects) {
		this.delegateProspects = delegateProspects;
	}

	public Integer getDelegateNumber() {
		return delegateNumber;
	}

	public void setDelegateNumber(Integer delegateNumber) {
		this.delegateNumber = delegateNumber;
	}
	
	

}
