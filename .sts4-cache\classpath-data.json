{"name": "birdnotes-web-back", "classpathEntries": [{"kind": "binary", "path": "/usr/lib/jvm/java-8-oracle/jre/lib/resources.jar", "sourceContainerUrl": "file:/usr/lib/jvm/java-8-oracle/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/usr/lib/jvm/java-8-oracle/jre/lib/rt.jar", "sourceContainerUrl": "file:/usr/lib/jvm/java-8-oracle/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/usr/lib/jvm/java-8-oracle/jre/lib/jsse.jar", "sourceContainerUrl": "file:/usr/lib/jvm/java-8-oracle/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/usr/lib/jvm/java-8-oracle/jre/lib/jce.jar", "sourceContainerUrl": "file:/usr/lib/jvm/java-8-oracle/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/usr/lib/jvm/java-8-oracle/jre/lib/charsets.jar", "sourceContainerUrl": "file:/usr/lib/jvm/java-8-oracle/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/usr/lib/jvm/java-8-oracle/jre/lib/jfr.jar", "sourceContainerUrl": "file:/usr/lib/jvm/java-8-oracle/src.zip", "javadocContainerUrl": "https://docs.oracle.com/javase/1/docs/api/", "isSystem": true, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/1.5.3.RELEASE/spring-boot-starter-data-jpa-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/1.5.3.RELEASE/spring-boot-starter-data-jpa-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/1.5.3.RELEASE/spring-boot-starter-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/1.5.3.RELEASE/spring-boot-starter-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/1.5.3.RELEASE/spring-boot-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/1.5.3.RELEASE/spring-boot-1.5.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/1.5.3.RELEASE/spring-boot-1.5.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/1.5.3.RELEASE/spring-boot-autoconfigure-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/1.5.3.RELEASE/spring-boot-autoconfigure-1.5.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/1.5.3.RELEASE/spring-boot-autoconfigure-1.5.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/1.5.3.RELEASE/spring-boot-starter-logging-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/1.5.3.RELEASE/spring-boot-starter-logging-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.1.11/logback-classic-1.1.11.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.1.11/logback-classic-1.1.11-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.1.11/logback-classic-1.1.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.1.11/logback-core-1.1.11.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.1.11/logback-core-1.1.11-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.1.11/logback-core-1.1.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.25/log4j-over-slf4j-1.7.25.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.25/log4j-over-slf4j-1.7.25-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/log4j-over-slf4j/1.7.25/log4j-over-slf4j-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.17/snakeyaml-1.17.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.17/snakeyaml-1.17-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/yaml/snakeyaml/1.17/snakeyaml-1.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/1.5.3.RELEASE/spring-boot-starter-aop-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/1.5.3.RELEASE/spring-boot-starter-aop-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.8.10/aspectjweaver-1.8.10.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.8.10/aspectjweaver-1.8.10-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.8.10/aspectjweaver-1.8.10-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/1.5.3.RELEASE/spring-boot-starter-jdbc-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/1.5.3.RELEASE/spring-boot-starter-jdbc-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/tomcat/tomcat-jdbc/8.5.14/tomcat-jdbc-8.5.14.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/tomcat/tomcat-jdbc/8.5.14/tomcat-jdbc-8.5.14-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/tomcat/tomcat-juli/8.5.14/tomcat-juli-8.5.14.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/tomcat/tomcat-juli/8.5.14/tomcat-juli-8.5.14-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.8.RELEASE/spring-jdbc-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.8.RELEASE/spring-jdbc-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/4.3.8.RELEASE/spring-jdbc-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hibernate/hibernate-core/5.0.12.Final/hibernate-core-5.0.12.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hibernate/hibernate-core/5.0.12.Final/hibernate-core-5.0.12.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.1.Final/jboss-logging-3.3.1.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.1.Final/jboss-logging-3.3.1.Final-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.1.Final/jboss-logging-3.3.1.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hibernate/javax/persistence/hibernate-jpa-2.1-api/1.0.0.Final/hibernate-jpa-2.1-api-1.0.0.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hibernate/javax/persistence/hibernate-jpa-2.1-api/1.0.0.Final/hibernate-jpa-2.1-api-1.0.0.Final-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/hibernate/javax/persistence/hibernate-jpa-2.1-api/1.0.0.Final/hibernate-jpa-2.1-api-1.0.0.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/javassist/javassist/3.21.0-GA/javassist-3.21.0-GA-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/antlr/antlr/2.7.7/antlr-2.7.7.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/jboss/jandex/2.0.0.Final/jandex-2.0.0.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/jboss/jandex/2.0.0.Final/jandex-2.0.0.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.0.1.Final/hibernate-commons-annotations-5.0.1.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/5.0.1.Final/hibernate-commons-annotations-5.0.1.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hibernate/hibernate-entitymanager/5.0.12.Final/hibernate-entitymanager-5.0.12.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hibernate/hibernate-entitymanager/5.0.12.Final/hibernate-entitymanager-5.0.12.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/transaction/javax.transaction-api/1.2/javax.transaction-api-1.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/transaction/javax.transaction-api/1.2/javax.transaction-api-1.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/transaction/javax.transaction-api/1.2/javax.transaction-api-1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/1.11.3.RELEASE/spring-data-jpa-1.11.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/1.11.3.RELEASE/spring-data-jpa-1.11.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/1.11.3.RELEASE/spring-data-jpa-1.11.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.13.3.RELEASE/spring-data-commons-1.13.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.13.3.RELEASE/spring-data-commons-1.13.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.13.3.RELEASE/spring-data-commons-1.13.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-orm/4.3.8.RELEASE/spring-orm-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-orm/4.3.8.RELEASE/spring-orm-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-orm/4.3.8.RELEASE/spring-orm-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-tx/4.3.8.RELEASE/spring-tx-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-tx/4.3.8.RELEASE/spring-tx-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-tx/4.3.8.RELEASE/spring-tx-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-beans/4.3.8.RELEASE/spring-beans-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-beans/4.3.8.RELEASE/spring-beans-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-beans/4.3.8.RELEASE/spring-beans-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-aspects/4.3.8.RELEASE/spring-aspects-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-aspects/4.3.8.RELEASE/spring-aspects-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-aspects/4.3.8.RELEASE/spring-aspects-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.3.RELEASE/spring-boot-starter-web-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/1.5.3.RELEASE/spring-boot-starter-web-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.3.5.Final/hibernate-validator-5.3.5.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hibernate/hibernate-validator/5.3.5.Final/hibernate-validator-5.3.5.Final-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/fasterxml/classmate/1.3.3/classmate-1.3.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.3.3/classmate-1.3.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.3.3/classmate-1.3.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.8.8/jackson-databind-2.8.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.8.8/jackson-databind-2.8.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.8.8/jackson-databind-2.8.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.8.0/jackson-annotations-2.8.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.8.0/jackson-annotations-2.8.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.8.0/jackson-annotations-2.8.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.8.8/jackson-core-2.8.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.8.8/jackson-core-2.8.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.8.8/jackson-core-2.8.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-web/4.3.8.RELEASE/spring-web-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-web/4.3.8.RELEASE/spring-web-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-web/4.3.8.RELEASE/spring-web-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.8.RELEASE/spring-webmvc-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.8.RELEASE/spring-webmvc-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/4.3.8.RELEASE/spring-webmvc-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-expression/4.3.8.RELEASE/spring-expression-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-expression/4.3.8.RELEASE/spring-expression-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-expression/4.3.8.RELEASE/spring-expression-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/postgresql/postgresql/9.4.1212.jre7/postgresql-9.4.1212.jre7.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/postgresql/postgresql/9.4.1212.jre7/postgresql-9.4.1212.jre7-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/postgresql/postgresql/9.4.1212.jre7/postgresql-9.4.1212.jre7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/1.5.3.RELEASE/spring-boot-starter-test-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/1.5.3.RELEASE/spring-boot-starter-test-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/1.5.3.RELEASE/spring-boot-test-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/1.5.3.RELEASE/spring-boot-test-1.5.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/1.5.3.RELEASE/spring-boot-test-1.5.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/1.5.3.RELEASE/spring-boot-test-autoconfigure-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/1.5.3.RELEASE/spring-boot-test-autoconfigure-1.5.3.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/1.5.3.RELEASE/spring-boot-test-autoconfigure-1.5.3.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.2.0/json-path-2.2.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.2.0/json-path-2.2.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.2.0/json-path-2.2.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/minidev/json-smart/2.2.1/json-smart-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/minidev/json-smart/2.2.1/json-smart-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/minidev/json-smart/2.2.1/json-smart-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/minidev/accessors-smart/1.1/accessors-smart-1.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/minidev/accessors-smart/1.1/accessors-smart-1.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/minidev/accessors-smart/1.1/accessors-smart-1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/ow2/asm/asm/5.0.3/asm-5.0.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/ow2/asm/asm/5.0.3/asm-5.0.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/ow2/asm/asm/5.0.3/asm-5.0.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/junit/junit/4.12/junit-4.12-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/junit/junit/4.12/junit-4.12-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/assertj/assertj-core/2.6.0/assertj-core-2.6.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/assertj/assertj-core/2.6.0/assertj-core-2.6.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/assertj/assertj-core/2.6.0/assertj-core-2.6.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/mockito/mockito-core/1.10.19/mockito-core-1.10.19-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/objenesis/objenesis/2.1/objenesis-2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/hamcrest/hamcrest-library/1.3/hamcrest-library-1.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-library/1.3/hamcrest-library-1.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-library/1.3/hamcrest-library-1.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.4.0/jsonassert-1.4.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.4.0/jsonassert-1.4.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.4.0/jsonassert-1.4.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-core/4.3.8.RELEASE/spring-core-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-core/4.3.8.RELEASE/spring-core-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-core/4.3.8.RELEASE/spring-core-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-test/4.3.8.RELEASE/spring-test-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-test/4.3.8.RELEASE/spring-test-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-test/4.3.8.RELEASE/spring-test-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-mail/1.5.3.RELEASE/spring-boot-starter-mail-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-mail/1.5.3.RELEASE/spring-boot-starter-mail-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-context/4.3.8.RELEASE/spring-context-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-context/4.3.8.RELEASE/spring-context-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-context/4.3.8.RELEASE/spring-context-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-context-support/4.3.8.RELEASE/spring-context-support-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-context-support/4.3.8.RELEASE/spring-context-support-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-context-support/4.3.8.RELEASE/spring-context-support-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/sun/mail/javax.mail/1.5.6/javax.mail-1.5.6.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/sun/mail/javax.mail/1.5.6/javax.mail-1.5.6-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/sun/mail/javax.mail/1.5.6/javax.mail-1.5.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/1.5.3.RELEASE/spring-boot-starter-security-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/1.5.3.RELEASE/spring-boot-starter-security-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/spring-aop/4.3.8.RELEASE/spring-aop-4.3.8.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-aop/4.3.8.RELEASE/spring-aop-4.3.8.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/spring-aop/4.3.8.RELEASE/spring-aop-4.3.8.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/4.2.2.RELEASE/spring-security-config-4.2.2.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/4.2.2.RELEASE/spring-security-config-4.2.2.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/4.2.2.RELEASE/spring-security-config-4.2.2.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/4.2.2.RELEASE/spring-security-core-4.2.2.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/4.2.2.RELEASE/spring-security-core-4.2.2.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/4.2.2.RELEASE/spring-security-core-4.2.2.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/4.2.2.RELEASE/spring-security-web-4.2.2.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/4.2.2.RELEASE/spring-security-web-4.2.2.RELEASE-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/4.2.2.RELEASE/spring-security-web-4.2.2.RELEASE-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/poi/poi/3.10-FINAL/poi-3.10-FINAL-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-codec/commons-codec/1.10/commons-codec-1.10.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.10/commons-codec-1.10-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-codec/commons-codec/1.10/commons-codec-1.10-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.10-FINAL/poi-ooxml-3.10-FINAL-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.10-FINAL/poi-ooxml-schemas-3.10-FINAL.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.3.0/xmlbeans-2.3.0.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/xml-apis/xml-apis/1.4.01/xml-apis-1.4.01-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.4/commons-lang3-3.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.4/commons-lang3-3.4-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.4/commons-lang3-3.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.3.RELEASE/spring-boot-starter-tomcat-1.5.3.RELEASE.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/1.5.3.RELEASE/spring-boot-starter-tomcat-1.5.3.RELEASE-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.14/tomcat-embed-core-8.5.14.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/8.5.14/tomcat-embed-core-8.5.14-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.14/tomcat-embed-el-8.5.14.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/8.5.14/tomcat-embed-el-8.5.14-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.14/tomcat-embed-websocket-8.5.14.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/8.5.14/tomcat-embed-websocket-8.5.14-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-core_2.11/2.2.1/spark-core_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-core_2.11/2.2.1/spark-core_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-core_2.11/2.2.1/spark-core_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/avro/avro/1.7.7/avro-1.7.7.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro/1.7.7/avro-1.7.7-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro/1.7.7/avro-1.7.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.9.13/jackson-core-asl-1.9.13.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.9.13/jackson-core-asl-1.9.13-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.9.13/jackson-core-asl-1.9.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.9.13/jackson-mapper-asl-1.9.13.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.9.13/jackson-mapper-asl-1.9.13-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.9.13/jackson-mapper-asl-1.9.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.3/paranamer-2.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.3/paranamer-2.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/thoughtworks/paranamer/paranamer/2.3/paranamer-2.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/commons/commons-compress/1.4.1/commons-compress-1.4.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-compress/1.4.1/commons-compress-1.4.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-compress/1.4.1/commons-compress-1.4.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/tukaani/xz/1.0/xz-1.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/tukaani/xz/1.0/xz-1.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/tukaani/xz/1.0/xz-1.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/avro/avro-mapred/1.7.7/avro-mapred-1.7.7-hadoop2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro-mapred/1.7.7/avro-mapred-1.7.7-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro-mapred/1.7.7/avro-mapred-1.7.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/avro/avro-ipc/1.7.7/avro-ipc-1.7.7.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro-ipc/1.7.7/avro-ipc-1.7.7-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro-ipc/1.7.7/avro-ipc-1.7.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/avro/avro-ipc/1.7.7/avro-ipc-1.7.7-tests.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro-ipc/1.7.7/avro-ipc-1.7.7-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/avro/avro-ipc/1.7.7/avro-ipc-1.7.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/twitter/chill_2.11/0.8.0/chill_2.11-0.8.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/twitter/chill_2.11/0.8.0/chill_2.11-0.8.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/twitter/chill_2.11/0.8.0/chill_2.11-0.8.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/esotericsoftware/kryo-shaded/3.0.3/kryo-shaded-3.0.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/esotericsoftware/kryo-shaded/3.0.3/kryo-shaded-3.0.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/esotericsoftware/kryo-shaded/3.0.3/kryo-shaded-3.0.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/esotericsoftware/minlog/1.3.0/minlog-1.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/twitter/chill-java/0.8.0/chill-java-0.8.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/twitter/chill-java/0.8.0/chill-java-0.8.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/twitter/chill-java/0.8.0/chill-java-0.8.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/xbean/xbean-asm5-shaded/4.4/xbean-asm5-shaded-4.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/xbean/xbean-asm5-shaded/4.4/xbean-asm5-shaded-4.4-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-client/2.6.5/hadoop-client-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-client/2.6.5/hadoop-client-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-client/2.6.5/hadoop-client-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-common/2.6.5/hadoop-common-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-common/2.6.5/hadoop-common-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-common/2.6.5/hadoop-common-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-cli/commons-cli/1.2/commons-cli-1.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-cli/commons-cli/1.2/commons-cli-1.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-cli/commons-cli/1.2/commons-cli-1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/xmlenc/xmlenc/0.52/xmlenc-0.52.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-httpclient/commons-httpclient/3.1/commons-httpclient-3.1-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-configuration/commons-configuration/1.6/commons-configuration-1.6.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-configuration/commons-configuration/1.6/commons-configuration-1.6-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-digester/commons-digester/2.1/commons-digester-2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.3/commons-beanutils-1.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils-core/1.8.0/commons-beanutils-core-1.8.0.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/google/protobuf/protobuf-java/2.5.0/protobuf-java-2.5.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/google/protobuf/protobuf-java/2.5.0/protobuf-java-2.5.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/google/protobuf/protobuf-java/2.5.0/protobuf-java-2.5.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/google/code/gson/gson/2.8.0/gson-2.8.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/google/code/gson/gson/2.8.0/gson-2.8.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/google/code/gson/gson/2.8.0/gson-2.8.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-auth/2.6.5/hadoop-auth-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-auth/2.6.5/hadoop-auth-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-auth/2.6.5/hadoop-auth-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/directory/server/apacheds-kerberos-codec/2.0.0-M15/apacheds-kerberos-codec-2.0.0-M15.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/server/apacheds-kerberos-codec/2.0.0-M15/apacheds-kerberos-codec-2.0.0-M15-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/server/apacheds-kerberos-codec/2.0.0-M15/apacheds-kerberos-codec-2.0.0-M15-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/directory/server/apacheds-i18n/2.0.0-M15/apacheds-i18n-2.0.0-M15.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/server/apacheds-i18n/2.0.0-M15/apacheds-i18n-2.0.0-M15-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/server/apacheds-i18n/2.0.0-M15/apacheds-i18n-2.0.0-M15-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/directory/api/api-asn1-api/1.0.0-M20/api-asn1-api-1.0.0-M20.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/api/api-asn1-api/1.0.0-M20/api-asn1-api-1.0.0-M20-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/api/api-asn1-api/1.0.0-M20/api-asn1-api-1.0.0-M20-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/directory/api/api-util/1.0.0-M20/api-util-1.0.0-M20.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/api/api-util/1.0.0-M20/api-util-1.0.0-M20-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/directory/api/api-util/1.0.0-M20/api-util-1.0.0-M20-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/curator/curator-client/2.6.0/curator-client-2.6.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/curator/curator-client/2.6.0/curator-client-2.6.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/curator/curator-client/2.6.0/curator-client-2.6.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/htrace/htrace-core/3.0.4/htrace-core-3.0.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/htrace/htrace-core/3.0.4/htrace-core-3.0.4-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/htrace/htrace-core/3.0.4/htrace-core-3.0.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-hdfs/2.6.5/hadoop-hdfs-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-hdfs/2.6.5/hadoop-hdfs-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-hdfs/2.6.5/hadoop-hdfs-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/mortbay/jetty/jetty-util/6.1.26/jetty-util-6.1.26.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/mortbay/jetty/jetty-util/6.1.26/jetty-util-6.1.26-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/mortbay/jetty/jetty-util/6.1.26/jetty-util-6.1.26-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/xerces/xercesImpl/2.9.1/xercesImpl-2.9.1.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-app/2.6.5/hadoop-mapreduce-client-app-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-app/2.6.5/hadoop-mapreduce-client-app-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-app/2.6.5/hadoop-mapreduce-client-app-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-common/2.6.5/hadoop-mapreduce-client-common-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-common/2.6.5/hadoop-mapreduce-client-common-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-common/2.6.5/hadoop-mapreduce-client-common-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-client/2.6.5/hadoop-yarn-client-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-client/2.6.5/hadoop-yarn-client-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-client/2.6.5/hadoop-yarn-client-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-server-common/2.6.5/hadoop-yarn-server-common-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-server-common/2.6.5/hadoop-yarn-server-common-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-server-common/2.6.5/hadoop-yarn-server-common-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-shuffle/2.6.5/hadoop-mapreduce-client-shuffle-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-shuffle/2.6.5/hadoop-mapreduce-client-shuffle-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-shuffle/2.6.5/hadoop-mapreduce-client-shuffle-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-api/2.6.5/hadoop-yarn-api-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-api/2.6.5/hadoop-yarn-api-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-api/2.6.5/hadoop-yarn-api-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-core/2.6.5/hadoop-mapreduce-client-core-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-core/2.6.5/hadoop-mapreduce-client-core-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-core/2.6.5/hadoop-mapreduce-client-core-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-common/2.6.5/hadoop-yarn-common-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-common/2.6.5/hadoop-yarn-common-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-common/2.6.5/hadoop-yarn-common-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.2.2/jaxb-api-2.2.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/xml/stream/stax-api/1.0-2/stax-api-1.0-2-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.9.13/jackson-jaxrs-1.9.13.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.9.13/jackson-jaxrs-1.9.13-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.9.13/jackson-jaxrs-1.9.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-xc/1.9.13/jackson-xc-1.9.13.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-xc/1.9.13/jackson-xc-1.9.13-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/jackson/jackson-xc/1.9.13/jackson-xc-1.9.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-jobclient/2.6.5/hadoop-mapreduce-client-jobclient-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-jobclient/2.6.5/hadoop-mapreduce-client-jobclient-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-jobclient/2.6.5/hadoop-mapreduce-client-jobclient-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-annotations/2.6.5/hadoop-annotations-2.6.5.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-annotations/2.6.5/hadoop-annotations-2.6.5-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/hadoop/hadoop-annotations/2.6.5/hadoop-annotations-2.6.5-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-launcher_2.11/2.2.1/spark-launcher_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-launcher_2.11/2.2.1/spark-launcher_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-launcher_2.11/2.2.1/spark-launcher_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-network-common_2.11/2.2.1/spark-network-common_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-network-common_2.11/2.2.1/spark-network-common_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-network-common_2.11/2.2.1/spark-network-common_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/fusesource/leveldbjni/leveldbjni-all/1.8/leveldbjni-all-1.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/fusesource/leveldbjni/leveldbjni-all/1.8/leveldbjni-all-1.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/fusesource/leveldbjni/leveldbjni-all/1.8/leveldbjni-all-1.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-network-shuffle_2.11/2.2.1/spark-network-shuffle_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-network-shuffle_2.11/2.2.1/spark-network-shuffle_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-network-shuffle_2.11/2.2.1/spark-network-shuffle_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-unsafe_2.11/2.2.1/spark-unsafe_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-unsafe_2.11/2.2.1/spark-unsafe_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-unsafe_2.11/2.2.1/spark-unsafe_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/java/dev/jets3t/jets3t/0.9.3/jets3t-0.9.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/java/dev/jets3t/jets3t/0.9.3/jets3t-0.9.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/java/dev/jets3t/jets3t/0.9.3/jets3t-0.9.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.6/httpcore-4.4.6.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.6/httpcore-4.4.6-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.6/httpcore-4.4.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.3/httpclient-4.5.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/mx4j/mx4j/3.0.2/mx4j-3.0.2.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/mail/mail/1.4.7/mail-1.4.7.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/mail/mail/1.4.7/mail-1.4.7-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/mail/mail/1.4.7/mail-1.4.7-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.51/bcprov-jdk15on-1.51.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.51/bcprov-jdk15on-1.51-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.51/bcprov-jdk15on-1.51-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/jamesmurty/utils/java-xmlbuilder/1.0/java-xmlbuilder-1.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/jamesmurty/utils/java-xmlbuilder/1.0/java-xmlbuilder-1.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/jamesmurty/utils/java-xmlbuilder/1.0/java-xmlbuilder-1.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/iharder/base64/2.3.8/base64-2.3.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/iharder/base64/2.3.8/base64-2.3.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/iharder/base64/2.3.8/base64-2.3.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/curator/curator-recipes/2.6.0/curator-recipes-2.6.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/curator/curator-recipes/2.6.0/curator-recipes-2.6.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/curator/curator-recipes/2.6.0/curator-recipes-2.6.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/curator/curator-framework/2.6.0/curator-framework-2.6.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/curator/curator-framework/2.6.0/curator-framework-2.6.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/curator/curator-framework/2.6.0/curator-framework-2.6.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/google/guava/guava/16.0.1/guava-16.0.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/google/guava/guava/16.0.1/guava-16.0.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/google/guava/guava/16.0.1/guava-16.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/commons/commons-math3/3.4.1/commons-math3-3.4.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-math3/3.4.1/commons-math3-3.4.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-math3/3.4.1/commons-math3-3.4.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/google/code/findbugs/jsr305/1.3.9/jsr305-1.3.9.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.25/jcl-over-slf4j-1.7.25.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.25/jcl-over-slf4j-1.7.25-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.25/jcl-over-slf4j-1.7.25-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/ning/compress-lzf/1.0.3/compress-lzf-1.0.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/ning/compress-lzf/1.0.3/compress-lzf-1.0.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/ning/compress-lzf/1.0.3/compress-lzf-1.0.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.2.6/snappy-java-1.1.2.6-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/0.5.11/RoaringBitmap-0.5.11.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/0.5.11/RoaringBitmap-0.5.11-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/0.5.11/RoaringBitmap-0.5.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/commons-net/commons-net/2.2/commons-net-2.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/commons-net/commons-net/2.2/commons-net-2.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/commons-net/commons-net/2.2/commons-net-2.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scala-lang/scala-library/2.11.8/scala-library-2.11.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scala-library/2.11.8/scala-library-2.11.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scala-library/2.11.8/scala-library-2.11.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/json4s/json4s-jackson_2.11/3.2.11/json4s-jackson_2.11-3.2.11.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/json4s/json4s-jackson_2.11/3.2.11/json4s-jackson_2.11-3.2.11-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/json4s/json4s-jackson_2.11/3.2.11/json4s-jackson_2.11-3.2.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/json4s/json4s-core_2.11/3.2.11/json4s-core_2.11-3.2.11.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/json4s/json4s-core_2.11/3.2.11/json4s-core_2.11-3.2.11-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/json4s/json4s-core_2.11/3.2.11/json4s-core_2.11-3.2.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/json4s/json4s-ast_2.11/3.2.11/json4s-ast_2.11-3.2.11.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/json4s/json4s-ast_2.11/3.2.11/json4s-ast_2.11-3.2.11-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/json4s/json4s-ast_2.11/3.2.11/json4s-ast_2.11-3.2.11-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scala-lang/scalap/2.11.0/scalap-2.11.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scalap/2.11.0/scalap-2.11.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scalap/2.11.0/scalap-2.11.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scala-lang/scala-compiler/2.11.0/scala-compiler-2.11.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scala-compiler/2.11.0/scala-compiler-2.11.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scala-compiler/2.11.0/scala-compiler-2.11.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scala-lang/modules/scala-xml_2.11/1.0.1/scala-xml_2.11-1.0.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/modules/scala-xml_2.11/1.0.1/scala-xml_2.11-1.0.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/modules/scala-xml_2.11/1.0.1/scala-xml_2.11-1.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scala-lang/modules/scala-parser-combinators_2.11/1.0.1/scala-parser-combinators_2.11-1.0.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/modules/scala-parser-combinators_2.11/1.0.1/scala-parser-combinators_2.11-1.0.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/modules/scala-parser-combinators_2.11/1.0.1/scala-parser-combinators_2.11-1.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/2.22.2/jersey-client-2.22.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/2.22.2/jersey-client-2.22.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-client/2.22.2/jersey-client-2.22.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.0.1/javax.ws.rs-api-2.0.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.0.1/javax.ws.rs-api-2.0.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.0.1/javax.ws.rs-api-2.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.4.0-b34/hk2-api-2.4.0-b34.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.4.0-b34/hk2-api-2.4.0-b34-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-api/2.4.0-b34/hk2-api-2.4.0-b34-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.4.0-b34/hk2-utils-2.4.0-b34.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.4.0-b34/hk2-utils-2.4.0-b34-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-utils/2.4.0-b34/hk2-utils-2.4.0-b34-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.4.0-b34/aopalliance-repackaged-2.4.0-b34.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.4.0-b34/aopalliance-repackaged-2.4.0-b34-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/external/aopalliance-repackaged/2.4.0-b34/aopalliance-repackaged-2.4.0-b34-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/hk2/external/javax.inject/2.4.0-b34/javax.inject-2.4.0-b34.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/external/javax.inject/2.4.0-b34/javax.inject-2.4.0-b34-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/external/javax.inject/2.4.0-b34/javax.inject-2.4.0-b34-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.4.0-b34/hk2-locator-2.4.0-b34.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.4.0-b34/hk2-locator-2.4.0-b34-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/hk2-locator/2.4.0-b34/hk2-locator-2.4.0-b34-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-common/2.22.2/jersey-common-2.22.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-common/2.22.2/jersey-common-2.22.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-common/2.22.2/jersey-common-2.22.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.2/javax.annotation-api-1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/bundles/repackaged/jersey-guava/2.22.2/jersey-guava-2.22.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/bundles/repackaged/jersey-guava/2.22.2/jersey-guava-2.22.2-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/hk2/osgi-resource-locator/1.0.1/osgi-resource-locator-1.0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-server/2.25.1/jersey-server-2.25.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-server/2.25.1/jersey-server-2.25.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/core/jersey-server/2.25.1/jersey-server-2.25.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-jaxb/2.25.1/jersey-media-jaxb-2.25.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-jaxb/2.25.1/jersey-media-jaxb-2.25.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/media/jersey-media-jaxb/2.25.1/jersey-media-jaxb-2.25.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/2.25.1/jersey-container-servlet-2.25.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/2.25.1/jersey-container-servlet-2.25.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet/2.25.1/jersey-container-servlet-2.25.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet-core/2.25.1/jersey-container-servlet-core-2.25.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet-core/2.25.1/jersey-container-servlet-core-2.25.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/glassfish/jersey/containers/jersey-container-servlet-core/2.25.1/jersey-container-servlet-core-2.25.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/io/netty/netty-all/4.0.43.Final/netty-all-4.0.43.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/io/netty/netty-all/4.0.43.Final/netty-all-4.0.43.Final-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/io/netty/netty-all/4.0.43.Final/netty-all-4.0.43.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/io/netty/netty/3.9.9.Final/netty-3.9.9.Final.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/io/netty/netty/3.9.9.Final/netty-3.9.9.Final-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/io/netty/netty/3.9.9.Final/netty-3.9.9.Final-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/clearspring/analytics/stream/2.7.0/stream-2.7.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/clearspring/analytics/stream/2.7.0/stream-2.7.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/clearspring/analytics/stream/2.7.0/stream-2.7.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/3.1.4/metrics-core-3.1.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/3.1.4/metrics-core-3.1.4-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-core/3.1.4/metrics-core-3.1.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jvm/3.1.2/metrics-jvm-3.1.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jvm/3.1.2/metrics-jvm-3.1.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-jvm/3.1.2/metrics-jvm-3.1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-json/3.1.2/metrics-json-3.1.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-json/3.1.2/metrics-json-3.1.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-json/3.1.2/metrics-json-3.1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-graphite/3.1.4/metrics-graphite-3.1.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-graphite/3.1.4/metrics-graphite-3.1.4-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/io/dropwizard/metrics/metrics-graphite/3.1.4/metrics-graphite-3.1.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-scala_2.11/2.8.8/jackson-module-scala_2.11-2.8.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-scala_2.11/2.8.8/jackson-module-scala_2.11-2.8.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-scala_2.11/2.8.8/jackson-module-scala_2.11-2.8.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scala-lang/scala-reflect/2.11.8/scala-reflect-2.11.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scala-reflect/2.11.8/scala-reflect-2.11.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scala-lang/scala-reflect/2.11.8/scala-reflect-2.11.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-paranamer/2.8.8/jackson-module-paranamer-2.8.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-paranamer/2.8.8/jackson-module-paranamer-2.8.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-paranamer/2.8.8/jackson-module-paranamer-2.8.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/ivy/ivy/2.4.0/ivy-2.4.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/ivy/ivy/2.4.0/ivy-2.4.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/ivy/ivy/2.4.0/ivy-2.4.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/oro/oro/2.0.8/oro-2.0.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/oro/oro/2.0.8/oro-2.0.8-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/razorvine/pyrolite/4.13/pyrolite-4.13.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/razorvine/pyrolite/4.13/pyrolite-4.13-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/razorvine/pyrolite/4.13/pyrolite-4.13-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/sf/py4j/py4j/0.10.4/py4j-0.10.4.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/sf/py4j/py4j/0.10.4/py4j-0.10.4-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/sf/py4j/py4j/0.10.4/py4j-0.10.4-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-tags_2.11/2.2.1/spark-tags_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-tags_2.11/2.2.1/spark-tags_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-tags_2.11/2.2.1/spark-tags_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/commons/commons-crypto/1.0.0/commons-crypto-1.0.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-crypto/1.0.0/commons-crypto-1.0.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/commons/commons-crypto/1.0.0/commons-crypto-1.0.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/spark-project/spark/unused/1.0.0/unused-1.0.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/spark-project/spark/unused/1.0.0/unused-1.0.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/spark-project/spark/unused/1.0.0/unused-1.0.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-mllib_2.11/2.2.1/spark-mllib_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-mllib_2.11/2.2.1/spark-mllib_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-mllib_2.11/2.2.1/spark-mllib_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-streaming_2.11/2.2.1/spark-streaming_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-streaming_2.11/2.2.1/spark-streaming_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-streaming_2.11/2.2.1/spark-streaming_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-sql_2.11/2.2.1/spark-sql_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-sql_2.11/2.2.1/spark-sql_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-sql_2.11/2.2.1/spark-sql_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/univocity/univocity-parsers/2.2.1/univocity-parsers-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/univocity/univocity-parsers/2.2.1/univocity-parsers-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/univocity/univocity-parsers/2.2.1/univocity-parsers-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-sketch_2.11/2.2.1/spark-sketch_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-sketch_2.11/2.2.1/spark-sketch_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-sketch_2.11/2.2.1/spark-sketch_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-catalyst_2.11/2.2.1/spark-catalyst_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-catalyst_2.11/2.2.1/spark-catalyst_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-catalyst_2.11/2.2.1/spark-catalyst_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/codehaus/janino/janino/2.7.8/janino-2.7.8.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/janino/janino/2.7.8/janino-2.7.8-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/janino/janino/2.7.8/janino-2.7.8-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.0.0/commons-compiler-3.0.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.0.0/commons-compiler-3.0.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/codehaus/janino/commons-compiler/3.0.0/commons-compiler-3.0.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.5.3/antlr4-runtime-4.5.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.5.3/antlr4-runtime-4.5.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.5.3/antlr4-runtime-4.5.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/parquet/parquet-column/1.8.2/parquet-column-1.8.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-column/1.8.2/parquet-column-1.8.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-column/1.8.2/parquet-column-1.8.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/parquet/parquet-common/1.8.2/parquet-common-1.8.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-common/1.8.2/parquet-common-1.8.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-common/1.8.2/parquet-common-1.8.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/parquet/parquet-encoding/1.8.2/parquet-encoding-1.8.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-encoding/1.8.2/parquet-encoding-1.8.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-encoding/1.8.2/parquet-encoding-1.8.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/parquet/parquet-hadoop/1.8.2/parquet-hadoop-1.8.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-hadoop/1.8.2/parquet-hadoop-1.8.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-hadoop/1.8.2/parquet-hadoop-1.8.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/parquet/parquet-format/2.3.1/parquet-format-2.3.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-format/2.3.1/parquet-format-2.3.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-format/2.3.1/parquet-format-2.3.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/parquet/parquet-jackson/1.8.2/parquet-jackson-1.8.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/parquet/parquet-jackson/1.8.2/parquet-jackson-1.8.2-sources.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-graphx_2.11/2.2.1/spark-graphx_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-graphx_2.11/2.2.1/spark-graphx_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-graphx_2.11/2.2.1/spark-graphx_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/github/fommil/netlib/core/1.1.2/core-1.1.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/github/fommil/netlib/core/1.1.2/core-1.1.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/github/fommil/netlib/core/1.1.2/core-1.1.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/sourceforge/f2j/arpack_combined_all/0.1/arpack_combined_all-0.1.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/sourceforge/f2j/arpack_combined_all/0.1/arpack_combined_all-0.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/apache/spark/spark-mllib-local_2.11/2.2.1/spark-mllib-local_2.11-2.2.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-mllib-local_2.11/2.2.1/spark-mllib-local_2.11-2.2.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/apache/spark/spark-mllib-local_2.11/2.2.1/spark-mllib-local_2.11-2.2.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scalanlp/breeze_2.11/0.13.2/breeze_2.11-0.13.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scalanlp/breeze_2.11/0.13.2/breeze_2.11-0.13.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scalanlp/breeze_2.11/0.13.2/breeze_2.11-0.13.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/scalanlp/breeze-macros_2.11/0.13.2/breeze-macros_2.11-0.13.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/scalanlp/breeze-macros_2.11/0.13.2/breeze-macros_2.11-0.13.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/scalanlp/breeze-macros_2.11/0.13.2/breeze-macros_2.11-0.13.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/net/sf/opencsv/opencsv/2.3/opencsv-2.3.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/net/sf/opencsv/opencsv/2.3/opencsv-2.3-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/net/sf/opencsv/opencsv/2.3/opencsv-2.3-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/github/rwl/jtransforms/2.4.0/jtransforms-2.4.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/github/rwl/jtransforms/2.4.0/jtransforms-2.4.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/github/rwl/jtransforms/2.4.0/jtransforms-2.4.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/spire-math/spire_2.11/0.13.0/spire_2.11-0.13.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/spire-math/spire_2.11/0.13.0/spire_2.11-0.13.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/spire-math/spire_2.11/0.13.0/spire_2.11-0.13.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/spire-math/spire-macros_2.11/0.13.0/spire-macros_2.11-0.13.0.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/spire-math/spire-macros_2.11/0.13.0/spire-macros_2.11-0.13.0-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/spire-math/spire-macros_2.11/0.13.0/spire-macros_2.11-0.13.0-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/typelevel/machinist_2.11/0.6.1/machinist_2.11-0.6.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/typelevel/machinist_2.11/0.6.1/machinist_2.11-0.6.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/typelevel/machinist_2.11/0.6.1/machinist_2.11-0.6.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/com/chuusai/shapeless_2.11/2.3.2/shapeless_2.11-2.3.2.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/com/chuusai/shapeless_2.11/2.3.2/shapeless_2.11-2.3.2-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/com/chuusai/shapeless_2.11/2.3.2/shapeless_2.11-2.3.2-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/typelevel/macro-compat_2.11/1.1.1/macro-compat_2.11-1.1.1.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/typelevel/macro-compat_2.11/1.1.1/macro-compat_2.11-1.1.1-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/typelevel/macro-compat_2.11/1.1.1/macro-compat_2.11-1.1.1-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/jpmml/pmml-model/1.2.15/pmml-model-1.2.15.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/jpmml/pmml-model/1.2.15/pmml-model-1.2.15-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/jpmml/pmml-model/1.2.15/pmml-model-1.2.15-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/org/jpmml/pmml-schema/1.2.15/pmml-schema-1.2.15.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/org/jpmml/pmml-schema/1.2.15/pmml-schema-1.2.15-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/org/jpmml/pmml-schema/1.2.15/pmml-schema-1.2.15-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "binary", "path": "/home/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17.jar", "sourceContainerUrl": "file:/home/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17-sources.jar", "javadocContainerUrl": "file:/home/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17-javadoc.jar", "isSystem": false, "isOwn": false, "isTest": false, "isJavaContent": false}, {"kind": "source", "path": "/home/<USER>/workspace/birdnotes/farmavans-web-back/src/main/java", "outputFolder": "/home/<USER>/workspace/birdnotes/farmavans-web-back/target/classes", "javadocContainerUrl": "file:/home/<USER>/workspace/birdnotes/farmavans-web-back/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": true}, {"kind": "source", "path": "/home/<USER>/workspace/birdnotes/farmavans-web-back/src/test/java", "outputFolder": "/home/<USER>/workspace/birdnotes/farmavans-web-back/target/test-classes", "javadocContainerUrl": "file:/home/<USER>/workspace/birdnotes/farmavans-web-back/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": true, "isJavaContent": true}, {"kind": "source", "path": "/home/<USER>/workspace/birdnotes/farmavans-web-back/src/main/resources", "outputFolder": "/home/<USER>/workspace/birdnotes/farmavans-web-back/target/classes", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": false}, {"kind": "source", "path": "/home/<USER>/workspace/birdnotes/farmavans-web-back/src/test/resources", "outputFolder": "/home/<USER>/workspace/birdnotes/farmavans-web-back/target/test-classes", "isSystem": false, "isOwn": true, "isTest": true, "isJavaContent": false}, {"kind": "source", "path": "/home/<USER>/workspace/birdnotes/farmavans-web-back/src/test/java", "outputFolder": "/home/<USER>/workspace/birdnotes/farmavans-web-back/target/test-classes", "javadocContainerUrl": "file:/home/<USER>/workspace/birdnotes/farmavans-web-back/target/site/apidocs", "isSystem": false, "isOwn": true, "isTest": false, "isJavaContent": false}]}