package com.intellitech.birdnotes.service;

import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.intellitech.birdnotes.data.dto.ReportMessageRequestDto;
import com.intellitech.birdnotes.data.dto.OrderPredictionRequestDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.ActivityByPeriod;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.ProspectMessage;
import com.intellitech.birdnotes.model.dto.VisitAveragePerDayDto;
import com.intellitech.birdnotes.model.dto.VisitDto;
import com.intellitech.birdnotes.model.dto.VisitHistorySummaryDto;
import com.intellitech.birdnotes.model.dto.VisitProspectDto;
import com.intellitech.birdnotes.model.dto.VisitRequestDto;


public interface VisitService {
	
	VisitHistorySummaryDto getCoverage(VisitRequestDto coverageRequestDto) throws BirdnotesException;

	VisitHistorySummaryDto getVisitHistory(VisitRequestDto visitRequestDto) throws BirdnotesException;

	List<VisitAveragePerDayDto> visitAveragePerDay(Date mondayDate) throws BirdnotesException;

	List<VisitProspectDto> visitProspect(Date mondayDate, Long specialityId);

	List<VisitAveragePerDayDto> visitAveragePerMonth(Integer month, Integer year);

	List<ActivityByPeriod> activityByPeriod(Date startDate, Date endDate);

	List<ActivityByPeriod> activityByPotential(Date startDate, Date endDate);

	List<ActivityByPeriod> activityByDelegate(Date startDate, Date endDate);

	List<ActivityByPeriod> activityBySpeciality(Date startDate, Date endDate);

	List<LabelValueDto> getProspectsSatisfaction(Date startDate, Date endDate);
	
	List<ProspectMessage> getUrgentMessages(Date startDate, Date endDate);

	Float getSalesRevenuee(Date startDate, Date endDate);

	List<VisitDto> getVisitReport(ReportMessageRequestDto messageRequest);

	Map<Long, Float> getUsersWorkingDays(Date startDate, Date endDate);
	
	void validatePurchaseOrder(Long purchaseOrderId);

	void refusePurchaseOrder(Long purchaseOrderId);


}
