spring.profiles.active=@activatedProperties@

# HIBERNATE
spring.jpa.show-sql=false
spring.jpa.database=POSTGRESQL
spring.jpa.hibernate.ddl-auto=update
spring.jpa.hibernate.naming.strategy=org.hibernate.cfg.ImprovedNamingStrategy
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.PostgreSQLDialect

# SMTP CONFIG
spring.mail.host=mail.supremecluster.com
spring.mail.username=<EMAIL>
spring.mail.password=7K7Zr3dPq(
spring.mail.properties.mail.smtp.auth = true
spring.mail.properties.mail.smtp.socketFactory.port=465
spring.mail.properties.mail.smtp.socketFactory.class=javax.net.ssl.SSLSocketFactory
spring.mail.properties.mail.smtp.socketFactory.fallback = false
spring.mail.default-encoding=UTF-8
spring.mail.properties.mail.transport.protocol=smtp
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.from.email=<EMAIL>


spring.messages.basename=lang/messages

server.port=9090
spring.batch.job.enabled=false


spring.http.multipart.max-file-size=100MB
spring.http.multipart.max-request-size=100MB

#MAP_KEY
mapKey = AIzaSyCKAoXNryzvvGkPDSoeqQXRabNu4sKrPr0

#MAIL PROPERTIES
sendRememberSynchronizationSubject= Synchronisation
sendRememberSynchronizationHtmlBody= Bonjour,<br/><br/> Merci de synchroniser vos donn\u00e9es depuis Birdnotes mobile \n\n \

sendMailReportSubject= Dashboards de statistiques
sendMailReportHtmlBody= Bonjour,<br/><br/> Vous pouvez consulter les dashboards de statistiques de la derni\u00e8re p\u00e9riode sur ce lien : \n\n \

sendWholesalerMailSubject = Commande prospect {0} / {2}
sendWholesalerMailHtmlBodyWithAttachment = Bonjour,<br/><br/> Nous avons une nouvelle commande pour le prospect {0}. <br/> L\u2019adresse du prospect est {1} (Secteur : {4}, Localit\u00e9 : {5} ). <br/>Les d\u00e9tails de la commande sont pr\u00e9sents dans la pi\u00e8ce jointe. <br/> Merci de confirmer la r\u00e9ception de l\u2019email. <br/><br/>{3}<br/>Cordialement<br/>CRM BirdNotes\n\n \

sendWholesalerMailHtmlBodyWithoutAttachment = Bonjour,<br/><br/> Nous avons une nouvelle commande pour le prospect {0}. <br/> L\u2019adresse du prospect est {1} (Secteur : {4}, Localit\u00e9 : {5}). <br/><br/>{3}<br/>Cordialement<br/>CRM BirdNotes\n\n \

sendCancellationMailSubject = Annulation de la commande prospect {0} / {2}
sendCancellationMailHtmlBody = Bonjour,<br/><br/> Nous regrettons de vous informer que la commande du prospect {0} est annul\u00e9e. <br/> L\u2019adresse du prospect est {1} (Secteur : {4}, Localit\u00e9 : {5}). <br/><br/>{3}<br/>Cordialement<br/>CRM BirdNotes\n\n \


sendCredentialsEmailSubject = Votre compte sur Birdnotes
sendCredentialsEmailHtmlBody=Bonjour,<br/><br/> Birdnotes vous a cr\u00e9e un compte avec les param\u00e8tres suivants:<br/> \n\n \
<strong> Identifiant: </strong> {0} <br/> <strong> Mot de passe: </strong> {1} <br/> <br/> Cordialement <br/> Birdnotes.
newIssueEmailHtmlBody=Hello Admin ,<br/><br/>  You have new issue :<br/> \n\n \
<strong> Delegate: </strong> {0} <br/> <strong> Issue: </strong> {1} <br/> <br/> Cordialement <br/> Birdnotes.
editPasswordEmailSubject=Modification du mot de passe sur Birdnotes
editPasswordEmailHtmlBody=Bonjour,<br/><br/> Birdnotes a modifi\u00e9 votre compte:<br/> \n\n \
<strong> Votre mot de passe devient: </strong> {0} <br/> <br/> Cordialement <br/> Birdnotes.
sendInvestigationSubject = Enqu\u00eate de satisfaction
sendInvestigationHtmlBody = {0},<br/>{1}<br/>{2}<br/><br/>Cordialement<br/>{3}<br/>
editUsernameEmailSubject = Modification identifiant sur Birdnotes
editUsernameEmailHtmlBody = Bonjour,<br/><br/> Birdnotes a modifi\u00e9 votre compte:<br/> \n\n \
<strong> Votre identifiant devient: </strong> {0} <br/> <br/> Cordialement <br/> Birdnotes.
editUsernamePasswordEmailSubject = Modification identifiant et mot de passe sur Birdnotes
editUsernamePasswordEmailHtmlBody  = Bonjour,<br/><br/> Birdnotes a modifi\u00e9e votre compte:<br/> \n\n \
<strong> Votre identifiant devient: </strong> {0} <br/> <strong> Votre mot de passe devient: </strong> {1} <br/> <br/> Cordialement <br/> Birdnotes.

planningNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 <a href="{5}/#/planification/{2}">un nouveau planning</a>
noteFraisNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 <a href="{5}/#/expense-report/{2}" >une nouvelle note de frais </a>
actionMarketingNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 <a href="{5}/#/action-marketing/{2}" > une nouvelle action marketing</a>
opportunityNoteNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a envoy\u00e9 une nouvelle note opportunit\u00e9
locationNotificationMessage = une nouvelle localisation 
addProspectNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a ajout\u00e9 <a href="{5}/#/change-prospect/{2}" > le nouveau prospect {1}</a>
updateProspectNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a modifi\u00e9 le prospect {1}

activityNotificationMessage = Le d\u00e9l\u00e9gu\u00e9 {0} a ajout\u00e9 <a href="{5}/#/activity/{2}" >une nouvelle activit\u00e9

planificationValidationNotificationMessage = {0} a valid\u00e9 votre planning
planificationReviseNotificationMessage = {0} a demand\u00e9 de r\u00e9viser le planning
planificationRefusNotificationMessage= {0} a refus\u00e9 le planning

actionMarketingValidationNotificationMessage = {0} a valid\u00e9 votre action marketing
actionMarketingRefusNotificationMessage = {0} a refus\u00e9 votre action marketing
opportunityNoteValidationNotificationMessage = {0} a valid\u00e9 votre note d\u0027 opportunit\u00e9s
opportunitynoteRefusNotificationMessage = {0} a refus\u00e9 votre note d\u0027 opportunit\u00e9s


noteFraisValidationNotificationMessage = {0} a valid\u00e9 votre note de frais
noteFraisRefusNotificationMessage = {0} a refus\u00e9 votre note de frais

delegateCommissionValidationNotificationMessage = {0} a valid\u00e9 votre commission
delegateCommissionRefusNotificationMessage = {0} a refus\u00e9 votre commission

prospectsAcceptationNotificationMessage = {0} a accept\u00e9 le prospect {1}
prospectsModificationAcceptationNotificationMessage = {0} a accept\u00e9 les modifications du prospect {1}
prospectsRefuseNotificationMessage =  {0} a refus\u00e9 le prospect {1}

prospectsAffectationNotificationMessage = {0} vous a affect\u00e9 le nouveau prospect {1}
prospectsAffectationDeleteNotificationMessage = {0} vous a supprim\u00e9 le prospect {1}

visitMessageTag = {3} : Le d\u00e9l\u00e9gu\u00e9 {0} a visit\u00e9 le prospect {1} et vous a tag\u00e9 dans le message suivant : {4} 
importantMessageTag =Le d\u00e9l\u00e9gu\u00e9 {0} a laiss\u00e9 un  commentaire impotant lors de sa visite la {3} au prospect {1} : {4} 
urgentMessageTag =Le d\u00e9l\u00e9gu\u00e9 {0} a laiss\u00e9 un  commentaire urgent lors de sa visite le {3} au prospect {1} : {4} | produit : {6}
reportValidation =Le rapport du d\u00e9l\u00e9gu\u00e9 {0} dat\u00e9 le {3} est {7}

planningValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider un <a href="{5}/#/planification/{2}" >planning</a> cr\u00e9e par  {0}
prospectValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider un <a href="{5}/#/change-prospect/{2}" >prospect</a> cr\u00e9e par  {0}
delegateCommissionValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider une <a href="{5}/#/delegate-commission/{2}" >commission de d\u00e9l\u00e9gu\u00e9</a> {0}
actionMarketingValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider la <a href="{5}/#/action-marketing/{2}" >nouvelle action marketing</a> cr\u00e9e par  {0}
noteFraisValidationRequest=Vous \u00eates invit\u00e9s \u00e0 valider la <a href="{5}/#/expense-report/{2}" >nouvelle note de frais</a> cr\u00e9e par  {0}
