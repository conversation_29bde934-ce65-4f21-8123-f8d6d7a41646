package com.intellitech.birdnotes.controller;



import java.util.Date;
import java.util.List;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.intellitech.birdnotes.data.dto.ProspectFilterDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.VisitsProducts;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToVisit;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToVisitProduct;
import com.intellitech.birdnotes.model.dto.LocalityDto;
import com.intellitech.birdnotes.model.dto.ProductDto;
import com.intellitech.birdnotes.model.dto.VisitProductDto;
import com.intellitech.birdnotes.service.ReportService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/report")
public class ReportController {
	
	@Autowired
	private ReportService reportService;
	@Autowired
	UserService userService;
	
	@RequestMapping(value = "/getLocalitiesBySector/{sectorId}", method = RequestMethod.GET)
	public ResponseEntity<List<LocalityDto>> getLocalitiesBySector(@PathVariable("sectorId") Long sectorId) {
		try {
				List<LocalityDto> localities = reportService.findLocalityBySectorId(sectorId);
				return new ResponseEntity<>(localities, HttpStatus.OK);
			
		} catch (BirdnotesException e) {
			return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
		
		}
	}
		
	@RequestMapping(value = "/getProspects", method = RequestMethod.POST)
	public ResponseEntity<List<ProspectDto>> getProspects(@RequestBody ProspectFilterDto prospectFilterDto) {
		

		try {
			
			List<ProspectDto> prospectDto = reportService.findProspectBySectorIdAndPotentialIdAndActivityId(prospectFilterDto);
			return new ResponseEntity<>(prospectDto, HttpStatus.OK);
		} catch (BirdnotesException e) {
			return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
		
		}
		
	}	
		
		


	
	@RequestMapping(value = "getProspectVisited/{prospectId}/{date}", method = RequestMethod.GET)
	public ResponseEntity<List<ProductDto>> getProspectVisitsHistory(@PathVariable("prospectId")Long prospectId, 
			@PathVariable("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date date){
	
	try{
		List<ProductDto> productsDto = reportService.getProductsHistoryForProspect(prospectId,date);
		
		return new ResponseEntity<>(productsDto, HttpStatus.OK);
		
	} catch (Exception e) {	
	return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
	}

}
	
	@RequestMapping(value = "/getVisit/{date}", method = RequestMethod.GET)
	public ResponseEntity<List<ProspectDto>> getVisit(@PathVariable("date") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) Date date) { 
		try{
			List<ProspectDto> prospectsDto = reportService.getVisit(date);
			
			return new ResponseEntity<>(prospectsDto, HttpStatus.OK);
			
		} catch (Exception e) {	
		return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
		}
	}



@RequestMapping(value = "/getAllProspect", method = RequestMethod.GET)
public  ResponseEntity<List<ProspectDto>> getAllProspect() {
	try {
		List<ProspectDto> prospectsDto = reportService.findAllProspect();
		return new ResponseEntity<>(prospectsDto, HttpStatus.OK);	
		
	} catch (Exception e) {
		return new ResponseEntity<>(null, HttpStatus.NOT_FOUND);
	}
}

}