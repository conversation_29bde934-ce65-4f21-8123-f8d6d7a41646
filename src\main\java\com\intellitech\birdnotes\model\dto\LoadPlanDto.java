package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.Date;

public class LoadPlanDto implements Serializable {
	private static final long serialVersionUID = 1L;

	private Long id;
	private String name;
	private Date endDate;
	private Date startDate;
	private Boolean active;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}
}
