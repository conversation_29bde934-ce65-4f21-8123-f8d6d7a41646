package com.intellitech.birdnotes.model.dto;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;

public class PlannedActivityRequest {
	private Long id;
	private UserValidationStatus status;
	private String comment;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

}
