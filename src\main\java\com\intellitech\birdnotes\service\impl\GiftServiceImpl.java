package com.intellitech.birdnotes.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.enumeration.GiftType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.dto.GiftDto;
import com.intellitech.birdnotes.model.request.GadgetRequest;
import com.intellitech.birdnotes.repository.GiftRepository;
import com.intellitech.birdnotes.service.GadgetService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;


@Service("gadgetService")
@Transactional
public class GiftServiceImpl implements GadgetService {
	
	@Autowired
	private GiftRepository gadgetRepository;
	
	@Autowired
	UserService userService;

	@Override
	public Gift add(GadgetRequest gadgetRequest) throws BirdnotesException {
		
		if (gadgetRequest == null) {
			throw new RuntimeException();
		}

       Gift gadget = new Gift();
		
		Gift result = gadgetRepository.findByName(gadgetRequest.getName());
		if(result!=null) {
			throw new BirdnotesException(Exceptions.ALREADY_EXIST);
		}
		
		gadget.setName(gadgetRequest.getName());
		gadget.setPrice(gadgetRequest.getPrix());
		gadget.setType(GiftType.valueOf(gadgetRequest.getType()));
		
		Gift gadgetSaved = gadgetRepository.save(gadget);
		return gadgetSaved;
	}

	@Override
	public List<GiftDto> findAll() throws BirdnotesException {
		List<GiftDto> result = new ArrayList<>();
		List<Gift> allGadgets = gadgetRepository.findAll();
		for(Gift gadget: allGadgets) {
			GiftDto g = new GiftDto();
			g.setId(gadget.getId());
			g.setName(gadget.getName());
			g.setPrice(gadget.getPrice());
			g.setType(gadget.getType().toString());
			result.add(g);
		}
		return result;
	}
	
	@Override
	public List<GiftDto> findAllPurchaseOrderTemplateGifts() throws BirdnotesException {
		List<GiftDto> result = new ArrayList<>();
		List<Gift> allGadgets = gadgetRepository.findByGiftType(GiftType.PURCHASE_ORDER_TEMPLATE);
		for(Gift gadget: allGadgets) {
			GiftDto g = new GiftDto();
			g.setId(gadget.getId());
			g.setName(gadget.getName());
			g.setPrice(gadget.getPrice());
			g.setType(gadget.getType().toString());
			result.add(g);
		}
		return result;
	}
	
	

	@Override
	public void delete(Long id) throws BirdnotesException {
		gadgetRepository.deleteById(id);

	}

	@Override
	public Gift saveGadget(GiftDto gadgetDto) throws BirdnotesException {

	    if (gadgetDto == null || gadgetDto.getId() == null) {
	        throw new BirdnotesException("gadgetDto est null");
	    }

	    // Check if the name is null or empty
	    if (gadgetDto.getName() == null || gadgetDto.getName().isEmpty()) {
	        throw new BirdnotesException("nom du gadget est vide");
	    }

	    // Check if the price is null
	    if (gadgetDto.getPrice() == null) {
	        throw new BirdnotesException("prix du gadget est vide");
	    }

	    // Check for existing gadget with the same name
	    Gift existingGadget = gadgetRepository.findByNameAndAnotherId(gadgetDto.getName(), gadgetDto.getId());
	    if (existingGadget != null) {
	        throw new BirdnotesException(userService.getTranslatedLabel("NAME_ALREADY_EXIST"));
	    }

	    // Retrieve or create a new Gift object
	    Gift gadget = null;
	    if (gadgetDto.getId() != null) {
	        gadget = gadgetRepository.findOne(gadgetDto.getId());
	    }
	    if (gadget == null) {
	        gadget = new Gift();
	    }

	    // Set properties on the gadget object
	    gadget.setName(gadgetDto.getName());
	    gadget.setPrice(gadgetDto.getPrice());
	    gadget.setType(GiftType.valueOf(gadgetDto.getType()));

	    // Save and return the gadget
	    return gadgetRepository.save(gadget);
	}


}
