package com.intellitech.birdnotes.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;


import com.intellitech.birdnotes.data.dto.DistanceResponse;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Location;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.LocationDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.LocationRepository;
import com.intellitech.birdnotes.repository.ExpenseReportRepository;
import com.intellitech.birdnotes.repository.ExpenseTypeRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.DistanceService;
import com.intellitech.birdnotes.service.LocationService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
@Service("LocationService")
@Transactional
public class LocationServiceImpl implements LocationService {

	@Autowired
	private LocationRepository locationRepository ; 
	
	@Autowired
	private ExpenseTypeRepository expenseTypeRepository ; 
	
	@Autowired
	private DelegateRepository delegateRepository;
	
	@Autowired
	private ExpenseReportRepository expenseRepository;
	
	@Autowired
	private NotificationService notificationService;
	
	@Autowired
	ValidationStepService validationStepService;
	
	@Autowired
	DistanceService distanceService;
	
	private static final Logger LOG = LoggerFactory.getLogger(LocationServiceImpl.class);
	
	@Override
	public List<LocationDto> findLocation(Long userId, Date firstDate, Date lastDate, boolean showOnlyVisistsPositions){
		List<LocationDto> result = new ArrayList<>();
		Date startDate = new Date(firstDate.getYear(), firstDate.getMonth(), firstDate.getDate(), 0, 0, 0);
		Date endDate = new Date(lastDate.getYear(), lastDate.getMonth(), lastDate.getDate(), 23, 59, 59);
		List<Location> allLocations = locationRepository.findLocationSelected(userId, startDate, endDate);
		for (Location location : allLocations) {
	        if ((showOnlyVisistsPositions && location.getVisit() != null) || !showOnlyVisistsPositions) {
	            LocationDto locationDto = new LocationDto();
	            locationDto.setId(location.getId());
	            locationDto.setDate(location.getDate());
	            locationDto.setLatitude(location.getLatitude());
	            locationDto.setLongitude(location.getLongitude());
	            locationDto.setDistance(location.getDistance());
	            result.add(locationDto);
	        }
	    }
		return result;
	}
	
	@Override
	public void addExpensesByDistance(Date day, UserDto user)  {
		Delegate delegate = delegateRepository.findById(user.getId());
		Date startDate = new Date(day.getYear(), day.getMonth(), day.getDate(), 0, 0, 0);
		Date endDate = new Date(day.getYear(), day.getMonth(), day.getDate(), 23, 59, 59);
		List<Location> locations = locationRepository.findLocationSelected(delegate.getId(), startDate, endDate);
		if (locations != null && !locations.isEmpty()) {
			List<Float> allDistances = new ArrayList<>();
			for (Location location : locations) {
				ResponseEntity<DistanceResponse> distances;
				try {
					distances = distanceService.distance(location.getLatitude(), 
							location.getLongitude(), delegate.getLatitude(), delegate.getLongitude());
					LinkedHashMap distance = (LinkedHashMap) (((List<HashMap>) ((HashMap) distances.getBody().getRows().get(0)).get("elements")).get(0)).get("distance");
					
					allDistances.add((float) ((float)distance.get("value")*0.001));
				} catch (BirdnotesException e) {
					
					LOG.error("Error when calculating distance", e);
				}
				
			}
			float maxDistance = Collections.max(allDistances);
			List<ExpenseType> expenseTypes = expenseTypeRepository.getTypesByMileage(maxDistance, delegate.getId());
			if(expenseTypes != null) {
				ExpenseType expenseType = Collections.max(expenseTypes, Comparator.comparing(s -> s.getMileage()));
				float expenseAmount = expenseType.getPrice();
				/*TypeNoteFrais expenseTypeUser = typeNoteFraisRepository.findByUser(delegate.getId());
				if(expenseTypeUser!=null) {
					expenseAmount = expenseTypeUser.getAmount();
				}*/
				
				ExpenseReport expense = new ExpenseReport();
				expense.setDate(new Date());
				expense.setDescription("Note de frais ajoutée automatiquement par calcul de distance");
				expense.setMontant(expenseAmount);
				expense.setIdentifier(new Date().getTime());
				expense.setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
				expense.setExpenseType(expenseType);
				expense.setDelegate(delegate);
				ExpenseReport addedExpense = expenseRepository.save(expense);
				notificationService.sendNotificationUsingWorkflow(delegate.getUser(), addedExpense.getId(), "ADD_EXPENSE" );
				try {
					validationStepService.addValidationStatus("NEW_EXPENSE", addedExpense.getId(), delegate.getId());
				} catch (BirdnotesException e) {
					LOG.error("Error while adding validation step : "+ e);
				}
			}
			
					
		}
	}
	
	@Override
	public List<LocationDto> findLocationByProspectAndDate(Long prospectId, Date date) {
		List<LocationDto> result = new ArrayList<>();
		Date startDate = new Date(date.getYear(), date.getMonth(), date.getDate(), 0, 0, 0);
		Date endDate = new Date(date.getYear(), date.getMonth(), date.getDate(), 23, 59, 59);
		List<Location> allLocations = locationRepository.findLocationSelectedProspect(prospectId, startDate, endDate);
		for (Location location : allLocations) {
			LocationDto locationDto = new LocationDto();
			locationDto.setId(location.getId());
			locationDto.setDate(location.getDate());
			locationDto.setLatitude(location.getLatitude());
			locationDto.setLongitude(location.getLongitude());
			locationDto.setDelegate(location.getDelegate());
			result.add(locationDto);
		}
		return result;
	}

}
