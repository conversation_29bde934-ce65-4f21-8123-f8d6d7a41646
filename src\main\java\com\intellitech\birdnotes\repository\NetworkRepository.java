package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Network;
@Repository
public interface NetworkRepository extends JpaRepository<Network, Long> {
	
	Network findByName(String name);
	
	@Override
	@Query("SELECT n from Network n order by n.name ASC")
	List<Network> findAll();
	
	@Query("SELECT n from Network n WHERE n.name=:name AND n.id!=:id order by n.name")
	Network findByNameExcludeId(@Param("name")String name, @Param("id")Long id);
}
