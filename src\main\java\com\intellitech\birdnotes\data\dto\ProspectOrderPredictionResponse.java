package com.intellitech.birdnotes.data.dto;

import java.io.IOException;
import java.util.Date;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intellitech.birdnotes.model.ProspectOrderPrediction;

public class ProspectOrderPredictionResponse {
	
	private Long id;
	private ProspectFeaturesDto features;
	private Long prospectId;
	private Long productId;
	private Date predictionDate;
	private Long orderQuantity;
	private String prospectName;
	private String productName;
	
	ObjectMapper mapper = new ObjectMapper();
	
	
	
	public ProspectOrderPredictionResponse () {
		
	}
	

	
	public ProspectOrderPredictionResponse (ProspectOrderPrediction prospectOrderPrediction) {
		
		this.id = prospectOrderPrediction.getId();
		
		try {
			this.features  = mapper.readValue(prospectOrderPrediction.getFeatures(), ProspectFeaturesDto.class);
		} catch (IOException e) {
			this.features = null; 
		}
		this.prospectId = prospectOrderPrediction.getProspect().getIdentifier();
		this.productId = prospectOrderPrediction.getProduct().getId();
		this.predictionDate = prospectOrderPrediction.getPredictionDate();
		this.orderQuantity = prospectOrderPrediction.getOrderQuantityPrediction();
		this.prospectName = prospectOrderPrediction.getProspect().getFirstName() + " " + prospectOrderPrediction.getProspect().getLastName();
		this.productName = prospectOrderPrediction.getProduct().getName();
	}
	
	
	public Long getId() {
		return id;
	}
	public void setId(Long id) {
		this.id = id;
	}
	public Long getProspectId() {
		return prospectId;
	}
	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}
	public Long getProductId() {
		return productId;
	}
	public void setProductId(Long productId) {
		this.productId = productId;
	}
	public Date getPredictionDate() {
		return predictionDate;
	}
	public void setPredictionDate(Date predictionDate) {
		this.predictionDate = predictionDate;
	}
	public Long getOrderQuantity() {
		return orderQuantity;
	}
	public void setOrderQuantity(Long orderQuantity) {
		this.orderQuantity = orderQuantity;
	}

	public String getProspectName() {
		return prospectName;
	}

	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}



	public ProspectFeaturesDto getFeatures() {
		return features;
	}



	public void setFeatures(ProspectFeaturesDto features) {
		this.features = features;
	}



	
}
