package com.intellitech.birdnotes.model.dto;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.beans.factory.annotation.Autowired;

import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Gift;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.convertor.ConvertGiftToDto;
import com.intellitech.birdnotes.model.convertor.DelegateToDtoConvertor;


public class PurchaseOrderTemplateDto {
	private Long id;

	private String name;
	
	private Date firstDate;
	
	private Date lastDate;

	private List<Long> delegatesId;
	private List<Long> giftsId;
	
	private List<DelegateDto> delegates;
	private List<GiftDto> gifts;
	
	private Long selectedDelegateId;
	private Long selectedGiftId;
	
	
	
	private DelegateToDtoConvertor delegateToDtoConvertor ;
	
	private ConvertGiftToDto convertGadgetToDto ;
	
	
	
	private List<PurchaseOrderTemplateItemDto> purchaseOrderTemplateItem;
	
	/*@Autowired
	public void setUserToDtoConvertor(UserToDtoConvertor userToDtoConvertor) {
		this.userToDtoConvertor = userToDtoConvertor;
	}
	@Autowired
	public void setConvertGadgetToDto(ConvertGadgetToDto convertGadgetToDto) {
		this.convertGadgetToDto = convertGadgetToDto;
	}*/
	
	public PurchaseOrderTemplateDto(PurchaseOrderTemplate purchaseOrderTemplate) throws BirdnotesException {
		this.gifts = new ArrayList<>();
		this.delegates = new ArrayList<>();
		delegateToDtoConvertor = new DelegateToDtoConvertor();
		convertGadgetToDto = new ConvertGiftToDto();
		this.id = purchaseOrderTemplate.getId();
		this.firstDate = purchaseOrderTemplate.getFirstDate();
		this.lastDate = purchaseOrderTemplate.getLastDate();
		this.name = purchaseOrderTemplate.getName();
		for(Gift gadget :purchaseOrderTemplate.getGifts()) {
			this.gifts.add(convertGadgetToDto.convert(gadget));
		}
		for(Delegate delegate :purchaseOrderTemplate.getDelegates()) {
			DelegateDto delegateDto = delegateToDtoConvertor.convert(delegate);
			this.delegates.add(delegateDto);
		}
		
	}

	public PurchaseOrderTemplateDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Date getFirstDate() {
		return firstDate;
	}

	public void setFirstDate(Date firstDate) {
		this.firstDate = firstDate;
	}

	public Date getLastDate() {
		return lastDate;
	}

	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}

	public List<Long> getDelegatesId() {
		return delegatesId;
	}

	public void setDelegatesId(List<Long> delegatesId) {
		this.delegatesId = delegatesId;
	}

	public List<Long> getGiftsId() {
		return giftsId;
	}

	public void setGiftsId(List<Long> giftsId) {
		this.giftsId = giftsId;
	}

	public List<PurchaseOrderTemplateItemDto> getPurchaseOrderTemplateItem() {
		return purchaseOrderTemplateItem;
	}

	public void setPurchaseOrderTemplateItem(List<PurchaseOrderTemplateItemDto> purchaseOrderTemplateItem) {
		this.purchaseOrderTemplateItem = purchaseOrderTemplateItem;
	}

	public List<DelegateDto> getDelegates() {
		return delegates;
	}

	public void setDelegates(List<DelegateDto> delegates) {
		this.delegates = delegates;
	}

	public List<GiftDto> getGifts() {
		return gifts;
	}

	public void setGifts(List<GiftDto> gifts) {
		this.gifts = gifts;
	}

	public Long getSelectedDelegateId() {
		return selectedDelegateId;
	}

	public void setSelectedDelegateId(Long selectedDelegateId) {
		this.selectedDelegateId = selectedDelegateId;
	}

	public Long getSelectedGiftId() {
		return selectedGiftId;
	}

	public void setSelectedGiftId(Long selectedGiftId) {
		this.selectedGiftId = selectedGiftId;
	}


	}
