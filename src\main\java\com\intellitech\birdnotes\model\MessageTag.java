package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.Lob;
import javax.persistence.ManyToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.enumeration.MessageType;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.MESSAGE_TAG, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class MessageTag implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.MESSAGE_TAG_SEQUENCE, sequenceName = Sequences.MESSAGE_TAG_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.MESSAGE_TAG_SEQUENCE)
	@Column(name = BirdnotesConstants.Columns.ID)
	private Long id;

	@Column(name = BirdnotesConstants.Columns.IDENTIFIER)
	private Long identifier;
	
	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = BirdnotesConstants.Columns.VISIT_ID, unique = false)
	private Visit visit;
	
	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	private User user;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}
	
	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public Visit getVisit() {
		return visit;
	}

	public void setVisit(Visit visit) {
		this.visit = visit;
	}
	
	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}

	public MessageTag() {
		super();
	}
	
}
