package com.intellitech.birdnotes.model.dto;

public class GoalItemDto {
	private Long id;
	private Integer value;
	private String activity;

	private Long goal;
	private Long productId;
	private Long potentialId;
	private Long sector;
	private Long specialityId;
	private Long goalId;
	private Long prospectTypeId;
	
	public GoalItemDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}

	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}

	public Long getGoal() {
		return goal;
	}

	public void setGoal(Long goal) {
		this.goal = goal;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long product) {
		this.productId = product;
	}

	public Long getPotentialId() {
		return potentialId;
	}

	public void setPotentialId(Long potentialId) {
		this.potentialId = potentialId;
	}

	public Long getSector() {
		return sector;
	}

	public void setSector(Long sector) {
		this.sector = sector;
	}

	public Long getSectorId() {
		return sector;
	}

	public void setSectorId(Long sector) {
		this.sector = sector;
	}

	public Long getSpecialityId() {
		return specialityId;
	}

	public void setSpecialityId(Long speciality) {
		this.specialityId = speciality;
	}

	public Long getGoalId() {
		return goalId;
	}

	public void setGoalId(Long goalId) {
		this.goalId = goalId;
	}
	public Long getProspectTypeId() {
		return prospectTypeId;
	}

	public void setProspectTypeId(Long prospectTypeId) {
		this.prospectTypeId = prospectTypeId;
	}


	
}
