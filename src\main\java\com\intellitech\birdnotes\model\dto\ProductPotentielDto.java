package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.Prospect;

public class ProductPotentielDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	
    private Long prospectId;
    private Prospect prospect;
    private String prospectFirstName;
    private String prospectLastName;
    private String productName;
    private String potentialName;
	private Product product;
	private List<Long> productIds;
	private List<Long> potentialIds;
	private Long productId;
	private Potential potential;
	private Long potentialId;
    private List<String> potentialNames;
    private List<String> productNames;


    public Long getId() {
 		return id;
 	}


    public String getProductName() {
		return productName;
	}


	public void setProductName(String productName) {
		this.productName = productName;
	}


	public String getPotentialName() {
		return potentialName;
	}


	public void setPotentialName(String potentialName) {
		this.potentialName = potentialName;
	}




 	public void setId(Long id) {
 		this.id = id;
 	}

	public String getProspectFirstName() {
		return prospectFirstName;
	}


	public void setProspectFirstName(String prospectFirstName) {
		this.prospectFirstName = prospectFirstName;
	}


	public String getProspectLastName() {
		return prospectLastName;
	}


	public void setProspectLastName(String prospectLastName) {
		this.prospectLastName = prospectLastName;
	}


	public List<String> getProductNames() {
		return productNames;
	}

	public void setProductNames(List<String> productNames) {
		this.productNames = productNames;
	}

	public List<String> getPotentialNames() {
		return potentialNames;
	}

	public void setPotentialNames(List<String> potentialNames) {
		this.potentialNames = potentialNames;
	}




	public List<Long> getProductIds() {
		return productIds;
	}




	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}




	public List<Long> getPotentialIds() {
		return potentialIds;
	}




	public void setPotentialIds(List<Long> potentialIds) {
		this.potentialIds = potentialIds;
	}


    public Prospect getProspect() {
		return prospect;
	}

	public void setProspect(Prospect prospect) {
		this.prospect = prospect;
	}

	public Long getProspectId() {
		return prospectId;
	}




	public void setProspectId(Long prospectId) {
		this.prospectId = prospectId;
	}




	public Product getProduct() {
		return product;
	}




	public void setProduct(Product product) {
		this.product = product;
	}




	public Long getProductId() {
		return productId;
	}




	public void setProductId(Long productId) {
		this.productId = productId;
	}




	public Potential getPotential() {
		return potential;
	}




	public void setPotential(Potential potential) {
		this.potential = potential;
	}




	public Long getPotentialId() {
		return potentialId;
	}




	public void setPotentialId(Long potentialId) {
		this.potentialId = potentialId;
	}




	public ProductPotentielDto() {
		super();
	
	}

	
}
