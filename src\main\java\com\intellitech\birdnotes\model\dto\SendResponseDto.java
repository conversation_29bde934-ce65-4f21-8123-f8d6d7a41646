package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class SendResponseDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private List<Long> prospectChangesIds;
	private List<Long> savedVisitIds;
	private List<Long> savedVisitProductIds;
	private List<Long> savedPurchaseOrderIds;
	private List<Long> savedExpenseReportsIds;
	private List<Long> savedMarketingActionsIds;
	private List<Long> savedActivityIds;
	private List<Long> savedPlanningIds;
	private List<Long> savedRecoveryIds;
	private List<Long> savedMessageIds;
	private List<Long> savedAttachmentIds;
	private List<Long> savedPlanningValidationIds;
	private List<Long> savedMessageTagIds;
	private List<Long> savedTrackingTimeIds;
	
	private List<SendResultDto> resultsActivity;
	private List<SendResultDto> planningsDto;
	private List<SendResultDto> planningValidationDto;
    
	private List<SendResultDto> actionMarketingValidationDto;
    private List<SendResultDto> opportunityNotesDto;
	private List<SendResultDto> locationsDto;
	private List<SendResultDto> opportunityNoteValidationDto;

	private List<Long> deletedVisitProducts;
	private List<Long> deletedVisits;
	private List<Long> deletedPurchaseOrders;
	private List<Long> deletedExpenseReports;
	private List<Long> deletedActivities;
	private List<Long> deletedMarketingActions;
	private List<Long> deletedOpportunityNotes;
	private List<Long> deletedPlannings;
	private List<Long> deletedPlanningValidations;
	private List<Long> deletedRecovery;
	private List<Long> deletedAttachment;
	
	
	public SendResponseDto() {
		
	}
	
	public SendResponseDto(List<Long> prospectChangesIds,
			List<Long> savedVisitIds, 
			List<Long> savedVisitProductIds,
			List<Long> savedPurchaseOrderIds,
			List<Long> savedExpenseReportsIds, 
			List<Long> savedMarketingActionsIds,
			List<Long> savedActivityIds,
			List<Long> savedPlanningIds,
			List<Long> savedPlanningValidationIds,
			List<Long> savedRecoveryIds,
			List<Long> savedAttachmentIds,
			List<Long> savedMessageIds,
			List<Long> savedMessageTagIds,
			List<Long> savedTrackingTimeIds,
			
			List<Long> deletedVisits,
			List<Long> deletedVisitProducts,
			List<Long> deletedPurchaseOrders,
			List<Long> deletedExpenseReports,
			List<Long> deletedMarketingActions,
			List<Long> deletedActivities,	
			List<Long> deletedPlannings,
			List<Long> deletedPlanningValidations,
			List<Long> deletedRecovery,
			List<Long> deletedAttachment,
			List<SendResultDto> opportunityNotesDto,
			List<Long> deletedOpportunityNotes

			)

	{
		super();
		this.prospectChangesIds = prospectChangesIds;
		this.savedExpenseReportsIds = savedExpenseReportsIds;
		this.savedVisitIds = savedVisitIds;
		this.savedVisitProductIds = savedVisitProductIds;
		this.savedPurchaseOrderIds = savedPurchaseOrderIds;
		this.savedActivityIds = savedActivityIds;
		this.savedPlanningIds = savedPlanningIds;
		this.savedRecoveryIds = savedRecoveryIds;
		this.savedMessageIds = savedMessageIds;
		this.savedMessageTagIds = savedMessageTagIds;
		this.savedTrackingTimeIds = savedTrackingTimeIds;
		this.savedAttachmentIds = savedAttachmentIds;
		this.savedPlanningValidationIds = savedPlanningValidationIds;
		this.deletedVisits = deletedVisits;
		this.deletedVisitProducts = deletedVisitProducts;
		this.deletedPurchaseOrders = deletedPurchaseOrders;
		this.deletedExpenseReports = deletedExpenseReports;
		this.deletedActivities = deletedActivities;
		this.deletedPlannings = deletedPlannings;
		this.deletedPlanningValidations = deletedPlanningValidations;
		this.deletedRecovery = deletedRecovery;
		this.deletedAttachment = deletedAttachment;
		this.savedMarketingActionsIds = savedMarketingActionsIds;
		this.deletedMarketingActions = deletedMarketingActions;
		this.opportunityNotesDto = opportunityNotesDto;
		this.setDeletedOpportunityNotes(deletedOpportunityNotes);
		
	}
		


	public List<Long> getSavedMarketingActionsIds() {
		return savedMarketingActionsIds;
	}

	public void setSavedMarketingActionsIds(List<Long> savedMarketingActionsIds) {
		this.savedMarketingActionsIds = savedMarketingActionsIds;
	}


	public List<Long> getDeletedMarketingActions() {
		return deletedMarketingActions;
	}

	public void setDeletedMarketingActions(List<Long> deletedMarketingActions) {
		this.deletedMarketingActions = deletedMarketingActions;
	}

	public List<SendResultDto> getActionMarketingValidationDto() {
		return actionMarketingValidationDto;
	}

	public void setActionMarketingValidationDto(List<SendResultDto> actionMarketingValidationDto) {
		this.actionMarketingValidationDto = actionMarketingValidationDto;
	}

	

	public List<SendResultDto> getOpportunityNotesDto() {
		return opportunityNotesDto;
	}

	public void setOpportunityNotesDto(List<SendResultDto> opportunityNotesDto) {
		this.opportunityNotesDto = opportunityNotesDto;
	}


	public List<SendResultDto> getLocationsDto() {
		return locationsDto;
	}

	public void setLocationsDto(List<SendResultDto> locationsDto) {
		this.locationsDto = locationsDto;
	}

	public List<Long> getDeletedOpportunityNotes() {
		return deletedOpportunityNotes;
	}

	public void setDeletedOpportunityNotes(List<Long> deletedOpportunityNotes) {
		this.deletedOpportunityNotes = deletedOpportunityNotes;
	}

	public List<SendResultDto> getOpportunityNoteValidationDto() {
		return opportunityNoteValidationDto;
	}

	public void setOpportunityNoteValidationDto(List<SendResultDto> opportunityNoteValidationDto) {
		this.opportunityNoteValidationDto = opportunityNoteValidationDto;
	}

	public List<SendResultDto> getPlanningValidationDto() {
		return planningValidationDto;
	}

	public void setPlanningValidationDto(List<SendResultDto> planningValidationDto) {
		this.planningValidationDto = planningValidationDto;
	}

	public List<SendResultDto> getPlanningsDto() {
		return planningsDto;
	}

	public void setPlanningsDto(List<SendResultDto> planningsDto) {
		this.planningsDto = planningsDto;
	}

	public List<Long> getDeletedPlannings() {
		return deletedPlannings;
	}

	public void setDeletedPlannings(List<Long> deletedPlannings) {
		this.deletedPlannings = deletedPlannings;
	}

	public List<Long> getSavedExpenseReportsIds() {
		return savedExpenseReportsIds;
	}

	public void setSavedExpenseReportsIds(List<Long> savedExpenseReportsIds) {
		this.savedExpenseReportsIds = savedExpenseReportsIds;
	}

	

	public List<Long> getSavedRecoveryIds() {
		return savedRecoveryIds;
	}

	public void setSavedRecoveryIds(List<Long> savedRecoveryIds) {
		this.savedRecoveryIds = savedRecoveryIds;
	}

	
	public List<Long> getSavedMessageIds() {
		return savedMessageIds;
	}

	public void setSavedMessageIds(List<Long> savedMessageIds) {
		this.savedMessageIds = savedMessageIds;
	}
	
	public List<Long> getSavedAttachmentIds() {
		return savedAttachmentIds;
	}

	public void setSavedAttachmentIds(List<Long> savedAttachmentIds) {
		this.savedAttachmentIds = savedAttachmentIds;
	}

	public List<Long> getSavedActivityIds() {
		return savedActivityIds;
	}

	public void setSavedActivityIds(List<Long> savedActivityIds) {
		this.savedActivityIds = savedActivityIds;
	}

	public List<SendResultDto> getResultsActivity() {
		return resultsActivity;
	}

	public void setResultsActivity(List<SendResultDto> resultsActivity) {
		this.resultsActivity = resultsActivity;
	}

	public List<Long> getDeletedActivities() {
		return deletedActivities;
	}

	public void setDeletedActivities(List<Long> deletedActivities) {
		this.deletedActivities = deletedActivities;
	}
	public List<Long> getDeletedVisitProducts() {
		return deletedVisitProducts;
	}
	public void setDeletedVisitProducts(List<Long> deletedVisitProducts) {
		this.deletedVisitProducts = deletedVisitProducts;
	}

	public List<Long> getSavedVisitIds() {
		return savedVisitIds;
	}

	public void setSavedVisitIds(List<Long> savedVisitIds) {
		this.savedVisitIds = savedVisitIds;
	}

	public List<Long> getSavedVisitProductIds() {
		return savedVisitProductIds;
	}

	public void setSavedVisitProductIds(List<Long> savedVisitProductIds) {
		this.savedVisitProductIds = savedVisitProductIds;
	}

	public List<Long> getSavedPurchaseOrderIds() {
		return savedPurchaseOrderIds;
	}

	public void setSavedPurchaseOrderIds(List<Long> savedPurchaseOrderIds) {
		this.savedPurchaseOrderIds = savedPurchaseOrderIds;
	}

	public List<Long> getDeletedVisits() {
		return deletedVisits;
	}

	public void setDeletedVisits(List<Long> deletedVisits) {
		this.deletedVisits = deletedVisits;
	}

	public List<Long> getDeletedPurchaseOrders() {
		return deletedPurchaseOrders;
	}

	public void setDeletedPurchaseOrders(List<Long> deletedPurchaseOrders) {
		this.deletedPurchaseOrders = deletedPurchaseOrders;
	}

	
	
	public List<Long> getDeletedExpenseReports() {
		return deletedExpenseReports;
	}

	public void setDeletedExpenseReports(List<Long> deletedExpenseReports) {
		this.deletedExpenseReports = deletedExpenseReports;
	}

	public List<Long> getProspectChangesIds() {
		return prospectChangesIds;
	}

	public void setProspectChangesIds(List<Long> prospectChangesIds) {
		this.prospectChangesIds = prospectChangesIds;
	}

	public List<Long> getSavedPlanningIds() {
		return savedPlanningIds;
	}

	public void setSavedPlanningIds(List<Long> savedPlanningIds) {
		this.savedPlanningIds = savedPlanningIds;
	}

	public List<Long> getSavedPlanningValidationIds() {
		return savedPlanningValidationIds;
	}

	public void setSavedPlanningValidationIds(List<Long> savedPlanningValidationIds) {
		this.savedPlanningValidationIds = savedPlanningValidationIds;
	}

	public List<Long> getDeletedPlanningValidations() {
		return deletedPlanningValidations;
	}

	public void setDeletedPlanningValidations(List<Long> deletedPlanningValidations) {
		this.deletedPlanningValidations = deletedPlanningValidations;
	}

	public List<Long> getDeletedRecovery() {
		return deletedRecovery;
	}

	public void setDeletedRecovery(List<Long> deletedRecovery) {
		this.deletedRecovery = deletedRecovery;
	}

	public List<Long> getDeletedAttachment() {
		return deletedAttachment;
	}

	public void setDeletedAttachment(List<Long> deletedAttachment) {
		this.deletedAttachment = deletedAttachment;
	}

	public List<Long> getSavedMessageTagIds() {
		return savedMessageTagIds;
	}

	public void setSavedMessageTagIds(List<Long> savedMessageTagIds) {
		this.savedMessageTagIds = savedMessageTagIds;
	}

	public List<Long> getSavedTrackingTimeIds() {
		return savedTrackingTimeIds;
	}

	public void setSavedTrackingTimeIds(List<Long> savedTrackingTimeIds) {
		this.savedTrackingTimeIds = savedTrackingTimeIds;
	}
	
	
	
}
