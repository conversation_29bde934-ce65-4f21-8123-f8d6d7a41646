package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.Issue;

public interface IssueRepository extends JpaRepository<Issue, Long> {

	

	@Query("Select i from Issue i where  (DATE(i.issueDate) BETWEEN DATE(:firstDate) AND DATE(:lastDate))")
	List<Issue> findAll(@Param("firstDate") Date firstDate, @Param("lastDate") Date lastDate);

}
