package com.intellitech.birdnotes.service;

import java.util.List;
import java.util.Set;


import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Preference;
import com.intellitech.birdnotes.model.dto.PreferenceDto;


public interface PreferenceService {
	
	List<PreferenceDto> findAll() throws BirdnotesException;

	PreferenceDto findPreferenceDto(String preferenceName, List<PreferenceDto> preferenceDtos);
	
	Preference savePreference(PreferenceDto preferenceDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;

	Set<Preference> findPreferencesByIds(List<Long> list);




	

}
