package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.User;

@Repository
public interface RangeRepository extends JpaRepository<Range, Integer> {

	Range findByName(String name);
	
	@Query("Select g from Range g ORDER BY g.name ASC")
	List<Range> findAll();
	
	@Query("Select g from Range g join g.users d where d.id= :userId ORDER BY g.name ASC")
	List<Range> findByUserRange(@Param ("userId") Long userId);
	
	@Modifying
	@Query("DELETE FROM Range where id=:id")
	void deleteById(@Param ("id") Integer id);
	
	@Query("Select DISTINCT g.products from Range g where g.id IN (:ids)")
	List<Product> findByGamme(@Param("ids") List<Integer> ids);
	
	@Query("SELECT g FROM Range g WHERE LOWER(g.name) = LOWER(?1) AND g.id != ?2")
	Range findByNameAndAnotherId(String name, Integer integer);
	

	@Query("Select g.products from Range g where g.id =:ids")
	List<Product> findByGamme(@Param("id") Integer id);

	@Query("Select g.users from Range g where g.id=:id")
	List<Delegate> findDelegatesByGamme(@Param("id") Integer id);
	
	@Query("Select DISTINCT g from Range g join g.users d where d.id= :userId and g.parent is not null ORDER BY g.name ASC")
	List<Range> findSubRange(@Param ("userId") Long userId);
	
}
