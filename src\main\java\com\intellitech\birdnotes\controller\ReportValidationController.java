package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.DelegateDto;
import com.intellitech.birdnotes.model.dto.ReportValidationDto;
import com.intellitech.birdnotes.model.dto.StatusReportValidation;
import com.intellitech.birdnotes.model.request.ReportValidationRequest;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.service.DelegateService;
import com.intellitech.birdnotes.service.ReportValidationService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/reportsValidation")
public class ReportValidationController {
	private static final Logger LOG = LoggerFactory.getLogger(ReportValidationController.class);
	@Autowired
	private ReportValidationService reportValidationService;
	@Autowired
	DelegateService delegateService;
	
	@Autowired
	DelegateRepository delegateRepository;

	@RequestMapping(value = "getAllDelegates", method = RequestMethod.GET)
	public ResponseEntity<List<DelegateDto>> getAllDelegates() {
		try {

			List<DelegateDto> userDtos = delegateService.findAllDelegates();
			return new ResponseEntity<>(userDtos, HttpStatus.OK);

		} catch (Exception e) {
			LOG.error("Error in getAllDelegates", e);
			return new ResponseEntity<>(null, HttpStatus.EXPECTATION_FAILED);
		}
	}

	@RequestMapping(value = "/getReportsValidationByDateAndUser", method = RequestMethod.POST)

	public ResponseEntity<List<ReportValidationDto>> getReportsValidationByDateAndUser(
			@RequestBody ReportValidationRequest reportValidationRequest) {

		try {
			List<ReportValidationDto> result = reportValidationService
					.getReportsValidationByDateAndUser(reportValidationRequest);
			return new ResponseEntity<>(result, HttpStatus.OK);

		} catch (Exception e) {

			LOG.error("An exception occurred while getting all reportsValidation", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}
	
	@RequestMapping(value = "/generateReportsValidation", method = RequestMethod.POST)

	public ResponseEntity<List<ReportValidationDto>> generateReportsValidation(
			@RequestBody ReportValidationRequest reportValidationRequest) {

		try {
			Delegate delegate = delegateRepository.findOne(reportValidationRequest.getSelectedUser());
			reportValidationService.validateVisitReport(reportValidationRequest.getVisitDate(),delegate );
			List<ReportValidationDto> result = reportValidationService
					.getReportsValidationByDateAndUser(reportValidationRequest);
			return new ResponseEntity<>(result, HttpStatus.OK);

		} catch (Exception e) {

			LOG.error("An exception occurred while getting all reportsValidation", e);

			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);

		}

	}

	@RequestMapping(value = "/updateStatusReportValidation", method = RequestMethod.PUT)
	public ResponseEntity<String> updateStatusReportValidation(
			@RequestBody StatusReportValidation statusReportValidation) {
		try {
			reportValidationService.updateStatusReportValidation(statusReportValidation);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while update status", e);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.INTERNAL_SERVER_ERROR);
		}

	}
}
