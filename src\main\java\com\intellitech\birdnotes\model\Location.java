package com.intellitech.birdnotes.model;

import java.util.Date;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;

@Entity
@Table(name = BirdnotesConstants.Tables.POSITION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA)
public class Location {
	@Id
	@SequenceGenerator(name = Sequences.LOCALISATION_SEQUENCE, sequenceName = Sequences.LOCALISATION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.LOCALISATION_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;


	//@Temporal(TemporalType.DATE)
	@Column(name = Columns.POSITION_DATE)
	private Date date;

	@ManyToOne
	@JoinColumn(name = BirdnotesConstants.Columns.DELEGATE_ID)
	private Delegate delegate;
	
	@Column(name = BirdnotesConstants.Columns.LATITUDE)
	private Double latitude;

	@Column(name = BirdnotesConstants.Columns.LONGITUDE)
	private Double longitude;
	
	@Column(name = BirdnotesConstants.Columns.DISTANCE)
	private Integer distance;
	
	@Column(name = BirdnotesConstants.Columns.DURATION)
	private Integer duration;
	
	@JsonIgnore
	@OneToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL, mappedBy = "location", targetEntity = Visit.class)
	private Visit visit;

	public Location(Long id,  Date date, Delegate delegate, Double latitude,Double longitude ) {
		super();
		this.id = id;
		this.date = date;
		this.delegate = delegate;
		this.latitude=latitude; 
		this.longitude = longitude;
	}
	

	public Location() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public Date getDate() {
		return date;
	}

	public void setDate(Date date) {
		this.date = date;
	}

	

	public Delegate getDelegate() {
		return delegate;
	}


	public void setDelegate(Delegate delegate) {
		this.delegate = delegate;
	}


	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}


	public Visit getVisit() {
		return visit;
	}


	public void setVisit(Visit visit) {
		this.visit = visit;
	}


	public Integer getDistance() {
		return distance;
	}


	public void setDistance(Integer distance) {
		this.distance = distance;
	}


	public Integer getDuration() {
		return duration;
	}


	public void setDuration(Integer duration) {
		this.duration = duration;
	}


}
