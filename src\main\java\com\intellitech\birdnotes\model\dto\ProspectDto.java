package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Set;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.Range;

public class ProspectDto implements Serializable {

	private static final long serialVersionUID = 1L;


	private Long id;
	private Long identifier;
	private String firstName;
	private String lastName;
	private String fullName;
	private String activity;
	private String potential;
	private String speciality;
	private String locality;
	private String sector;
	private String prospectType;
	private String interest;
	private String establishment;

	private PotentialDto potentialDto;
	private String address;
	private String gsm;
	private String phone;
	private String email;
	private String note;
	private String grade;
	private String secretary;
	private Boolean active;

	private String scrappingId;

	private SpecialityDto specialityDto;
	private SectorDto sectorDto;
	private LocalityDto localityDto;
	private ProspectTypeDto prospectTypeDto;
	private InterestDto interestDto;
	private EstablishmentDto establishmentDto;
	private Date creationDate;
	private List<String> delegateNames;

	private Double latitude;
	private Double longitude;
	private String mapAddress;
	private UserValidationStatus status;
	private Long idprospect;
	private List<Long> productIds;
	private List<Long> potentialIds;
	private Set<Range> gammes;
	private List<Long> preferenceIds;
	private List<Long> contactTypeIds;
	private List<Long> interestIds;
	private String socialMedia;
	private String taxIdNumber;
	private String code;




	private List<Long> delegateIds;
	private Date updatePositionDate;
	private Date updateDate;
	private Long appUserId;
	private String creatorUser;
	private String userName;
	private String password;
	private List<Integer> rangeIds;
	private List<Integer> roleIds;
	private List<Long> supervisorIds;
	private boolean isUser;
	private DateFormat df = new SimpleDateFormat("dd-MM-yyyy");
	private List<ValidationStatusDto> validationStatusDto;
	private Long doctorId;
	private String prospectName;
	
	private Float orderPrediction;

	public ProspectDto(Prospect p) {
		super();
		this.fullName = p.getFirstName() + " " + p.getLastName();
		this.activity = p.getActivity();
		this.potential = p.getPotential().getName();
		this.speciality = p.getSpeciality().getName();
		this.prospectType = p.getProspectType().getName();
		if (p.getEstablishment() != null) {
			this.establishment = p.getEstablishment().getName();
		}
		this.locality = p.getLocality().getName();
		this.latitude = p.getLatitude();
		this.longitude = p.getLongitude();
		this.mapAddress = p.getMapAddress();
		this.sector = p.getSector().getName();
		this.address = p.getAddress();
		this.phone = p.getPhone();
		this.gsm = p.getGsm();
		this.secretary = p.getSecretary();
		if (p.getAppUser() != null) {
			this.appUserId = p.getAppUser().getId();
		}

		this.grade = p.getGrade();
		this.active = p.getActive();
		this.email = p.getEmail();
		this.sector = p.getSector().getName();
		this.updatePositionDate = p.getUpdatePositionDate();
		this.specialityDto = new SpecialityDto();
		this.specialityDto.setId(p.getSpeciality().getId());
		this.specialityDto.setIcon(p.getSpeciality().getIcon());

	}

	public ProspectDto(Long id, String fullName, String activity, String potential, String speciality, String locality,
			String sector, String prospectType, String establishment, String address, String gsm, String phone,
			String email, String note, String grade, String secretary, Date creationDate, Double latitude,
			Double longitude, String mapAddress, Date updatePositionDate, Date updateDate, Long doctorId) {
		super();
		this.id = id;
		this.fullName = fullName;
		this.activity = activity;
		this.potential = potential;
		this.speciality = speciality;
		this.locality = locality;
		this.sector = sector;
		this.prospectType = prospectType;
		this.establishment = establishment;
		this.address = address;
		this.gsm = gsm;
		this.phone = phone;
		this.email = email;
		this.note = note;
		this.grade = grade;
		this.secretary = secretary;
		this.creationDate = creationDate;
		this.latitude = latitude;
		this.longitude = longitude;
		this.mapAddress = mapAddress;
		this.updatePositionDate = updatePositionDate;
		this.updateDate = updateDate;

	}

	public boolean isUser() {
		return isUser;
	}

	public void setIsUser(boolean isUser) {
		this.isUser = isUser;
	}

	public String getCreatorUser() {
		return creatorUser;
	}

	public void setCreatorUser(String creatorUser) {
		this.creatorUser = creatorUser;
	}

	public String getPassword() {
		return password;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public List<Integer> getRoleIds() {
		return roleIds;
	}

	public void setRoleIds(List<Integer> roleIds) {
		this.roleIds = roleIds;
	}

	public List<Long> getSupervisorIds() {
		return supervisorIds;
	}

	public void setSupervisorIds(List<Long> supervisorIds) {
		this.supervisorIds = supervisorIds;
	}

	public String getPotential() {
		return potential;
	}

	public void setPotential(String potential) {
		this.potential = potential;
	}

	public String getSpeciality() {
		return speciality;
	}

	public void setSpeciality(String speciality) {
		this.speciality = speciality;
	}

	public String getLocality() {
		return locality;
	}

	public void setLocality(String locality) {
		this.locality = locality;
	}

	public String getSector() {
		return sector;
	}

	public void setSector(String sector) {
		this.sector = sector;
	}

	public Date getUpdateDate() {
		return updateDate;
	}

	public void setUpdateDate(Date updateDate) {
		this.updateDate = updateDate;
	}

	public Date getUpdatePositionDate() {
		return updatePositionDate;
	}

	public void setUpdatePositionDate(Date updatePositionDate) {
		this.updatePositionDate = updatePositionDate;
	}

	public ProspectDto() {
		super();
		// TODO Auto-generated constructor stub
	}

	public Set<Range> getGammes() {
		return gammes;
	}

	public void setGammes(Set<Range> gammes) {
		this.gammes = gammes;
	}

	public List<Integer> getRangeIds() {
		return rangeIds;
	}

	public void setRangeIds(List<Integer> rangeIds) {
		this.rangeIds = rangeIds;
	}

	public List<Long> getProductIds() {
		return productIds;
	}

	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}

	public List<Long> getPotentialIds() {
		return potentialIds;
	}

	public void setPotentialIds(List<Long> potentialIds) {
		this.potentialIds = potentialIds;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getLastName() {
		return lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public String getGsm() {
		return gsm;
	}

	public void setGsm(String gsm) {
		this.gsm = gsm;
	}

	public String getPhone() {
		return phone;
	}

	public void setPhone(String phone) {
		this.phone = phone;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getNote() {
		return note;
	}
	

	public void setNote(String note) {
		this.note = note;
	}

	public String getGrade() {
		return grade;
	}

	public void setGrade(String grade) {
		this.grade = grade;
	}

	public Boolean getActive() {
		return active;
	}

	public void setActive(Boolean active) {
		this.active = active;
	}

	public void setUser(boolean isUser) {
		this.isUser = isUser;
	}

	public SpecialityDto getSpecialityDto() {
		return specialityDto;
	}

	public void setSpecialityDto(SpecialityDto specialityDto) {
		this.specialityDto = specialityDto;
	}

	public SectorDto getSectorDto() {
		return sectorDto;
	}

	public void setSectorDto(SectorDto sectorDto) {
		this.sectorDto = sectorDto;
	}

	public LocalityDto getLocalityDto() {
		return localityDto;
	}

	public void setLocalityDto(LocalityDto localityDto) {
		this.localityDto = localityDto;
	}

	public ProspectTypeDto getProspectTypeDto() {
		return prospectTypeDto;
	}
	
	
	public InterestDto getInterestDto() {
		return interestDto;
	}

	public void setProspectTypeDto(ProspectTypeDto prospectTypeDto) {
		this.prospectTypeDto = prospectTypeDto;
	}

	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	public List<String> getDelegateNames() {
		return delegateNames;
	}

	public void setDelegateNames(List<String> delegateNames) {
		this.delegateNames = delegateNames;
	}

	public List<Long> getDelegateIds() {
		return delegateIds;
	}

	public void setDelegateIds(List<Long> delegateIds) {
		this.delegateIds = delegateIds;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Double getLatitude() {
		return latitude;
	}

	public void setLatitude(Double latitude) {
		this.latitude = latitude;
	}

	public Double getLongitude() {
		return longitude;
	}

	public void setLongitude(Double longitude) {
		this.longitude = longitude;
	}

	public String getMapAddress() {
		return mapAddress;
	}

	public void setMapAddress(String mapAddress) {
		this.mapAddress = mapAddress;
	}

	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	public Long getIdprospect() {
		return idprospect;
	}

	public void setIdprospect(Long idprospect) {
		this.idprospect = idprospect;
	}

	public PotentialDto getPotentialDto() {
		return potentialDto;
	}

	public void setPotentialDto(PotentialDto potentialDto) {
		this.potentialDto = potentialDto;
	}

	public String getFullName() {
		return fullName;
	}

	public void setFullName(String fullName) {
		this.fullName = fullName;
	}

	public String getSecretary() {
		return secretary;
	}

	public void setSecretary(String secretary) {
		this.secretary = secretary;
	}
	
	public String getInterest() {
		return interest;
	}

	public String getProspectType() {
		return prospectType;
	}

	public void setProspectType(String prospectType) {
		this.prospectType = prospectType;
	}

	public List<ValidationStatusDto> getValidationStatusDto() {
		return validationStatusDto;
	}

	public void setValidationStatusDto(List<ValidationStatusDto> validationStatusDto) {
		this.validationStatusDto = validationStatusDto;
	}

	public Long getAppUserId() {
		return appUserId;
	}

	public void setAppUserId(Long appUserId) {
		this.appUserId = appUserId;
	}

	public String getEstablishment() {
		return establishment;
	}

	public void setEstablishment(String establishment) {
		this.establishment = establishment;
	}

	public EstablishmentDto getEstablishmentDto() {
		return establishmentDto;
	}

	public void setEstablishmentDto(EstablishmentDto establishmentDto) {
		this.establishmentDto = establishmentDto;
	}

	@Override
	public String toString() {
		return "ProspectDto [id=" + id + ", firstName=" + firstName + ", lastName=" + lastName + ", activity="
				+ activity + ", potentialDto=" + potentialDto + ", address=" + address + ", gsm=" + gsm + ", phone="
				+ phone + ", email=" + email + ", note=" + note + ",  secretary=" + secretary + ", specialityDto="
				+ specialityDto + ", sectorDto=" + sectorDto + ", localityDto=" + localityDto + ", creationDate="
				+ creationDate + ", delegateNames=" + delegateNames + ", userName=" + userName + ", latitude="
				+ latitude + ", longitude=" + longitude + ", mapAddress=" + mapAddress + ", status=" + status
				+ ", idprospect=" + idprospect + ", productIds=" + productIds + ", potentialIds=" + potentialIds
				+ ", gammes=" + gammes + ", rangeIds=" + rangeIds + ", delegateIds=" + delegateIds + "]";
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((activity == null) ? 0 : activity.hashCode());
		result = prime * result + ((address == null) ? 0 : address.hashCode());
		result = prime * result + ((creationDate == null) ? 0 : creationDate.hashCode());
		result = prime * result + ((delegateIds == null) ? 0 : delegateIds.hashCode());
		result = prime * result + ((delegateNames == null) ? 0 : delegateNames.hashCode());
		result = prime * result + ((email == null) ? 0 : email.hashCode());
		result = prime * result + ((firstName == null) ? 0 : firstName.hashCode());
		result = prime * result + ((rangeIds == null) ? 0 : rangeIds.hashCode());
		result = prime * result + ((gammes == null) ? 0 : gammes.hashCode());
		result = prime * result + ((gsm == null) ? 0 : gsm.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((idprospect == null) ? 0 : idprospect.hashCode());
		result = prime * result + ((lastName == null) ? 0 : lastName.hashCode());
		result = prime * result + ((latitude == null) ? 0 : latitude.hashCode());
		result = prime * result + ((localityDto == null) ? 0 : localityDto.hashCode());
		result = prime * result + ((longitude == null) ? 0 : longitude.hashCode());
		result = prime * result + ((mapAddress == null) ? 0 : mapAddress.hashCode());
		result = prime * result + ((note == null) ? 0 : note.hashCode());
		result = prime * result + ((secretary == null) ? 0 : secretary.hashCode());
		result = prime * result + ((phone == null) ? 0 : phone.hashCode());
		result = prime * result + ((potentialDto == null) ? 0 : potentialDto.hashCode());
		result = prime * result + ((potentialIds == null) ? 0 : potentialIds.hashCode());
		result = prime * result + ((productIds == null) ? 0 : productIds.hashCode());
		result = prime * result + ((sectorDto == null) ? 0 : sectorDto.hashCode());
		result = prime * result + ((specialityDto == null) ? 0 : specialityDto.hashCode());
		result = prime * result + ((status == null) ? 0 : status.hashCode());
		result = prime * result + ((userName == null) ? 0 : userName.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ProspectDto other = (ProspectDto) obj;
		if (activity == null) {
			if (other.activity != null)
				return false;
		} else if (!activity.equals(other.activity))
			return false;
		if (address == null) {
			if (other.address != null)
				return false;
		} else if (!address.equals(other.address))
			return false;

		if (creationDate == null) {
			if (other.creationDate != null)
				return false;
		} else if (!creationDate.equals(other.creationDate))
			return false;
		if (delegateIds == null) {
			if (other.delegateIds != null)
				return false;
		} else if (!delegateIds.equals(other.delegateIds))
			return false;
		if (delegateNames == null) {
			if (other.delegateNames != null)
				return false;
		} else if (!delegateNames.equals(other.delegateNames))
			return false;
		if (email == null) {
			if (other.email != null)
				return false;
		} else if (!email.equals(other.email))
			return false;
		if (firstName == null) {
			if (other.firstName != null)
				return false;
		} else if (!firstName.equals(other.firstName))
			return false;
		if (rangeIds == null) {
			if (other.rangeIds != null)
				return false;
		} else if (!rangeIds.equals(other.rangeIds))
			return false;
		if (gammes == null) {
			if (other.gammes != null)
				return false;
		} else if (!gammes.equals(other.gammes))
			return false;
		if (gsm == null) {
			if (other.gsm != null)
				return false;
		} else if (!gsm.equals(other.gsm))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (idprospect == null) {
			if (other.idprospect != null)
				return false;
		} else if (!idprospect.equals(other.idprospect))
			return false;
		if (lastName == null) {
			if (other.lastName != null)
				return false;
		} else if (!lastName.equals(other.lastName))
			return false;
		if (latitude == null) {
			if (other.latitude != null)
				return false;
		} else if (!latitude.equals(other.latitude))
			return false;
		if (localityDto == null) {
			if (other.localityDto != null)
				return false;
		} else if (!localityDto.equals(other.localityDto))
			return false;
		if (longitude == null) {
			if (other.longitude != null)
				return false;
		} else if (!longitude.equals(other.longitude))
			return false;
		if (mapAddress == null) {
			if (other.mapAddress != null)
				return false;
		} else if (!mapAddress.equals(other.mapAddress))
			return false;
		if (note == null) {
			if (other.note != null)
				return false;
		} else if (!note.equals(other.note))
			return false;
		if (secretary == null) {
			if (other.secretary != null)
				return false;
		} else if (!secretary.equals(other.secretary))
			return false;
		if (phone == null) {
			if (other.phone != null)
				return false;
		} else if (!phone.equals(other.phone))
			return false;
		if (potentialDto == null) {
			if (other.potentialDto != null)
				return false;
		} else if (!potentialDto.equals(other.potentialDto))
			return false;
		if (potentialIds == null) {
			if (other.potentialIds != null)
				return false;
		} else if (!potentialIds.equals(other.potentialIds))
			return false;
		if (productIds == null) {
			if (other.productIds != null)
				return false;
		} else if (!productIds.equals(other.productIds))
			return false;
		if (sectorDto == null) {
			if (other.sectorDto != null)
				return false;
		} else if (!sectorDto.equals(other.sectorDto))
			return false;
		if (specialityDto == null) {
			if (other.specialityDto != null)
				return false;
		} else if (!specialityDto.equals(other.specialityDto))
			return false;
		if (status == null) {
			if (other.status != null)
				return false;
		} else if (!status.equals(other.status))
			return false;
		if (userName == null) {
			if (other.userName != null)
				return false;
		} else if (!userName.equals(other.userName))
			return false;
		return true;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public String getScrappingId() {
		return scrappingId;
	}

	public void setScrappingId(String scrappingId) {
		this.scrappingId = scrappingId;
	}

	public Long getDoctorId() {
		return doctorId;
	}

	public void setDoctorId(Long doctorId) {
		this.doctorId = doctorId;
	}

	public String getProspectName() {
		return prospectName;
	}

	public void setProspectName(String prospectName) {
		this.prospectName = prospectName;
	}
	
	public List<Long> getPreferenceIds(){

		return preferenceIds;
	}

	public void setPreferenceIds(List<Long> list) {
		this.preferenceIds = list;
	}
	public List<Long> getInterestIds() {
		return interestIds;
	}

	public void setInterestIds(List<Long> interestIds) {
		this.interestIds = interestIds;
	}

	public List<Long> getContactTypeIds() {
		return contactTypeIds;
	}

	public void setContactTypeIds(List<Long> contactTypeIds) {
		this.contactTypeIds = contactTypeIds;
	}

	public String getSocialMedia() {
		return socialMedia;
	}

	public void setSocialMedia(String socialMedia) {
		this.socialMedia = socialMedia;

	}

	public String getTaxIdNumber() {
		return taxIdNumber;
	}

	public void setTaxIdNumber(String taxIdNumber) {
		this.taxIdNumber = taxIdNumber;
	}

	public Float getOrderPrediction() {
		return orderPrediction;
	}

	public void setOrderPrediction(Float orderPrediction) {
		this.orderPrediction = orderPrediction;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}}

