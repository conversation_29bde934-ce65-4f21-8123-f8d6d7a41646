package com.intellitech.birdnotes.service;

import java.util.List;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.dto.LoadPlanItemDto;
import com.intellitech.birdnotes.model.dto.LoadPlanRequest;

public interface LoadPlanItemService {

	void saveLoadPlan(List<LoadPlanRequest> LoadPlanRequest) throws BirdnotesException;

	
	List<LoadPlanItemDto> findAll(List<Integer> rangeIds, List<Long> specialityIds, Long loadPlanId) throws BirdnotesException;

}
