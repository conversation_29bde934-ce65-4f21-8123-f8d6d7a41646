package com.intellitech.birdnotes.service;

import java.util.Date;
import java.util.Locale;
import java.util.Map;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.security.BirdnotesUser;

public interface DownloadDataService {



	Map<String, Object> findAllDataForProspect(BirdnotesUser birdnotesUser) throws BirdnotesException;

	Map<String, Object> findAllDataForSector() throws BirdnotesException;

	Map<String, Object> findAllDataForLocality() throws BirdnotesException;

	Map<String, Object> findAllDataForNewProspect() throws BirdnotesException;

	Map<String, Object> getProspectUpdateRequests() throws BirdnotesException;

	Map<String, Object> loadDataForProspectDistribution(BirdnotesUser birdnotesUser, Locale locale) throws BirdnotesException;

	Map<String, Object> getNoteFraisDyDate(Date firstDate, Date lastDate, Long userId) throws BirdnotesException;

	Map<String, Object> loadDataToVisitHistory(BirdnotesUser birdnotesUser, Locale l) throws BirdnotesException;
	
}
