package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.dto.UserTransferDto;
import com.intellitech.birdnotes.model.request.AuthenticationRequest;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.AuthenticationService;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.TokenUtils;

@Service("authenticationService")
@Transactional
public class AuthenticationServiceImpl implements AuthenticationService {

	private AuthenticationManager authenticationManager;

	private UserDetailsService birdnotesUserDetailsService;

	private CurrentUser currentUser;

	private ConfigurationRepository configureRepository;
	
	@Autowired
	private UserRepository userRepository;
	
	@Autowired
	private	DelegateRepository delegateRepository;
	
	
	@Value("${uploadUrl}")
	private String uploadUrl;
	
	@Value("${logoPath}")
	private String logoPath;
	
	@Autowired
	public void setConfigureRepository(ConfigurationRepository configureRepository) {
		this.configureRepository = configureRepository;
	}
	
	@Autowired
	public AuthenticationServiceImpl(AuthenticationManager authenticationManager,
			UserDetailsService birdnotesUserDetailsService, CurrentUser currentUser) {
		super();
		this.authenticationManager = authenticationManager;
		this.birdnotesUserDetailsService = birdnotesUserDetailsService;
		this.currentUser = currentUser;
	}

	@Override
	public UserTransferDto authenticate(AuthenticationRequest authenticationRequest) {

		UsernamePasswordAuthenticationToken token = new UsernamePasswordAuthenticationToken(
				authenticationRequest.getUsername(), authenticationRequest.getPassword());
		Authentication authentication = authenticationManager.authenticate(token);
		SecurityContextHolder.getContext().setAuthentication(authentication);

		BirdnotesUser birdnotesUser = (BirdnotesUser) birdnotesUserDetailsService.loadUserByUsername(authenticationRequest.getUsername());

		if (birdnotesUser != null) {
			
	
			if(authenticationRequest.getMobileAppVersion() != null && ! authenticationRequest.getMobileAppVersion().equals("")) {
				delegateRepository.updateMobileAppVersion(authenticationRequest.getMobileAppVersion(), birdnotesUser.getUserDto().getId());
			}
			
			if(authenticationRequest.getOneSignalUserId() != null && !authenticationRequest.getOneSignalUserId().equals("")) {
				delegateRepository.updateOneSignalUserId(authenticationRequest.getOneSignalUserId(), birdnotesUser.getUserDto().getId());
			}
			
			userRepository.updateLastLogin(new Date(), birdnotesUser.getUserDto().getId());
			
			UserTransferDto userTransferDto = new UserTransferDto();
			userTransferDto.setToken(TokenUtils.createTokenForUser(birdnotesUser));
            //For Mobile Authentication
			Boolean isDelegate = Boolean.FALSE;
		

			List<String> roleNames = new ArrayList<>();

			for (GrantedAuthority autority : birdnotesUser.getAuthorities()) {
				roleNames.add(autority.toString());
			}

			Delegate delegate = delegateRepository.findDelegateByUserId(birdnotesUser.getUserDto().getId());
		
			Configuration config = configureRepository.findById(1);
			
			userTransferDto.setAutoSync(config.getAutoSync());
			
			userTransferDto.setAutoExpense(config.getAutoExpense());
			
			if(config.getOrderValidation() != null) {
				userTransferDto.setOrderValidation(config.getOrderValidation().toString());
			}
			
			
			userTransferDto.setSyncCycle(config.getSyncCycle());
			
			userTransferDto.setLockAfterSync(config.getLockAfterSync());
			
			userTransferDto.setMultiWholesaler(config.getMultiWholesaler());
			if(delegate != null) {
				userTransferDto.setWorkType(delegate.getWorkType().toString());			
			}
			if(delegate != null) {
				userTransferDto.setWorkingDays(delegate.getWorkingDaysPerWeek());
			}
			
		
			userTransferDto.setRoles(birdnotesUser.getUserDto().getRoles());
			
			userTransferDto.setUserId(birdnotesUser.getUserDto().getId());
			
			userTransferDto.setCommentsDictionary(config.getCommentsDictionary());

			if (currentUser != null && delegate != null) {
				userTransferDto.setFirstLastName(delegate.getFirstName() + " "
						+ delegate.getLastName());
			}

			return userTransferDto;
		}
		return null;

	}
	
	@Override
	public boolean checkTokenIsExpired(String authToken) throws BirdnotesException {

		if (authToken == null || authToken.isEmpty()) {
			throw new BirdnotesException(BirdnotesConstants.Exceptions.TOKEN_IS_EMPTY);
		}

		if (StringUtils.hasText(authToken)) {
			String username = TokenUtils.getUserNameFromToken(authToken);
			UserDetails userDetails = birdnotesUserDetailsService.loadUserByUsername(username);
			if (TokenUtils.validateToken(authToken, userDetails)) {
				return true;
			}
		}
		return false;
	}
	
	public String getLogo() {

		Configuration config = configureRepository.findOne(1) ;
		String logoFilePath =  config.getBackendUrl() + uploadUrl + logoPath + File.separator + config.getLogo();
		return logoFilePath;
		
	}

}
