<?xml version="1.0" encoding="UTF-8"?>
<configuration>

	<property name="LOGS" value="./birdnotes" />

	<appender name="Console"
		class="ch.qos.logback.core.ConsoleAppender">
		<layout class="ch.qos.logback.classic.PatternLayout">
			<Pattern>
				%yellow(%d{ISO8601}) %highlight(%-5level) [%blue(%t)]
				%yellow(%C{1.}): %msg%n%throwable
			</Pattern>
		</layout>
	</appender>

	<appender name="RollingFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS}/sync-logger.log</file>
		<encoder
			class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<Pattern>%d %p %C{1.} [%t] %m%n</Pattern>
		</encoder>
		<filter class="ch.qos.logback.core.filter.EvaluatorFilter">
			<evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
				<marker>SYNC</marker>
			</evaluator>
			<onMismatch>DENY</onMismatch>
			<onMatch>ACCEPT</onMatch>
		</filter>
		<rollingPolicy
			class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- rollover daily and when the file reaches 10 MegaBytes -->
			<fileNamePattern>${LOGS}/archived/sync-logger-%d{yyyy-MM-dd}.%i.log
			</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>10MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
	</appender>

	<appender name="ProspectLogFile"
		class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOGS}/prospect-logger.log</file>
		<encoder
			class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
			<Pattern>%d %p %C{1.} [%t] %m%n</Pattern>
		</encoder>
		<filter class="ch.qos.logback.core.filter.EvaluatorFilter">
			<evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
				<marker>PROSPECT</marker>
			</evaluator>
			<onMismatch>DENY</onMismatch>
			<onMatch>ACCEPT</onMatch>
		</filter>
		<rollingPolicy
			class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<!-- rollover daily and when the file reaches 10 MegaBytes -->
			<fileNamePattern>
				${LOGS}/archived/prospect-logger-%d{yyyy-MM-dd}.%i.log
            </fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy
				class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>10MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
		</rollingPolicy>
	</appender>

	<!-- LOG everything at INFO level -->
	<root level="info">
		<appender-ref ref="RollingFile" />
		<appender-ref ref="Console" />
		<appender-ref ref="ProspectLogFile" />
	</root>


</configuration>