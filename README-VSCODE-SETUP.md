# Configuration Spring Boot dans VSCode

## ✅ Extensions installées

Les extensions suivantes ont été installées avec succès :

- **Extension Pack for Java** - Pack complet pour le développement Java
- **Spring Boot Extension Pack** - Extensions pour Spring Boot
- **Spring Boot Tools** - Outils pour Spring Boot
- **Spring Initializr Java Support** - Support pour Spring Initializr
- **Spring Boot Dashboard** - Tableau de bord Spring Boot
- **Maven for Java** - Support Maven
- **Language Support for Java** - Support du langage Java

## 📁 Configuration VSCode

Les fichiers de configuration suivants ont été créés dans `.vscode/` :

### `settings.json`
- Configuration automatique de Maven
- Formatage automatique du code
- Organisation automatique des imports
- Exclusion des dossiers de build

### `launch.json`
- Configuration de débogage pour Spring Boot
- Profil de développement activé
- Support des variables d'environnement

### `tasks.json`
- Tâches Maven prédéfinies
- Compilation, packaging, tests
- Démarrage Spring Boot

## 🗄️ Configuration de la base de données

L'application utilise PostgreSQL. Vous devez configurer une base de données avant de démarrer l'application.

### Option 1: PostgreSQL local

1. Installez PostgreSQL sur votre machine
2. Créez une base de données nommée `test`
3. Configurez l'utilisateur `postgres` avec le mot de passe `intellitech`

```sql
CREATE DATABASE test;
CREATE USER postgres WITH PASSWORD 'intellitech';
GRANT ALL PRIVILEGES ON DATABASE test TO postgres;
```

### Option 2: Docker PostgreSQL

```bash
docker run --name postgres-birdnotes \
  -e POSTGRES_DB=test \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=intellitech \
  -p 5432:5432 \
  -d postgres:13
```

## 🚀 Démarrage de l'application

### Via VSCode
1. Ouvrez la palette de commandes (`Ctrl+Shift+P`)
2. Tapez "Spring Boot Dashboard"
3. Cliquez sur le bouton de démarrage à côté de votre application

### Via Terminal
```bash
mvn spring-boot:run
```

### Via les tâches VSCode
1. `Ctrl+Shift+P` → "Tasks: Run Task"
2. Sélectionnez "spring-boot: run"

## 🐛 Débogage

1. Placez des points d'arrêt dans votre code
2. Appuyez sur `F5` ou utilisez la configuration "Debug Spring Boot App"
3. L'application démarrera en mode débogage sur le port 5005

## 📊 Accès à l'application

Une fois démarrée, l'application sera accessible sur :
- **URL**: http://localhost:9090
- **Swagger UI**: http://localhost:9090/swagger-ui.html (si configuré)

## 🔧 Commandes Maven utiles

```bash
# Compilation
mvn clean compile

# Tests
mvn test

# Package
mvn clean package

# Démarrage
mvn spring-boot:run

# Démarrage avec profil dev
mvn spring-boot:run -Dspring.profiles.active=dev
```

## 📝 Profils disponibles

- `dev` - Développement (base de données locale)
- `test` - Tests
- `prod` - Production
- `devops` - DevOps

## ⚠️ Problèmes courants

### Erreur de compilation AspectJ
✅ **Résolu** - La dépendance AspectJ problématique a été exclue du pom.xml

### Base de données non accessible
- Vérifiez que PostgreSQL est démarré
- Vérifiez les paramètres de connexion dans `application-dev.properties`
- Vérifiez que la base de données `test` existe

### Port 9090 déjà utilisé
```bash
# Trouver le processus utilisant le port
netstat -ano | findstr :9090

# Ou changer le port dans application.properties
server.port=8080
```

## 🎯 Prochaines étapes

1. Configurez votre base de données PostgreSQL
2. Démarrez l'application
3. Testez les endpoints avec Postman ou curl
4. Explorez le code avec les fonctionnalités de navigation de VSCode

## 📚 Ressources utiles

- [Spring Boot Documentation](https://spring.io/projects/spring-boot)
- [VSCode Java Documentation](https://code.visualstudio.com/docs/languages/java)
- [Spring Boot in VSCode](https://code.visualstudio.com/docs/java/java-spring-boot)
