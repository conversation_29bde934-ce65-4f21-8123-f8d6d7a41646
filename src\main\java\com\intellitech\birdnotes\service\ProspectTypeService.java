package com.intellitech.birdnotes.service;

import java.util.List;

import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Potential;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.dto.PotentialDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;

public interface ProspectTypeService {
	
	List<ProspectTypeDto> findAll() throws BirdnotesException;

	void saveAllProspectTypes(List<ProspectTypeDto> prospectTypeDtos) throws BirdnotesException;

	ProspectTypeDto findProspectTypeDto(String prospectTypeName, List<ProspectTypeDto> prospectTypeDtos);
	
	ProspectType saveProspectType(ProspectTypeDto prospectTypeDto) throws BirdnotesException;
	
	void delete (long id) throws BirdnotesException;


	

}
