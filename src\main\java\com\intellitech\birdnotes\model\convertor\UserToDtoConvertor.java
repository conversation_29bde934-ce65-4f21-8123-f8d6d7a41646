package com.intellitech.birdnotes.model.convertor;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;
import com.intellitech.birdnotes.model.dto.GoalDto;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.Network;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;

@Component("userToDtoConvertor")
public class UserToDtoConvertor implements Converter<User, UserDto> {

	private static final Logger LOG = LoggerFactory.getLogger(UserToDtoConvertor.class);

	private ConvertGoalToDto convertGoalToDto;

	@Value("${uploadPath}")
	private String uploadPath;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${userPath}")
	private String userPath;

	@Autowired
	public void setConvertGoalToDto(ConvertGoalToDto convertGoalToDto) {
		this.convertGoalToDto = convertGoalToDto;

	}

	@Override
	public UserDto convert(User user) {

		if (user == null) {
			LOG.error("user is null");
			return null;
		}
		UserDto userDto = new UserDto();
		Set<Role> roles = user.getRoles();
		if (roles == null) {
			LOG.error("user role is null ");
			return null;
		} else {
			if (user.getGoals() != null) {
				try {
					List<GoalDto> goalDtoList = new ArrayList<>();
					for (Goal goal : user.getGoals()) {
						GoalDto goalDto = convertGoalToDto.convert(goal);
						goalDtoList.add(goalDto);
						userDto.setGoals(goalDtoList);
					}
				} catch (BirdnotesException e) {

					e.printStackTrace();
				}
			}
			userDto.setId(user.getId());
			userDto.setRoles(roles);
			userDto.setRoleIds(roles.stream().map(Role::getId).collect(Collectors.toList()));
			userDto.setRangeIds(user.getRanges().stream().map(Range::getId).collect(Collectors.toList()));
			userDto.setEmail(user.getEmail());
			userDto.setUsername(user.getUsername());
			userDto.setPassword(user.getPassword());
			userDto.setPhone(user.getPhone());
			userDto.setOneSignalUserId(user.getOneSignalUserId());
			userDto.setActive(user.getActive());
			if (user.getSuperiors() == null) {
				LOG.error("getSuperiors of " + user.getUsername() + " is null");
			} else {
				userDto.setSuperiors(user.getSuperiors().stream().map(User::getId).collect(Collectors.toList()));
			}
			if (user.getDelegate() != null) {
				userDto.setDelegateId(user.getDelegate().getId());
				userDto.setFirstName(user.getDelegate().getFirstName());
				userDto.setLastName(user.getDelegate().getLastName());
			}else {
				userDto.setFirstName(user.getUsername());
				userDto.setLastName(user.getUsername());
			}
			if (user.getProspect() != null) {
				userDto.setProspectId(user.getProspect().getId());
			}
			DateFormat df = new SimpleDateFormat("dd/MM/yyyy HH:mm");
			if (user.getLastLogin() != null) {
				userDto.setLastLogin(df.format(user.getLastLogin()));
			}
		}
		return userDto;
	}

}
