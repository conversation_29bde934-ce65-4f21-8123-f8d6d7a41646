package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.intellitech.birdnotes.enumeration.CommissionType;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.COMMISSION, schema = Common.PUBLIC_SCHEMA)
public class Commission implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Date FirstDate;
	private Date lastDate; 
	private String name;
	private List<Product> products ;
	private List<Prospect> wholesalers ;
	private List<User> users ;
	private Set<CommissionItem> commissionItem;
	private CommissionType type;
	
	

	@Id
	@SequenceGenerator(name = Sequences.COMMISSION_SEQUENCE, sequenceName = Sequences.COMMISSION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.COMMISSION_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	
	@Temporal(TemporalType.DATE)
	@Column(name = Columns.FIRST_DATE)
	public Date getFirstDate() {
		return FirstDate;
	}

	public void setFirstDate(Date firstDate) {
		FirstDate = firstDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.LAST_DATE)
	public Date getLastDate() {
		return lastDate;
	}

	public void setLastDate(Date lastDate) {
		this.lastDate = lastDate;
	}
	
	
	@Column(name = Columns.NAME)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.PRODUCT_COMMISSION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.COMMISSION_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.PRODUCT_ID, nullable = false, updatable = false) })
	public List<Product> getProducts() {
		return products;
	}

	public void setProducts(List<Product> products) {
		this.products = products;
	}
	
	
	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.WHOLESALER_COMMISSION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.COMMISSION_ID) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.WHOLESALER_ID) })
	public List<Prospect> getWholesalers() {
		return wholesalers;
	}

	public void setWholesalers(List<Prospect> wholesalers) {
		this.wholesalers = wholesalers;
	}

	@JsonIgnore
	@ManyToMany(fetch = FetchType.LAZY)
	@JoinTable(name = BirdnotesConstants.Tables.USER_COMMISSION, schema = BirdnotesConstants.Common.PUBLIC_SCHEMA, joinColumns = {
			@JoinColumn(name = BirdnotesConstants.Columns.COMMISSION_ID, nullable = false, updatable = false) }, inverseJoinColumns = {
					@JoinColumn(name = BirdnotesConstants.Columns.USER_ID, nullable = false, updatable = false) })
	public List<User> getUsers() {
		return users;
	}

	public void setUsers(List<User> users) {
		this.users = users;
	}
	
	@OneToMany (fetch = FetchType.LAZY, mappedBy = "commission")
	@JsonIgnore
	public Set<CommissionItem> getCommissionItem() {
		return commissionItem;
	}

	public void setCommissionItem(Set<CommissionItem> commissionItem) {
		this.commissionItem = commissionItem;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.TYPE)
	public CommissionType getType() {
		return type;
	}

	public void setType(CommissionType type) {
		this.type = type;
	}
	
}