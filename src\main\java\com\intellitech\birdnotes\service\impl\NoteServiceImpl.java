package com.intellitech.birdnotes.service.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import javax.transaction.Transactional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.intellitech.birdnotes.data.dto.NoteFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Note;
import com.intellitech.birdnotes.model.Sector;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.convertor.ConvertSectorToDto;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.dto.NoteDto;
import com.intellitech.birdnotes.model.dto.SectorDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.repository.NoteRepository;
import com.intellitech.birdnotes.repository.SectorRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.service.NoteService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.SpecialityService;

@Service("noteService")
@Transactional
public class NoteServiceImpl implements NoteService {

	Logger log = LoggerFactory.getLogger(this.getClass().getName());
	@Autowired
	private NoteRepository noteRepository;
	@Autowired
	private SectorRepository sectorRepository;
	@Autowired
	private SpecialityRepository specialityRepository;
	@Autowired
	private ConvertSectorToDto convertSectorToDto;
	@Autowired
	private ConvertSpecialityToDto convertSpecialityToDto;
	@Autowired
	private SectorService sectorService;
	@Autowired
	private SpecialityService specialityService;

	@Override
	public Note saveNote(NoteDto noteDto) throws BirdnotesException {

		Note note = null;
		if (noteDto.getId() != null) {
			note = noteRepository.findOne(noteDto.getId());
		}
		if (note == null) {
			note = new Note();
		}

		note.setLink(noteDto.getLink());
		note.setNote(noteDto.getNote());
		note.setActivity(noteDto.getActivity());

		List<Sector> sectors = new ArrayList<>();
		for (Long sectorId : noteDto.getSectorId()) {
			Sector sector = sectorRepository.findOne(sectorId);
			sectors.add(sector);
		}
		note.setSectors(sectors);

		List<Speciality> specialities = new ArrayList<>();
		for (Long specialityId : noteDto.getSpecialityId()) {
			Speciality speciality = specialityRepository.findOne(specialityId);
			specialities.add(speciality);
		}
		note.setSpecialities(specialities);

		return noteRepository.save(note);
	}

	@Override
	public List<NoteDto> findAll() throws BirdnotesException {
		List<NoteDto> result = new ArrayList<>();
		List<Note> allNote = noteRepository.findAll();

		for (Note note : allNote) {
			NoteDto noteDto = new NoteDto();
			List<String> sectorName = new ArrayList<>();
			List<Long> sectorIds = new ArrayList<>();

			List<SectorDto> sectors = new ArrayList<SectorDto>();

			List<SpecialityDto> specialities = new ArrayList<SpecialityDto>();
			List<String> specialityName = new ArrayList<>();
			List<Long> specialityIds = new ArrayList<>();

			noteDto.setId(note.getId());
			noteDto.setLink(note.getLink());
			noteDto.setNote(note.getNote());
			noteDto.setActivity(note.getActivity());
			for (Sector sector : note.getSectors()) {
				SectorDto sectorDto = convertSectorToDto.convert(sector);
				sectors.add(sectorDto);
				sectorName.add(sector.getName());
				sectorIds.add(sector.getId());
			}
			noteDto.setSectorName(sectorName);
			noteDto.setSectors(sectors);
			noteDto.setSectorId(sectorIds);

			for (Speciality speciality : note.getSpecialities()) {
				SpecialityDto specialityDto = convertSpecialityToDto.convert(speciality);
				specialities.add(specialityDto);
				specialityName.add(speciality.getName());
				specialityIds.add(speciality.getId());
			}
			noteDto.setSpecialityName(specialityName);
			noteDto.setSpecialities(specialities);
			noteDto.setSpecialityId(specialityIds);

			result.add(noteDto);
		}

		return result;
	}

	@Override
	public NoteFormData getAllDataNote() throws IOException, BirdnotesException {
		List<SectorDto> sectorDto = sectorService.findAll();
		List<SpecialityDto> specialityDto = specialityService.findAll();
		NoteFormData noteFormData = new NoteFormData();
		noteFormData.setSectors(sectorDto);
		noteFormData.setSpecialities(specialityDto);

		return noteFormData;
	}

	@Override
	public void delete(Long id) {
		noteRepository.delete(id);

	}

}