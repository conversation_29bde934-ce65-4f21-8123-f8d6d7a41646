package com.intellitech.birdnotes.service;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.util.BirdnotesConstants;

@Service
@Transactional
public class StorageFileLogService {

	@Value("${mobileAppLogsPath}")
	private String mobileAppLogsPath;
	@Value("${uploadPath}")
	private String uploadPath;
	private Path pathLogFile;

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	public void store(MultipartFile file, Long issueId) throws BirdnotesException {

		pathLogFile = Paths.get(uploadPath+mobileAppLogsPath + File.separator + issueId);
		try {
			Files.copy(file.getInputStream(), this.pathLogFile.resolve(BirdnotesConstants.Configuration.MOBILE_LOG_FILE_NAME));

		} catch (Exception e) {
			log.error("Failed to copy log file",e);
			throw new BirdnotesException("Failed of copy file");

		}

	}
}
