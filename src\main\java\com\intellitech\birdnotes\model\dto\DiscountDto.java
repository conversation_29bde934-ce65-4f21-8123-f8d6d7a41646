package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;
import java.util.List;

public class DiscountDto implements Serializable {

	private static final long serialVersionUID = 1L;
	private Long id;
	private List<Long> productIds;
	private String productName;
	private List<ProductDto> products;
	private Long productId;
	private ProductDto product;
	private Float percentage;
	private String prospectType;
	private Long prospectTypeId;
	private List<Long> wholesalerIds;
	private Long wholesalerId;
	private String wholesalerName;
	private Integer rangeId;
	private String rangeName;

	public DiscountDto(Long id) {
		super();
		this.id = id;

	}

	public String getProductName() {
		return productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public List<Long> getProductIds() {
		return productIds;
	}

	public void setProductIds(List<Long> productIds) {
		this.productIds = productIds;
	}

	public DiscountDto() {
		super();

	}

	public List<ProductDto> getProducts() {
		return products;
	}

	public void setProducts(List<ProductDto> products) {
		this.products = products;
	}

	public ProductDto getProduct() {
		return product;
	}

	public void setProduct(ProductDto product) {
		this.product = product;
	}

	public Float getPercentage() {
		return percentage;
	}

	public void setPercentage(Float percentage) {
		this.percentage = percentage;
	}

	public Long getProductId() {
		return productId;
	}

	public void setProductId(Long productId) {
		this.productId = productId;
	}

	public String getProspectType() {
		return prospectType;
	}

	public void setProspectType(String prospectType) {
		this.prospectType = prospectType;
	}

	public Long getProspectTypeId() {
		return prospectTypeId;
	}

	public void setProspectTypeId(Long prospectTypeId) {
		this.prospectTypeId = prospectTypeId;
	}

	public List<Long> getWholesalerIds() {
		return wholesalerIds;
	}

	public void setWholesalerIds(List<Long> wholesalerIds) {
		this.wholesalerIds = wholesalerIds;
	}

	public Long getWholesalerId() {
		return wholesalerId;
	}

	public void setWholesalerId(Long wholesalerId) {
		this.wholesalerId = wholesalerId;
	}

	public String getWholesalerName() {
		return wholesalerName;
	}

	public void setWholesalerName(String wholesalerName) {
		this.wholesalerName = wholesalerName;
	}

	public Integer getRangeId() {
		return rangeId;
	}

	public void setRangeId(Integer rangeId) {
		this.rangeId = rangeId;
	}

	public String getRangeName() {
		return rangeName;
	}

	public void setRangeName(String rangeName) {
		this.rangeName = rangeName;
	}

}
