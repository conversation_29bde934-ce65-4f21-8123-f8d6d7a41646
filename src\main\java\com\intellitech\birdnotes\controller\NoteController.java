package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.data.dto.NoteFormData;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Note;
import com.intellitech.birdnotes.model.dto.NoteDto;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.NoteService;
import com.intellitech.birdnotes.service.SectorService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@RestController
@RequestMapping("/note")
public class NoteController {
	private static final Logger LOG = LoggerFactory.getLogger(NoteController.class);
	@Autowired
	UserService userService;
	@Autowired
	private CurrentUser currentUser;
	@Autowired
	private SectorService sectorService;
	@Autowired
	private SpecialityService specialityService;
	@Autowired
	private NoteService noteService;

	@RequestMapping(value = "/getAllDataNoteForm", method = RequestMethod.GET)
	public ResponseEntity<NoteFormData> getAllDataNoteForm() {
		try {
			NoteFormData result = noteService.getAllDataNote();
			return new ResponseEntity<>(result, HttpStatus.OK);
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all data ", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/saveNote", method = RequestMethod.POST)
	public ResponseEntity<Long> saveNote(@RequestBody NoteDto noteDto) {
		try {
			if (userService.checkHasPermission("NOTE_EDIT") || userService.checkHasPermission("PRODUCT_VIEW")) {
				Note savedNote = noteService.saveNote(noteDto);
				if (savedNote != null) {
					return new ResponseEntity<>(savedNote.getId(), HttpStatus.OK);
				}
				return new ResponseEntity<>(null, HttpStatus.OK);
			} else {
				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}

		} catch (BirdnotesException e) {
			LOG.error("An exception occurred when saving savedNote", e);
			return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
		} catch (Exception e) {
			LOG.error("An exception occurred while saving savedNote", e);
			return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "/findAllNote", method = RequestMethod.GET)
	public ResponseEntity<List<NoteDto>> findAll() {

		try {
			if (userService.checkHasPermission("NOTE_VIEW")) {
				List<NoteDto> noteDto = noteService.findAll();
				return new ResponseEntity<>(noteDto, HttpStatus.OK);
			} else {

				return new ResponseEntity<>(null, HttpStatus.NON_AUTHORITATIVE_INFORMATION);
			}
		} catch (Exception e) {
			LOG.error("An exception occurred while getting all notes", e);
			return new ResponseEntity<>(Collections.emptyList(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping(value = "delete/{id}", method = RequestMethod.DELETE)
	public ResponseEntity<String> delete(@PathVariable("id") Long id) {
		try {

			noteService.delete(id);
			return new ResponseEntity<>(Exceptions.OK, HttpStatus.OK);
			
		} catch (DataIntegrityViolationException e) {	
			LOG.error("An DataIntegrityViolationException occurred when deleting note", e);
			return new ResponseEntity<>(userService.handleDataIntegrityViolationException(e), HttpStatus.INTERNAL_SERVER_ERROR);


		} catch (Exception e) {
			LOG.error("Error in delete note", e);
			return new ResponseEntity<>(e.getMessage(), HttpStatus.INTERNAL_SERVER_ERROR);
		}
	}
}
