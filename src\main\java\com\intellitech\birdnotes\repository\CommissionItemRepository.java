package com.intellitech.birdnotes.repository;

import java.util.Date;
import java.util.List;
import java.util.Set;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.enumeration.GoalType;
import com.intellitech.birdnotes.model.Commission;
import com.intellitech.birdnotes.model.CommissionItem;
import com.intellitech.birdnotes.model.Goal;
import com.intellitech.birdnotes.model.GoalItem;
import com.intellitech.birdnotes.model.PurchaseOrderTemplate;
import com.intellitech.birdnotes.model.PurchaseOrderTemplateItem;

@Repository
public interface CommissionItemRepository extends JpaRepository<CommissionItem, Long>{
	
	
	Set<CommissionItem> findByCommission(Commission commission);
	
}
