package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Numbers;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.ISSUE, schema = Common.PUBLIC_SCHEMA)
public class Issue implements Serializable {

	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.ISSUE_SEQUENCE, sequenceName = Sequences.ISSUE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.ISSUE_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.USERNAME, length = Numbers.N_85)
	private String username;

	@Column(name = Columns.DESCRIPTION, length = Numbers.N_85)
	private String description;

	@Temporal(TemporalType.DATE)
	@Column(name = Columns.ISSUE_DATE)
	private Date issueDate;

	public Issue(Long id, String username, String description, Date date) {
		super();
		this.id = id;
		this.username = username;
		this.description = description;
		this.issueDate = date;
	}

	public Issue(String username, String description, Date date) {
		super();
		this.username = username;
		this.description = description;
		this.issueDate = date;
	}

	public Issue() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Date getDate() {
		return issueDate;
	}

	public void setDate(Date date) {
		this.issueDate = date;
	}


}
