package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;
@Entity
@Table(name = Tables.ACTIVITYTYPE, schema = Common.PUBLIC_SCHEMA)

public class ActivityType implements Serializable{
	
	private static final long serialVersionUID = 1L;

	@Id
	@SequenceGenerator(name = Sequences.ACTIVITY_TYPE_SEQUENCE, sequenceName = Sequences.ACTIVITY_TYPE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.ACTIVITY_TYPE_SEQUENCE)
	@Column(name = Columns.ID)
	private Long id;

	@Column(name = Columns.NAME)
	private String name;
	
	public ActivityType(Long id, String name) {
		super();
		this.id = id;
		this.name = name;
	}
	

	public ActivityType() {
		super();

	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

}
