package com.intellitech.birdnotes.util;

import java.text.DateFormat;
import java.text.MessageFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.model.Notification;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.UserDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.UserService;

@Component
public class NotificationMessageBuilder {

	private static final Logger LOG = LoggerFactory.getLogger(NotificationMessageBuilder.class);



	@Autowired
	private Environment env;
	
	@Autowired
	private ConfigurationService configurationService;
	
	private User user;
	
	private User targetUser;
	
	private Date date;
	
	private List<String> prospects = new ArrayList();
	
	private String messageType;
	
	private String note;
	
	private String productName;
	
	private Long entityId ;
	
	private String status;
	
	@Autowired
	private UserService userService; 
	
	
	
	public Long getEntityId() {
		return entityId;
	}

	public void setEntityId(Long entityId) {
		this.entityId = entityId;
	}

	public Notification Build () {
		
		String[] args = new String[8];
		if(user.getDelegate() != null) {
			args[0] = user.getDelegate().getFirstName()+' '+user.getDelegate().getLastName();
		}else {
			args[0] = user.getUsername();
		}
		
		String message =  userService.getTranslatedLabel(messageType);
		MessageFormat messageFormat = new MessageFormat(message);		
		if(messageType.equals("prospectsAcceptationNotificationMessage" )
				|| messageType.equals("prospectsModificationAcceptationNotificationMessage")
				|| messageType.equals("prospectsRefuseNotificationMessage")
				|| messageType.equals("addProspectNotificationMessage")
				|| messageType.equals("updateProspectNotificationMessage")
				|| messageType.equals("prospectsAffectationNotificationMessage") 
				|| messageType.equals("prospectsAffectationDeleteNotificationMessage")
				|| messageType.equals("visitMessageTag")
				|| messageType.equals("importantMessageTag")
				|| messageType.equals("urgentMessageTag")
				|| messageType.equals("reportValidation")){
			
			StringBuilder prospectsNames = new StringBuilder();
			int counter = 1;
			for (String prospect : prospects) {
				prospectsNames.append(prospect);
				if(counter < prospects.size()){
					prospectsNames.append(", ");
				}
				counter++;
				
			}
			
			args[1] = prospectsNames.toString();
		}
		

		if(entityId != null) {
			args[2] = entityId.toString();
		}
		if(date!=null) {
			DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
			args[3] = dateFormat.format(date);	
		}
		if(note != null) {
			args[4] = note;
		}
		ConfigurationDto config = configurationService.findConfiguration();
		args[5] = config.getServerPath();
		
		if(productName != null) {
			args[6] = productName;
		}
		if(status != null) {
			args[7] = status;
		}
		
		  String notificationMessage = messageFormat.format(args); 
		  Notification notification = new Notification(notificationMessage, false, targetUser);
		  return notification;
 
		}
	
	public void setUser(User user) {
		this.user = user;
	}
	public void setTagetUser(User targetUser) {
		this.targetUser = targetUser;
	}
	
	
	public void setMessageType (String messageType) {
		this.messageType = messageType;
	}
	
	public void setProspect(Prospect prospect) {
		this.prospects = new ArrayList<>();
		this.prospects.add(prospect.getFirstName() + " " + prospect.getLastName());
		
	}

	public void setProspects(List<String> prospects) {
		this.prospects = prospects;
	}


	public void setDate(Date date) {
		this.date = date;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	public void setStatus(String status) {
		this.status = status;
	}
	

}
