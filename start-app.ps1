# Script PowerShell pour démarrer l'application BirdNotes
# Usage: .\start-app.ps1

Write-Host "🚀 Démarrage de l'application BirdNotes Spring Boot" -ForegroundColor Green

# Vérifier si Maven est installé
try {
    $mavenVersion = mvn --version | Select-Object -First 1
    Write-Host "✅ Maven détecté: $mavenVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Maven n'est pas installé ou n'est pas dans le PATH" -ForegroundColor Red
    Write-Host "Veuillez installer Maven depuis https://maven.apache.org/download.cgi" -ForegroundColor Yellow
    exit 1
}

# Vérifier si PostgreSQL est accessible
Write-Host "🔍 Vérification de la base de données PostgreSQL..." -ForegroundColor Blue

# Test de connexion simple avec timeout
$testConnection = $false
try {
    # Utiliser Test-NetConnection pour vérifier si le port 5432 est ouvert
    $connectionTest = Test-NetConnection -ComputerName localhost -Port 5432 -WarningAction SilentlyContinue
    if ($connectionTest.TcpTestSucceeded) {
        $testConnection = $true
        Write-Host "✅ PostgreSQL est accessible sur localhost:5432" -ForegroundColor Green
    }
} catch {
    # Ignorer les erreurs
}

if (-not $testConnection) {
    Write-Host "⚠️  PostgreSQL n'est pas accessible sur localhost:5432" -ForegroundColor Yellow
    Write-Host "💡 Voulez-vous démarrer PostgreSQL avec Docker? (y/n): " -ForegroundColor Cyan -NoNewline
    $response = Read-Host
    
    if ($response -eq "y" -or $response -eq "Y" -or $response -eq "yes") {
        Write-Host "🔄 Exécution du script de configuration de la base de données..." -ForegroundColor Blue
        & .\setup-database.ps1
        if ($LASTEXITCODE -ne 0) {
            Write-Host "❌ Erreur lors de la configuration de la base de données" -ForegroundColor Red
            exit 1
        }
    } else {
        Write-Host "⚠️  Assurez-vous que PostgreSQL est démarré avant de continuer" -ForegroundColor Yellow
        Write-Host "📋 Configuration requise:" -ForegroundColor Cyan
        Write-Host "   Host: localhost:5432" -ForegroundColor White
        Write-Host "   Database: test" -ForegroundColor White
        Write-Host "   Username: postgres" -ForegroundColor White
        Write-Host "   Password: intellitech" -ForegroundColor White
    }
}

# Compilation du projet
Write-Host "🔨 Compilation du projet..." -ForegroundColor Blue
mvn clean compile

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Erreur lors de la compilation" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Compilation réussie" -ForegroundColor Green

# Démarrage de l'application
Write-Host "🚀 Démarrage de l'application Spring Boot..." -ForegroundColor Blue
Write-Host "📍 L'application sera accessible sur http://localhost:9090" -ForegroundColor Cyan
Write-Host "⏹️  Appuyez sur Ctrl+C pour arrêter l'application" -ForegroundColor Yellow
Write-Host ""

# Démarrer avec le profil dev
mvn spring-boot:run -Dspring.profiles.active=dev
