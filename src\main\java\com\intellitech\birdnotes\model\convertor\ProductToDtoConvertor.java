package com.intellitech.birdnotes.model.convertor;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Range;
import com.intellitech.birdnotes.model.Product;
import com.intellitech.birdnotes.model.dto.ProductDto;

@Component("productToDtoConvertor")
public class ProductToDtoConvertor {

	private static final Logger LOG = LoggerFactory.getLogger(ProductToDtoConvertor.class);

	public ProductDto convert(Product product) throws BirdnotesException {

		if (product == null) {
			LOG.error("product is null");
			throw new BirdnotesException("product is null");
		}

		ProductDto productDto = new ProductDto();
		productDto.setId(product.getId());
		productDto.setName(product.getName());
		productDto.setNumberOfCapsules(product.getNumberOfCapsules());
		productDto.setPrice(product.getPrice());
		productDto.setBuyingPrice(product.getBuyingPrice());
		productDto.setDescription(product.getDescription());
		productDto.setStock(product.getStock());
		productDto.setVersion(product.getVersion());
		productDto.setQuantityUnit(product.getQuantityUnit());
		productDto.setCode(product.getCode());
		if(product.getVat() != null) {
			productDto.setVat(product.getVat());
		}

		Set<Range> gammes = product.getRanges();

		if (gammes == null) {
			LOG.error("gamme of product " + product.getName() + " is null");
			throw new BirdnotesException("gamme is null");
		}

		productDto.setRanges(gammes);

		if (!gammes.isEmpty()) {

			StringBuilder gammesStringBuild = new StringBuilder();
			List<Integer> rangeIds = new ArrayList<>();
			for (Range gamme : gammes) {
				gammesStringBuild.append(gamme.getName());
				gammesStringBuild.append(", ");
				rangeIds.add(gamme.getId());
			}
			productDto.setRangeIds(rangeIds);
			String gammesString = gammesStringBuild.toString();

			if (!gammesString.isEmpty()) {
				gammesString = gammesString.substring(0, gammesString.length() - 2);
			}

			productDto.setRangesString(gammesString);
		}

		return productDto;

	}

}
