package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.intellitech.birdnotes.model.Establishment;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.Speciality;

@Repository
public interface ProspectTypeRepository extends JpaRepository<ProspectType, Long> {
	
	
	
	@Override
	@Query("SELECT pt from ProspectType pt order by pt.name ASC")
	List<ProspectType> findAll();

	ProspectType findByName(String name);

	@Modifying
	@Query("DELETE FROM ProspectType where id=:id")
	void deleteById(@Param ("id") Long id);
    @Query("SELECT p.id from ProspectType p")
	List<Long> getAllProspectTypeIds();
    
	@Query("SELECT pt from ProspectType pt where  LOWER(name) = LOWER(?1) AND id != ?2")
	ProspectType findByNameAndAnotherId(String name, Long id);

	
	
}