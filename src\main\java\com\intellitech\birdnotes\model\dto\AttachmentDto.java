package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;


public class AttachmentDto implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	
	private Long identifier;
	
	private String attachmentBase64;
	
	private String attachmentName;

	
	public AttachmentDto() {
		super();
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}


	public String getAttachmentBase64() {
		return attachmentBase64;
	}

	public void setAttachmentBase64(String attachmentBase64) {
		this.attachmentBase64 = attachmentBase64;
	}

	public Long getIdentifier() {
		return identifier;
	}

	public void setIdentifier(Long identifier) {
		this.identifier = identifier;
	}

	public String getAttachmentName() {
		return attachmentName;
	}

	public void setAttachmentName(String attachmentName) {
		this.attachmentName = attachmentName;
	}

}
