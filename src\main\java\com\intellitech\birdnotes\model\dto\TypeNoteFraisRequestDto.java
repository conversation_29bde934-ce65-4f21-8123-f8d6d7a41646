package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class TypeNoteFraisRequestDto {
	
	private String name;
	
	private float price;
	
	private float mileage;
	
	private Boolean requiredAttachment;
	
	private List<Long> delegatesId;
	
	private List<UserDto> delegates;
	
	public TypeNoteFraisRequestDto() {
		super();
	}

	public TypeNoteFraisRequestDto(String name) {
		super();
		this.name = name;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public float getMileage() {
		return mileage;
	}

	public void setMileage(float mileage) {
		this.mileage = mileage;
	}

	public List<Long> getDelegatesId() {
		return delegatesId;
	}

	public void setDelegatesId(List<Long> delegatesId) {
		this.delegatesId = delegatesId;
	}

	public List<UserDto> getDelegates() {
		return delegates;
	}

	public void setDelegates(List<UserDto> delegates) {
		this.delegates = delegates;
	}

	public Boolean getRequiredAttachment() {
		return requiredAttachment;
	}

	public void setRequiredAttachment(Boolean requiredAttachment) {
		this.requiredAttachment = requiredAttachment;
	}
	

}
