package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class NoteDto {
	private Long id;
	private String link;
	private String note;
	private String activity;
	private List<SectorDto> sectors;
	private List<Long> sectorId;
	private List<String> sectorName;
	private List<SpecialityDto> specialities;
	private List<Long> specialityId;
	private List<String> specialityName;

	public List<String> getSectorName() {
		return sectorName;
	}

	public void setSectorName(List<String> sectorName) {
		this.sectorName = sectorName;
	}

	public List<String> getSpecialityName() {
		return specialityName;
	}

	public void setSpecialityName(List<String> specialityName) {
		this.specialityName = specialityName;
	}

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getLink() {
		return link;
	}

	public void setLink(String link) {
		this.link = link;
	}

	public String getNote() {
		return note;
	}

	public void setNote(String note) {
		this.note = note;
	}

	public String getActivity() {
		return activity;
	}

	public void setActivity(String activity) {
		this.activity = activity;
	}

	public List<SectorDto> getSectors() {
		return sectors;
	}

	public void setSectors(List<SectorDto> sectors) {
		this.sectors = sectors;
	}

	public List<Long> getSectorId() {
		return sectorId;
	}

	public void setSectorId(List<Long> sectorId) {
		this.sectorId = sectorId;
	}

	

	public List<SpecialityDto> getSpecialities() {
		return specialities;
	}

	public void setSpecialities(List<SpecialityDto> specialities) {
		this.specialities = specialities;
	}

	public List<Long> getSpecialityId() {
		return specialityId;
	}

	public void setSpecialityId(List<Long> specialityId) {
		this.specialityId = specialityId;
	}

}
