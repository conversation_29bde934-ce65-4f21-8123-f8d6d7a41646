package com.intellitech.birdnotes.model;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.JoinTable;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.intellitech.birdnotes.data.dto.ProductRevenueByMonthDto;
import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.enumeration.ValueType;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;

@Entity
@Table(name = Tables.DELEGATE_COMMISSION, schema = Common.PUBLIC_SCHEMA)
public class DelegateCommission implements Serializable {

	private static final long serialVersionUID = 1L;

	private Long id;
	private Date month;
	private User user ;
	private Commission commission;
	private Float amount;
	private UserValidationStatus status;
	private List<ValidationStatus> validationStatus;
	

	@Id
	@SequenceGenerator(name = Sequences.DELEGATE_COMMISSION_SEQUENCE, sequenceName = Sequences.DELEGATE_COMMISSION_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.DELEGATE_COMMISSION_SEQUENCE)
	@Column(name = Columns.ID)
	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	
	@Temporal(TemporalType.DATE)
	@Column(name = Columns.MONTH)
	public Date getMonth() {
		return month;
	}

	public void setMonth(Date month) {
		this.month = month;
	}
	

	@Column(name = Columns.AMOUNT)
	public Float getAmount() {
		return amount;
	}

	public void setAmount(Float amount) {
		this.amount = amount;
	}

	@JsonIgnore
	@ManyToOne(optional = true)
	@JoinColumn(name = BirdnotesConstants.Columns.USER_ID)
	public User getUser() {
		return user;
	}

	public void setUser(User user) {
		this.user = user;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = BirdnotesConstants.Columns.COMMISSION_ID, unique = false)
	public Commission getCommission() {
		return commission;
	}

	public void setCommission(Commission commission) {
		this.commission = commission;
	}

	@Enumerated(EnumType.STRING)
	@Column(name = Columns.STATUS)
	public UserValidationStatus getStatus() {
		return status;
	}

	public void setStatus(UserValidationStatus status) {
		this.status = status;
	}

	@OneToMany(mappedBy = "delegateCommission")
	public List<ValidationStatus> getValidationStatus() {
		return validationStatus;
	}

	public void setValidationStatus(List<ValidationStatus> validationStatus) {
		this.validationStatus = validationStatus;
	}
	
	
	
	
	

	
	
}