package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class NameValueDto implements Serializable, Comparable<NameValueDto> {

	private static final long serialVersionUID = 1L;
	private String name;
	private float count;

	public NameValueDto() {
	}

	public NameValueDto(String name, float count) {
		super();
		this.name = name;
		this.count = count;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public float getCount() {
		return count;
	}

	public void setCount(float count) {
		this.count = count;
	}

	@Override
	public int compareTo(NameValueDto o) {
		if(count == o.getCount()) {
			return 0;
		}
		else if(count < o.getCount()) {
			return -1;
		}else {
			return 1;
		}
		
	}
}
