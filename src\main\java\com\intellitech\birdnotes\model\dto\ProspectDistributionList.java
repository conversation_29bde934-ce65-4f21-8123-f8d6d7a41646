package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class ProspectDistributionList {

private List<ProspectDistribution> prospectCountBySector;

private List<ProspectDto> prospectList;

private Long prospectCount;

private String specialityIconsRootPath;





public ProspectDistributionList() {
	super();
	
}

public ProspectDistributionList(List<ProspectDistribution> prospectCountBySector, List<ProspectDto> prospectList) {
	super();
	this.prospectCountBySector = prospectCountBySector;
	this.prospectList = prospectList;
}

public List<ProspectDto> getProspectList() {
	return prospectList;
}

public void setProspectList(List<ProspectDto> prospectList) {
	this.prospectList = prospectList;
}

public List<ProspectDistribution> getProspectCountBySector() {
	return prospectCountBySector;
}

public void setProspectCountBySector(List<ProspectDistribution> prospectCountBySector) {
	this.prospectCountBySector = prospectCountBySector;
}

public Long getProspectCount() {
	return prospectCount;
}

public void setProspectCount(Long prospectCount) {
	this.prospectCount = prospectCount;
}

public String getSpecialityIconsRootPath() {
	return specialityIconsRootPath;
}

public void setSpecialityIconsRootPath(String specialityIconsRootPath) {
	this.specialityIconsRootPath = specialityIconsRootPath;
}



}
