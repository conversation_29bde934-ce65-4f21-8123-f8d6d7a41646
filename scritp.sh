#!/bin/bash


mkdir /opt/loki
#touch /opt/loki/loki-linux-amd64.zip
#curl --output /opt/loki/loki-linux-amd64.zip --url "https://github.com/grafana/loki/releases/download/v2.7.4/loki-linux-amd64.zip"\
wget -P /opt/loki/ "https://github.com/grafana/loki/releases/download/v2.7.4/loki-linux-amd64.zip" 
unzip -d /opt/loki /opt/loki/loki-linux-amd64.zip

#touch /opt/loki/promtail-linux-amd64.zip
wget -P /opt/loki/ "https://github.com/grafana/loki/releases/download/v2.7.4/promtail-linux-amd64.zip"

unzip -d /opt/loki /opt/loki/promtail-linux-amd64.zip

wget -P  /opt/loki/ "https://raw.githubusercontent.com/grafana/loki/v2.7.4/cmd/loki/loki-local-config.yaml"
wget -P  /opt/loki/ "https://raw.githubusercontent.com/grafana/loki/v2.7.4/clients/cmd/promtail/promtail-local-config.yaml"

sed -i 's/\/var\/log\/\*log/\/usr\/local\/tomcat\/logs\/catalina.*/g' /opt/loki/promtail-local-config.yaml


/opt/loki/promtail-linux-amd64 -config.file /opt/loki/promtail-local-config.yaml &
/opt/loki/loki-linux-amd64 -config.file /opt/loki/loki-local-config.yaml &
