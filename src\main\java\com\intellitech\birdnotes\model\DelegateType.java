package com.intellitech.birdnotes.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.intellitech.birdnotes.util.BirdnotesConstants.Columns;
import com.intellitech.birdnotes.util.BirdnotesConstants.Common;
import com.intellitech.birdnotes.util.BirdnotesConstants.Sequences;
import com.intellitech.birdnotes.util.BirdnotesConstants.Tables;
@Entity
@Table(name = Tables.DELEGATE_TYPE, schema = Common.PUBLIC_SCHEMA)

public class DelegateType implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	private Long id;
	private String name;
	
	
	@Id
	@SequenceGenerator(name = Sequences.DELEGATE_TYPE_SEQUENCE, sequenceName = Sequences.DELEGATE_TYPE_SEQUENCE, allocationSize = 1)
	@GeneratedValue(strategy = GenerationType.SEQUENCE, generator = Sequences.DELEGATE_TYPE_SEQUENCE)
	@Column(name = Columns.ID)

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	} 

	@Column(name = Columns.NAME)

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}


	
	public DelegateType() {
		super();

	}
}
