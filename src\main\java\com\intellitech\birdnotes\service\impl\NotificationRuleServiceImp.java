package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.Unmarshaller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.intellitech.birdnotes.enumeration.NotificationMethod;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Event;
import com.intellitech.birdnotes.model.EventList;
import com.intellitech.birdnotes.model.NotificationRule;
import com.intellitech.birdnotes.model.Role;
import com.intellitech.birdnotes.model.User;
import com.intellitech.birdnotes.model.dto.NotificationRuleDto;
import com.intellitech.birdnotes.model.request.NotificationRuleItem;
import com.intellitech.birdnotes.model.request.NotificationRuleRequest;
import com.intellitech.birdnotes.repository.NotificationRuleRepository;
import com.intellitech.birdnotes.repository.RoleRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.service.NotificationRuleService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;

@Service("notificationManagerService")
@Transactional
public class NotificationRuleServiceImp implements NotificationRuleService {

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	private UserRepository userRepository;
	private RoleRepository roleRepository;
	private NotificationRuleRepository notificationRuleRepository;

	@Autowired
	ResourceLoader resourceLoader;

	@Autowired
	public NotificationRuleServiceImp(UserRepository userRepository, RoleRepository roleRepository,
			NotificationRuleRepository notificationRuleRepository) {
		super();
		this.userRepository = userRepository;
		this.roleRepository = roleRepository;
		this.notificationRuleRepository = notificationRuleRepository;
	}

	private void setNotification(NotificationRuleItem notificationRuleItem, NotificationRule notificationRule) {
		if (notificationRuleItem.getNotificationMethod() != null) {
			if (BirdnotesConstants.NotificationMethod.EMAIL.equals(notificationRuleItem.getNotificationMethod())) {
				notificationRule.setNotificationMethod(NotificationMethod.EMAIL);
			} else if (BirdnotesConstants.NotificationMethod.PUSH
					.equals(notificationRuleItem.getNotificationMethod())) {
				notificationRule.setNotificationMethod(NotificationMethod.PUSH);
			} else if (BirdnotesConstants.NotificationMethod.APP_NOTIF
					.equals(notificationRuleItem.getNotificationMethod())) {
				notificationRule.setNotificationMethod(NotificationMethod.APP_NOTIF);
			} else {
				notificationRule.setNotificationMethod(NotificationMethod.SMS);
			}
		}
	}

	@Override
	public void deleteByEventType(String eventType) {
		notificationRuleRepository.deleteByEventType(eventType);

	}

	@Override
	public boolean saveNotification(NotificationRuleRequest notificationRuleRequest) throws BirdnotesException {

		if (notificationRuleRequest.getSelectedEventType() == null
				|| "".equals(notificationRuleRequest.getSelectedEventType())) {
			throw new BirdnotesException(Exceptions.EVENT_IS_EMPTY);
		}
		if (notificationRuleRequest.getNotificationsRule().size() == 0) {
			throw new BirdnotesException(Exceptions.NOTIFICATIONS_IS_EMPTY);
		}

		deleteByEventType(notificationRuleRequest.getSelectedEventType());

		for (NotificationRuleItem notificationRuleItem : notificationRuleRequest.getNotificationsRule()) {
			NotificationRule notificationRule = new NotificationRule();

			notificationRule.setEventType(notificationRuleRequest.getSelectedEventType());
			notificationRule.setNotificationReceiver(notificationRuleItem.getNotificationReceiver());
			setNotification(notificationRuleItem, notificationRule);
			if (notificationRuleItem.getIdRole() != null) {
				Role role = roleRepository.findOne(notificationRuleItem.getIdRole());
				notificationRule.setRole(role);
			}
			if (notificationRuleItem.getIdUser() != null) {
				User user = userRepository.findOne(notificationRuleItem.getIdUser());
				notificationRule.setUser(user);
			}
			notificationRuleRepository.save(notificationRule);
		}

		return true;
	}

	@Override
	public List<Event> getEventXml() throws IOException {
		try {
			Resource resource = resourceLoader.getResource("classpath:events.xml");

			InputStream input = resource.getInputStream();

			File file = resource.getFile();
			JAXBContext jaxbContext = JAXBContext.newInstance(EventList.class);
			Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
			EventList eventsList = (EventList) jaxbUnmarshaller.unmarshal(file);

			return eventsList.getEvent();

		} catch (Exception e) {
			log.error("error in getXml ", e);
			return new ArrayList<>();
		}

	}

	@Override
	public List<NotificationRuleDto> findNotificationByEvent(String eventType) {

		List<NotificationRule> notificationsRule = notificationRuleRepository.findNotificationByEvent(eventType);
		List<NotificationRuleDto> notificationsRuleDtos = new ArrayList<>();
		if (notificationsRule != null && !notificationsRule.isEmpty()) {
			for (NotificationRule notificationRule : notificationsRule) {
				NotificationRuleDto notificationRuleDto = new NotificationRuleDto();
				notificationRuleDto.setNotificationReceiver(notificationRule.getNotificationReceiver());
				if (notificationRule.getNotificationMethod() == NotificationMethod.PUSH) {

					notificationRuleDto.setNotificationMethod("PUSH");
				}
				if (notificationRule.getNotificationMethod() == NotificationMethod.EMAIL) {

					notificationRuleDto.setNotificationMethod("EMAIL");
				}
				if (notificationRule.getNotificationMethod() == NotificationMethod.APP_NOTIF) {

					notificationRuleDto.setNotificationMethod("APP_NOTIF");
				}
				if (notificationRule.getNotificationMethod() == NotificationMethod.SMS) {

					notificationRuleDto.setNotificationMethod("SMS");
				}

				if (notificationRule.getRole() != null) {
					Role role = roleRepository.findOne(notificationRule.getRole().getId());
					notificationRuleDto.setIdRole(role.getId());
				}
				if (notificationRule.getUser() != null) {
					User user = userRepository.findOne(notificationRule.getUser().getId());
					notificationRuleDto.setIdUser(user.getId());

				}
				notificationsRuleDtos.add(notificationRuleDto);
			}
		}
		return notificationsRuleDtos;
	}

	@Override
	public Set<User> getSuperiorsByRole(Long id, String roleName) {
		User user = userRepository.findOne(id);
		Set<User> superiorsHavingRole = new HashSet<>();
		Set<User> userSuperiors = user.getSuperiors();
		if (user != null) {
			for (User userSuperior : userSuperiors) {
				Set<Role> roles = userSuperior.getRoles();
				for (Role role : roles) {

					if (role.getName().equals(roleName)) {

						superiorsHavingRole.add(userSuperior);

					}

				}
			}
		}

		return superiorsHavingRole;

	}

}
