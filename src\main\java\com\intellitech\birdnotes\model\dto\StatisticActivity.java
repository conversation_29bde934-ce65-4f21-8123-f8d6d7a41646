package com.intellitech.birdnotes.model.dto;

import java.io.Serializable;

public class StatisticActivity implements Serializable {

	private static final long serialVersionUID = 1L;
	
	private Long userId;
	private String name;
	private Long hourNumber;
	

	public StatisticActivity() {
		super();
	}

	public StatisticActivity(String name, Long hourNumber) {
		this.name = name;
		this.hourNumber = hourNumber;
	}
	
	public StatisticActivity(Long userId, Long hourNumber) {
		this.userId = userId;
		this.hourNumber = hourNumber;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Long getHourNumber() {
		return hourNumber;
	}

	public void setHourNumber(Long hourNumber) {
		this.hourNumber = hourNumber;
	}

	public Long getUserId() {
		return userId;
	}

	public void setUserId(Long userId) {
		this.userId = userId;
	}

	

}
