package com.intellitech.birdnotes.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import com.intellitech.birdnotes.model.OpportunityNote;

public interface OpportunityNoteRepository extends JpaRepository<OpportunityNote, Long> {

	OpportunityNote findByName(String name);

	OpportunityNote findById(Long id);


	@Query("SELECT p from OpportunityNote p WHERE p.user.id =:id")
	List<OpportunityNote> findOpportunityNoteByUser(@Param("id") Long id);

	
	
	@Query("Select a from OpportunityNote a ORDER BY a.id DESC")
	List<OpportunityNote> findAll();

	@Modifying
	@Query("DELETE FROM OpportunityNote where id=:id")
	void deleteById(@Param("id") Long id);
	
	@Modifying
	@Query("DELETE OpportunityNote p WHERE p.id in (:opportunityNoteReportIds)")
	void deleteByIds(@Param("opportunityNoteReportIds") List<Long> opportunityNoteReportIds);
    

}
