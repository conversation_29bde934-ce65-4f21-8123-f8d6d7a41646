package com.intellitech.birdnotes.controller;

import java.util.Collections;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.intellitech.birdnotes.model.dto.IssueDto;
import com.intellitech.birdnotes.model.request.IssueRequest;
import com.intellitech.birdnotes.service.IssueService;
import com.intellitech.birdnotes.service.UserService;

@RestController
@RequestMapping("/issues")
public class IssuesController {
	private static final Logger LOG = LoggerFactory.getLogger(IssuesController.class);

	@Autowired
	private IssueService issueService;
	@Autowired
	UserService userService;

	@RequestMapping(value = "issueList", method = RequestMethod.POST)
	public List<IssueDto> findAll(@RequestBody IssueRequest issueRequest) {
		try {
			if (userService.checkHasPermission("ANOMALY_VIEW")) {
				List<IssueDto> Response = issueService.findAll(issueRequest);

				return Response;
			} else {
				return null;
			}

		} catch (Exception e) {
			LOG.error("An exception occurred ", e);
			return Collections.emptyList();
		}
	}

}
