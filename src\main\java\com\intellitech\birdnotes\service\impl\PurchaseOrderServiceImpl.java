package com.intellitech.birdnotes.service.impl;

import java.io.File;

import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.collections.map.HashedMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.multipart.MultipartFile;

import com.intellitech.birdnotes.data.dto.PurchaseOrderDataDto;
import com.intellitech.birdnotes.data.dto.PurchaseOrderItemDto;
import com.intellitech.birdnotes.enumeration.EmailType;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.Prospect;
import com.intellitech.birdnotes.model.ProspectType;
import com.intellitech.birdnotes.model.PurchaseOrder;
import com.intellitech.birdnotes.model.Speciality;
import com.intellitech.birdnotes.model.Visit;
import com.intellitech.birdnotes.model.Wholesaler;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToPurchaseOrder;
import com.intellitech.birdnotes.model.convertor.ConvertProspectToDto;
import com.intellitech.birdnotes.model.convertor.ConvertSpecialityToDto;
import com.intellitech.birdnotes.model.convertor.DelegateToDtoConvertor;
import com.intellitech.birdnotes.model.convertor.WholesalerToDtoConvertor;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.ProspectDto;
import com.intellitech.birdnotes.model.dto.ProspectTypeDto;
import com.intellitech.birdnotes.model.dto.PurchaseOrderDto;
import com.intellitech.birdnotes.model.dto.SpecialityDto;
import com.intellitech.birdnotes.model.dto.SpecialityRequestDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.NotificationRuleRepository;
import com.intellitech.birdnotes.repository.ProspectTypeRepository;
import com.intellitech.birdnotes.repository.PurchaseOrderRepository;
import com.intellitech.birdnotes.repository.SpecialityRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.VisitRepository;
import com.intellitech.birdnotes.repository.VisitsProductsRepository;
import com.intellitech.birdnotes.repository.WholesalerRepository;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ProspectService;
import com.intellitech.birdnotes.service.ProspectTypeService;
import com.intellitech.birdnotes.service.PurchaseOrderService;
import com.intellitech.birdnotes.service.SpecialityService;
import com.intellitech.birdnotes.service.StorageService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.BirdnotesUtils;

import net.sf.jasperreports.engine.JRDataSource;
import net.sf.jasperreports.engine.JREmptyDataSource;
import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Service("purchaseOrderService")
@Transactional
public class PurchaseOrderServiceImpl implements PurchaseOrderService {
	
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
	
	@Autowired
	private PurchaseOrderRepository purchaseOrderRepository;
	@Autowired
	private VisitsProductsRepository visitProductRepository;
	@Autowired
	private ConfigurationRepository configurationRepository;
	@Autowired
	private ConfigurationService configurationService;
	@Autowired
	private JavaMailSender javaMailSender;
	@Autowired
	private ConvertDtoToPurchaseOrder convertDtoToPurchaseOrder;
	@Autowired
	private UserRepository userRepository;
	@Autowired
	private PurchaseOrderService purchaseOrderService;
	@Autowired
	NotificationRuleRepository notificationRuleRepository;

	@Autowired
	private VisitRepository visitRepository;
	@Autowired
	private WholesalerRepository wholesalerRepository;
	@Autowired
	private ProspectService prospectService;
	@Autowired
	private DelegateToDtoConvertor delegateToDtoConvertor;
	@Autowired
	private ConvertProspectToDto convertProspectToDto;
	@Autowired
	private WholesalerToDtoConvertor wholesalerToDtoConvertor;
	
	@Value("${purchaseOrderPath}")
	private String purchaseOrderPath;
	
	@Value("${logoPath}")
	private String logoPath;

	@Value("${uploadUrl}")
	private String uploadUrl;

	@Value("${uploadPath}")
	private String uploadPath;
	
	@Value("${sendCancellationMailSubject}")
	private String sendCancellationMailSubject;
	
	@Value("${sendCancellationMailHtmlBody}")
	private String sendCancellationMailHtmlBody;
	
	@Value("${sendWholesalerMailSubject}")
	private String sendWholesalerMailSubject;

	@Value("${sendWholesalerMailHtmlBodyWithAttachment}")
	private String sendWholesalerMailHtmlBodyWithAttachment;

	@Autowired
	private MessageSource messageSource;
	
	@Value("${generatedDocumentPath}")
	private String generatedDocumentPath;
	
	@Value("${generatedDoPath}")
	private String generatedDoPath;
	
	private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderServiceImpl.class);
	

	@Override
	public List<PurchaseOrderDto> getAllByUser(Long userId) throws BirdnotesException {

		List<PurchaseOrderDto> back = new ArrayList<>();

		List<PurchaseOrder> purchaseOrderList;

		purchaseOrderList = purchaseOrderRepository.findByUser(userId);


		if (purchaseOrderList != null && !purchaseOrderList.isEmpty()) {

			for (PurchaseOrder purchaseOrder : purchaseOrderList) {
				PurchaseOrderDto purchaseOrderDto = new PurchaseOrderDto();
				purchaseOrderDto.setIdentifier(purchaseOrder.getIdentifier());
				purchaseOrderDto.setPlacementMethod(purchaseOrder.getPlacementMethod());
				purchaseOrderDto.setVisitId(purchaseOrder.getVisit().getIdentifier());
				purchaseOrderDto.setWholesalerId(purchaseOrder.getWholesaler().getIdentifier());
				//purchaseOrderDto.setAttachmentBase64(purchaseOrder.getAttachmentBase64());
				purchaseOrderDto.setStatus(purchaseOrder.getStatus().toString());
				purchaseOrderDto.setAttachmentName(purchaseOrder.getAttachmentName());
				back.add(purchaseOrderDto);

			}

		}

		return back;

	}
	
	/*@Override
	public List<PurchaseOrderDataDto>findPurchaseOrderByDateAndUser	(Date startDate, Date endDate, Long userId, Long wholesalerId){
		
	}*/
	
	
	
	@Override
	public void generatePurchaseOrder(String destFileName, Long purchaseOrderId, String type) throws FileNotFoundException, JRException{
		 
		// get getJasperReport
		 File template = ResourceUtils.getFile("classpath:jasper/purchase_order_v2.jrxml");
		 JasperReport jasperReport = JasperCompileManager.compileReport(template.getAbsolutePath());
		 // get data source
		 Collection<PurchaseOrderItemDto> purchaseOrderItems = visitProductRepository.findPurchaseOrderById(purchaseOrderId);
		 JRBeanCollectionDataSource jdParameters = new JRBeanCollectionDataSource(purchaseOrderItems);
		 // set parameters
		 Map<String, Object> parameters= new HashMap<String, Object>();
	     parameters.put("purchaseOrderItem", jdParameters);
	     
	     List<Map<String, Object>> dataSource = getPurchaseOrderData(purchaseOrderId, type);
	     
	     JRBeanCollectionDataSource jdDataSource = new JRBeanCollectionDataSource(dataSource);
	     LOG.info("generating jasper file..." );

	     JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, 
	                parameters, 
	                jdDataSource);
	     JasperExportManager.exportReportToPdfFile(jasperPrint, destFileName);
	        
	     LOG.info("terminate generating jasper file" );
	       
	}
	
	
	public List<Map<String, Object>> getPurchaseOrderData(Long purchaseOrderId, String title) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		PurchaseOrderDataDto purchaseOrderData = purchaseOrderRepository.findPurchaseOrderData(purchaseOrderId);
		Configuration config = configurationRepository.findOne(1);
	
		Map<String, Object> item = new HashMap<String, Object>();
		String pathLogo = uploadPath +  logoPath + File.separator + config.getLogo();
		item.put("name", config.getName());
		item.put("logoUrl", pathLogo);
		item.put("id", purchaseOrderData.getId());
		item.put("wholesalerName", purchaseOrderData.getWholesalerName());
		item.put("wholesalerAddress", purchaseOrderData.getWholesalerAddress());
		item.put("wholesalerEmail", purchaseOrderData.getWholesalerEmail());
		item.put("prospectName", purchaseOrderData.getProspectName());
		item.put("prospectAddress", purchaseOrderData.getProspectAddress());
		item.put("delegateName", purchaseOrderData.getDelegateName());
		item.put("date", purchaseOrderData.getDate());
		item.put("title", title);
		

		result.add(item);

		return result;
	}


/*	private JasperReport getJasperReport() throws FileNotFoundException, JRException {
        File template = ResourceUtils.getFile("classpath:purchase_order.jrxml");
        return JasperCompileManager.compileReport(template.getAbsolutePath());
    }
    private Map<String, Object> getParameters(){
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("CollectionBeanParam", itemsJRBean);
        return parameters;
    }

    private JRDataSource getDataSource(Long purchaseOrderId){
    	
    	List<PurchaseOrderItemDto> purchaseOrderItems = visitProductRepository.findPurchaseOrderById(purchaseOrderId);

       

        return new JRBeanCollectionDataSource(purchaseOrderItems);
    }
*/
	
	@Override
	public void sendPurchaseOrderMail(PurchaseOrderDto purchaseOrderDto) throws BirdnotesException {
	    ConfigurationDto config = configurationService.findConfiguration();

	    String attachmentPath = uploadPath + purchaseOrderPath + File.separator + purchaseOrderDto.getId();
	    String poPath = uploadPath + generatedDocumentPath + File.separator + purchaseOrderDto.getId();
	    String doPath = uploadPath + generatedDoPath + File.separator + purchaseOrderDto.getId();

	    purchaseOrderDto.setEmailType(EmailType.VALIDATION.toString());
	    Visit visit = visitRepository.findById(purchaseOrderDto.getVisitId());
	ProspectDto wholesaler = prospectService.findProspectById(purchaseOrderDto.getWholesalerId());
	purchaseOrderDto.setDelegate(delegateToDtoConvertor.convert(visit.getDelegate()));
	purchaseOrderDto.setProspect(convertProspectToDto.convert(visit.getProspect()));
	purchaseOrderDto.setWholesaler(wholesaler);
	purchaseOrderDto.setGenerateDo(false);
	purchaseOrderDto.setGeneratePo(true);
	    Locale locale = new Locale(config.getLanguage());
	    
	    String sendWholesalerMailHtmlBodyWithoutAttachment = messageSource.getMessage(
	            "sendWholesalerMailHtmlBodyWithoutAttachment", null, locale);

	    String sendWholesalerMailSubject = messageSource.getMessage(
	            "sendWholesalerMailSubject", null, locale); 
	    
	    String sendWholesalerMailHtmlBodyWithAttachment = messageSource.getMessage(
	            "sendWholesalerMailHtmlBodyWithAttachment", null, locale); 
	    
	    String sendCancellationMailSubject = messageSource.getMessage(
	            "sendCancellationMailSubject", null, locale); 
	    
	    String sendCancellationMailHtmlBody = messageSource.getMessage(
	            "sendCancellationMailHtmlBody", null, locale); 

	    BirdnotesUtils.sendMailPurchaseOrder(purchaseOrderDto, config, 
	            userRepository, purchaseOrderService, purchaseOrderRepository, 
	            convertDtoToPurchaseOrder, uploadUrl, logoPath, sendWholesalerMailSubject, 
	            sendCancellationMailSubject, sendCancellationMailHtmlBody, 
	            sendWholesalerMailHtmlBodyWithAttachment, sendWholesalerMailHtmlBodyWithoutAttachment, 
	            purchaseOrderPath, generatedDocumentPath, uploadPath, attachmentPath, poPath, doPath, 
	            javaMailSender, notificationRuleRepository);
	}

	
private String createFolder(Long id, String path, String uploadPath) throws BirdnotesException {
		
		String pathUpload = uploadPath + path + File.separator + id;
		File dirName = new File(pathUpload);
		// if the directory does not exist, create it
		if (!dirName.exists()) {
			try {
				dirName.mkdir();
			} catch (SecurityException se) {
				throw new BirdnotesException("Error in creation of folder : " + se);
			}
		}
		return pathUpload;
	}


	
}
