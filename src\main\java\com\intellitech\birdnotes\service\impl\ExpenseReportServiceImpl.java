package com.intellitech.birdnotes.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.tomcat.util.http.fileupload.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;

import com.intellitech.birdnotes.enumeration.UserValidationStatus;
import com.intellitech.birdnotes.exceptions.BirdnotesException;
import com.intellitech.birdnotes.model.Configuration;
import com.intellitech.birdnotes.model.Delegate;
import com.intellitech.birdnotes.model.ExpenseReport;
import com.intellitech.birdnotes.model.ExpenseType;
import com.intellitech.birdnotes.model.ValidationStatus;
import com.intellitech.birdnotes.model.convertor.ConvertDtoToExpenseReport;
import com.intellitech.birdnotes.model.convertor.ConvertExpenseReportToDto;
import com.intellitech.birdnotes.model.dto.ConfigurationDto;
import com.intellitech.birdnotes.model.dto.ExpenseReportDto;
import com.intellitech.birdnotes.model.dto.LabelValueDto;
import com.intellitech.birdnotes.model.dto.ValidationStatusDto;
import com.intellitech.birdnotes.repository.ConfigurationRepository;
import com.intellitech.birdnotes.repository.DelegateRepository;
import com.intellitech.birdnotes.repository.ExpenseReportRepository;
import com.intellitech.birdnotes.repository.ExpenseTypeRepository;
import com.intellitech.birdnotes.repository.UserRepository;
import com.intellitech.birdnotes.repository.ValidationStatusRepository;
import com.intellitech.birdnotes.security.BirdnotesUser;
import com.intellitech.birdnotes.security.CurrentUser;
import com.intellitech.birdnotes.service.ConfigurationService;
import com.intellitech.birdnotes.service.ExpenseReportService;
import com.intellitech.birdnotes.service.NotificationService;
import com.intellitech.birdnotes.service.UserService;
import com.intellitech.birdnotes.service.ValidationStepService;
import com.intellitech.birdnotes.util.BirdnotesConstants;
import com.intellitech.birdnotes.util.BirdnotesConstants.Exceptions;
import com.intellitech.birdnotes.util.BirdnotesUtils;
import com.intellitech.birdnotes.util.NotificationMessageBuilder;

import net.sf.jasperreports.engine.JRException;
import net.sf.jasperreports.engine.JasperCompileManager;
import net.sf.jasperreports.engine.JasperExportManager;
import net.sf.jasperreports.engine.JasperFillManager;
import net.sf.jasperreports.engine.JasperPrint;
import net.sf.jasperreports.engine.JasperReport;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;

@Service("noteFraisService")

@Transactional

public class ExpenseReportServiceImpl implements ExpenseReportService {

	private ExpenseReportRepository expenseReportRepository;

	private ExpenseTypeRepository expenseTypeRepository;

	private ConvertExpenseReportToDto convertNoteFraisToDto;

	private ConvertDtoToExpenseReport convertDtoToNoteFrais;

	private CurrentUser currentUser;

	private DelegateRepository delegateRepository;

	private ValidationStepService validationStepService;

	private ValidationStatusRepository validationStatusRepository;

	private NotificationMessageBuilder notificationMessageBuilder;

	private NotificationService notificationService;

	private UserService userService;
	private ConfigurationRepository configurationRepository;
	private SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy");
	private ConfigurationService configurationService;

	@Autowired
	public void setUserRepository(UserService userService) {
		this.userService = userService;

	}
	
	@Autowired
	private MessageSource messageSource;

	@Value("${uploadPath}")

	private String uploadPath;

	@Value("${expenseReportPath}")

	private String expenseReportPath;

	@Value("${logoPath}")
	private String logoPath;

	Logger log = LoggerFactory.getLogger(this.getClass().getName());

	@Autowired

	public ExpenseReportServiceImpl(ExpenseReportRepository expenseReportRepository,

			ExpenseTypeRepository expenseTypeRepository, ConvertExpenseReportToDto convertNoteFraisToDto,

			ConvertDtoToExpenseReport convertDtoToNoteFrais, CurrentUser currentUser, UserRepository userRepository,

			ValidationStepService validationStepService, ValidationStatusRepository validationStatusRepository,
			NotificationService notificationService, NotificationMessageBuilder notificationMessageBuilder,
			DelegateRepository delegateRepository, ConfigurationRepository configurationRepository) {

		this.expenseReportRepository = expenseReportRepository;

		this.expenseTypeRepository = expenseTypeRepository;

		this.convertNoteFraisToDto = convertNoteFraisToDto;

		this.convertDtoToNoteFrais = convertDtoToNoteFrais;

		this.currentUser = currentUser;

		this.delegateRepository = delegateRepository;

		this.validationStepService = validationStepService;

		this.validationStatusRepository = validationStatusRepository;

		this.notificationMessageBuilder = notificationMessageBuilder;

		this.notificationService = notificationService;
		this.configurationRepository = configurationRepository;

	}
	

	@Override
	public ExpenseReport saveExpenseReport(ExpenseReportDto expenseReportDto) throws BirdnotesException {
		ExpenseReport expenseReport = null;
		if (expenseReportDto.getId() != null) {
			expenseReport = expenseReportRepository.findOne(expenseReportDto.getId());
		}
		if (expenseReport == null) {
			expenseReport = new ExpenseReport();
		}
		Delegate delegate = null;
		if (expenseReportDto.getDelegateId() != null) {

			delegate = delegateRepository.findOne(expenseReportDto.getDelegateId());
			expenseReport.setDelegate(delegate);
		}
		if (expenseReportDto.getExpenseTypeId() != null) {
			ExpenseType expenseType = expenseTypeRepository.findOne(expenseReportDto.getExpenseTypeId());
			expenseReport.setExpenseType(expenseType);
		}
		expenseReport.setDescription(expenseReportDto.getDescription());
		expenseReport.setMontant(expenseReportDto.getMontant());
		expenseReport.setDate(expenseReportDto.getDate());
		expenseReport.setIdentifier(new Date().getTime());
		expenseReport.setStatus(UserValidationStatus.ACCEPTED);
		ExpenseReport savedExpenseReport = expenseReportRepository.save(expenseReport);
		return savedExpenseReport;
	}

	@Override
	public void acceptValidationStep(long planningValidationId) throws BirdnotesException {

		ValidationStatus userValidationStatus = validationStatusRepository.findOne(planningValidationId);
		List<ValidationStatus> allValidationStatus = validationStatusRepository
				.findByNoteFraisOrderByRankAsc(userValidationStatus.getNoteFrais());

		boolean isWorkflowFinished = this.validationStepService.accept(allValidationStatus, planningValidationId,
				userValidationStatus.getNoteFrais().getId());

		if (isWorkflowFinished) {
			String noteFraisValidationNotificationMessage =  userService.getTranslatedLabel("noteFraisValidationNotificationMessage");
			userValidationStatus.getNoteFrais().setStatus(UserValidationStatus.ACCEPTED);
			expenseReportRepository.save(userValidationStatus.getNoteFrais());
			notificationService.sendSingleNotification(userValidationStatus.getNoteFrais().getDelegate().getUser(),
					null, noteFraisValidationNotificationMessage);

		} else {

			userValidationStatus.getNoteFrais().setStatus(UserValidationStatus.WAITING_FOR_VALIDATION);
			expenseReportRepository.save(userValidationStatus.getNoteFrais());
		}

	}

	@Override
	public void refuseValidationStep(long planningValidationId) throws BirdnotesException {
		String noteFraisRefusNotificationMessage =  userService.getTranslatedLabel("noteFraisRefusNotificationMessage");
		this.validationStepService.refuse(planningValidationId);
		ValidationStatus userValidationStatus = validationStatusRepository.findOne(planningValidationId);
		userValidationStatus.getNoteFrais().setStatus(UserValidationStatus.REFUSED);
		expenseReportRepository.save(userValidationStatus.getNoteFrais());
		notificationService.sendSingleNotification(userValidationStatus.getNoteFrais().getDelegate().getUser(), null,
				noteFraisRefusNotificationMessage);

	}

	@Override
	public List<ExpenseReportDto> findAll() throws BirdnotesException {
		List<ExpenseReportDto> result = new ArrayList<>();
		List<ExpenseReport> listOfNoteFrais = expenseReportRepository.findAll();
		if (listOfNoteFrais != null && !listOfNoteFrais.isEmpty()) {
			for (ExpenseReport noteFrais : listOfNoteFrais) {
				ExpenseReportDto expenseReportDto = new ExpenseReportDto();
				expenseReportDto.setId(noteFrais.getId());
				expenseReportDto.setDate(noteFrais.getDate());
				expenseReportDto.setMontant(noteFrais.getMontant());
				expenseReportDto.setExpenseTypeId(noteFrais.getExpenseType().getId());
				expenseReportDto.setDescription(noteFrais.getDescription());
				result.add(expenseReportDto);
			}

		}
		return result;
	}

	@Override

	public List<ExpenseReportDto> findAllByUser(Long idUser, List<Long> expenseReportList) throws BirdnotesException {

		List<ExpenseReportDto> back = new ArrayList<>();

		List<ExpenseReport> listOfNoteFrais;

		if (expenseReportList == null || expenseReportList.isEmpty()) {

			listOfNoteFrais = expenseReportRepository.findByUser(idUser);

		} else {

			listOfNoteFrais = expenseReportRepository.findByUser(idUser, expenseReportList);

		}

		if (listOfNoteFrais != null && !listOfNoteFrais.isEmpty()) {

			for (ExpenseReport noteFrais : listOfNoteFrais) {

				back.add(convertNoteFraisToDto.convert(noteFrais));

			}

		}

		return back;

	}

	@Override

	public ExpenseReport add(ExpenseReportDto noteFraisDto) throws BirdnotesException {

		ExpenseReport noteFrais = convertDtoToNoteFrais.convert(noteFraisDto);

		validateNoteFrais(noteFrais);

		BirdnotesUser birdnotesUser = currentUser.getBirdnotesUser();

		if (birdnotesUser == null || birdnotesUser.getUserDto() == null || birdnotesUser.getUserDto().getId() == null) {

			throw new BirdnotesException(Exceptions.SESSION_EXPIRE);

		}

		noteFrais.setDelegate(delegateRepository.findOne(birdnotesUser.getUserDto().getId()));

		return expenseReportRepository.save(noteFrais);

	}

	private void validateNoteFrais(ExpenseReport noteFrais) throws BirdnotesException {

		ExpenseType typeNoteFrais = expenseTypeRepository.findOne(noteFrais.getExpenseType().getId());

		if (typeNoteFrais == null) {

			throw new BirdnotesException(Exceptions.TYPE_IS_NULL);

		}

		noteFrais.setExpenseType(typeNoteFrais);

	}

	@Override

	public void delete(Long noteFraisId) throws BirdnotesException {

		try {

			validationStatusRepository.deleteById(noteFraisId);

			expenseReportRepository.deleteById(noteFraisId);

			String pathToFolder = uploadPath + expenseReportPath + File.separator + noteFraisId;

			File fileName = new File(pathToFolder);

			FileUtils.deleteDirectory(fileName);


		} catch (Exception e) {

			log.error("error when delete note frais", e);

			throw new BirdnotesException(Exceptions.NOTE_FRAIS_COULD_NOT_DELETED);

		}

	}

	@Override

	public void updateNoteFrais(ExpenseReportDto noteFraisDto) throws BirdnotesException {

		if (noteFraisDto == null || noteFraisDto.getId() == null) {

			throw new BirdnotesException(Exceptions.NOTE_FRAIS_DTO_ID_NULL);

		}

		ExpenseReport noteFraisToUpdate = expenseReportRepository.findOne(noteFraisDto.getId());

		if (noteFraisToUpdate == null) {

			throw new BirdnotesException(BirdnotesConstants.Exceptions.NOTEFRAIS_TO_UPDATE_ALREADY_DELETED);

		}

		if (noteFraisDto.getExpenseTypeDto() == null) {

			throw new BirdnotesException(Exceptions.TYPE_IS_NULL);

		}

		noteFraisToUpdate.setDate(noteFraisDto.getDate());

		noteFraisToUpdate.setDescription(noteFraisDto.getDescription());

		noteFraisToUpdate.setMontant(noteFraisDto.getMontant());

		noteFraisToUpdate.setPieceJointe(noteFraisDto.getPieceJointe());

		expenseReportRepository.save(noteFraisToUpdate);

	}

	@Override

	public List<ExpenseReportDto> getAllByUser(Long userId) throws BirdnotesException {

		List<ExpenseReportDto> back = new ArrayList<>();

		List<ExpenseReport> listOfNoteFrais;

		listOfNoteFrais = expenseReportRepository.findByUser(userId);

		if (listOfNoteFrais != null && !listOfNoteFrais.isEmpty()) {

			for (ExpenseReport noteFrais : listOfNoteFrais) {

				back.add(convertNoteFraisToDto.convert(noteFrais));

			}

		}

		return back;

	}

	@Override

	public List<LabelValueDto> getExpensesReportByDelegate(Date startDate, Date endDate) {

		return expenseReportRepository.getExpensesReportByDelegate(startDate, endDate);

	}

	@Override

	public List<LabelValueDto> getExpensesReportByType(Date startDate, Date endDate) {

		return expenseReportRepository.getExpensesReportByType(startDate, endDate);

	}

	@Override

	public List<ExpenseReportDto> findByUser(Long userId) throws BirdnotesException {

		List<ExpenseReportDto> noteFraisDto = new ArrayList<>();

		List<ExpenseReport> noteFraisList;

		noteFraisList = expenseReportRepository.findByUser(userId);

		if (noteFraisList != null && !noteFraisList.isEmpty()) {

			for (ExpenseReport noteFrais : noteFraisList) {

				noteFraisDto.add(convertNoteFraisToDto.convert(noteFrais));

			}

		}

		return noteFraisDto;

	}

////// à utiliser
	@Override

	public List<ExpenseReportDto> findByDate(Date firstDate, Date lastDate, Long userId) throws BirdnotesException {

		List<ExpenseReportDto> noteFraisDtos = new ArrayList<>();

		List<ExpenseReport> noteFraisList;

	    List<Long> subUsers = new ArrayList<>();
	    if (userId != null && userId != 0) {
	        subUsers.add(userId);
	    } else {
	        subUsers = userService.getSubDelegatesIds().stream()
	            .filter(Objects::nonNull)
	            .collect(Collectors.toList());
	    }

		noteFraisList = expenseReportRepository.findByDate(firstDate, lastDate, subUsers);

		if (noteFraisList != null && !noteFraisList.isEmpty()) {

			for (ExpenseReport noteFrais : noteFraisList) {

				List<ValidationStatusDto> listValidationStatusDto = validationStepService.findByNoteFrais(noteFrais);

				ExpenseReportDto noteFraisDto = convertNoteFraisToDto.convert(noteFrais);

				noteFraisDto.setValidationStatusDto(listValidationStatusDto);

				noteFraisDtos.add(noteFraisDto);

			}

		}

		return noteFraisDtos;

	}

	@Override

	public ExpenseReportDto findNoteFraisById(Long id) throws BirdnotesException {

		ExpenseReport noteFrais = expenseReportRepository.findById(id);

		ExpenseReportDto noteFraisDto = convertNoteFraisToDto.convert(noteFrais);

		return noteFraisDto;

	}

	@Override
	public List<ExpenseReportDto> findExpenseValidationByUser(List<Long> expenseWaitingValidation, Long UserId)
			throws BirdnotesException {

		List<ExpenseReportDto> expenseDtos = new ArrayList<>();
		if (expenseWaitingValidation != null && expenseWaitingValidation.size() > 0) {
			List<ExpenseReport> expenses = expenseReportRepository.findExpenseValidationByUser(expenseWaitingValidation,
					UserId);
			if (expenses != null && !expenses.isEmpty()) {
				for (ExpenseReport expense : expenses) {
					ExpenseReportDto expenseDto = new ExpenseReportDto();
					expenseDto.setStatus(expense.getStatus().toString());
					expenseDto.setDescription(expense.getDescription());
					expenseDto.setDelegateName(
							expense.getDelegate().getFirstName() + ' ' + expense.getDelegate().getLastName());
					expenseDto.setDate(expense.getDate());
					expenseDto.setId(expense.getId());
					expenseDto.setIdentifier(expense.getIdentifier());
					expenseDtos.add(expenseDto);
				}
			}
		}

		return expenseDtos;
	}

	public List<Map<String, Object>> getExpenseReportData(Date startDate, Date endDate, String delegateFullName) {
		List<Map<String, Object>> result = new ArrayList<Map<String, Object>>();
		Configuration config = configurationRepository.findOne(1);

		Map<String, Object> item = new HashMap<String, Object>();
		String pathLogo = uploadPath + logoPath + File.separator + config.getLogo();
		item.put("name", config.getName());
		item.put("logoUrl", pathLogo);
		item.put("date", format.format(new Date()));

		item.put("delegateName", delegateFullName);
		item.put("startDate", format.format(startDate));
		item.put("endDate", format.format(endDate));

		result.add(item);
		return result;
	}

	@Override
	public void generateExpenseReport(String fileName, Date startDate, Date endDate, Long userId)
			throws FileNotFoundException, JRException, BirdnotesException {
		Delegate delegate = delegateRepository.findById(userId);

		// get getJasperReport
		File template = ResourceUtils.getFile("classpath:jasper/expense_report.jrxml");
		JasperReport jasperReport = JasperCompileManager.compileReport(template.getAbsolutePath());
		// get data source
		Collection<ExpenseReportDto> expenseReportItem = findByDate(startDate, endDate, delegate.getId());
		JRBeanCollectionDataSource jdParameters = new JRBeanCollectionDataSource(expenseReportItem);
		// set parameters
		Map<String, Object> parameters = new HashMap<String, Object>();
		parameters.put("ExpenseReportItem", jdParameters);

		List<Map<String, Object>> dataSource = getExpenseReportData(startDate, endDate,
				delegate.getFirstName() + " " + delegate.getLastName());

		JRBeanCollectionDataSource jdDataSource = new JRBeanCollectionDataSource(dataSource);
		log.info("generating jasper file for expense report...");

		JasperPrint jasperPrint = JasperFillManager.fillReport(jasperReport, parameters, jdDataSource);
		JasperExportManager.exportReportToPdfFile(jasperPrint, fileName);

		log.info("terminate generating jasper file for expense report");

	}

	@Override
	public String generateExpensePDF(Date startDate, Date endDate, Long userId) throws BirdnotesException {
		String fileName = BirdnotesUtils.createFolder(new Date().getTime(), expenseReportPath, uploadPath);
		try {
			generateExpenseReport(fileName + "/" + "generatedExpenseReport.pdf", startDate, endDate, userId);
		} catch (FileNotFoundException | JRException | BirdnotesException e) {
			log.error("Error in generation expense report as PDF ", e);
		}
		return fileName;
	}

}
