package com.intellitech.birdnotes.model.dto;

import java.util.List;

public class ExpenseTypeDto extends KeyValueDto {

	private static final long serialVersionUID = 1L;

	private float price;

	private float mileage;

	private List<Long> delegatesId;

	private List<DelegateDto> delegates;

	private Boolean requiredAttachment;

	public ExpenseTypeDto() {
		super();

	}

	public ExpenseTypeDto(String name, Long id, float price, float mileage, Boolean requiredAttachment) {
		super(name, id);
		this.price = price;
		this.mileage = mileage;
		this.requiredAttachment = requiredAttachment;
	}

	public float getPrice() {
		return price;
	}

	public void setPrice(float price) {
		this.price = price;
	}

	public float getMileage() {
		return mileage;
	}

	public void setMileage(float mileage) {
		this.mileage = mileage;
	}

	public List<Long> getDelegatesId() {
		return delegatesId;
	}

	public void setDelegatesId(List<Long> delegatesId) {
		this.delegatesId = delegatesId;
	}

	public List<DelegateDto> getDelegates() {
		return delegates;
	}

	public void setDelegates(List<DelegateDto> delegates) {
		this.delegates = delegates;
	}

	public Boolean getRequiredAttachment() {
		return requiredAttachment;
	}

	public void setRequiredAttachment(Boolean requiredAttachment) {
		this.requiredAttachment = requiredAttachment;
	}

}
